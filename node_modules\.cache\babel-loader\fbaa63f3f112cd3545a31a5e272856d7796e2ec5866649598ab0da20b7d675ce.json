{"ast": null, "code": "function t() {\n  return /iPhone/gi.test(window.navigator.platform) || /Mac/gi.test(window.navigator.platform) && window.navigator.maxTouchPoints > 0;\n}\nfunction i() {\n  return /Android/gi.test(window.navigator.userAgent);\n}\nfunction n() {\n  return t() || i();\n}\nexport { i as isAndroid, t as isIOS, n as isMobile };", "map": {"version": 3, "names": ["t", "test", "window", "navigator", "platform", "maxTouchPoints", "i", "userAgent", "n", "isAndroid", "isIOS", "isMobile"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/node_modules/@headlessui/react/dist/utils/platform.js"], "sourcesContent": ["function t(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function i(){return/Android/gi.test(window.navigator.userAgent)}function n(){return t()||i()}export{i as isAndroid,t as isIOS,n as isMobile};\n"], "mappings": "AAAA,SAASA,CAACA,CAAA,EAAE;EAAC,OAAM,UAAU,CAACC,IAAI,CAACC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAAC,IAAE,OAAO,CAACH,IAAI,CAACC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAAC,IAAEF,MAAM,CAACC,SAAS,CAACE,cAAc,GAAC,CAAC;AAAA;AAAC,SAASC,CAACA,CAAA,EAAE;EAAC,OAAM,WAAW,CAACL,IAAI,CAACC,MAAM,CAACC,SAAS,CAACI,SAAS,CAAC;AAAA;AAAC,SAASC,CAACA,CAAA,EAAE;EAAC,OAAOR,CAAC,CAAC,CAAC,IAAEM,CAAC,CAAC,CAAC;AAAA;AAAC,SAAOA,CAAC,IAAIG,SAAS,EAACT,CAAC,IAAIU,KAAK,EAACF,CAAC,IAAIG,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}