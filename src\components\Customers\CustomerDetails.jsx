import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { Button } from '../ui/button';

const CustomerDetails = ({ customer, onClose }) => {
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="bg-white rounded-2xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
        >
          {/* الرأس */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">تفاصيل العميل</h2>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>

          {/* المحتوى */}
          <div className="p-6 space-y-6">
            {/* المعلومات الأساسية */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">المعلومات الأساسية</h3>
                <div className="space-y-3">
                  <div>
                    <span className="text-sm font-medium text-gray-600">كود العميل:</span>
                    <p className="text-gray-900">{customer.code}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-600">اسم العميل:</span>
                    <p className="text-gray-900 font-semibold">{customer.name}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-600">نوع العميل:</span>
                    <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
                      customer.type === 'فرد' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'
                    }`}>
                      {customer.type}
                    </span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-600">الحالة:</span>
                    <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
                      customer.status === 'نشط' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {customer.status}
                    </span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-600">عميل منذ:</span>
                    <p className="text-gray-900">{new Date(customer.customerSince).toLocaleDateString('ar-SA')}</p>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">معلومات الاتصال</h3>
                <div className="space-y-3">
                  <div>
                    <span className="text-sm font-medium text-gray-600">رقم الهاتف:</span>
                    <p className="text-gray-900 font-mono">{customer.phone}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-600">البريد الإلكتروني:</span>
                    <p className="text-gray-900">{customer.email || '-'}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-600">الشخص المسؤول:</span>
                    <p className="text-gray-900">{customer.contactPerson || '-'}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-600">الرقم الضريبي:</span>
                    <p className="text-gray-900 font-mono">{customer.taxNumber || '-'}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* العنوان */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">العنوان</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-gray-900">{customer.address || 'لم يتم تحديد العنوان'}</p>
                {customer.city && (
                  <p className="text-gray-600 mt-2">{customer.city}, {customer.country}</p>
                )}
              </div>
            </div>

            {/* المعلومات المالية */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">المعلومات المالية</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <span className="text-sm font-medium text-blue-600">الحد الائتماني</span>
                  <p className="text-xl font-bold text-blue-900">{formatCurrency(customer.creditLimit)}</p>
                </div>
                <div className={`p-4 rounded-lg ${
                  customer.currentBalance > 0 ? 'bg-red-50' : 'bg-green-50'
                }`}>
                  <span className={`text-sm font-medium ${
                    customer.currentBalance > 0 ? 'text-red-600' : 'text-green-600'
                  }`}>
                    الرصيد الحالي
                  </span>
                  <p className={`text-xl font-bold ${
                    customer.currentBalance > 0 ? 'text-red-900' : 'text-green-900'
                  }`}>
                    {formatCurrency(customer.currentBalance)}
                  </p>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg">
                  <span className="text-sm font-medium text-purple-600">إجمالي المشتريات</span>
                  <p className="text-xl font-bold text-purple-900">{formatCurrency(customer.totalPurchases)}</p>
                </div>
                <div className="bg-orange-50 p-4 rounded-lg">
                  <span className="text-sm font-medium text-orange-600">نسبة الخصم</span>
                  <p className="text-xl font-bold text-orange-900">{customer.discount}%</p>
                </div>
              </div>
            </div>

            {/* شروط التعامل */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">شروط التعامل</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <span className="text-sm font-medium text-gray-600">شروط الدفع:</span>
                  <p className="text-gray-900">{customer.paymentTerms}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-gray-600">آخر عملية شراء:</span>
                  <p className="text-gray-900">
                    {customer.lastPurchase ? new Date(customer.lastPurchase).toLocaleDateString('ar-SA') : 'لا توجد مشتريات'}
                  </p>
                </div>
              </div>
            </div>

            {/* الملاحظات */}
            {customer.notes && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">ملاحظات</h3>
                <p className="text-gray-700 bg-gray-50 p-4 rounded-lg">{customer.notes}</p>
              </div>
            )}

            {/* إحصائيات سريعة */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">إحصائيات سريعة</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <p className="text-2xl font-bold text-gray-900">
                    {customer.totalPurchases > 0 ? Math.round(customer.totalPurchases / 12) : 0}
                  </p>
                  <p className="text-sm text-gray-600">متوسط الشراء الشهري (تقديري)</p>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <p className="text-2xl font-bold text-gray-900">
                    {customer.currentBalance > 0 ? Math.ceil(customer.currentBalance / (customer.totalPurchases / 12 || 1)) : 0}
                  </p>
                  <p className="text-sm text-gray-600">أشهر للسداد (تقديري)</p>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <p className="text-2xl font-bold text-gray-900">
                    {((customer.creditLimit - customer.currentBalance) / customer.creditLimit * 100).toFixed(0)}%
                  </p>
                  <p className="text-sm text-gray-600">الائتمان المتاح</p>
                </div>
              </div>
            </div>

            {/* زر الإغلاق */}
            <div className="flex justify-end pt-6 border-t border-gray-200">
              <Button onClick={onClose} variant="outline">
                إغلاق
              </Button>
            </div>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default CustomerDetails;
