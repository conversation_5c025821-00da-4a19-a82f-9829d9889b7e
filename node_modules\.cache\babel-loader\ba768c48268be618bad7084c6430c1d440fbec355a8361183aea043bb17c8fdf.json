{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u0633\\u0637\\u062D \\u0627\\u0644\\u0645\\u0643\\u062A\\u0628\\\\333\\\\src\\\\pages\\\\Dashboard.jsx\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { BuildingOfficeIcon, UsersIcon, ChartBarIcon, CurrencyDollarIcon, ArrowTrendingUpIcon, ArrowTrendingDownIcon } from '@heroicons/react/24/outline';\nimport { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';\nimport StatsCard from '../components/Dashboard/StatsCard';\nimport RecentActivities from '../components/Dashboard/RecentActivities';\nimport CompanyChart from '../components/Dashboard/CompanyChart';\nimport QuickActions from '../components/Dashboard/QuickActions';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  const stats = [{\n    title: 'إجمالي الشركات',\n    value: '156',\n    change: '+12%',\n    changeType: 'increase',\n    icon: BuildingOfficeIcon,\n    color: 'blue'\n  }, {\n    title: 'الشركات النشطة',\n    value: '142',\n    change: '+8%',\n    changeType: 'increase',\n    icon: UsersIcon,\n    color: 'green'\n  }, {\n    title: 'التقارير الشهرية',\n    value: '24',\n    change: '+15%',\n    changeType: 'increase',\n    icon: ChartBarIcon,\n    color: 'purple'\n  }, {\n    title: 'القيمة الإجمالية',\n    value: '2.4M ر.س',\n    change: '-3%',\n    changeType: 'decrease',\n    icon: CurrencyDollarIcon,\n    color: 'orange'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900\",\n          children: \"\\u0644\\u0648\\u062D\\u0629 \\u0627\\u0644\\u062A\\u062D\\u0643\\u0645\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mt-2\",\n          children: \"\\u0645\\u0631\\u062D\\u0628\\u0627\\u064B \\u0628\\u0643\\u060C \\u0625\\u0644\\u064A\\u0643 \\u0646\\u0638\\u0631\\u0629 \\u0639\\u0627\\u0645\\u0629 \\u0639\\u0644\\u0649 \\u0646\\u0634\\u0627\\u0637 \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm text-gray-500\",\n        children: [\"\\u0622\\u062E\\u0631 \\u062A\\u062D\\u062F\\u064A\\u062B: \", new Date().toLocaleDateString('ar-SA')]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n      children: stats.map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: index * 0.1\n        },\n        children: /*#__PURE__*/_jsxDEV(StatsCard, {\n          ...stat\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 13\n        }, this)\n      }, stat.title, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          x: -20\n        },\n        animate: {\n          opacity: 1,\n          x: 0\n        },\n        transition: {\n          delay: 0.4\n        },\n        className: \"lg:col-span-2\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n            children: /*#__PURE__*/_jsxDEV(CardTitle, {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(ChartBarIcon, {\n                className: \"w-5 h-5 ml-2 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 17\n              }, this), \"\\u0646\\u0645\\u0648 \\u0627\\u0644\\u0634\\u0631\\u0643\\u0627\\u062A \\u062E\\u0644\\u0627\\u0644 \\u0627\\u0644\\u0639\\u0627\\u0645\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(CompanyChart, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          x: 20\n        },\n        animate: {\n          opacity: 1,\n          x: 0\n        },\n        transition: {\n          delay: 0.5\n        },\n        children: /*#__PURE__*/_jsxDEV(RecentActivities, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.6\n      },\n      children: /*#__PURE__*/_jsxDEV(QuickActions, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 5\n  }, this);\n};\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "motion", "BuildingOfficeIcon", "UsersIcon", "ChartBarIcon", "CurrencyDollarIcon", "ArrowTrendingUpIcon", "ArrowTrendingDownIcon", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "StatsCard", "RecentActivities", "CompanyChart", "QuickActions", "jsxDEV", "_jsxDEV", "Dashboard", "stats", "title", "value", "change", "changeType", "icon", "color", "className", "children", "div", "initial", "opacity", "y", "animate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Date", "toLocaleDateString", "map", "stat", "index", "transition", "delay", "x", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/src/pages/Dashboard.jsx"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  BuildingOfficeIcon,\n  UsersIcon,\n  ChartBarIcon,\n  CurrencyDollarIcon,\n  ArrowTrendingUpIcon,\n  ArrowTrendingDownIcon\n} from '@heroicons/react/24/outline';\nimport { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';\nimport StatsCard from '../components/Dashboard/StatsCard';\nimport RecentActivities from '../components/Dashboard/RecentActivities';\nimport CompanyChart from '../components/Dashboard/CompanyChart';\nimport QuickActions from '../components/Dashboard/QuickActions';\n\nconst Dashboard = () => {\n  const stats = [\n    {\n      title: 'إجمالي الشركات',\n      value: '156',\n      change: '+12%',\n      changeType: 'increase',\n      icon: BuildingOfficeIcon,\n      color: 'blue'\n    },\n    {\n      title: 'الشركات النشطة',\n      value: '142',\n      change: '+8%',\n      changeType: 'increase',\n      icon: UsersIcon,\n      color: 'green'\n    },\n    {\n      title: 'التقارير الشهرية',\n      value: '24',\n      change: '+15%',\n      changeType: 'increase',\n      icon: ChartBarIcon,\n      color: 'purple'\n    },\n    {\n      title: 'القيمة الإجمالية',\n      value: '2.4M ر.س',\n      change: '-3%',\n      changeType: 'decrease',\n      icon: CurrencyDollarIcon,\n      color: 'orange'\n    }\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* العنوان الرئيسي */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        className=\"flex items-center justify-between\"\n      >\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">لوحة التحكم</h1>\n          <p className=\"text-gray-600 mt-2\">مرحباً بك، إليك نظرة عامة على نشاط النظام</p>\n        </div>\n        <div className=\"text-sm text-gray-500\">\n          آخر تحديث: {new Date().toLocaleDateString('ar-SA')}\n        </div>\n      </motion.div>\n\n      {/* بطاقات الإحصائيات */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {stats.map((stat, index) => (\n          <motion.div\n            key={stat.title}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: index * 0.1 }}\n          >\n            <StatsCard {...stat} />\n          </motion.div>\n        ))}\n      </div>\n\n      {/* الصف الثاني - الرسوم البيانية والأنشطة */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* الرسم البياني */}\n        <motion.div\n          initial={{ opacity: 0, x: -20 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ delay: 0.4 }}\n          className=\"lg:col-span-2\"\n        >\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <ChartBarIcon className=\"w-5 h-5 ml-2 text-blue-600\" />\n                نمو الشركات خلال العام\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <CompanyChart />\n            </CardContent>\n          </Card>\n        </motion.div>\n\n        {/* الأنشطة الأخيرة */}\n        <motion.div\n          initial={{ opacity: 0, x: 20 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ delay: 0.5 }}\n        >\n          <RecentActivities />\n        </motion.div>\n      </div>\n\n      {/* الصف الثالث - الإجراءات السريعة */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.6 }}\n      >\n        <QuickActions />\n      </motion.div>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,kBAAkB,EAClBC,SAAS,EACTC,YAAY,EACZC,kBAAkB,EAClBC,mBAAmB,EACnBC,qBAAqB,QAChB,6BAA6B;AACpC,SAASC,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,SAAS,QAAQ,uBAAuB;AAChF,OAAOC,SAAS,MAAM,mCAAmC;AACzD,OAAOC,gBAAgB,MAAM,0CAA0C;AACvE,OAAOC,YAAY,MAAM,sCAAsC;AAC/D,OAAOC,YAAY,MAAM,sCAAsC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAMC,SAAS,GAAGA,CAAA,KAAM;EACtB,MAAMC,KAAK,GAAG,CACZ;IACEC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,MAAM;IACdC,UAAU,EAAE,UAAU;IACtBC,IAAI,EAAEtB,kBAAkB;IACxBuB,KAAK,EAAE;EACT,CAAC,EACD;IACEL,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,UAAU,EAAE,UAAU;IACtBC,IAAI,EAAErB,SAAS;IACfsB,KAAK,EAAE;EACT,CAAC,EACD;IACEL,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,MAAM;IACdC,UAAU,EAAE,UAAU;IACtBC,IAAI,EAAEpB,YAAY;IAClBqB,KAAK,EAAE;EACT,CAAC,EACD;IACEL,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAE,UAAU;IACjBC,MAAM,EAAE,KAAK;IACbC,UAAU,EAAE,UAAU;IACtBC,IAAI,EAAEnB,kBAAkB;IACxBoB,KAAK,EAAE;EACT,CAAC,CACF;EAED,oBACER,OAAA;IAAKS,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBV,OAAA,CAAChB,MAAM,CAAC2B,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BL,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAE7CV,OAAA;QAAAU,QAAA,gBACEV,OAAA;UAAIS,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAW;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjEnB,OAAA;UAAGS,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAyC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E,CAAC,eACNnB,OAAA;QAAKS,SAAS,EAAC,uBAAuB;QAAAC,QAAA,GAAC,qDAC1B,EAAC,IAAIU,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAC;MAAA;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGbnB,OAAA;MAAKS,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClER,KAAK,CAACoB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrBxB,OAAA,CAAChB,MAAM,CAAC2B,GAAG;QAETC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BW,UAAU,EAAE;UAAEC,KAAK,EAAEF,KAAK,GAAG;QAAI,CAAE;QAAAd,QAAA,eAEnCV,OAAA,CAACL,SAAS;UAAA,GAAK4B;QAAI;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC,GALlBI,IAAI,CAACpB,KAAK;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAML,CACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNnB,OAAA;MAAKS,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDV,OAAA,CAAChB,MAAM,CAAC2B,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEc,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCZ,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEc,CAAC,EAAE;QAAE,CAAE;QAC9BF,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BjB,SAAS,EAAC,eAAe;QAAAC,QAAA,eAEzBV,OAAA,CAACT,IAAI;UAAAmB,QAAA,gBACHV,OAAA,CAACP,UAAU;YAAAiB,QAAA,eACTV,OAAA,CAACN,SAAS;cAACe,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBACtCV,OAAA,CAACb,YAAY;gBAACsB,SAAS,EAAC;cAA4B;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,yHAEzD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACbnB,OAAA,CAACR,WAAW;YAAAkB,QAAA,eACVV,OAAA,CAACH,YAAY;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAGbnB,OAAA,CAAChB,MAAM,CAAC2B,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEc,CAAC,EAAE;QAAG,CAAE;QAC/BZ,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEc,CAAC,EAAE;QAAE,CAAE;QAC9BF,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAhB,QAAA,eAE3BV,OAAA,CAACJ,gBAAgB;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGNnB,OAAA,CAAChB,MAAM,CAAC2B,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BW,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAAAhB,QAAA,eAE3BV,OAAA,CAACF,YAAY;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAACS,EAAA,GA7GI3B,SAAS;AA+Gf,eAAeA,SAAS;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}