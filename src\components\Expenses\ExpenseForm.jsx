import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import expensesAPI from '../../api/expensesAPI';
import toast from 'react-hot-toast';

const ExpenseForm = ({ expense, isEditing, onSave, onClose }) => {
  const [formData, setFormData] = useState({
    description: '',
    category: '',
    amount: 0,
    date: new Date().toISOString().split('T')[0],
    paymentMethod: 'نقدي',
    vendor: '',
    reference: '',
    approvedBy: '',
    notes: '',
    receiptNumber: '',
    taxAmount: 0,
    isRecurring: false,
    recurringPeriod: ''
  });

  const [categories, setCategories] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadCategories();
    
    if (isEditing && expense) {
      setFormData({
        description: expense.description || '',
        category: expense.category || '',
        amount: expense.amount || 0,
        date: expense.date || new Date().toISOString().split('T')[0],
        paymentMethod: expense.paymentMethod || 'نقدي',
        vendor: expense.vendor || '',
        reference: expense.reference || '',
        approvedBy: expense.approvedBy || '',
        notes: expense.notes || '',
        receiptNumber: expense.receiptNumber || '',
        taxAmount: expense.taxAmount || 0,
        isRecurring: expense.isRecurring || false,
        recurringPeriod: expense.recurringPeriod || ''
      });
    }
  }, [isEditing, expense]);

  useEffect(() => {
    // حساب الضريبة تلقائياً (15%)
    const tax = formData.amount * 0.15;
    setFormData(prev => ({ ...prev, taxAmount: tax }));
  }, [formData.amount]);

  const loadCategories = () => {
    const categoriesData = expensesAPI.getCategories();
    setCategories(categoriesData);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.description.trim()) {
      toast.error('يرجى إدخال وصف المصروف');
      return;
    }

    if (!formData.category.trim()) {
      toast.error('يرجى اختيار فئة المصروف');
      return;
    }

    if (formData.amount <= 0) {
      toast.error('يرجى إدخال مبلغ صحيح');
      return;
    }

    setIsLoading(true);
    try {
      await onSave(formData);
    } catch (error) {
      toast.error('حدث خطأ أثناء حفظ المصروف');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCategoryChange = (category) => {
    setFormData(prev => ({ ...prev, category }));
    
    // إضافة فئة جديدة إذا لم تكن موجودة
    if (category && !categories.includes(category)) {
      expensesAPI.addCategory(category);
      setCategories(prev => [...prev, category]);
    }
  };

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="bg-white rounded-2xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
        >
          {/* الرأس */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">
              {isEditing ? 'تعديل المصروف' : 'مصروف جديد'}
            </h2>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>

          {/* المحتوى */}
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* المعلومات الأساسية */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">المعلومات الأساسية</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="md:col-span-2">
                  <Label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                    وصف المصروف *
                  </Label>
                  <Input
                    id="description"
                    type="text"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="أدخل وصف المصروف"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
                    الفئة *
                  </Label>
                  <input
                    id="category"
                    list="categories"
                    value={formData.category}
                    onChange={(e) => handleCategoryChange(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    placeholder="اختر أو أدخل فئة جديدة"
                    required
                  />
                  <datalist id="categories">
                    {categories.map((category, index) => (
                      <option key={index} value={category} />
                    ))}
                  </datalist>
                </div>

                <div>
                  <Label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-2">
                    المبلغ *
                  </Label>
                  <Input
                    id="amount"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.amount}
                    onChange={(e) => setFormData(prev => ({ ...prev, amount: parseFloat(e.target.value) || 0 }))}
                    placeholder="0.00"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-2">
                    التاريخ *
                  </Label>
                  <Input
                    id="date"
                    type="date"
                    value={formData.date}
                    onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="vendor" className="block text-sm font-medium text-gray-700 mb-2">
                    المورد/الجهة
                  </Label>
                  <Input
                    id="vendor"
                    type="text"
                    value={formData.vendor}
                    onChange={(e) => setFormData(prev => ({ ...prev, vendor: e.target.value }))}
                    placeholder="اسم المورد أو الجهة"
                  />
                </div>
              </div>
            </div>

            {/* تفاصيل الدفع */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">تفاصيل الدفع</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <Label htmlFor="paymentMethod" className="block text-sm font-medium text-gray-700 mb-2">
                    طريقة الدفع
                  </Label>
                  <select
                    id="paymentMethod"
                    value={formData.paymentMethod}
                    onChange={(e) => setFormData(prev => ({ ...prev, paymentMethod: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                  >
                    <option value="نقدي">نقدي</option>
                    <option value="تحويل بنكي">تحويل بنكي</option>
                    <option value="بطاقة ائتمان">بطاقة ائتمان</option>
                    <option value="شيك">شيك</option>
                  </select>
                </div>

                <div>
                  <Label htmlFor="reference" className="block text-sm font-medium text-gray-700 mb-2">
                    رقم المرجع
                  </Label>
                  <Input
                    id="reference"
                    type="text"
                    value={formData.reference}
                    onChange={(e) => setFormData(prev => ({ ...prev, reference: e.target.value }))}
                    placeholder="رقم الفاتورة أو المرجع"
                  />
                </div>

                <div>
                  <Label htmlFor="receiptNumber" className="block text-sm font-medium text-gray-700 mb-2">
                    رقم الإيصال
                  </Label>
                  <Input
                    id="receiptNumber"
                    type="text"
                    value={formData.receiptNumber}
                    onChange={(e) => setFormData(prev => ({ ...prev, receiptNumber: e.target.value }))}
                    placeholder="رقم الإيصال"
                  />
                </div>
              </div>
            </div>

            {/* الضريبة والموافقة */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">تفاصيل إضافية</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="taxAmount" className="block text-sm font-medium text-gray-700 mb-2">
                    مبلغ الضريبة (15%)
                  </Label>
                  <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg">
                    {new Intl.NumberFormat('ar-SA', {
                      style: 'currency',
                      currency: 'SAR'
                    }).format(formData.taxAmount)}
                  </div>
                </div>

                <div>
                  <Label htmlFor="approvedBy" className="block text-sm font-medium text-gray-700 mb-2">
                    معتمد من
                  </Label>
                  <Input
                    id="approvedBy"
                    type="text"
                    value={formData.approvedBy}
                    onChange={(e) => setFormData(prev => ({ ...prev, approvedBy: e.target.value }))}
                    placeholder="اسم الشخص المعتمد"
                  />
                </div>
              </div>

              {/* المصروف المتكرر */}
              <div className="mt-6">
                <div className="flex items-center space-x-3 space-x-reverse">
                  <input
                    id="isRecurring"
                    type="checkbox"
                    checked={formData.isRecurring}
                    onChange={(e) => setFormData(prev => ({ ...prev, isRecurring: e.target.checked }))}
                    className="w-4 h-4 text-red-600 border-gray-300 rounded focus:ring-red-500"
                  />
                  <Label htmlFor="isRecurring" className="text-sm font-medium text-gray-700">
                    مصروف متكرر
                  </Label>
                </div>

                {formData.isRecurring && (
                  <div className="mt-4">
                    <Label htmlFor="recurringPeriod" className="block text-sm font-medium text-gray-700 mb-2">
                      فترة التكرار
                    </Label>
                    <select
                      id="recurringPeriod"
                      value={formData.recurringPeriod}
                      onChange={(e) => setFormData(prev => ({ ...prev, recurringPeriod: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    >
                      <option value="">اختر فترة التكرار</option>
                      <option value="أسبوعي">أسبوعي</option>
                      <option value="شهري">شهري</option>
                      <option value="ربع سنوي">ربع سنوي</option>
                      <option value="نصف سنوي">نصف سنوي</option>
                      <option value="سنوي">سنوي</option>
                    </select>
                  </div>
                )}
              </div>
            </div>

            {/* الملاحظات */}
            <div>
              <Label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
                ملاحظات
              </Label>
              <textarea
                id="notes"
                rows={3}
                value={formData.notes}
                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                placeholder="أدخل أي ملاحظات إضافية..."
              />
            </div>

            {/* ملخص المبلغ */}
            <div className="bg-gray-50 p-6 rounded-lg">
              <div className="flex justify-between items-center text-lg">
                <span className="font-semibold text-gray-900">إجمالي المصروف (شامل الضريبة):</span>
                <span className="text-2xl font-bold text-red-600">
                  {new Intl.NumberFormat('ar-SA', {
                    style: 'currency',
                    currency: 'SAR'
                  }).format(formData.amount + formData.taxAmount)}
                </span>
              </div>
            </div>

            {/* الأزرار */}
            <div className="flex items-center justify-end space-x-4 space-x-reverse pt-6 border-t border-gray-200">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isLoading}
              >
                إلغاء
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ml-2"></div>
                    جاري الحفظ...
                  </div>
                ) : (
                  isEditing ? 'حفظ التعديلات' : 'حفظ المصروف'
                )}
              </Button>
            </div>
          </form>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default ExpenseForm;
