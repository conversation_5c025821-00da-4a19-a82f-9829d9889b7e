// أداة اختبار QR Code للفوترة الإلكترونية

import { 
  generateQRCodeData, 
  decodeQRCodeData, 
  testQRCodeReading,
  validateSaudiVATNumber 
} from './qrCodeGenerator';

/**
 * اختبار شامل لـ QR Code
 * @param {Object} invoiceData - بيانات الفاتورة
 * @returns {Object} نتائج الاختبار
 */
export const comprehensiveQRTest = (invoiceData) => {
  const results = {
    generation: { success: false, data: null, error: null },
    reading: { success: false, data: null, error: null },
    validation: { success: false, issues: [] },
    performance: { generationTime: 0, readingTime: 0 }
  };

  try {
    // اختبار إنشاء QR Code
    const startGeneration = performance.now();
    const qrData = generateQRCodeData(invoiceData);
    const endGeneration = performance.now();
    
    results.generation.success = !!qrData;
    results.generation.data = qrData;
    results.performance.generationTime = endGeneration - startGeneration;

    if (qrData) {
      // اختبار قراءة QR Code
      const startReading = performance.now();
      const readingResult = testQRCodeReading(qrData);
      const endReading = performance.now();
      
      results.reading.success = readingResult.isValid && readingResult.hasAllRequiredFields;
      results.reading.data = readingResult.data;
      results.performance.readingTime = endReading - startReading;

      // التحقق من صحة البيانات
      if (readingResult.data) {
        const validationIssues = [];
        
        // التحقق من الرقم الضريبي
        if (!validateSaudiVATNumber(readingResult.data.vatNumber)) {
          validationIssues.push('الرقم الضريبي غير صحيح');
        }
        
        // التحقق من اسم البائع
        if (!readingResult.data.sellerName || readingResult.data.sellerName.trim().length === 0) {
          validationIssues.push('اسم البائع مفقود');
        }
        
        // التحقق من الطابع الزمني
        try {
          new Date(readingResult.data.timestamp);
        } catch (e) {
          validationIssues.push('تنسيق الطابع الزمني غير صحيح');
        }
        
        // التحقق من المبالغ
        const invoiceTotal = parseFloat(readingResult.data.invoiceTotal);
        const vatAmount = parseFloat(readingResult.data.vatAmount);
        
        if (isNaN(invoiceTotal) || invoiceTotal <= 0) {
          validationIssues.push('إجمالي الفاتورة غير صحيح');
        }
        
        if (isNaN(vatAmount) || vatAmount < 0) {
          validationIssues.push('مبلغ الضريبة غير صحيح');
        }
        
        // التحقق من نسبة الضريبة (15%)
        if (!isNaN(invoiceTotal) && !isNaN(vatAmount)) {
          const expectedVat = (invoiceTotal - vatAmount) * 0.15;
          const vatDifference = Math.abs(vatAmount - expectedVat);
          
          if (vatDifference > 0.01) { // هامش خطأ صغير
            validationIssues.push('نسبة الضريبة لا تتطابق مع 15%');
          }
        }
        
        results.validation.success = validationIssues.length === 0;
        results.validation.issues = validationIssues;
      }
    }
    
  } catch (error) {
    results.generation.error = error.message;
    results.reading.error = error.message;
  }

  return results;
};

/**
 * اختبار QR Code مع بيانات متنوعة
 * @returns {Array} نتائج الاختبارات
 */
export const runMultipleQRTests = () => {
  const testCases = [
    {
      name: 'فاتورة عادية',
      invoice: {
        id: 1,
        invoiceNumber: 'INV-2024-001',
        customerName: 'أحمد محمد',
        date: '2024-01-15',
        total: 1150,
        tax: 150,
        items: [{ productName: 'منتج تجريبي', quantity: 1, price: 1000, total: 1000 }]
      }
    },
    {
      name: 'فاتورة بمبلغ كبير',
      invoice: {
        id: 2,
        invoiceNumber: 'INV-2024-002',
        customerName: 'شركة التجارة المحدودة',
        date: '2024-01-16',
        total: 115000,
        tax: 15000,
        items: [{ productName: 'معدات مكتبية', quantity: 10, price: 10000, total: 100000 }]
      }
    },
    {
      name: 'فاتورة بمبلغ صغير',
      invoice: {
        id: 3,
        invoiceNumber: 'INV-2024-003',
        customerName: 'عميل فردي',
        date: '2024-01-17',
        total: 23,
        tax: 3,
        items: [{ productName: 'قلم', quantity: 1, price: 20, total: 20 }]
      }
    },
    {
      name: 'فاتورة بأحرف خاصة',
      invoice: {
        id: 4,
        invoiceNumber: 'INV-2024-004',
        customerName: 'شركة الأعمال & التجارة (المحدودة)',
        date: '2024-01-18',
        total: 575,
        tax: 75,
        items: [{ productName: 'منتج بأحرف خاصة @#$%', quantity: 2, price: 250, total: 500 }]
      }
    }
  ];

  return testCases.map(testCase => ({
    ...testCase,
    result: comprehensiveQRTest(testCase.invoice)
  }));
};

/**
 * تقرير شامل عن حالة QR Code
 * @param {Array} testResults - نتائج الاختبارات
 * @returns {Object} تقرير شامل
 */
export const generateQRReport = (testResults) => {
  const report = {
    summary: {
      totalTests: testResults.length,
      passedTests: 0,
      failedTests: 0,
      averageGenerationTime: 0,
      averageReadingTime: 0
    },
    details: [],
    recommendations: []
  };

  let totalGenerationTime = 0;
  let totalReadingTime = 0;

  testResults.forEach(test => {
    const passed = test.result.generation.success && 
                   test.result.reading.success && 
                   test.result.validation.success;
    
    if (passed) {
      report.summary.passedTests++;
    } else {
      report.summary.failedTests++;
    }

    totalGenerationTime += test.result.performance.generationTime;
    totalReadingTime += test.result.performance.readingTime;

    report.details.push({
      testName: test.name,
      passed: passed,
      issues: test.result.validation.issues,
      generationTime: test.result.performance.generationTime.toFixed(2),
      readingTime: test.result.performance.readingTime.toFixed(2)
    });
  });

  report.summary.averageGenerationTime = (totalGenerationTime / testResults.length).toFixed(2);
  report.summary.averageReadingTime = (totalReadingTime / testResults.length).toFixed(2);

  // توصيات
  if (report.summary.failedTests > 0) {
    report.recommendations.push('يوجد اختبارات فاشلة - يرجى مراجعة إعدادات الشركة');
  }

  if (parseFloat(report.summary.averageGenerationTime) > 10) {
    report.recommendations.push('وقت إنشاء QR Code مرتفع - قد تحتاج لتحسين الأداء');
  }

  if (parseFloat(report.summary.averageReadingTime) > 5) {
    report.recommendations.push('وقت قراءة QR Code مرتفع - قد تحتاج لتحسين خوارزمية القراءة');
  }

  if (report.summary.passedTests === report.summary.totalTests) {
    report.recommendations.push('جميع الاختبارات نجحت - النظام جاهز للإنتاج');
  }

  return report;
};

/**
 * اختبار سريع لـ QR Code
 * @param {Object} invoiceData - بيانات الفاتورة
 * @returns {boolean} نجح الاختبار أم لا
 */
export const quickQRTest = (invoiceData) => {
  try {
    const qrData = generateQRCodeData(invoiceData);
    if (!qrData) return false;
    
    const readingResult = testQRCodeReading(qrData);
    return readingResult.isValid && readingResult.hasAllRequiredFields;
  } catch (error) {
    console.error('خطأ في الاختبار السريع:', error);
    return false;
  }
};

/**
 * مقارنة QR Code مع البيانات الأصلية
 * @param {Object} originalData - البيانات الأصلية
 * @param {string} qrData - بيانات QR Code
 * @returns {Object} نتيجة المقارنة
 */
export const compareQRWithOriginal = (originalData, qrData) => {
  const decodedData = decodeQRCodeData(qrData);
  
  if (!decodedData) {
    return { match: false, error: 'فشل في فك تشفير QR Code' };
  }

  const comparison = {
    match: true,
    differences: []
  };

  // مقارنة المبالغ
  const originalTotal = originalData.total.toFixed(2);
  const decodedTotal = decodedData.invoiceTotal;
  
  if (originalTotal !== decodedTotal) {
    comparison.match = false;
    comparison.differences.push({
      field: 'إجمالي الفاتورة',
      original: originalTotal,
      decoded: decodedTotal
    });
  }

  const originalTax = originalData.tax.toFixed(2);
  const decodedTax = decodedData.vatAmount;
  
  if (originalTax !== decodedTax) {
    comparison.match = false;
    comparison.differences.push({
      field: 'مبلغ الضريبة',
      original: originalTax,
      decoded: decodedTax
    });
  }

  return comparison;
};
