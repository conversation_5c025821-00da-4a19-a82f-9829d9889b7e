{"ast": null, "code": "import * as l from \"react\";\nfunction i(e, t) {\n  return e === t && (e !== 0 || 1 / e === 1 / t) || e !== e && t !== t;\n}\nconst d = typeof Object.is == \"function\" ? Object.is : i,\n  {\n    useState: u,\n    useEffect: h,\n    useLayoutEffect: f,\n    useDebugValue: p\n  } = l;\nlet S = !1,\n  _ = !1;\nfunction y(e, t, c) {\n  const a = t(),\n    [{\n      inst: n\n    }, o] = u({\n      inst: {\n        value: a,\n        getSnapshot: t\n      }\n    });\n  return f(() => {\n    n.value = a, n.getSnapshot = t, r(n) && o({\n      inst: n\n    });\n  }, [e, a, t]), h(() => (r(n) && o({\n    inst: n\n  }), e(() => {\n    r(n) && o({\n      inst: n\n    });\n  })), [e]), p(a), a;\n}\nfunction r(e) {\n  const t = e.getSnapshot,\n    c = e.value;\n  try {\n    const a = t();\n    return !d(c, a);\n  } catch {\n    return !0;\n  }\n}\nexport { y as useSyncExternalStore };", "map": {"version": 3, "names": ["l", "i", "e", "t", "d", "Object", "is", "useState", "u", "useEffect", "h", "useLayoutEffect", "f", "useDebugValue", "p", "S", "_", "y", "c", "a", "inst", "n", "o", "value", "getSnapshot", "r", "useSyncExternalStore"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/node_modules/@headlessui/react/dist/use-sync-external-store-shim/useSyncExternalStoreShimClient.js"], "sourcesContent": ["import*as l from\"react\";function i(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}const d=typeof Object.is==\"function\"?Object.is:i,{useState:u,useEffect:h,useLayoutEffect:f,useDebugValue:p}=l;let S=!1,_=!1;function y(e,t,c){const a=t(),[{inst:n},o]=u({inst:{value:a,getSnapshot:t}});return f(()=>{n.value=a,n.getSnapshot=t,r(n)&&o({inst:n})},[e,a,t]),h(()=>(r(n)&&o({inst:n}),e(()=>{r(n)&&o({inst:n})})),[e]),p(a),a}function r(e){const t=e.getSnapshot,c=e.value;try{const a=t();return!d(c,a)}catch{return!0}}export{y as useSyncExternalStore};\n"], "mappings": "AAAA,OAAM,KAAIA,CAAC,MAAK,OAAO;AAAC,SAASC,CAACA,CAACC,CAAC,EAACC,CAAC,EAAC;EAAC,OAAOD,CAAC,KAAGC,CAAC,KAAGD,CAAC,KAAG,CAAC,IAAE,CAAC,GAACA,CAAC,KAAG,CAAC,GAACC,CAAC,CAAC,IAAED,CAAC,KAAGA,CAAC,IAAEC,CAAC,KAAGA,CAAC;AAAA;AAAC,MAAMC,CAAC,GAAC,OAAOC,MAAM,CAACC,EAAE,IAAE,UAAU,GAACD,MAAM,CAACC,EAAE,GAACL,CAAC;EAAC;IAACM,QAAQ,EAACC,CAAC;IAACC,SAAS,EAACC,CAAC;IAACC,eAAe,EAACC,CAAC;IAACC,aAAa,EAACC;EAAC,CAAC,GAACd,CAAC;AAAC,IAAIe,CAAC,GAAC,CAAC,CAAC;EAACC,CAAC,GAAC,CAAC,CAAC;AAAC,SAASC,CAACA,CAACf,CAAC,EAACC,CAAC,EAACe,CAAC,EAAC;EAAC,MAAMC,CAAC,GAAChB,CAAC,CAAC,CAAC;IAAC,CAAC;MAACiB,IAAI,EAACC;IAAC,CAAC,EAACC,CAAC,CAAC,GAACd,CAAC,CAAC;MAACY,IAAI,EAAC;QAACG,KAAK,EAACJ,CAAC;QAACK,WAAW,EAACrB;MAAC;IAAC,CAAC,CAAC;EAAC,OAAOS,CAAC,CAAC,MAAI;IAACS,CAAC,CAACE,KAAK,GAACJ,CAAC,EAACE,CAAC,CAACG,WAAW,GAACrB,CAAC,EAACsB,CAAC,CAACJ,CAAC,CAAC,IAAEC,CAAC,CAAC;MAACF,IAAI,EAACC;IAAC,CAAC,CAAC;EAAA,CAAC,EAAC,CAACnB,CAAC,EAACiB,CAAC,EAAChB,CAAC,CAAC,CAAC,EAACO,CAAC,CAAC,OAAKe,CAAC,CAACJ,CAAC,CAAC,IAAEC,CAAC,CAAC;IAACF,IAAI,EAACC;EAAC,CAAC,CAAC,EAACnB,CAAC,CAAC,MAAI;IAACuB,CAAC,CAACJ,CAAC,CAAC,IAAEC,CAAC,CAAC;MAACF,IAAI,EAACC;IAAC,CAAC,CAAC;EAAA,CAAC,CAAC,CAAC,EAAC,CAACnB,CAAC,CAAC,CAAC,EAACY,CAAC,CAACK,CAAC,CAAC,EAACA,CAAC;AAAA;AAAC,SAASM,CAACA,CAACvB,CAAC,EAAC;EAAC,MAAMC,CAAC,GAACD,CAAC,CAACsB,WAAW;IAACN,CAAC,GAAChB,CAAC,CAACqB,KAAK;EAAC,IAAG;IAAC,MAAMJ,CAAC,GAAChB,CAAC,CAAC,CAAC;IAAC,OAAM,CAACC,CAAC,CAACc,CAAC,EAACC,CAAC,CAAC;EAAA,CAAC,OAAK;IAAC,OAAM,CAAC,CAAC;EAAA;AAAC;AAAC,SAAOF,CAAC,IAAIS,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}