import React, { useState, useEffect } from 'react';
import { PencilIcon, TrashIcon, PlusIcon, MagnifyingGlassIcon, BuildingOfficeIcon } from '@heroicons/react/24/outline';
import { Loader2 } from 'lucide-react';
import toast, { Toaster } from 'react-hot-toast';

const CompanyManagement = () => {
  // تخزين البيانات في localStorage
  const STORAGE_KEY = 'companyManagementData';
  
  // الحصول على البيانات من localStorage
  const getStoredData = () => {
    const storedData = localStorage.getItem(STORAGE_KEY);
    return storedData ? JSON.parse(storedData) : { companies: [], lastId: 0 };
  };
  
  // حفظ البيانات في localStorage
  const saveStoredData = (data) => {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
  };
  
  // حالة النظام
  const [companies, setCompanies] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentCompany, setCurrentCompany] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    tax_id: '',
    address: '',
    contact_person: '',
    email: '',
    phone: ''
  });
  const [formErrors, setFormErrors] = useState({});
  const [submitting, setSubmitting] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isAdmin] = useState(true); // يمكنك تغيير هذه القيمة لتجربة صلاحيات مختلفة

  // جلب البيانات الأولية
  useEffect(() => {
    const fetchData = () => {
      try {
        const data = getStoredData();
        setCompanies(data.companies);
        
        // إضافة بيانات تجريبية إذا لم يكن هناك بيانات
        if (data.companies.length === 0) {
          const demoData = {
            companies: [
              {
                company_id: 1,
                name: 'شركة التقنية المتقدمة',
                tax_id: '1234567890',
                address: 'الرياض، شارع الملك فهد',
                contact_person: 'أحمد محمد',
                email: '<EMAIL>',
                phone: '+966112233445'
              },
              {
                company_id: 2,
                name: 'شركة التطوير الحديثة',
                tax_id: '0987654321',
                address: 'جدة، حي الصفا',
                contact_person: 'سارة عبدالله',
                email: '<EMAIL>',
                phone: '+966556677889'
              },
              {
                company_id: 3,
                name: 'شركة الحلول الذكية',
                tax_id: '4567890123',
                address: 'الدمام، طريق الملك عبدالعزيز',
                contact_person: 'خالد سعيد',
                email: '<EMAIL>',
                phone: '+966990011223'
              }
            ],
            lastId: 3
          };
          saveStoredData(demoData);
          setCompanies(demoData.companies);
        }
      } catch (err) {
        toast.error('حدث خطأ في تحميل البيانات');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, []);

  // التحقق من صحة النموذج
  const validateForm = () => {
    const errors = {};
    if (!formData.name.trim()) errors.name = 'اسم الشركة مطلوب';
    if (!formData.tax_id.trim()) errors.tax_id = 'الرقم الضريبي مطلوب';
    if (formData.email && !/^\S+@\S+\.\S+$/.test(formData.email)) errors.email = 'بريد إلكتروني غير صالح';
    if (formData.phone && !/^\+?[0-9\s-]{7,}$/.test(formData.phone)) errors.phone = 'رقم هاتف غير صالح';
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (e) => {
    const { id, value } = e.target;
    setFormData(prev => ({ ...prev, [id]: value }));
    
    // مسح الخطأ عند الكتابة
    if (formErrors[id]) {
      setFormErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[id];
        return newErrors;
      });
    }
  };

  // حفظ الشركة
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) return;
    
    setSubmitting(true);
    
    try {
      const data = getStoredData();
      
      if (currentCompany) {
        // تحديث الشركة
        const updatedCompanies = data.companies.map(c => 
          c.company_id === currentCompany.company_id ? { ...formData, company_id: currentCompany.company_id } : c
        );
        
        saveStoredData({
          companies: updatedCompanies,
          lastId: data.lastId
        });
        
        setCompanies(updatedCompanies);
        toast.success('تم تحديث بيانات الشركة بنجاح');
      } else {
        // إضافة شركة جديدة
        const newCompany = { 
          ...formData, 
          company_id: data.lastId + 1 
        };
        
        saveStoredData({
          companies: [...data.companies, newCompany],
          lastId: data.lastId + 1
        });
        
        setCompanies([...data.companies, newCompany]);
        toast.success('تم إضافة الشركة بنجاح');
      }
      
      closeModal();
    } catch (err) {
      toast.error('فشل في حفظ بيانات الشركة');
    } finally {
      setSubmitting(false);
    }
  };

  // حذف الشركة
  const handleDelete = (companyId) => {
    if (!window.confirm('هل أنت متأكد أنك تريد حذف هذه الشركة؟')) return;
    
    try {
      const data = getStoredData();
      const updatedCompanies = data.companies.filter(c => c.company_id !== companyId);
      
      saveStoredData({
        companies: updatedCompanies,
        lastId: data.lastId
      });
      
      setCompanies(updatedCompanies);
      toast.success('تم حذف الشركة بنجاح');
    } catch (err) {
      toast.error('فشل في حذف الشركة');
    }
  };

  // فتح نموذج التعديل
  const openEditModal = (company) => {
    setCurrentCompany(company);
    setFormData({
      name: company.name,
      tax_id: company.tax_id,
      address: company.address,
      contact_person: company.contact_person,
      email: company.email,
      phone: company.phone
    });
    setIsModalOpen(true);
  };

  // فتح نموذج الإضافة
  const openAddModal = () => {
    setCurrentCompany(null);
    setFormData({
      name: '',
    tax_id: '',
    address: '',
    contact_person: '',
    email: '',
    phone: ''
    });
    setFormErrors({});
    setIsModalOpen(true);
  };

  // إغلاق النموذج
  const closeModal = () => {
    setIsModalOpen(false);
    setCurrentCompany(null);
    setFormErrors({});
  };

  // فلترة الشركات حسب البحث
  const filteredCompanies = companies.filter(company => 
    company.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    company.tax_id.includes(searchTerm) ||
    (company.contact_person && company.contact_person.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (company.email && company.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (company.phone && company.phone.includes(searchTerm))
  );

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-lg text-gray-600">جاري تحميل بيانات الشركات...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 md:p-6 max-w-6xl">
      <Toaster position="top-left" />
      
      <div className="bg-white rounded-xl shadow-lg border">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-t-xl">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
              <h1 className="text-2xl md:text-3xl font-bold text-gray-800">إدارة الشركات</h1>
              <p className="mt-2 text-gray-600">
                قم بإدارة بيانات الشركات المسجلة في النظام
              </p>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-3 w-full md:w-auto">
              <div className="relative w-full">
                <input 
                  type="text" 
                  placeholder="ابحث عن شركة..." 
                  className="w-full px-4 py-2 pl-10 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              </div>
              
              {isAdmin && (
                <button 
                  onClick={openAddModal}
                  className="flex items-center justify-center bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700 hover:to-indigo-800 text-white px-4 py-2 rounded-lg"
                >
                  <PlusIcon className="h-5 w-5 ml-2" /> 
                  <span>إضافة شركة جديدة</span>
                </button>
              )}
            </div>
          </div>
        </div>
        
        {/* Content */}
        <div className="p-0">
          {filteredCompanies.length === 0 ? (
            <div className="text-center py-16 border rounded-lg m-6">
              <div className="mx-auto bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mb-4">
                <BuildingOfficeIcon className="w-8 h-8 text-gray-500" />
              </div>
              <p className="text-gray-500 text-lg">لا توجد شركات مسجلة</p>
              {isAdmin && (
                <button 
                  onClick={openAddModal} 
                  className="mt-4 flex items-center justify-center mx-auto bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700 hover:to-indigo-800 text-white px-4 py-2 rounded-lg"
                >
                  <PlusIcon className="h-5 w-5 ml-2" /> 
                  <span>إضافة شركة جديدة</span>
                </button>
              )}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="py-3 px-4 text-right font-semibold text-gray-700">اسم الشركة</th>
                    <th className="py-3 px-4 text-right font-semibold text-gray-700">الرقم الضريبي</th>
                    <th className="py-3 px-4 text-right font-semibold text-gray-700">شخص الاتصال</th>
                    <th className="py-3 px-4 text-right font-semibold text-gray-700">البريد الإلكتروني</th>
                    <th className="py-3 px-4 text-right font-semibold text-gray-700">الهاتف</th>
                    {isAdmin && <th className="py-3 px-4 text-center font-semibold text-gray-700">الإجراءات</th>}
                  </tr>
                </thead>
                <tbody>
                  {filteredCompanies.map((company) => (
                    <tr key={company.company_id} className="border-t hover:bg-gray-50 transition-colors">
                      <td className="py-3 px-4 text-gray-900 font-medium">{company.name}</td>
                      <td className="py-3 px-4 text-gray-700">{company.tax_id}</td>
                      <td className="py-3 px-4 text-gray-700">{company.contact_person}</td>
                      <td className="py-3 px-4 text-gray-700">
                        <a href={`mailto:${company.email}`} className="text-blue-600 hover:underline">
                          {company.email}
                        </a>
                      </td>
                      <td className="py-3 px-4 text-gray-700">
                        <a href={`tel:${company.phone}`} className="text-blue-600 hover:underline">
                          {company.phone}
                        </a>
                      </td>
                      {isAdmin && (
                        <td className="py-3 px-4 flex justify-center space-x-2">
                          <button 
                            onClick={() => openEditModal(company)}
                            className="p-2 text-blue-600 rounded-lg border border-blue-200 hover:bg-blue-50"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </button>
                          <button 
                            onClick={() => handleDelete(company.company_id)}
                            className="p-2 text-red-600 rounded-lg border border-red-200 hover:bg-red-50"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </td>
                      )}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
      
      {/* نموذج إضافة/تعديل الشركة */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl">
            <div className="p-6">
              <div className="flex justify-between items-center border-b pb-4">
                <h2 className="text-xl font-bold text-gray-800">
                  {currentCompany ? 'تعديل شركة' : 'إضافة شركة جديدة'}
                </h2>
                <button onClick={closeModal} className="text-gray-500 hover:text-gray-700">
                  &times;
                </button>
              </div>
              
              <form onSubmit={handleSubmit} className="space-y-4 py-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="name" className="block text-gray-700 mb-2">اسم الشركة *</label>
                    <input
                      id="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${formErrors.name ? 'border-red-500' : ''}`}
                    />
                    {formErrors.name && <p className="text-red-500 text-sm mt-1">{formErrors.name}</p>}
                  </div>
                  
                  <div>
                    <label htmlFor="tax_id" className="block text-gray-700 mb-2">الرقم الضريبي *</label>
                    <input
                      id="tax_id"
                      value={formData.tax_id}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${formErrors.tax_id ? 'border-red-500' : ''}`}
                    />
                    {formErrors.tax_id && <p className="text-red-500 text-sm mt-1">{formErrors.tax_id}</p>}
                  </div>
                  
                  <div>
                    <label htmlFor="address" className="block text-gray-700 mb-2">العنوان</label>
                    <input
                      id="address"
                      value={formData.address}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="contact_person" className="block text-gray-700 mb-2">شخص الاتصال</label>
                    <input
                      id="contact_person"
                      value={formData.contact_person}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="email" className="block text-gray-700 mb-2">البريد الإلكتروني</label>
                    <input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${formErrors.email ? 'border-red-500' : ''}`}
                    />
                    {formErrors.email && <p className="text-red-500 text-sm mt-1">{formErrors.email}</p>}
                  </div>
                  
                  <div>
                    <label htmlFor="phone" className="block text-gray-700 mb-2">رقم الهاتف</label>
                    <input
                      id="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${formErrors.phone ? 'border-red-500' : ''}`}
                    />
                    {formErrors.phone && <p className="text-red-500 text-sm mt-1">{formErrors.phone}</p>}
                  </div>
                </div>
                
                <div className="flex justify-end space-x-3 pt-4 border-t mt-6">
                  <button 
                    type="button" 
                    onClick={closeModal}
                    className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
                  >
                    إلغاء
                  </button>
                  <button 
                    type="submit" 
                    disabled={submitting}
                    className="px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg hover:from-blue-700 hover:to-indigo-800 disabled:opacity-50"
                  >
                    {submitting ? (
                      <span className="flex items-center">
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        جاري الحفظ...
                      </span>
                    ) : currentCompany ? 'حفظ التعديلات' : 'إضافة شركة'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CompanyManagement;