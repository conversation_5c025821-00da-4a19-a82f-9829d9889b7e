import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  BuildingOfficeIcon, 
  CheckCircleIcon,
  ExclamationTriangleIcon,
  DocumentTextIcon,
  BanknotesIcon,
  PhoneIcon,
  EnvelopeIcon
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { getCompanyConfig, saveCompanyConfig, validateCompanyConfig } from '../../config/companyConfig';
import toast from 'react-hot-toast';

const CompanySettings = () => {
  const [config, setConfig] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [errors, setErrors] = useState({});
  const [activeTab, setActiveTab] = useState('basic');

  const tabs = [
    { id: 'basic', name: 'المعلومات الأساسية', icon: BuildingOfficeIcon },
    { id: 'contact', name: 'الاتصال والعنوان', icon: PhoneIcon },
    { id: 'bank', name: 'المعلومات المصرفية', icon: BanknotesIcon },
    { id: 'system', name: 'إعدادات النظام', icon: DocumentTextIcon }
  ];

  useEffect(() => {
    loadConfig();
  }, []);

  const loadConfig = () => {
    try {
      const companyConfig = getCompanyConfig();
      setConfig(companyConfig);
    } catch (error) {
      toast.error('خطأ في تحميل إعدادات الشركة');
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (section, field, value) => {
    setConfig(prev => {
      const newConfig = { ...prev };
      
      if (section) {
        newConfig[section] = {
          ...newConfig[section],
          [field]: value
        };
      } else {
        newConfig[field] = value;
      }
      
      return newConfig;
    });

    // إزالة الخطأ عند التعديل
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  const handleNestedInputChange = (section, subsection, field, value) => {
    setConfig(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [subsection]: {
          ...prev[section][subsection],
          [field]: value
        }
      }
    }));
  };

  const handleSave = async () => {
    setIsSaving(true);
    
    try {
      const validation = validateCompanyConfig(config);
      
      if (!validation.isValid) {
        const errorObj = {};
        validation.errors.forEach(error => {
          errorObj[error] = error;
        });
        setErrors(errorObj);
        toast.error('يرجى تصحيح الأخطاء المذكورة');
        return;
      }

      if (saveCompanyConfig(config)) {
        toast.success('تم حفظ إعدادات الشركة بنجاح');
        setErrors({});
      } else {
        toast.error('حدث خطأ أثناء حفظ الإعدادات');
      }
    } catch (error) {
      toast.error('حدث خطأ غير متوقع');
      console.error(error);
    } finally {
      setIsSaving(false);
    }
  };

  const resetToDefaults = () => {
    if (window.confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
      localStorage.removeItem('company_config');
      loadConfig();
      toast.success('تم إعادة تعيين الإعدادات');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!config) {
    return (
      <div className="text-center p-8">
        <ExclamationTriangleIcon className="w-12 h-12 text-red-500 mx-auto mb-4" />
        <p className="text-gray-600">خطأ في تحميل إعدادات الشركة</p>
        <Button onClick={loadConfig} className="mt-4">
          إعادة المحاولة
        </Button>
      </div>
    );
  }

  const renderBasicInfo = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <Label htmlFor="companyName" className="block text-sm font-medium text-gray-700 mb-2">
            اسم الشركة *
          </Label>
          <Input
            id="companyName"
            type="text"
            value={config.companyInfo.name}
            onChange={(e) => handleNestedInputChange('companyInfo', null, 'name', e.target.value)}
            placeholder="اسم الشركة"
            className={errors.name ? 'border-red-500' : ''}
          />
          {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
        </div>

        <div>
          <Label htmlFor="companyNameEn" className="block text-sm font-medium text-gray-700 mb-2">
            اسم الشركة بالإنجليزية
          </Label>
          <Input
            id="companyNameEn"
            type="text"
            value={config.companyInfo.nameEn || ''}
            onChange={(e) => handleNestedInputChange('companyInfo', null, 'nameEn', e.target.value)}
            placeholder="Company Name in English"
          />
        </div>

        <div>
          <Label htmlFor="vatNumber" className="block text-sm font-medium text-gray-700 mb-2">
            الرقم الضريبي *
          </Label>
          <Input
            id="vatNumber"
            type="text"
            value={config.companyInfo.vatNumber}
            onChange={(e) => handleNestedInputChange('companyInfo', null, 'vatNumber', e.target.value)}
            placeholder="300000000000003"
            maxLength={15}
            className={errors.vatNumber ? 'border-red-500' : ''}
          />
          {errors.vatNumber && <p className="text-red-500 text-sm mt-1">{errors.vatNumber}</p>}
        </div>

        <div>
          <Label htmlFor="crNumber" className="block text-sm font-medium text-gray-700 mb-2">
            رقم السجل التجاري
          </Label>
          <Input
            id="crNumber"
            type="text"
            value={config.companyInfo.crNumber || ''}
            onChange={(e) => handleNestedInputChange('companyInfo', null, 'crNumber', e.target.value)}
            placeholder="**********"
          />
        </div>

        <div>
          <Label htmlFor="licenseNumber" className="block text-sm font-medium text-gray-700 mb-2">
            رقم الرخصة التجارية
          </Label>
          <Input
            id="licenseNumber"
            type="text"
            value={config.companyInfo.licenseNumber || ''}
            onChange={(e) => handleNestedInputChange('companyInfo', null, 'licenseNumber', e.target.value)}
            placeholder="رقم الرخصة"
          />
        </div>
      </div>
    </div>
  );

  const renderContactInfo = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <Label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
            رقم الهاتف *
          </Label>
          <Input
            id="phone"
            type="tel"
            value={config.companyInfo.contact.phone}
            onChange={(e) => handleNestedInputChange('companyInfo', 'contact', 'phone', e.target.value)}
            placeholder="**********"
            className={errors.phone ? 'border-red-500' : ''}
          />
          {errors.phone && <p className="text-red-500 text-sm mt-1">{errors.phone}</p>}
        </div>

        <div>
          <Label htmlFor="mobile" className="block text-sm font-medium text-gray-700 mb-2">
            رقم الجوال
          </Label>
          <Input
            id="mobile"
            type="tel"
            value={config.companyInfo.contact.mobile || ''}
            onChange={(e) => handleNestedInputChange('companyInfo', 'contact', 'mobile', e.target.value)}
            placeholder="0501234567"
          />
        </div>

        <div>
          <Label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
            البريد الإلكتروني *
          </Label>
          <Input
            id="email"
            type="email"
            value={config.companyInfo.contact.email}
            onChange={(e) => handleNestedInputChange('companyInfo', 'contact', 'email', e.target.value)}
            placeholder="<EMAIL>"
            className={errors.email ? 'border-red-500' : ''}
          />
          {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
        </div>

        <div>
          <Label htmlFor="website" className="block text-sm font-medium text-gray-700 mb-2">
            الموقع الإلكتروني
          </Label>
          <Input
            id="website"
            type="url"
            value={config.companyInfo.contact.website || ''}
            onChange={(e) => handleNestedInputChange('companyInfo', 'contact', 'website', e.target.value)}
            placeholder="www.company.com"
          />
        </div>

        <div>
          <Label htmlFor="fax" className="block text-sm font-medium text-gray-700 mb-2">
            رقم الفاكس
          </Label>
          <Input
            id="fax"
            type="tel"
            value={config.companyInfo.contact.fax || ''}
            onChange={(e) => handleNestedInputChange('companyInfo', 'contact', 'fax', e.target.value)}
            placeholder="0112345679"
          />
        </div>
      </div>

      <div>
        <Label htmlFor="street" className="block text-sm font-medium text-gray-700 mb-2">
          العنوان التفصيلي
        </Label>
        <textarea
          id="street"
          rows={3}
          value={config.companyInfo.address.street || ''}
          onChange={(e) => handleNestedInputChange('companyInfo', 'address', 'street', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          placeholder="الشارع، الحي، رقم المبنى"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div>
          <Label htmlFor="city" className="block text-sm font-medium text-gray-700 mb-2">
            المدينة *
          </Label>
          <Input
            id="city"
            type="text"
            value={config.companyInfo.address.city}
            onChange={(e) => handleNestedInputChange('companyInfo', 'address', 'city', e.target.value)}
            placeholder="الرياض"
            className={errors.city ? 'border-red-500' : ''}
          />
          {errors.city && <p className="text-red-500 text-sm mt-1">{errors.city}</p>}
        </div>

        <div>
          <Label htmlFor="region" className="block text-sm font-medium text-gray-700 mb-2">
            المنطقة
          </Label>
          <Input
            id="region"
            type="text"
            value={config.companyInfo.address.region || ''}
            onChange={(e) => handleNestedInputChange('companyInfo', 'address', 'region', e.target.value)}
            placeholder="منطقة الرياض"
          />
        </div>

        <div>
          <Label htmlFor="postalCode" className="block text-sm font-medium text-gray-700 mb-2">
            الرمز البريدي
          </Label>
          <Input
            id="postalCode"
            type="text"
            value={config.companyInfo.address.postalCode || ''}
            onChange={(e) => handleNestedInputChange('companyInfo', 'address', 'postalCode', e.target.value)}
            placeholder="12345"
          />
        </div>
      </div>
    </div>
  );

  const renderBankInfo = () => (
    <div className="space-y-6">
      <div className="bg-blue-50 p-4 rounded-lg">
        <p className="text-blue-800 text-sm">
          المعلومات المصرفية ستظهر في الفواتير لتسهيل عملية الدفع على العملاء.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <Label htmlFor="bankName" className="block text-sm font-medium text-gray-700 mb-2">
            اسم البنك
          </Label>
          <Input
            id="bankName"
            type="text"
            value={config.companyInfo.bankInfo.bankName || ''}
            onChange={(e) => handleNestedInputChange('companyInfo', 'bankInfo', 'bankName', e.target.value)}
            placeholder="البنك الأهلي السعودي"
          />
        </div>

        <div>
          <Label htmlFor="accountNumber" className="block text-sm font-medium text-gray-700 mb-2">
            رقم الحساب
          </Label>
          <Input
            id="accountNumber"
            type="text"
            value={config.companyInfo.bankInfo.accountNumber || ''}
            onChange={(e) => handleNestedInputChange('companyInfo', 'bankInfo', 'accountNumber', e.target.value)}
            placeholder="*********"
          />
        </div>

        <div>
          <Label htmlFor="iban" className="block text-sm font-medium text-gray-700 mb-2">
            رقم الآيبان (IBAN)
          </Label>
          <Input
            id="iban"
            type="text"
            value={config.companyInfo.bankInfo.iban || ''}
            onChange={(e) => handleNestedInputChange('companyInfo', 'bankInfo', 'iban', e.target.value)}
            placeholder="************************"
          />
        </div>

        <div>
          <Label htmlFor="swiftCode" className="block text-sm font-medium text-gray-700 mb-2">
            رمز السويفت (SWIFT)
          </Label>
          <Input
            id="swiftCode"
            type="text"
            value={config.companyInfo.bankInfo.swiftCode || ''}
            onChange={(e) => handleNestedInputChange('companyInfo', 'bankInfo', 'swiftCode', e.target.value)}
            placeholder="NCBKSARI"
          />
        </div>
      </div>
    </div>
  );

  const renderSystemSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <Label htmlFor="currency" className="block text-sm font-medium text-gray-700 mb-2">
            العملة
          </Label>
          <Input
            id="currency"
            type="text"
            value={config.systemSettings.currency}
            onChange={(e) => handleNestedInputChange('systemSettings', null, 'currency', e.target.value)}
            placeholder="ر.س"
          />
        </div>

        <div>
          <Label htmlFor="vatRate" className="block text-sm font-medium text-gray-700 mb-2">
            نسبة ضريبة القيمة المضافة (%)
          </Label>
          <Input
            id="vatRate"
            type="number"
            value={config.systemSettings.defaultVatRate}
            onChange={(e) => handleNestedInputChange('systemSettings', null, 'defaultVatRate', parseFloat(e.target.value))}
            placeholder="15"
            min="0"
            max="100"
          />
        </div>

        <div>
          <Label htmlFor="invoicePrefix" className="block text-sm font-medium text-gray-700 mb-2">
            بادئة رقم الفاتورة
          </Label>
          <Input
            id="invoicePrefix"
            type="text"
            value={config.systemSettings.invoicePrefix}
            onChange={(e) => handleNestedInputChange('systemSettings', null, 'invoicePrefix', e.target.value)}
            placeholder="INV"
          />
        </div>

        <div>
          <Label htmlFor="lowStockThreshold" className="block text-sm font-medium text-gray-700 mb-2">
            حد تنبيه المخزون المنخفض
          </Label>
          <Input
            id="lowStockThreshold"
            type="number"
            value={config.systemSettings.lowStockThreshold}
            onChange={(e) => handleNestedInputChange('systemSettings', null, 'lowStockThreshold', parseInt(e.target.value))}
            placeholder="10"
            min="0"
          />
        </div>
      </div>

      <div>
        <Label htmlFor="termsAndConditions" className="block text-sm font-medium text-gray-700 mb-2">
          الشروط والأحكام
        </Label>
        <textarea
          id="termsAndConditions"
          rows={6}
          value={config.printSettings.termsAndConditions}
          onChange={(e) => handleNestedInputChange('printSettings', null, 'termsAndConditions', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          placeholder="أدخل الشروط والأحكام التي ستظهر في الفواتير"
        />
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'basic':
        return renderBasicInfo();
      case 'contact':
        return renderContactInfo();
      case 'bank':
        return renderBankInfo();
      case 'system':
        return renderSystemSettings();
      default:
        return renderBasicInfo();
    }
  };

  return (
    <div className="space-y-6">
      {/* العنوان */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <BuildingOfficeIcon className="w-8 h-8 text-blue-600 ml-3" />
            <div>
              <h2 className="text-2xl font-bold text-gray-900">إعدادات الشركة</h2>
              <p className="text-gray-600">إدارة معلومات الشركة والإعدادات العامة</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3 space-x-reverse">
            <Button
              onClick={resetToDefaults}
              variant="outline"
              disabled={isSaving}
            >
              إعادة تعيين
            </Button>
            <Button
              onClick={handleSave}
              disabled={isSaving}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              {isSaving ? (
                <div className="flex items-center">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ml-2"></div>
                  جاري الحفظ...
                </div>
              ) : (
                'حفظ التغييرات'
              )}
            </Button>
          </div>
        </div>
      </motion.div>

      {/* التبويبات */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card>
          <CardHeader>
            <div className="flex space-x-1 space-x-reverse">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                      activeTab === tab.id
                        ? 'bg-blue-100 text-blue-700 border border-blue-200'
                        : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <Icon className="w-4 h-4 ml-2" />
                    {tab.name}
                  </button>
                );
              })}
            </div>
          </CardHeader>

          <CardContent>
            {renderContent()}
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default CompanySettings;
