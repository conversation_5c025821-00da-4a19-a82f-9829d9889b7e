{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u0633\\u0637\\u062D \\u0627\\u0644\\u0645\\u0643\\u062A\\u0628\\\\333\\\\src\\\\components\\\\CompanyManagement.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Button } from './ui/button';\nimport { Input } from './ui/input';\nimport { Label } from './ui/label';\nimport { Card, CardContent, CardHeader, CardTitle, CardDescription } from './ui/card';\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table';\nimport { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter, DialogDescription } from './ui/dialog';\nimport { PencilIcon, TrashIcon, PlusIcon } from '@heroicons/react/24/outline';\nimport { Loader2 } from 'lucide-react';\nimport toast, { Toaster } from 'react-hot-toast';\n\n/**\r\n * Company Management Component\r\n * Provides full CRUD functionality for companies with a responsive UI\r\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CompanyManagement = () => {\n  _s();\n  // Local storage operations\n  const getLocalStorageData = () => {\n    const savedData = localStorage.getItem('companyManagementData');\n    return savedData ? JSON.parse(savedData) : {\n      companies: [],\n      lastId: 0\n    };\n  };\n  const saveLocalStorageData = data => {\n    localStorage.setItem('companyManagementData', JSON.stringify(data));\n  };\n\n  // Component state\n  const [companies, setCompanies] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [currentCompany, setCurrentCompany] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    tax_id: '',\n    address: '',\n    contact_person: '',\n    email: '',\n    phone: ''\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [submitting, setSubmitting] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [isAdmin] = useState(true);\n\n  // Initial data loading\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        const data = getLocalStorageData();\n        setCompanies(data.companies);\n\n        // Add demo data if empty\n        if (data.companies.length === 0) {\n          const demoData = {\n            companies: [{\n              company_id: 1,\n              name: 'شركة التقنية المتقدمة',\n              tax_id: '1234567890',\n              address: 'الرياض، شارع الملك فهد',\n              contact_person: 'أحمد محمد',\n              email: '<EMAIL>',\n              phone: '+966112233445'\n            }, {\n              company_id: 2,\n              name: 'شركة التطوير الحديثة',\n              tax_id: '0987654321',\n              address: 'جدة، حي الصفا',\n              contact_person: 'سارة عبدالله',\n              email: '<EMAIL>',\n              phone: '+966556677889'\n            }, {\n              company_id: 3,\n              name: 'شركة الحلول الذكية',\n              tax_id: '4567890123',\n              address: 'الدمام، طريق الملك عبدالعزيز',\n              contact_person: 'خالد سعيد',\n              email: '<EMAIL>',\n              phone: '+966990011223'\n            }],\n            lastId: 3\n          };\n          saveLocalStorageData(demoData);\n          setCompanies(demoData.companies);\n        }\n      } catch (err) {\n        toast.error('حدث خطأ في تحميل البيانات');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, []);\n\n  // Helper functions\n  const showToast = (message, type = 'success') => {\n    toast[type](message);\n  };\n  const validateForm = () => {\n    const errors = {};\n    if (!formData.name.trim()) errors.name = 'اسم الشركة مطلوب';\n    if (!formData.tax_id.trim()) errors.tax_id = 'الرقم الضريبي مطلوب';\n    if (formData.email && !/^\\S+@\\S+\\.\\S+$/.test(formData.email)) errors.email = 'بريد إلكتروني غير صالح';\n    if (formData.phone && !/^\\+?[0-9\\s-]{7,}$/.test(formData.phone)) errors.phone = 'رقم هاتف غير صالح';\n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n  const handleInputChange = e => {\n    const {\n      id,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [id]: value\n    }));\n\n    // Clear error when typing\n    if (formErrors[id]) {\n      setFormErrors(prev => {\n        const newErrors = {\n          ...prev\n        };\n        delete newErrors[id];\n        return newErrors;\n      });\n    }\n  };\n\n  // CRUD operations\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) return;\n    setSubmitting(true);\n    try {\n      const data = getLocalStorageData();\n      if (currentCompany) {\n        // Update company\n        const updatedCompanies = data.companies.map(c => c.company_id === currentCompany.company_id ? {\n          ...formData,\n          company_id: currentCompany.company_id\n        } : c);\n        saveLocalStorageData({\n          companies: updatedCompanies,\n          lastId: data.lastId\n        });\n        setCompanies(updatedCompanies);\n        showToast('تم تحديث بيانات الشركة بنجاح');\n      } else {\n        // Add new company\n        const newCompany = {\n          ...formData,\n          company_id: data.lastId + 1\n        };\n        saveLocalStorageData({\n          companies: [...data.companies, newCompany],\n          lastId: data.lastId + 1\n        });\n        setCompanies([...data.companies, newCompany]);\n        showToast('تم إضافة الشركة بنجاح');\n      }\n      closeModal();\n    } catch (err) {\n      showToast('فشل في حفظ بيانات الشركة', 'error');\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  const handleDelete = companyId => {\n    if (!window.confirm('هل أنت متأكد أنك تريد حذف هذه الشركة؟')) return;\n    try {\n      const data = getLocalStorageData();\n      const updatedCompanies = data.companies.filter(c => c.company_id !== companyId);\n      saveLocalStorageData({\n        companies: updatedCompanies,\n        lastId: data.lastId\n      });\n      setCompanies(updatedCompanies);\n      showToast('تم حذف الشركة بنجاح');\n    } catch (err) {\n      showToast('فشل في حذف الشركة', 'error');\n    }\n  };\n\n  // Modal operations\n  const openEditModal = company => {\n    setCurrentCompany(company);\n    setFormData({\n      name: company.name,\n      tax_id: company.tax_id,\n      address: company.address,\n      contact_person: company.contact_person,\n      email: company.email,\n      phone: company.phone\n    });\n    setIsModalOpen(true);\n  };\n  const openAddModal = () => {\n    setCurrentCompany(null);\n    setFormData({\n      name: '',\n      tax_id: '',\n      address: '',\n      contact_person: '',\n      email: '',\n      phone: ''\n    });\n    setFormErrors({});\n    setIsModalOpen(true);\n  };\n  const closeModal = () => {\n    setIsModalOpen(false);\n    setCurrentCompany(null);\n    setFormErrors({});\n  };\n\n  // Filter companies based on search term\n  const filteredCompanies = companies.filter(company => company.name.toLowerCase().includes(searchTerm.toLowerCase()) || company.tax_id.includes(searchTerm) || company.contact_person && company.contact_person.toLowerCase().includes(searchTerm.toLowerCase()) || company.email && company.email.toLowerCase().includes(searchTerm.toLowerCase()) || company.phone && company.phone.includes(searchTerm));\n\n  // Loading state\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center items-center h-screen\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(Loader2, {\n          className: \"h-12 w-12 animate-spin text-primary mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600\",\n          children: \"\\u062C\\u0627\\u0631\\u064A \\u062A\\u062D\\u0645\\u064A\\u0644 \\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0634\\u0631\\u0643\\u0627\\u062A...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Main render\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mx-auto p-4 md:p-6 max-w-6xl\",\n    children: [/*#__PURE__*/_jsxDEV(Toaster, {\n      position: \"top-right\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 256,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"border rounded-xl shadow-lg\",\n      children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n        className: \"bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-t-xl\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col md:flex-row justify-between items-start md:items-center gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(CardTitle, {\n              className: \"text-2xl md:text-3xl font-bold text-gray-800\",\n              children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0634\\u0631\\u0643\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(CardDescription, {\n              className: \"mt-2 text-gray-600\",\n              children: \"\\u0642\\u0645 \\u0628\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0634\\u0631\\u0643\\u0627\\u062A \\u0627\\u0644\\u0645\\u0633\\u062C\\u0644\\u0629 \\u0641\\u064A \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row gap-3 w-full md:w-auto\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative w-full\",\n              children: [/*#__PURE__*/_jsxDEV(Input, {\n                type: \"text\",\n                placeholder: \"\\u0627\\u0628\\u062D\\u062B \\u0639\\u0646 \\u0634\\u0631\\u0643\\u0629...\",\n                className: \"pl-10 pr-4 py-2 w-full\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: \"2\",\n                  d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this), isAdmin && /*#__PURE__*/_jsxDEV(Dialog, {\n              open: isModalOpen,\n              onOpenChange: setIsModalOpen,\n              children: /*#__PURE__*/_jsxDEV(DialogTrigger, {\n                asChild: true,\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  onClick: openAddModal,\n                  className: \"bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700 hover:to-indigo-800 text-white\",\n                  children: [/*#__PURE__*/_jsxDEV(PlusIcon, {\n                    className: \"h-5 w-5 ml-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 300,\n                    columnNumber: 23\n                  }, this), \" \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0634\\u0631\\u0643\\u0629 \\u062C\\u062F\\u064A\\u062F\\u0629\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n        className: \"p-0\",\n        children: filteredCompanies.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-16 border rounded-lg m-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mx-auto bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-8 h-8 text-gray-500\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              xmlns: \"http://www.w3.org/2000/svg\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: \"2\",\n                d: \"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-500 text-lg\",\n            children: \"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0634\\u0631\\u0643\\u0627\\u062A \\u0645\\u0633\\u062C\\u0644\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 15\n          }, this), isAdmin && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: openAddModal,\n            className: \"mt-4 bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700 hover:to-indigo-800\",\n            children: [/*#__PURE__*/_jsxDEV(PlusIcon, {\n              className: \"h-5 w-5 ml-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 19\n            }, this), \" \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0634\\u0631\\u0643\\u0629 \\u062C\\u062F\\u064A\\u062F\\u0629\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-x-auto\",\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            className: \"min-w-full\",\n            children: [/*#__PURE__*/_jsxDEV(TableHeader, {\n              className: \"bg-gray-50\",\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                  className: \"w-1/4\",\n                  children: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0634\\u0631\\u0643\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableHead, {\n                  children: \"\\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u064A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableHead, {\n                  children: \"\\u0634\\u062E\\u0635 \\u0627\\u0644\\u0627\\u062A\\u0635\\u0627\\u0644\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableHead, {\n                  children: \"\\u0627\\u0644\\u0628\\u0631\\u064A\\u062F \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableHead, {\n                  children: \"\\u0627\\u0644\\u0647\\u0627\\u062A\\u0641\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 21\n                }, this), isAdmin && /*#__PURE__*/_jsxDEV(TableHead, {\n                  className: \"w-32 text-center\",\n                  children: \"\\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 33\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n              children: filteredCompanies.map(company => /*#__PURE__*/_jsxDEV(TableRow, {\n                className: \"hover:bg-gray-50 transition-colors\",\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  className: \"font-medium text-gray-900\",\n                  children: company.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  className: \"text-gray-700\",\n                  children: company.tax_id\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  className: \"text-gray-700\",\n                  children: company.contact_person\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  className: \"text-gray-700\",\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: `mailto:${company.email}`,\n                    className: \"text-blue-600 hover:underline\",\n                    children: company.email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 347,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  className: \"text-gray-700\",\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: `tel:${company.phone}`,\n                    className: \"text-blue-600 hover:underline\",\n                    children: company.phone\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 352,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 23\n                }, this), isAdmin && /*#__PURE__*/_jsxDEV(TableCell, {\n                  className: \"flex justify-center space-x-2 py-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline\",\n                    size: \"icon\",\n                    onClick: () => openEditModal(company),\n                    className: \"text-blue-600 border-blue-200 hover:bg-blue-50\",\n                    children: /*#__PURE__*/_jsxDEV(PencilIcon, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 364,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline\",\n                    size: \"icon\",\n                    onClick: () => handleDelete(company.company_id),\n                    className: \"text-red-600 border-red-200 hover:bg-red-50\",\n                    children: /*#__PURE__*/_jsxDEV(TrashIcon, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 372,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 366,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 25\n                }, this)]\n              }, company.company_id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: isModalOpen,\n      onOpenChange: setIsModalOpen,\n      children: /*#__PURE__*/_jsxDEV(DialogContent, {\n        className: \"sm:max-w-xl rounded-lg shadow-xl\",\n        children: [/*#__PURE__*/_jsxDEV(DialogHeader, {\n          children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n            className: \"text-xl font-bold text-gray-800\",\n            children: currentCompany ? 'تعديل شركة' : 'إضافة شركة جديدة'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(DialogDescription, {\n            className: \"text-gray-600\",\n            children: currentCompany ? 'قم بتعديل بيانات الشركة' : 'قم بإضافة شركة جديدة إلى النظام'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"space-y-4 py-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                htmlFor: \"name\",\n                className: \"block text-gray-700\",\n                children: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0634\\u0631\\u0643\\u0629 *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                id: \"name\",\n                value: formData.name,\n                onChange: handleInputChange,\n                className: `${formErrors.name ? 'border-red-500' : ''}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 17\n              }, this), formErrors.name && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-red-500 text-sm mt-1\",\n                children: formErrors.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                htmlFor: \"tax_id\",\n                className: \"block text-gray-700\",\n                children: \"\\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u064A *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                id: \"tax_id\",\n                value: formData.tax_id,\n                onChange: handleInputChange,\n                className: `${formErrors.tax_id ? 'border-red-500' : ''}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 17\n              }, this), formErrors.tax_id && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-red-500 text-sm mt-1\",\n                children: formErrors.tax_id\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 39\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                htmlFor: \"address\",\n                className: \"block text-gray-700\",\n                children: \"\\u0627\\u0644\\u0639\\u0646\\u0648\\u0627\\u0646\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                id: \"address\",\n                value: formData.address,\n                onChange: handleInputChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                htmlFor: \"contact_person\",\n                className: \"block text-gray-700\",\n                children: \"\\u0634\\u062E\\u0635 \\u0627\\u0644\\u0627\\u062A\\u0635\\u0627\\u0644\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                id: \"contact_person\",\n                value: formData.contact_person,\n                onChange: handleInputChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                htmlFor: \"email\",\n                className: \"block text-gray-700\",\n                children: \"\\u0627\\u0644\\u0628\\u0631\\u064A\\u062F \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                id: \"email\",\n                type: \"email\",\n                value: formData.email,\n                onChange: handleInputChange,\n                className: `${formErrors.email ? 'border-red-500' : ''}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 17\n              }, this), formErrors.email && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-red-500 text-sm mt-1\",\n                children: formErrors.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 38\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                htmlFor: \"phone\",\n                className: \"block text-gray-700\",\n                children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0647\\u0627\\u062A\\u0641\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                id: \"phone\",\n                value: formData.phone,\n                onChange: handleInputChange,\n                className: `${formErrors.phone ? 'border-red-500' : ''}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 17\n              }, this), formErrors.phone && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-red-500 text-sm mt-1\",\n                children: formErrors.phone\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 38\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(DialogFooter, {\n            className: \"pt-4\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"button\",\n              variant: \"outline\",\n              onClick: closeModal,\n              className: \"text-gray-700 hover:bg-gray-100\",\n              children: \"\\u0625\\u0644\\u063A\\u0627\\u0621\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"submit\",\n              disabled: submitting,\n              className: \"bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700 hover:to-indigo-800\",\n              children: submitting ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Loader2, {\n                  className: \"h-4 w-4 animate-spin mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 21\n                }, this), \"\\u062C\\u0627\\u0631\\u064A \\u0627\\u0644\\u062D\\u0641\\u0638...\"]\n              }, void 0, true) : currentCompany ? 'حفظ التعديلات' : 'إضافة شركة'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 387,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 386,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 255,\n    columnNumber: 5\n  }, this);\n};\n_s(CompanyManagement, \"7nciqJtedgVe1hzPcgx9OhP9Jm0=\");\n_c = CompanyManagement;\nexport default CompanyManagement;\nvar _c;\n$RefreshReg$(_c, \"CompanyManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON>", "Input", "Label", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "Table", "TableBody", "TableCell", "TableHead", "TableHeader", "TableRow", "Dialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "DialogTitle", "DialogTrigger", "<PERSON><PERSON><PERSON><PERSON>er", "DialogDescription", "PencilIcon", "TrashIcon", "PlusIcon", "Loader2", "toast", "Toaster", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CompanyManagement", "_s", "getLocalStorageData", "savedData", "localStorage", "getItem", "JSON", "parse", "companies", "lastId", "saveLocalStorageData", "data", "setItem", "stringify", "setCompanies", "loading", "setLoading", "isModalOpen", "setIsModalOpen", "currentCompany", "setCurrentCompany", "formData", "setFormData", "name", "tax_id", "address", "contact_person", "email", "phone", "formErrors", "setFormErrors", "submitting", "setSubmitting", "searchTerm", "setSearchTerm", "isAdmin", "fetchData", "length", "demoData", "company_id", "err", "error", "showToast", "message", "type", "validateForm", "errors", "trim", "test", "Object", "keys", "handleInputChange", "e", "id", "value", "target", "prev", "newErrors", "handleSubmit", "preventDefault", "updatedCompanies", "map", "c", "newCompany", "closeModal", "handleDelete", "companyId", "window", "confirm", "filter", "openEditModal", "company", "openAddModal", "filteredCompanies", "toLowerCase", "includes", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "position", "placeholder", "onChange", "fill", "stroke", "viewBox", "xmlns", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "open", "onOpenChange", "<PERSON><PERSON><PERSON><PERSON>", "onClick", "href", "variant", "size", "onSubmit", "htmlFor", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/src/components/CompanyManagement.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { Button } from './ui/button';\r\nimport { Input } from './ui/input';\r\nimport { Label } from './ui/label';\r\nimport { Card, CardContent, CardHeader, CardTitle, CardDescription } from './ui/card';\r\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table';\r\nimport { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter, DialogDescription } from './ui/dialog';\r\nimport { PencilIcon, TrashIcon, PlusIcon } from '@heroicons/react/24/outline';\r\nimport { Loader2 } from 'lucide-react';\r\nimport toast, { Toaster } from 'react-hot-toast';\r\n\r\n/**\r\n * Company Management Component\r\n * Provides full CRUD functionality for companies with a responsive UI\r\n */\r\nconst CompanyManagement = () => {\r\n  // Local storage operations\r\n  const getLocalStorageData = () => {\r\n    const savedData = localStorage.getItem('companyManagementData');\r\n    return savedData ? JSON.parse(savedData) : {\r\n      companies: [],\r\n      lastId: 0\r\n    };\r\n  };\r\n\r\n  const saveLocalStorageData = (data) => {\r\n    localStorage.setItem('companyManagementData', JSON.stringify(data));\r\n  };\r\n\r\n  // Component state\r\n  const [companies, setCompanies] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [isModalOpen, setIsModalOpen] = useState(false);\r\n  const [currentCompany, setCurrentCompany] = useState(null);\r\n  const [formData, setFormData] = useState({\r\n    name: '',\r\n    tax_id: '',\r\n    address: '',\r\n    contact_person: '',\r\n    email: '',\r\n    phone: ''\r\n  });\r\n  const [formErrors, setFormErrors] = useState({});\r\n  const [submitting, setSubmitting] = useState(false);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [isAdmin] = useState(true);\r\n\r\n  // Initial data loading\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      try {\r\n        const data = getLocalStorageData();\r\n        setCompanies(data.companies);\r\n\r\n        // Add demo data if empty\r\n        if (data.companies.length === 0) {\r\n          const demoData = {\r\n            companies: [\r\n              {\r\n                company_id: 1,\r\n                name: 'شركة التقنية المتقدمة',\r\n                tax_id: '1234567890',\r\n                address: 'الرياض، شارع الملك فهد',\r\n                contact_person: 'أحمد محمد',\r\n                email: '<EMAIL>',\r\n                phone: '+966112233445'\r\n              },\r\n              {\r\n                company_id: 2,\r\n                name: 'شركة التطوير الحديثة',\r\n                tax_id: '0987654321',\r\n                address: 'جدة، حي الصفا',\r\n                contact_person: 'سارة عبدالله',\r\n                email: '<EMAIL>',\r\n                phone: '+966556677889'\r\n              },\r\n              {\r\n                company_id: 3,\r\n                name: 'شركة الحلول الذكية',\r\n                tax_id: '4567890123',\r\n                address: 'الدمام، طريق الملك عبدالعزيز',\r\n                contact_person: 'خالد سعيد',\r\n                email: '<EMAIL>',\r\n                phone: '+966990011223'\r\n              }\r\n            ],\r\n            lastId: 3\r\n          };\r\n          saveLocalStorageData(demoData);\r\n          setCompanies(demoData.companies);\r\n        }\r\n      } catch (err) {\r\n        toast.error('حدث خطأ في تحميل البيانات');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchData();\r\n  }, []);\r\n\r\n  // Helper functions\r\n  const showToast = (message, type = 'success') => {\r\n    toast[type](message);\r\n  };\r\n\r\n  const validateForm = () => {\r\n    const errors = {};\r\n    if (!formData.name.trim()) errors.name = 'اسم الشركة مطلوب';\r\n    if (!formData.tax_id.trim()) errors.tax_id = 'الرقم الضريبي مطلوب';\r\n    if (formData.email && !/^\\S+@\\S+\\.\\S+$/.test(formData.email)) errors.email = 'بريد إلكتروني غير صالح';\r\n    if (formData.phone && !/^\\+?[0-9\\s-]{7,}$/.test(formData.phone)) errors.phone = 'رقم هاتف غير صالح';\r\n\r\n    setFormErrors(errors);\r\n    return Object.keys(errors).length === 0;\r\n  };\r\n\r\n  const handleInputChange = (e) => {\r\n    const { id, value } = e.target;\r\n    setFormData(prev => ({ ...prev, [id]: value }));\r\n\r\n    // Clear error when typing\r\n    if (formErrors[id]) {\r\n      setFormErrors(prev => {\r\n        const newErrors = { ...prev };\r\n        delete newErrors[id];\r\n        return newErrors;\r\n      });\r\n    }\r\n  };\r\n\r\n  // CRUD operations\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    if (!validateForm()) return;\r\n\r\n    setSubmitting(true);\r\n\r\n    try {\r\n      const data = getLocalStorageData();\r\n\r\n      if (currentCompany) {\r\n        // Update company\r\n        const updatedCompanies = data.companies.map(c =>\r\n          c.company_id === currentCompany.company_id ? { ...formData, company_id: currentCompany.company_id } : c\r\n        );\r\n\r\n        saveLocalStorageData({\r\n          companies: updatedCompanies,\r\n          lastId: data.lastId\r\n        });\r\n\r\n        setCompanies(updatedCompanies);\r\n        showToast('تم تحديث بيانات الشركة بنجاح');\r\n      } else {\r\n        // Add new company\r\n        const newCompany = {\r\n          ...formData,\r\n          company_id: data.lastId + 1\r\n        };\r\n\r\n        saveLocalStorageData({\r\n          companies: [...data.companies, newCompany],\r\n          lastId: data.lastId + 1\r\n        });\r\n\r\n        setCompanies([...data.companies, newCompany]);\r\n        showToast('تم إضافة الشركة بنجاح');\r\n      }\r\n\r\n      closeModal();\r\n    } catch (err) {\r\n      showToast('فشل في حفظ بيانات الشركة', 'error');\r\n    } finally {\r\n      setSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const handleDelete = (companyId) => {\r\n    if (!window.confirm('هل أنت متأكد أنك تريد حذف هذه الشركة؟')) return;\r\n\r\n    try {\r\n      const data = getLocalStorageData();\r\n      const updatedCompanies = data.companies.filter(c => c.company_id !== companyId);\r\n\r\n      saveLocalStorageData({\r\n        companies: updatedCompanies,\r\n        lastId: data.lastId\r\n      });\r\n\r\n      setCompanies(updatedCompanies);\r\n      showToast('تم حذف الشركة بنجاح');\r\n    } catch (err) {\r\n      showToast('فشل في حذف الشركة', 'error');\r\n    }\r\n  };\r\n\r\n  // Modal operations\r\n  const openEditModal = (company) => {\r\n    setCurrentCompany(company);\r\n    setFormData({\r\n      name: company.name,\r\n      tax_id: company.tax_id,\r\n      address: company.address,\r\n      contact_person: company.contact_person,\r\n      email: company.email,\r\n      phone: company.phone\r\n    });\r\n    setIsModalOpen(true);\r\n  };\r\n\r\n  const openAddModal = () => {\r\n    setCurrentCompany(null);\r\n    setFormData({\r\n      name: '',\r\n      tax_id: '',\r\n      address: '',\r\n      contact_person: '',\r\n      email: '',\r\n      phone: ''\r\n    });\r\n    setFormErrors({});\r\n    setIsModalOpen(true);\r\n  };\r\n\r\n  const closeModal = () => {\r\n    setIsModalOpen(false);\r\n    setCurrentCompany(null);\r\n    setFormErrors({});\r\n  };\r\n\r\n  // Filter companies based on search term\r\n  const filteredCompanies = companies.filter(company =>\r\n    company.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n    company.tax_id.includes(searchTerm) ||\r\n    (company.contact_person && company.contact_person.toLowerCase().includes(searchTerm.toLowerCase())) ||\r\n    (company.email && company.email.toLowerCase().includes(searchTerm.toLowerCase())) ||\r\n    (company.phone && company.phone.includes(searchTerm))\r\n  );\r\n\r\n  // Loading state\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex justify-center items-center h-screen\">\r\n        <div className=\"text-center\">\r\n          <Loader2 className=\"h-12 w-12 animate-spin text-primary mx-auto mb-4\" />\r\n          <p className=\"text-lg text-gray-600\">جاري تحميل بيانات الشركات...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Main render\r\n  return (\r\n    <div className=\"container mx-auto p-4 md:p-6 max-w-6xl\">\r\n      <Toaster position=\"top-right\" />\r\n\r\n      <Card className=\"border rounded-xl shadow-lg\">\r\n        <CardHeader className=\"bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-t-xl\">\r\n          <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center gap-4\">\r\n            <div>\r\n              <CardTitle className=\"text-2xl md:text-3xl font-bold text-gray-800\">إدارة الشركات</CardTitle>\r\n              <CardDescription className=\"mt-2 text-gray-600\">\r\n                قم بإدارة بيانات الشركات المسجلة في النظام\r\n              </CardDescription>\r\n            </div>\r\n\r\n            <div className=\"flex flex-col sm:flex-row gap-3 w-full md:w-auto\">\r\n              <div className=\"relative w-full\">\r\n                <Input\r\n                  type=\"text\"\r\n                  placeholder=\"ابحث عن شركة...\"\r\n                  className=\"pl-10 pr-4 py-2 w-full\"\r\n                  value={searchTerm}\r\n                  onChange={(e) => setSearchTerm(e.target.value)}\r\n                />\r\n                <svg\r\n                  className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    strokeWidth=\"2\"\r\n                    d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\r\n                  ></path>\r\n                </svg>\r\n              </div>\r\n\r\n              {isAdmin && (\r\n                <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>\r\n                  <DialogTrigger asChild>\r\n                    <Button\r\n                      onClick={openAddModal}\r\n                      className=\"bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700 hover:to-indigo-800 text-white\"\r\n                    >\r\n                      <PlusIcon className=\"h-5 w-5 ml-2\" /> إضافة شركة جديدة\r\n                    </Button>\r\n                  </DialogTrigger>\r\n                </Dialog>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </CardHeader>\r\n\r\n        <CardContent className=\"p-0\">\r\n          {filteredCompanies.length === 0 ? (\r\n            <div className=\"text-center py-16 border rounded-lg m-6\">\r\n              <div className=\"mx-auto bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mb-4\">\r\n                <svg className=\"w-8 h-8 text-gray-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"></path>\r\n                </svg>\r\n              </div>\r\n              <p className=\"text-gray-500 text-lg\">لا توجد شركات مسجلة</p>\r\n              {isAdmin && (\r\n                <Button\r\n                  onClick={openAddModal}\r\n                  className=\"mt-4 bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700 hover:to-indigo-800\"\r\n                >\r\n                  <PlusIcon className=\"h-5 w-5 ml-2\" /> إضافة شركة جديدة\r\n                </Button>\r\n              )}\r\n            </div>\r\n          ) : (\r\n            <div className=\"overflow-x-auto\">\r\n              <Table className=\"min-w-full\">\r\n                <TableHeader className=\"bg-gray-50\">\r\n                  <TableRow>\r\n                    <TableHead className=\"w-1/4\">اسم الشركة</TableHead>\r\n                    <TableHead>الرقم الضريبي</TableHead>\r\n                    <TableHead>شخص الاتصال</TableHead>\r\n                    <TableHead>البريد الإلكتروني</TableHead>\r\n                    <TableHead>الهاتف</TableHead>\r\n                    {isAdmin && <TableHead className=\"w-32 text-center\">الإجراءات</TableHead>}\r\n                  </TableRow>\r\n                </TableHeader>\r\n                <TableBody>\r\n                  {filteredCompanies.map((company) => (\r\n                    <TableRow key={company.company_id} className=\"hover:bg-gray-50 transition-colors\">\r\n                      <TableCell className=\"font-medium text-gray-900\">{company.name}</TableCell>\r\n                      <TableCell className=\"text-gray-700\">{company.tax_id}</TableCell>\r\n                      <TableCell className=\"text-gray-700\">{company.contact_person}</TableCell>\r\n                      <TableCell className=\"text-gray-700\">\r\n                        <a href={`mailto:${company.email}`} className=\"text-blue-600 hover:underline\">\r\n                          {company.email}\r\n                        </a>\r\n                      </TableCell>\r\n                      <TableCell className=\"text-gray-700\">\r\n                        <a href={`tel:${company.phone}`} className=\"text-blue-600 hover:underline\">\r\n                          {company.phone}\r\n                        </a>\r\n                      </TableCell>\r\n                      {isAdmin && (\r\n                        <TableCell className=\"flex justify-center space-x-2 py-3\">\r\n                          <Button\r\n                            variant=\"outline\"\r\n                            size=\"icon\"\r\n                            onClick={() => openEditModal(company)}\r\n                            className=\"text-blue-600 border-blue-200 hover:bg-blue-50\"\r\n                          >\r\n                            <PencilIcon className=\"h-4 w-4\" />\r\n                          </Button>\r\n                          <Button\r\n                            variant=\"outline\"\r\n                            size=\"icon\"\r\n                            onClick={() => handleDelete(company.company_id)}\r\n                            className=\"text-red-600 border-red-200 hover:bg-red-50\"\r\n                          >\r\n                            <TrashIcon className=\"h-4 w-4\" />\r\n                          </Button>\r\n                        </TableCell>\r\n                      )}\r\n                    </TableRow>\r\n                  ))}\r\n                </TableBody>\r\n              </Table>\r\n            </div>\r\n          )}\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Add/Edit Company Modal */}\r\n      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>\r\n        <DialogContent className=\"sm:max-w-xl rounded-lg shadow-xl\">\r\n          <DialogHeader>\r\n            <DialogTitle className=\"text-xl font-bold text-gray-800\">\r\n              {currentCompany ? 'تعديل شركة' : 'إضافة شركة جديدة'}\r\n            </DialogTitle>\r\n            <DialogDescription className=\"text-gray-600\">\r\n              {currentCompany ? 'قم بتعديل بيانات الشركة' : 'قم بإضافة شركة جديدة إلى النظام'}\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n          <form onSubmit={handleSubmit} className=\"space-y-4 py-4\">\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"name\" className=\"block text-gray-700\">اسم الشركة *</Label>\r\n                <Input\r\n                  id=\"name\"\r\n                  value={formData.name}\r\n                  onChange={handleInputChange}\r\n                  className={`${formErrors.name ? 'border-red-500' : ''}`}\r\n                />\r\n                {formErrors.name && <p className=\"text-red-500 text-sm mt-1\">{formErrors.name}</p>}\r\n              </div>\r\n\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"tax_id\" className=\"block text-gray-700\">الرقم الضريبي *</Label>\r\n                <Input\r\n                  id=\"tax_id\"\r\n                  value={formData.tax_id}\r\n                  onChange={handleInputChange}\r\n                  className={`${formErrors.tax_id ? 'border-red-500' : ''}`}\r\n                />\r\n                {formErrors.tax_id && <p className=\"text-red-500 text-sm mt-1\">{formErrors.tax_id}</p>}\r\n              </div>\r\n\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"address\" className=\"block text-gray-700\">العنوان</Label>\r\n                <Input\r\n                  id=\"address\"\r\n                  value={formData.address}\r\n                  onChange={handleInputChange}\r\n                />\r\n              </div>\r\n\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"contact_person\" className=\"block text-gray-700\">شخص الاتصال</Label>\r\n                <Input\r\n                  id=\"contact_person\"\r\n                  value={formData.contact_person}\r\n                  onChange={handleInputChange}\r\n                />\r\n              </div>\r\n\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"email\" className=\"block text-gray-700\">البريد الإلكتروني</Label>\r\n                <Input\r\n                  id=\"email\"\r\n                  type=\"email\"\r\n                  value={formData.email}\r\n                  onChange={handleInputChange}\r\n                  className={`${formErrors.email ? 'border-red-500' : ''}`}\r\n                />\r\n                {formErrors.email && <p className=\"text-red-500 text-sm mt-1\">{formErrors.email}</p>}\r\n              </div>\r\n\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"phone\" className=\"block text-gray-700\">رقم الهاتف</Label>\r\n                <Input\r\n                  id=\"phone\"\r\n                  value={formData.phone}\r\n                  onChange={handleInputChange}\r\n                  className={`${formErrors.phone ? 'border-red-500' : ''}`}\r\n                />\r\n                {formErrors.phone && <p className=\"text-red-500 text-sm mt-1\">{formErrors.phone}</p>}\r\n              </div>\r\n            </div>\r\n\r\n            <DialogFooter className=\"pt-4\">\r\n              <Button\r\n                type=\"button\"\r\n                variant=\"outline\"\r\n                onClick={closeModal}\r\n                className=\"text-gray-700 hover:bg-gray-100\"\r\n              >\r\n                إلغاء\r\n              </Button>\r\n              <Button\r\n                type=\"submit\"\r\n                disabled={submitting}\r\n                className=\"bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700 hover:to-indigo-800\"\r\n              >\r\n                {submitting ? (\r\n                  <>\r\n                    <Loader2 className=\"h-4 w-4 animate-spin mr-2\" />\r\n                    جاري الحفظ...\r\n                  </>\r\n                ) : currentCompany ? 'حفظ التعديلات' : 'إضافة شركة'}\r\n              </Button>\r\n            </DialogFooter>\r\n          </form>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CompanyManagement;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,KAAK,QAAQ,YAAY;AAClC,SAASC,KAAK,QAAQ,YAAY;AAClC,SAASC,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,SAAS,EAAEC,eAAe,QAAQ,WAAW;AACrF,SAASC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAEC,SAAS,EAAEC,WAAW,EAAEC,QAAQ,QAAQ,YAAY;AAC1F,SAASC,MAAM,EAAEC,aAAa,EAAEC,YAAY,EAAEC,WAAW,EAAEC,aAAa,EAAEC,YAAY,EAAEC,iBAAiB,QAAQ,aAAa;AAC9H,SAASC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,6BAA6B;AAC7E,SAASC,OAAO,QAAQ,cAAc;AACtC,OAAOC,KAAK,IAAIC,OAAO,QAAQ,iBAAiB;;AAEhD;AACA;AACA;AACA;AAHA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAIA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B;EACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC;IAC/D,OAAOF,SAAS,GAAGG,IAAI,CAACC,KAAK,CAACJ,SAAS,CAAC,GAAG;MACzCK,SAAS,EAAE,EAAE;MACbC,MAAM,EAAE;IACV,CAAC;EACH,CAAC;EAED,MAAMC,oBAAoB,GAAIC,IAAI,IAAK;IACrCP,YAAY,CAACQ,OAAO,CAAC,uBAAuB,EAAEN,IAAI,CAACO,SAAS,CAACF,IAAI,CAAC,CAAC;EACrE,CAAC;;EAED;EACA,MAAM,CAACH,SAAS,EAAEM,YAAY,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACgD,OAAO,EAAEC,UAAU,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACoD,cAAc,EAAEC,iBAAiB,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACsD,QAAQ,EAAEC,WAAW,CAAC,GAAGvD,QAAQ,CAAC;IACvCwD,IAAI,EAAE,EAAE;IACRC,MAAM,EAAE,EAAE;IACVC,OAAO,EAAE,EAAE;IACXC,cAAc,EAAE,EAAE;IAClBC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG/D,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACgE,UAAU,EAAEC,aAAa,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACkE,UAAU,EAAEC,aAAa,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoE,OAAO,CAAC,GAAGpE,QAAQ,CAAC,IAAI,CAAC;;EAEhC;EACAC,SAAS,CAAC,MAAM;IACd,MAAMoE,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF,MAAMzB,IAAI,GAAGT,mBAAmB,CAAC,CAAC;QAClCY,YAAY,CAACH,IAAI,CAACH,SAAS,CAAC;;QAE5B;QACA,IAAIG,IAAI,CAACH,SAAS,CAAC6B,MAAM,KAAK,CAAC,EAAE;UAC/B,MAAMC,QAAQ,GAAG;YACf9B,SAAS,EAAE,CACT;cACE+B,UAAU,EAAE,CAAC;cACbhB,IAAI,EAAE,uBAAuB;cAC7BC,MAAM,EAAE,YAAY;cACpBC,OAAO,EAAE,wBAAwB;cACjCC,cAAc,EAAE,WAAW;cAC3BC,KAAK,EAAE,eAAe;cACtBC,KAAK,EAAE;YACT,CAAC,EACD;cACEW,UAAU,EAAE,CAAC;cACbhB,IAAI,EAAE,sBAAsB;cAC5BC,MAAM,EAAE,YAAY;cACpBC,OAAO,EAAE,eAAe;cACxBC,cAAc,EAAE,cAAc;cAC9BC,KAAK,EAAE,iBAAiB;cACxBC,KAAK,EAAE;YACT,CAAC,EACD;cACEW,UAAU,EAAE,CAAC;cACbhB,IAAI,EAAE,oBAAoB;cAC1BC,MAAM,EAAE,YAAY;cACpBC,OAAO,EAAE,8BAA8B;cACvCC,cAAc,EAAE,WAAW;cAC3BC,KAAK,EAAE,mBAAmB;cAC1BC,KAAK,EAAE;YACT,CAAC,CACF;YACDnB,MAAM,EAAE;UACV,CAAC;UACDC,oBAAoB,CAAC4B,QAAQ,CAAC;UAC9BxB,YAAY,CAACwB,QAAQ,CAAC9B,SAAS,CAAC;QAClC;MACF,CAAC,CAAC,OAAOgC,GAAG,EAAE;QACZ9C,KAAK,CAAC+C,KAAK,CAAC,2BAA2B,CAAC;MAC1C,CAAC,SAAS;QACRzB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDoB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMM,SAAS,GAAGA,CAACC,OAAO,EAAEC,IAAI,GAAG,SAAS,KAAK;IAC/ClD,KAAK,CAACkD,IAAI,CAAC,CAACD,OAAO,CAAC;EACtB,CAAC;EAED,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,MAAM,GAAG,CAAC,CAAC;IACjB,IAAI,CAACzB,QAAQ,CAACE,IAAI,CAACwB,IAAI,CAAC,CAAC,EAAED,MAAM,CAACvB,IAAI,GAAG,kBAAkB;IAC3D,IAAI,CAACF,QAAQ,CAACG,MAAM,CAACuB,IAAI,CAAC,CAAC,EAAED,MAAM,CAACtB,MAAM,GAAG,qBAAqB;IAClE,IAAIH,QAAQ,CAACM,KAAK,IAAI,CAAC,gBAAgB,CAACqB,IAAI,CAAC3B,QAAQ,CAACM,KAAK,CAAC,EAAEmB,MAAM,CAACnB,KAAK,GAAG,wBAAwB;IACrG,IAAIN,QAAQ,CAACO,KAAK,IAAI,CAAC,mBAAmB,CAACoB,IAAI,CAAC3B,QAAQ,CAACO,KAAK,CAAC,EAAEkB,MAAM,CAAClB,KAAK,GAAG,mBAAmB;IAEnGE,aAAa,CAACgB,MAAM,CAAC;IACrB,OAAOG,MAAM,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACT,MAAM,KAAK,CAAC;EACzC,CAAC;EAED,MAAMc,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,EAAE;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAC9BjC,WAAW,CAACkC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACH,EAAE,GAAGC;IAAM,CAAC,CAAC,CAAC;;IAE/C;IACA,IAAIzB,UAAU,CAACwB,EAAE,CAAC,EAAE;MAClBvB,aAAa,CAAC0B,IAAI,IAAI;QACpB,MAAMC,SAAS,GAAG;UAAE,GAAGD;QAAK,CAAC;QAC7B,OAAOC,SAAS,CAACJ,EAAE,CAAC;QACpB,OAAOI,SAAS;MAClB,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,MAAON,CAAC,IAAK;IAChCA,CAAC,CAACO,cAAc,CAAC,CAAC;IAClB,IAAI,CAACd,YAAY,CAAC,CAAC,EAAE;IAErBb,aAAa,CAAC,IAAI,CAAC;IAEnB,IAAI;MACF,MAAMrB,IAAI,GAAGT,mBAAmB,CAAC,CAAC;MAElC,IAAIiB,cAAc,EAAE;QAClB;QACA,MAAMyC,gBAAgB,GAAGjD,IAAI,CAACH,SAAS,CAACqD,GAAG,CAACC,CAAC,IAC3CA,CAAC,CAACvB,UAAU,KAAKpB,cAAc,CAACoB,UAAU,GAAG;UAAE,GAAGlB,QAAQ;UAAEkB,UAAU,EAAEpB,cAAc,CAACoB;QAAW,CAAC,GAAGuB,CACxG,CAAC;QAEDpD,oBAAoB,CAAC;UACnBF,SAAS,EAAEoD,gBAAgB;UAC3BnD,MAAM,EAAEE,IAAI,CAACF;QACf,CAAC,CAAC;QAEFK,YAAY,CAAC8C,gBAAgB,CAAC;QAC9BlB,SAAS,CAAC,8BAA8B,CAAC;MAC3C,CAAC,MAAM;QACL;QACA,MAAMqB,UAAU,GAAG;UACjB,GAAG1C,QAAQ;UACXkB,UAAU,EAAE5B,IAAI,CAACF,MAAM,GAAG;QAC5B,CAAC;QAEDC,oBAAoB,CAAC;UACnBF,SAAS,EAAE,CAAC,GAAGG,IAAI,CAACH,SAAS,EAAEuD,UAAU,CAAC;UAC1CtD,MAAM,EAAEE,IAAI,CAACF,MAAM,GAAG;QACxB,CAAC,CAAC;QAEFK,YAAY,CAAC,CAAC,GAAGH,IAAI,CAACH,SAAS,EAAEuD,UAAU,CAAC,CAAC;QAC7CrB,SAAS,CAAC,uBAAuB,CAAC;MACpC;MAEAsB,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAOxB,GAAG,EAAE;MACZE,SAAS,CAAC,0BAA0B,EAAE,OAAO,CAAC;IAChD,CAAC,SAAS;MACRV,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMiC,YAAY,GAAIC,SAAS,IAAK;IAClC,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,uCAAuC,CAAC,EAAE;IAE9D,IAAI;MACF,MAAMzD,IAAI,GAAGT,mBAAmB,CAAC,CAAC;MAClC,MAAM0D,gBAAgB,GAAGjD,IAAI,CAACH,SAAS,CAAC6D,MAAM,CAACP,CAAC,IAAIA,CAAC,CAACvB,UAAU,KAAK2B,SAAS,CAAC;MAE/ExD,oBAAoB,CAAC;QACnBF,SAAS,EAAEoD,gBAAgB;QAC3BnD,MAAM,EAAEE,IAAI,CAACF;MACf,CAAC,CAAC;MAEFK,YAAY,CAAC8C,gBAAgB,CAAC;MAC9BlB,SAAS,CAAC,qBAAqB,CAAC;IAClC,CAAC,CAAC,OAAOF,GAAG,EAAE;MACZE,SAAS,CAAC,mBAAmB,EAAE,OAAO,CAAC;IACzC;EACF,CAAC;;EAED;EACA,MAAM4B,aAAa,GAAIC,OAAO,IAAK;IACjCnD,iBAAiB,CAACmD,OAAO,CAAC;IAC1BjD,WAAW,CAAC;MACVC,IAAI,EAAEgD,OAAO,CAAChD,IAAI;MAClBC,MAAM,EAAE+C,OAAO,CAAC/C,MAAM;MACtBC,OAAO,EAAE8C,OAAO,CAAC9C,OAAO;MACxBC,cAAc,EAAE6C,OAAO,CAAC7C,cAAc;MACtCC,KAAK,EAAE4C,OAAO,CAAC5C,KAAK;MACpBC,KAAK,EAAE2C,OAAO,CAAC3C;IACjB,CAAC,CAAC;IACFV,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMsD,YAAY,GAAGA,CAAA,KAAM;IACzBpD,iBAAiB,CAAC,IAAI,CAAC;IACvBE,WAAW,CAAC;MACVC,IAAI,EAAE,EAAE;MACRC,MAAM,EAAE,EAAE;MACVC,OAAO,EAAE,EAAE;MACXC,cAAc,EAAE,EAAE;MAClBC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE;IACT,CAAC,CAAC;IACFE,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBZ,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAM8C,UAAU,GAAGA,CAAA,KAAM;IACvB9C,cAAc,CAAC,KAAK,CAAC;IACrBE,iBAAiB,CAAC,IAAI,CAAC;IACvBU,aAAa,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC;;EAED;EACA,MAAM2C,iBAAiB,GAAGjE,SAAS,CAAC6D,MAAM,CAACE,OAAO,IAChDA,OAAO,CAAChD,IAAI,CAACmD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC1C,UAAU,CAACyC,WAAW,CAAC,CAAC,CAAC,IAC7DH,OAAO,CAAC/C,MAAM,CAACmD,QAAQ,CAAC1C,UAAU,CAAC,IAClCsC,OAAO,CAAC7C,cAAc,IAAI6C,OAAO,CAAC7C,cAAc,CAACgD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC1C,UAAU,CAACyC,WAAW,CAAC,CAAC,CAAE,IAClGH,OAAO,CAAC5C,KAAK,IAAI4C,OAAO,CAAC5C,KAAK,CAAC+C,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC1C,UAAU,CAACyC,WAAW,CAAC,CAAC,CAAE,IAChFH,OAAO,CAAC3C,KAAK,IAAI2C,OAAO,CAAC3C,KAAK,CAAC+C,QAAQ,CAAC1C,UAAU,CACrD,CAAC;;EAED;EACA,IAAIlB,OAAO,EAAE;IACX,oBACElB,OAAA;MAAK+E,SAAS,EAAC,2CAA2C;MAAAC,QAAA,eACxDhF,OAAA;QAAK+E,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BhF,OAAA,CAACJ,OAAO;UAACmF,SAAS,EAAC;QAAkD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxEpF,OAAA;UAAG+E,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAA4B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,oBACEpF,OAAA;IAAK+E,SAAS,EAAC,wCAAwC;IAAAC,QAAA,gBACrDhF,OAAA,CAACF,OAAO;MAACuF,QAAQ,EAAC;IAAW;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEhCpF,OAAA,CAACzB,IAAI;MAACwG,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC3ChF,OAAA,CAACvB,UAAU;QAACsG,SAAS,EAAC,6DAA6D;QAAAC,QAAA,eACjFhF,OAAA;UAAK+E,SAAS,EAAC,6EAA6E;UAAAC,QAAA,gBAC1FhF,OAAA;YAAAgF,QAAA,gBACEhF,OAAA,CAACtB,SAAS;cAACqG,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC7FpF,OAAA,CAACrB,eAAe;cAACoG,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAEhD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAiB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAENpF,OAAA;YAAK+E,SAAS,EAAC,kDAAkD;YAAAC,QAAA,gBAC/DhF,OAAA;cAAK+E,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BhF,OAAA,CAAC3B,KAAK;gBACJ0E,IAAI,EAAC,MAAM;gBACXuC,WAAW,EAAC,mEAAiB;gBAC7BP,SAAS,EAAC,wBAAwB;gBAClCtB,KAAK,EAAErB,UAAW;gBAClBmD,QAAQ,EAAGhC,CAAC,IAAKlB,aAAa,CAACkB,CAAC,CAACG,MAAM,CAACD,KAAK;cAAE;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACFpF,OAAA;gBACE+E,SAAS,EAAC,0EAA0E;gBACpFS,IAAI,EAAC,MAAM;gBACXC,MAAM,EAAC,cAAc;gBACrBC,OAAO,EAAC,WAAW;gBACnBC,KAAK,EAAC,4BAA4B;gBAAAX,QAAA,eAElChF,OAAA;kBACE4F,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC,OAAO;kBACtBC,WAAW,EAAC,GAAG;kBACfC,CAAC,EAAC;gBAA6C;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAEL9C,OAAO,iBACNtC,OAAA,CAACd,MAAM;cAAC8G,IAAI,EAAE5E,WAAY;cAAC6E,YAAY,EAAE5E,cAAe;cAAA2D,QAAA,eACtDhF,OAAA,CAACV,aAAa;gBAAC4G,OAAO;gBAAAlB,QAAA,eACpBhF,OAAA,CAAC5B,MAAM;kBACL+H,OAAO,EAAExB,YAAa;kBACtBI,SAAS,EAAC,iGAAiG;kBAAAC,QAAA,gBAE3GhF,OAAA,CAACL,QAAQ;oBAACoF,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,2FACvC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAEbpF,OAAA,CAACxB,WAAW;QAACuG,SAAS,EAAC,KAAK;QAAAC,QAAA,EACzBJ,iBAAiB,CAACpC,MAAM,KAAK,CAAC,gBAC7BxC,OAAA;UAAK+E,SAAS,EAAC,yCAAyC;UAAAC,QAAA,gBACtDhF,OAAA;YAAK+E,SAAS,EAAC,kFAAkF;YAAAC,QAAA,eAC/FhF,OAAA;cAAK+E,SAAS,EAAC,uBAAuB;cAACS,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAACC,KAAK,EAAC,4BAA4B;cAAAX,QAAA,eAC7HhF,OAAA;gBAAM4F,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAC,GAAG;gBAACC,CAAC,EAAC;cAA2I;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNpF,OAAA;YAAG+E,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,EAC3D9C,OAAO,iBACNtC,OAAA,CAAC5B,MAAM;YACL+H,OAAO,EAAExB,YAAa;YACtBI,SAAS,EAAC,2FAA2F;YAAAC,QAAA,gBAErGhF,OAAA,CAACL,QAAQ;cAACoF,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,2FACvC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,gBAENpF,OAAA;UAAK+E,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BhF,OAAA,CAACpB,KAAK;YAACmG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAC3BhF,OAAA,CAAChB,WAAW;cAAC+F,SAAS,EAAC,YAAY;cAAAC,QAAA,eACjChF,OAAA,CAACf,QAAQ;gBAAA+F,QAAA,gBACPhF,OAAA,CAACjB,SAAS;kBAACgG,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACnDpF,OAAA,CAACjB,SAAS;kBAAAiG,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACpCpF,OAAA,CAACjB,SAAS;kBAAAiG,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAClCpF,OAAA,CAACjB,SAAS;kBAAAiG,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACxCpF,OAAA,CAACjB,SAAS;kBAAAiG,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,EAC5B9C,OAAO,iBAAItC,OAAA,CAACjB,SAAS;kBAACgG,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACdpF,OAAA,CAACnB,SAAS;cAAAmG,QAAA,EACPJ,iBAAiB,CAACZ,GAAG,CAAEU,OAAO,iBAC7B1E,OAAA,CAACf,QAAQ;gBAA0B8F,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBAC/EhF,OAAA,CAAClB,SAAS;kBAACiG,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAEN,OAAO,CAAChD;gBAAI;kBAAAuD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC3EpF,OAAA,CAAClB,SAAS;kBAACiG,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEN,OAAO,CAAC/C;gBAAM;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjEpF,OAAA,CAAClB,SAAS;kBAACiG,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEN,OAAO,CAAC7C;gBAAc;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACzEpF,OAAA,CAAClB,SAAS;kBAACiG,SAAS,EAAC,eAAe;kBAAAC,QAAA,eAClChF,OAAA;oBAAGoG,IAAI,EAAE,UAAU1B,OAAO,CAAC5C,KAAK,EAAG;oBAACiD,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,EAC1EN,OAAO,CAAC5C;kBAAK;oBAAAmD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eACZpF,OAAA,CAAClB,SAAS;kBAACiG,SAAS,EAAC,eAAe;kBAAAC,QAAA,eAClChF,OAAA;oBAAGoG,IAAI,EAAE,OAAO1B,OAAO,CAAC3C,KAAK,EAAG;oBAACgD,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,EACvEN,OAAO,CAAC3C;kBAAK;oBAAAkD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,EACX9C,OAAO,iBACNtC,OAAA,CAAClB,SAAS;kBAACiG,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,gBACvDhF,OAAA,CAAC5B,MAAM;oBACLiI,OAAO,EAAC,SAAS;oBACjBC,IAAI,EAAC,MAAM;oBACXH,OAAO,EAAEA,CAAA,KAAM1B,aAAa,CAACC,OAAO,CAAE;oBACtCK,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,eAE1DhF,OAAA,CAACP,UAAU;sBAACsF,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACTpF,OAAA,CAAC5B,MAAM;oBACLiI,OAAO,EAAC,SAAS;oBACjBC,IAAI,EAAC,MAAM;oBACXH,OAAO,EAAEA,CAAA,KAAM/B,YAAY,CAACM,OAAO,CAAChC,UAAU,CAAE;oBAChDqC,SAAS,EAAC,6CAA6C;oBAAAC,QAAA,eAEvDhF,OAAA,CAACN,SAAS;sBAACqF,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CACZ;cAAA,GAjCYV,OAAO,CAAChC,UAAU;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAkCvB,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPpF,OAAA,CAACd,MAAM;MAAC8G,IAAI,EAAE5E,WAAY;MAAC6E,YAAY,EAAE5E,cAAe;MAAA2D,QAAA,eACtDhF,OAAA,CAACb,aAAa;QAAC4F,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBACzDhF,OAAA,CAACZ,YAAY;UAAA4F,QAAA,gBACXhF,OAAA,CAACX,WAAW;YAAC0F,SAAS,EAAC,iCAAiC;YAAAC,QAAA,EACrD1D,cAAc,GAAG,YAAY,GAAG;UAAkB;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACdpF,OAAA,CAACR,iBAAiB;YAACuF,SAAS,EAAC,eAAe;YAAAC,QAAA,EACzC1D,cAAc,GAAG,yBAAyB,GAAG;UAAiC;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACfpF,OAAA;UAAMuG,QAAQ,EAAE1C,YAAa;UAACkB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBACtDhF,OAAA;YAAK+E,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDhF,OAAA;cAAK+E,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBhF,OAAA,CAAC1B,KAAK;gBAACkI,OAAO,EAAC,MAAM;gBAACzB,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1EpF,OAAA,CAAC3B,KAAK;gBACJmF,EAAE,EAAC,MAAM;gBACTC,KAAK,EAAEjC,QAAQ,CAACE,IAAK;gBACrB6D,QAAQ,EAAEjC,iBAAkB;gBAC5ByB,SAAS,EAAE,GAAG/C,UAAU,CAACN,IAAI,GAAG,gBAAgB,GAAG,EAAE;cAAG;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,EACDpD,UAAU,CAACN,IAAI,iBAAI1B,OAAA;gBAAG+E,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAEhD,UAAU,CAACN;cAAI;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC,eAENpF,OAAA;cAAK+E,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBhF,OAAA,CAAC1B,KAAK;gBAACkI,OAAO,EAAC,QAAQ;gBAACzB,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/EpF,OAAA,CAAC3B,KAAK;gBACJmF,EAAE,EAAC,QAAQ;gBACXC,KAAK,EAAEjC,QAAQ,CAACG,MAAO;gBACvB4D,QAAQ,EAAEjC,iBAAkB;gBAC5ByB,SAAS,EAAE,GAAG/C,UAAU,CAACL,MAAM,GAAG,gBAAgB,GAAG,EAAE;cAAG;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC,EACDpD,UAAU,CAACL,MAAM,iBAAI3B,OAAA;gBAAG+E,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAEhD,UAAU,CAACL;cAAM;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF,CAAC,eAENpF,OAAA;cAAK+E,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBhF,OAAA,CAAC1B,KAAK;gBAACkI,OAAO,EAAC,SAAS;gBAACzB,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxEpF,OAAA,CAAC3B,KAAK;gBACJmF,EAAE,EAAC,SAAS;gBACZC,KAAK,EAAEjC,QAAQ,CAACI,OAAQ;gBACxB2D,QAAQ,EAAEjC;cAAkB;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENpF,OAAA;cAAK+E,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBhF,OAAA,CAAC1B,KAAK;gBAACkI,OAAO,EAAC,gBAAgB;gBAACzB,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnFpF,OAAA,CAAC3B,KAAK;gBACJmF,EAAE,EAAC,gBAAgB;gBACnBC,KAAK,EAAEjC,QAAQ,CAACK,cAAe;gBAC/B0D,QAAQ,EAAEjC;cAAkB;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENpF,OAAA;cAAK+E,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBhF,OAAA,CAAC1B,KAAK;gBAACkI,OAAO,EAAC,OAAO;gBAACzB,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChFpF,OAAA,CAAC3B,KAAK;gBACJmF,EAAE,EAAC,OAAO;gBACVT,IAAI,EAAC,OAAO;gBACZU,KAAK,EAAEjC,QAAQ,CAACM,KAAM;gBACtByD,QAAQ,EAAEjC,iBAAkB;gBAC5ByB,SAAS,EAAE,GAAG/C,UAAU,CAACF,KAAK,GAAG,gBAAgB,GAAG,EAAE;cAAG;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,EACDpD,UAAU,CAACF,KAAK,iBAAI9B,OAAA;gBAAG+E,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAEhD,UAAU,CAACF;cAAK;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF,CAAC,eAENpF,OAAA;cAAK+E,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBhF,OAAA,CAAC1B,KAAK;gBAACkI,OAAO,EAAC,OAAO;gBAACzB,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzEpF,OAAA,CAAC3B,KAAK;gBACJmF,EAAE,EAAC,OAAO;gBACVC,KAAK,EAAEjC,QAAQ,CAACO,KAAM;gBACtBwD,QAAQ,EAAEjC,iBAAkB;gBAC5ByB,SAAS,EAAE,GAAG/C,UAAU,CAACD,KAAK,GAAG,gBAAgB,GAAG,EAAE;cAAG;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,EACDpD,UAAU,CAACD,KAAK,iBAAI/B,OAAA;gBAAG+E,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAEhD,UAAU,CAACD;cAAK;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpF,OAAA,CAACT,YAAY;YAACwF,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC5BhF,OAAA,CAAC5B,MAAM;cACL2E,IAAI,EAAC,QAAQ;cACbsD,OAAO,EAAC,SAAS;cACjBF,OAAO,EAAEhC,UAAW;cACpBY,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAC5C;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTpF,OAAA,CAAC5B,MAAM;cACL2E,IAAI,EAAC,QAAQ;cACb0D,QAAQ,EAAEvE,UAAW;cACrB6C,SAAS,EAAC,sFAAsF;cAAAC,QAAA,EAE/F9C,UAAU,gBACTlC,OAAA,CAAAE,SAAA;gBAAA8E,QAAA,gBACEhF,OAAA,CAACJ,OAAO;kBAACmF,SAAS,EAAC;gBAA2B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,8DAEnD;cAAA,eAAE,CAAC,GACD9D,cAAc,GAAG,eAAe,GAAG;YAAY;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAChF,EAAA,CAzdID,iBAAiB;AAAAuG,EAAA,GAAjBvG,iBAAiB;AA2dvB,eAAeA,iBAAiB;AAAC,IAAAuG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}