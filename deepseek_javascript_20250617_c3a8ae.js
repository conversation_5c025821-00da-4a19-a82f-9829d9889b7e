const API_URL = process.env.REACT_APP_API_URL || 'https://your-api-domain.com/api';

const handleResponse = async (response) => {
  if (!response.ok) {
    const error = await response.json().catch(() => ({
      message: `HTTP error! status: ${response.status}`
    }));
    throw new Error(error.message || 'Request failed');
  }
  return response.json();
};

export const companiesAPI = {
  getAll: async () => {
    try {
      const response = await fetch(`${API_URL}/companies`);
      return handleResponse(response);
    } catch (error) {
      console.error('Failed to fetch companies:', error);
      throw error;
    }
  },
  create: async (data) => {
    try {
      const response = await fetch(`${API_URL}/companies`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Failed to create company:', error);
      throw error;
    }
  },
  update: async (id, data) => {
    try {
      const response = await fetch(`${API_URL}/companies/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      return handleResponse(response);
    } catch (error) {
      console.error(`Failed to update company ${id}:`, error);
      throw error;
    }
  },
  delete: async (id) => {
    try {
      const response = await fetch(`${API_URL}/companies/${id}`, {
        method: 'DELETE',
      });
      return handleResponse(response);
    } catch (error) {
      console.error(`Failed to delete company ${id}:`, error);
      throw error;
    }
  }
};