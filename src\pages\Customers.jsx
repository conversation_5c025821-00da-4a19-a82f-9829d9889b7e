import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  UsersIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../components/ui/table';
import customersAPI from '../api/customersAPI';
import CustomerForm from '../components/Customers/CustomerForm';
import CustomerDetails from '../components/Customers/CustomerDetails';
import toast from 'react-hot-toast';

const Customers = () => {
  const [customers, setCustomers] = useState([]);
  const [filteredCustomers, setFilteredCustomers] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [showForm, setShowForm] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [stats, setStats] = useState({});

  useEffect(() => {
    loadCustomers();
    loadStats();
  }, []);

  useEffect(() => {
    filterCustomers();
  }, [customers, searchTerm]);

  const loadCustomers = () => {
    const customersData = customersAPI.getAllCustomers();
    setCustomers(customersData);
  };

  const loadStats = () => {
    const customersStats = customersAPI.getCustomersStats();
    setStats(customersStats);
  };

  const filterCustomers = () => {
    if (!searchTerm) {
      setFilteredCustomers(customers);
    } else {
      const filtered = customersAPI.searchCustomers(searchTerm);
      setFilteredCustomers(filtered);
    }
  };

  const handleAddCustomer = () => {
    setSelectedCustomer(null);
    setIsEditing(false);
    setShowForm(true);
  };

  const handleEditCustomer = (customer) => {
    setSelectedCustomer(customer);
    setIsEditing(true);
    setShowForm(true);
  };

  const handleViewDetails = (customer) => {
    setSelectedCustomer(customer);
    setShowDetails(true);
  };

  const handleDeleteCustomer = (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذا العميل؟')) {
      customersAPI.deleteCustomer(id);
      loadCustomers();
      loadStats();
      toast.success('تم حذف العميل بنجاح');
    }
  };

  const handleSaveCustomer = (customerData) => {
    try {
      if (isEditing) {
        customersAPI.updateCustomer(selectedCustomer.id, customerData);
        toast.success('تم تحديث العميل بنجاح');
      } else {
        customersAPI.addCustomer(customerData);
        toast.success('تم إضافة العميل بنجاح');
      }
      
      loadCustomers();
      loadStats();
      setShowForm(false);
    } catch (error) {
      toast.error('حدث خطأ أثناء حفظ العميل');
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const getCustomerTypeColor = (type) => {
    switch (type) {
      case 'فرد':
        return 'bg-blue-100 text-blue-800';
      case 'شركة':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'نشط':
        return 'bg-green-100 text-green-800';
      case 'غير نشط':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* العنوان والإحصائيات */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة العملاء</h1>
            <p className="text-gray-600 mt-2">إدارة بيانات العملاء والحسابات</p>
          </div>
          <Button
            onClick={handleAddCustomer}
            className="bg-indigo-600 hover:bg-indigo-700 text-white"
          >
            <PlusIcon className="w-4 h-4 ml-2" />
            عميل جديد
          </Button>
        </div>

        {/* بطاقات الإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">إجمالي العملاء</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalCustomers || 0}</p>
                </div>
                <div className="p-3 bg-indigo-100 rounded-full">
                  <UsersIcon className="w-6 h-6 text-indigo-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">العملاء النشطون</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.activeCustomers || 0}</p>
                </div>
                <div className="p-3 bg-green-100 rounded-full">
                  <UsersIcon className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">إجمالي المبيعات</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(stats.totalSales || 0)}
                  </p>
                </div>
                <div className="p-3 bg-blue-100 rounded-full">
                  <CurrencyDollarIcon className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">الأرصدة المستحقة</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(stats.totalBalance || 0)}
                  </p>
                </div>
                <div className="p-3 bg-orange-100 rounded-full">
                  <CurrencyDollarIcon className="w-6 h-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </motion.div>

      {/* البحث والجدول */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>قائمة العملاء</CardTitle>
              <div className="flex items-center space-x-4 space-x-reverse">
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="البحث في العملاء..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10 w-64"
                  />
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>كود العميل</TableHead>
                    <TableHead>اسم العميل</TableHead>
                    <TableHead>النوع</TableHead>
                    <TableHead>الهاتف</TableHead>
                    <TableHead>البريد الإلكتروني</TableHead>
                    <TableHead>الرصيد الحالي</TableHead>
                    <TableHead>إجمالي المشتريات</TableHead>
                    <TableHead>الحالة</TableHead>
                    <TableHead className="text-center">الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredCustomers.map((customer) => (
                    <TableRow key={customer.id}>
                      <TableCell className="font-medium">{customer.code}</TableCell>
                      <TableCell>{customer.name}</TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCustomerTypeColor(customer.type)}`}>
                          {customer.type}
                        </span>
                      </TableCell>
                      <TableCell>{customer.phone}</TableCell>
                      <TableCell>{customer.email}</TableCell>
                      <TableCell>
                        <span className={customer.currentBalance > 0 ? 'text-red-600 font-medium' : 'text-gray-600'}>
                          {formatCurrency(customer.currentBalance)}
                        </span>
                      </TableCell>
                      <TableCell>{formatCurrency(customer.totalPurchases)}</TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(customer.status)}`}>
                          {customer.status}
                        </span>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center justify-center space-x-2 space-x-reverse">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleViewDetails(customer)}
                          >
                            <EyeIcon className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditCustomer(customer)}
                          >
                            <PencilIcon className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteCustomer(customer.id)}
                            className="text-red-600 hover:text-red-800"
                          >
                            <TrashIcon className="w-4 h-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* نموذج إضافة/تعديل العميل */}
      {showForm && (
        <CustomerForm
          customer={selectedCustomer}
          isEditing={isEditing}
          onSave={handleSaveCustomer}
          onClose={() => setShowForm(false)}
        />
      )}

      {/* تفاصيل العميل */}
      {showDetails && selectedCustomer && (
        <CustomerDetails
          customer={selectedCustomer}
          onClose={() => setShowDetails(false)}
        />
      )}
    </div>
  );
};

export default Customers;
