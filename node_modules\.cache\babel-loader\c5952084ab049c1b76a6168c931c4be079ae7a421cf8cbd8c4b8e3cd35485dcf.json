{"ast": null, "code": "import * as e from \"react\";\nimport { useSyncExternalStore as t } from './useSyncExternalStoreShimClient.js';\nimport { useSyncExternalStore as o } from './useSyncExternalStoreShimServer.js';\nconst r = typeof window != \"undefined\" && typeof window.document != \"undefined\" && typeof window.document.createElement != \"undefined\",\n  s = !r,\n  c = s ? o : t,\n  a = \"useSyncExternalStore\" in e ? (n => n.useSyncExternalStore)(e) : c;\nexport { a as useSyncExternalStore };", "map": {"version": 3, "names": ["e", "useSyncExternalStore", "t", "o", "r", "window", "document", "createElement", "s", "c", "a", "n"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/node_modules/@headlessui/react/dist/use-sync-external-store-shim/index.js"], "sourcesContent": ["import*as e from\"react\";import{useSyncExternalStore as t}from'./useSyncExternalStoreShimClient.js';import{useSyncExternalStore as o}from'./useSyncExternalStoreShimServer.js';const r=typeof window!=\"undefined\"&&typeof window.document!=\"undefined\"&&typeof window.document.createElement!=\"undefined\",s=!r,c=s?o:t,a=\"useSyncExternalStore\"in e?(n=>n.useSyncExternalStore)(e):c;export{a as useSyncExternalStore};\n"], "mappings": "AAAA,OAAM,KAAIA,CAAC,MAAK,OAAO;AAAC,SAAOC,oBAAoB,IAAIC,CAAC,QAAK,qCAAqC;AAAC,SAAOD,oBAAoB,IAAIE,CAAC,QAAK,qCAAqC;AAAC,MAAMC,CAAC,GAAC,OAAOC,MAAM,IAAE,WAAW,IAAE,OAAOA,MAAM,CAACC,QAAQ,IAAE,WAAW,IAAE,OAAOD,MAAM,CAACC,QAAQ,CAACC,aAAa,IAAE,WAAW;EAACC,CAAC,GAAC,CAACJ,CAAC;EAACK,CAAC,GAACD,CAAC,GAACL,CAAC,GAACD,CAAC;EAACQ,CAAC,GAAC,sBAAsB,IAAGV,CAAC,GAAC,CAACW,CAAC,IAAEA,CAAC,CAACV,oBAAoB,EAAED,CAAC,CAAC,GAACS,CAAC;AAAC,SAAOC,CAAC,IAAIT,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}