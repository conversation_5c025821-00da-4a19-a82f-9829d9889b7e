{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u0633\\u0637\\u062D \\u0627\\u0644\\u0645\\u0643\\u062A\\u0628\\\\333\\\\src\\\\components\\\\Dashboard\\\\QuickActions.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { useNavigate } from 'react-router-dom';\nimport { PlusIcon, DocumentArrowDownIcon, ChartBarIcon, Cog6ToothIcon } from '@heroicons/react/24/outline';\nimport { Card, CardContent, CardHeader, CardTitle } from '../ui/card';\nimport { Button } from '../ui/button';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuickActions = () => {\n  _s();\n  const navigate = useNavigate();\n  const actions = [{\n    title: 'إضافة شركة جديدة',\n    description: 'أضف شركة جديدة إلى النظام',\n    icon: PlusIcon,\n    color: 'bg-blue-500 hover:bg-blue-600',\n    action: () => navigate('/companies')\n  }, {\n    title: 'تصدير التقارير',\n    description: 'تصدير تقارير الشركات',\n    icon: DocumentArrowDownIcon,\n    color: 'bg-green-500 hover:bg-green-600',\n    action: () => navigate('/reports')\n  }, {\n    title: 'عرض الإحصائيات',\n    description: 'مراجعة الإحصائيات التفصيلية',\n    icon: ChartBarIcon,\n    color: 'bg-purple-500 hover:bg-purple-600',\n    action: () => navigate('/reports')\n  }, {\n    title: 'إعدادات النظام',\n    description: 'تخصيص إعدادات النظام',\n    icon: Cog6ToothIcon,\n    color: 'bg-orange-500 hover:bg-orange-600',\n    action: () => navigate('/settings')\n  }];\n  return /*#__PURE__*/_jsxDEV(Card, {\n    children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n      children: /*#__PURE__*/_jsxDEV(CardTitle, {\n        children: \"\\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A \\u0627\\u0644\\u0633\\u0631\\u064A\\u0639\\u0629\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n        children: actions.map((action, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: index * 0.1\n          },\n          whileHover: {\n            scale: 1.02\n          },\n          whileTap: {\n            scale: 0.98\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: action.action,\n            className: `w-full h-auto p-6 ${action.color} text-white border-0 flex flex-col items-center space-y-3 transition-all duration-200`,\n            variant: \"default\",\n            children: [/*#__PURE__*/_jsxDEV(action.icon, {\n              className: \"w-8 h-8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-semibold text-sm\",\n                children: action.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs opacity-90 mt-1\",\n                children: action.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 15\n          }, this)\n        }, action.title, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this);\n};\n_s(QuickActions, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = QuickActions;\nexport default QuickActions;\nvar _c;\n$RefreshReg$(_c, \"QuickActions\");", "map": {"version": 3, "names": ["React", "motion", "useNavigate", "PlusIcon", "DocumentArrowDownIcon", "ChartBarIcon", "Cog6ToothIcon", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "QuickActions", "_s", "navigate", "actions", "title", "description", "icon", "color", "action", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "map", "index", "div", "initial", "opacity", "y", "animate", "transition", "delay", "whileHover", "scale", "whileTap", "onClick", "variant", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/src/components/Dashboard/QuickActions.jsx"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  PlusIcon,\n  DocumentArrowDownIcon,\n  ChartBarIcon,\n  Cog6ToothIcon\n} from '@heroicons/react/24/outline';\nimport { Card, CardContent, CardHeader, CardTitle } from '../ui/card';\nimport { Button } from '../ui/button';\n\nconst QuickActions = () => {\n  const navigate = useNavigate();\n\n  const actions = [\n    {\n      title: 'إضافة شركة جديدة',\n      description: 'أضف شركة جديدة إلى النظام',\n      icon: PlusIcon,\n      color: 'bg-blue-500 hover:bg-blue-600',\n      action: () => navigate('/companies')\n    },\n    {\n      title: 'تصدير التقارير',\n      description: 'تصدير تقارير الشركات',\n      icon: DocumentArrowDownIcon,\n      color: 'bg-green-500 hover:bg-green-600',\n      action: () => navigate('/reports')\n    },\n    {\n      title: 'عرض الإحصائيات',\n      description: 'مراجعة الإحصائيات التفصيلية',\n      icon: ChartBarIcon,\n      color: 'bg-purple-500 hover:bg-purple-600',\n      action: () => navigate('/reports')\n    },\n    {\n      title: 'إعدادات النظام',\n      description: 'تخصيص إعدادات النظام',\n      icon: Cog6ToothIcon,\n      color: 'bg-orange-500 hover:bg-orange-600',\n      action: () => navigate('/settings')\n    }\n  ];\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle>الإجراءات السريعة</CardTitle>\n      </CardHeader>\n      <CardContent>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n          {actions.map((action, index) => (\n            <motion.div\n              key={action.title}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: index * 0.1 }}\n              whileHover={{ scale: 1.02 }}\n              whileTap={{ scale: 0.98 }}\n            >\n              <Button\n                onClick={action.action}\n                className={`w-full h-auto p-6 ${action.color} text-white border-0 flex flex-col items-center space-y-3 transition-all duration-200`}\n                variant=\"default\"\n              >\n                <action.icon className=\"w-8 h-8\" />\n                <div className=\"text-center\">\n                  <h3 className=\"font-semibold text-sm\">{action.title}</h3>\n                  <p className=\"text-xs opacity-90 mt-1\">{action.description}</p>\n                </div>\n              </Button>\n            </motion.div>\n          ))}\n        </div>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default QuickActions;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,QAAQ,EACRC,qBAAqB,EACrBC,YAAY,EACZC,aAAa,QACR,6BAA6B;AACpC,SAASC,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,SAAS,QAAQ,YAAY;AACrE,SAASC,MAAM,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAMC,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAE9B,MAAMe,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE,2BAA2B;IACxCC,IAAI,EAAEjB,QAAQ;IACdkB,KAAK,EAAE,+BAA+B;IACtCC,MAAM,EAAEA,CAAA,KAAMN,QAAQ,CAAC,YAAY;EACrC,CAAC,EACD;IACEE,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE,sBAAsB;IACnCC,IAAI,EAAEhB,qBAAqB;IAC3BiB,KAAK,EAAE,iCAAiC;IACxCC,MAAM,EAAEA,CAAA,KAAMN,QAAQ,CAAC,UAAU;EACnC,CAAC,EACD;IACEE,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE,6BAA6B;IAC1CC,IAAI,EAAEf,YAAY;IAClBgB,KAAK,EAAE,mCAAmC;IAC1CC,MAAM,EAAEA,CAAA,KAAMN,QAAQ,CAAC,UAAU;EACnC,CAAC,EACD;IACEE,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE,sBAAsB;IACnCC,IAAI,EAAEd,aAAa;IACnBe,KAAK,EAAE,mCAAmC;IAC1CC,MAAM,EAAEA,CAAA,KAAMN,QAAQ,CAAC,WAAW;EACpC,CAAC,CACF;EAED,oBACEH,OAAA,CAACN,IAAI;IAAAgB,QAAA,gBACHV,OAAA,CAACJ,UAAU;MAAAc,QAAA,eACTV,OAAA,CAACH,SAAS;QAAAa,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC,eACbd,OAAA,CAACL,WAAW;MAAAe,QAAA,eACVV,OAAA;QAAKe,SAAS,EAAC,sDAAsD;QAAAL,QAAA,EAClEN,OAAO,CAACY,GAAG,CAAC,CAACP,MAAM,EAAEQ,KAAK,kBACzBjB,OAAA,CAACZ,MAAM,CAAC8B,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,KAAK,EAAEP,KAAK,GAAG;UAAI,CAAE;UACnCQ,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAAAhB,QAAA,eAE1BV,OAAA,CAACF,MAAM;YACL8B,OAAO,EAAEnB,MAAM,CAACA,MAAO;YACvBM,SAAS,EAAE,qBAAqBN,MAAM,CAACD,KAAK,uFAAwF;YACpIqB,OAAO,EAAC,SAAS;YAAAnB,QAAA,gBAEjBV,OAAA,CAACS,MAAM,CAACF,IAAI;cAACQ,SAAS,EAAC;YAAS;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnCd,OAAA;cAAKe,SAAS,EAAC,aAAa;cAAAL,QAAA,gBAC1BV,OAAA;gBAAIe,SAAS,EAAC,uBAAuB;gBAAAL,QAAA,EAAED,MAAM,CAACJ;cAAK;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACzDd,OAAA;gBAAGe,SAAS,EAAC,yBAAyB;gBAAAL,QAAA,EAAED,MAAM,CAACH;cAAW;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC,GAjBJL,MAAM,CAACJ,KAAK;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkBP,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEX,CAAC;AAACZ,EAAA,CAnEID,YAAY;EAAA,QACCZ,WAAW;AAAA;AAAAyC,EAAA,GADxB7B,YAAY;AAqElB,eAAeA,YAAY;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}