[{"C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\CompanyManagement.jsx": "3", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\companiesAPI.js": "4", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\button.jsx": "5", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\card.jsx": "6", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\table.jsx": "7", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\dialog.jsx": "8", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\input.jsx": "9", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\label.jsx": "10", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\lib\\utils.js": "11", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\contexts\\AuthContext.js": "12", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Auth\\ProtectedRoute.js": "13", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\LoadingSpinner.jsx": "14", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Login.jsx": "15", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Layout\\Layout.jsx": "16", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Layout\\Sidebar.jsx": "17", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Layout\\Header.jsx": "18", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Dashboard.jsx": "19", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\StatsCard.jsx": "20", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\RecentActivities.jsx": "21", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\CompanyChart.jsx": "22", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\QuickActions.jsx": "23", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Companies.jsx": "24", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Reports.jsx": "25", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Reports\\ReportsChart.jsx": "26", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Reports\\ReportFilters.jsx": "27", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Reports\\ExportOptions.jsx": "28", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Settings.jsx": "29", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Purchases.jsx": "30", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Sales.jsx": "31", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\purchasesAPI.js": "32", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\customersAPI.js": "33", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\salesAPI.js": "34", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\inventoryAPI.js": "35", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Inventory.jsx": "36", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Customers.jsx": "37", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Expenses.jsx": "38", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\expensesAPI.js": "39", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Sales\\SalesForm.jsx": "40", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Sales\\SalesInvoice.jsx": "41", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Purchases\\PurchaseForm.jsx": "42", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Purchases\\PurchaseInvoice.jsx": "43", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Inventory\\ProductForm.jsx": "44", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Customers\\CustomerForm.jsx": "45", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Expenses\\ExpenseForm.jsx": "46", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Inventory\\ProductDetails.jsx": "47", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Customers\\CustomerDetails.jsx": "48", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Expenses\\ExpenseDetails.jsx": "49", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\QRCode\\InvoiceQRCode.jsx": "50", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\utils\\qrCodeGenerator.js": "51", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Settings\\EInvoiceSettings.jsx": "52", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\QRCodeTest.jsx": "53"}, {"size": 263, "mtime": 1750149838411, "results": "54", "hashOfConfig": "55"}, {"size": 3097, "mtime": 1750165154783, "results": "56", "hashOfConfig": "55"}, {"size": 19179, "mtime": 1750160550738, "results": "57", "hashOfConfig": "55"}, {"size": 2696, "mtime": 1750149893012, "results": "58", "hashOfConfig": "55"}, {"size": 1359, "mtime": 1750160192372, "results": "59", "hashOfConfig": "55"}, {"size": 1508, "mtime": 1750160215710, "results": "60", "hashOfConfig": "55"}, {"size": 2188, "mtime": 1750160228722, "results": "61", "hashOfConfig": "55"}, {"size": 3008, "mtime": 1750160588665, "results": "62", "hashOfConfig": "55"}, {"size": 685, "mtime": 1750160199947, "results": "63", "hashOfConfig": "55"}, {"size": 371, "mtime": 1750160205839, "results": "64", "hashOfConfig": "55"}, {"size": 225, "mtime": 1750160250760, "results": "65", "hashOfConfig": "55"}, {"size": 1905, "mtime": 1750161144315, "results": "66", "hashOfConfig": "55"}, {"size": 640, "mtime": 1750161153757, "results": "67", "hashOfConfig": "55"}, {"size": 889, "mtime": 1750161164023, "results": "68", "hashOfConfig": "55"}, {"size": 6371, "mtime": 1750161191711, "results": "69", "hashOfConfig": "55"}, {"size": 1716, "mtime": 1750161204463, "results": "70", "hashOfConfig": "55"}, {"size": 4127, "mtime": 1750163074848, "results": "71", "hashOfConfig": "55"}, {"size": 7098, "mtime": 1750161255384, "results": "72", "hashOfConfig": "55"}, {"size": 3791, "mtime": 1750161576871, "results": "73", "hashOfConfig": "55"}, {"size": 1649, "mtime": 1750161563760, "results": "74", "hashOfConfig": "55"}, {"size": 3423, "mtime": 1750161315525, "results": "75", "hashOfConfig": "55"}, {"size": 2877, "mtime": 1750161332248, "results": "76", "hashOfConfig": "55"}, {"size": 2654, "mtime": 1750161348353, "results": "77", "hashOfConfig": "55"}, {"size": 385, "mtime": 1750161356392, "results": "78", "hashOfConfig": "55"}, {"size": 8679, "mtime": 1750161394588, "results": "79", "hashOfConfig": "55"}, {"size": 3992, "mtime": 1750161417721, "results": "80", "hashOfConfig": "55"}, {"size": 6035, "mtime": 1750161443223, "results": "81", "hashOfConfig": "55"}, {"size": 7977, "mtime": 1750161474940, "results": "82", "hashOfConfig": "55"}, {"size": 16091, "mtime": 1750165057799, "results": "83", "hashOfConfig": "55"}, {"size": 11548, "mtime": 1750163028092, "results": "84", "hashOfConfig": "55"}, {"size": 11112, "mtime": 1750162984504, "results": "85", "hashOfConfig": "55"}, {"size": 4613, "mtime": 1750162839467, "results": "86", "hashOfConfig": "55"}, {"size": 6415, "mtime": 1750162903194, "results": "87", "hashOfConfig": "55"}, {"size": 4177, "mtime": 1750162815740, "results": "88", "hashOfConfig": "55"}, {"size": 7144, "mtime": 1750162872375, "results": "89", "hashOfConfig": "55"}, {"size": 13389, "mtime": 1750163130963, "results": "90", "hashOfConfig": "55"}, {"size": 12146, "mtime": 1750163173798, "results": "91", "hashOfConfig": "55"}, {"size": 12367, "mtime": 1750163221644, "results": "92", "hashOfConfig": "55"}, {"size": 8399, "mtime": 1750162941810, "results": "93", "hashOfConfig": "55"}, {"size": 15337, "mtime": 1750163276893, "results": "94", "hashOfConfig": "55"}, {"size": 9309, "mtime": 1750164885488, "results": "95", "hashOfConfig": "55"}, {"size": 14994, "mtime": 1750163378240, "results": "96", "hashOfConfig": "55"}, {"size": 8789, "mtime": 1750163415999, "results": "97", "hashOfConfig": "55"}, {"size": 14566, "mtime": 1750163463903, "results": "98", "hashOfConfig": "55"}, {"size": 13625, "mtime": 1750163508364, "results": "99", "hashOfConfig": "55"}, {"size": 15378, "mtime": 1750163560124, "results": "100", "hashOfConfig": "55"}, {"size": 11070, "mtime": 1750163603317, "results": "101", "hashOfConfig": "55"}, {"size": 9819, "mtime": 1750163644888, "results": "102", "hashOfConfig": "55"}, {"size": 10866, "mtime": 1750163692199, "results": "103", "hashOfConfig": "55"}, {"size": 7732, "mtime": 1750164856652, "results": "104", "hashOfConfig": "55"}, {"size": 4808, "mtime": 1750165076219, "results": "105", "hashOfConfig": "55"}, {"size": 17501, "mtime": 1750164953748, "results": "106", "hashOfConfig": "55"}, {"size": 11777, "mtime": 1750165127202, "results": "107", "hashOfConfig": "55"}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "7sy7mo", {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\CompanyManagement.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\companiesAPI.js", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\button.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\card.jsx", ["267"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\table.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\dialog.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\input.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\label.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\lib\\utils.js", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Auth\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\LoadingSpinner.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Login.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Layout\\Layout.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Layout\\Sidebar.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Layout\\Header.jsx", ["268"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Dashboard.jsx", ["269", "270"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\StatsCard.jsx", ["271"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\RecentActivities.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\CompanyChart.jsx", ["272", "273"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\QuickActions.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Companies.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Reports.jsx", ["274", "275", "276", "277"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Reports\\ReportsChart.jsx", ["278", "279"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Reports\\ReportFilters.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Reports\\ExportOptions.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Settings.jsx", ["280"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Purchases.jsx", ["281"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Sales.jsx", ["282", "283", "284"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\purchasesAPI.js", ["285"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\customersAPI.js", ["286"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\salesAPI.js", ["287"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\inventoryAPI.js", ["288"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Inventory.jsx", ["289"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Customers.jsx", ["290"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Expenses.jsx", ["291"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\expensesAPI.js", ["292"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Sales\\SalesForm.jsx", ["293"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Sales\\SalesInvoice.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Purchases\\PurchaseForm.jsx", ["294"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Purchases\\PurchaseInvoice.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Inventory\\ProductForm.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Customers\\CustomerForm.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Expenses\\ExpenseForm.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Inventory\\ProductDetails.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Customers\\CustomerDetails.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Expenses\\ExpenseDetails.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\QRCode\\InvoiceQRCode.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\utils\\qrCodeGenerator.js", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Settings\\EInvoiceSettings.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\QRCodeTest.jsx", [], [], {"ruleId": "295", "severity": 1, "message": "296", "line": 26, "column": 3, "nodeType": "297", "endLine": 33, "endColumn": 5}, {"ruleId": "298", "severity": 1, "message": "299", "line": 11, "column": 10, "nodeType": "300", "messageId": "301", "endLine": 11, "endColumn": 16}, {"ruleId": "298", "severity": 1, "message": "302", "line": 8, "column": 3, "nodeType": "300", "messageId": "301", "endLine": 8, "endColumn": 22}, {"ruleId": "298", "severity": 1, "message": "303", "line": 9, "column": 3, "nodeType": "300", "messageId": "301", "endLine": 9, "endColumn": 24}, {"ruleId": "298", "severity": 1, "message": "304", "line": 2, "column": 10, "nodeType": "300", "messageId": "301", "endLine": 2, "endColumn": 16}, {"ruleId": "298", "severity": 1, "message": "305", "line": 3, "column": 3, "nodeType": "300", "messageId": "301", "endLine": 3, "endColumn": 12}, {"ruleId": "298", "severity": 1, "message": "306", "line": 4, "column": 3, "nodeType": "300", "messageId": "301", "endLine": 4, "endColumn": 7}, {"ruleId": "298", "severity": 1, "message": "307", "line": 6, "column": 3, "nodeType": "300", "messageId": "301", "endLine": 6, "endColumn": 15}, {"ruleId": "298", "severity": 1, "message": "308", "line": 7, "column": 3, "nodeType": "300", "messageId": "301", "endLine": 7, "endColumn": 13}, {"ruleId": "298", "severity": 1, "message": "309", "line": 11, "column": 10, "nodeType": "300", "messageId": "301", "endLine": 11, "endColumn": 15}, {"ruleId": "298", "severity": 1, "message": "310", "line": 12, "column": 10, "nodeType": "300", "messageId": "301", "endLine": 12, "endColumn": 15}, {"ruleId": "298", "severity": 1, "message": "311", "line": 13, "column": 3, "nodeType": "300", "messageId": "301", "endLine": 13, "endColumn": 11}, {"ruleId": "298", "severity": 1, "message": "312", "line": 14, "column": 3, "nodeType": "300", "messageId": "301", "endLine": 14, "endColumn": 6}, {"ruleId": "298", "severity": 1, "message": "313", "line": 7, "column": 3, "nodeType": "300", "messageId": "301", "endLine": 7, "endColumn": 15}, {"ruleId": "314", "severity": 1, "message": "315", "line": 37, "column": 6, "nodeType": "316", "endLine": 37, "endColumn": 29, "suggestions": "317"}, {"ruleId": "298", "severity": 1, "message": "318", "line": 16, "column": 8, "nodeType": "300", "messageId": "301", "endLine": 16, "endColumn": 20}, {"ruleId": "298", "severity": 1, "message": "319", "line": 17, "column": 8, "nodeType": "300", "messageId": "301", "endLine": 17, "endColumn": 20}, {"ruleId": "314", "severity": 1, "message": "320", "line": 39, "column": 6, "nodeType": "316", "endLine": 39, "endColumn": 25, "suggestions": "321"}, {"ruleId": "322", "severity": 1, "message": "323", "line": 140, "column": 1, "nodeType": "324", "endLine": 140, "endColumn": 35}, {"ruleId": "322", "severity": 1, "message": "323", "line": 199, "column": 1, "nodeType": "324", "endLine": 199, "endColumn": 35}, {"ruleId": "322", "severity": 1, "message": "323", "line": 138, "column": 1, "nodeType": "324", "endLine": 138, "endColumn": 31}, {"ruleId": "322", "severity": 1, "message": "323", "line": 223, "column": 1, "nodeType": "324", "endLine": 223, "endColumn": 35}, {"ruleId": "314", "severity": 1, "message": "325", "line": 40, "column": 6, "nodeType": "316", "endLine": 40, "endColumn": 28, "suggestions": "326"}, {"ruleId": "314", "severity": 1, "message": "327", "line": 38, "column": 6, "nodeType": "316", "endLine": 38, "endColumn": 29, "suggestions": "328"}, {"ruleId": "314", "severity": 1, "message": "329", "line": 38, "column": 6, "nodeType": "316", "endLine": 38, "endColumn": 28, "suggestions": "330"}, {"ruleId": "322", "severity": 1, "message": "323", "line": 259, "column": 1, "nodeType": "324", "endLine": 259, "endColumn": 34}, {"ruleId": "314", "severity": 1, "message": "331", "line": 49, "column": 6, "nodeType": "316", "endLine": 49, "endColumn": 41, "suggestions": "332"}, {"ruleId": "314", "severity": 1, "message": "331", "line": 46, "column": 6, "nodeType": "316", "endLine": 46, "endColumn": 41, "suggestions": "333"}, "jsx-a11y/heading-has-content", "Headings must have content and the content must be accessible by a screen reader.", "JSXOpeningElement", "no-unused-vars", "'Button' is defined but never used.", "Identifier", "unusedVar", "'ArrowTrendingUpIcon' is defined but never used.", "'ArrowTrendingDownIcon' is defined but never used.", "'motion' is defined but never used.", "'LineChart' is defined but never used.", "'Line' is defined but never used.", "'CalendarIcon' is defined but never used.", "'FunnelIcon' is defined but never used.", "'Input' is defined but never used.", "'Label' is defined but never used.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'GlobeAltIcon' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'filterPurchases'. Either include it or remove the dependency array.", "ArrayExpression", ["334"], "'customersAPI' is defined but never used.", "'inventoryAPI' is defined but never used.", "React Hook useEffect has a missing dependency: 'filterSales'. Either include it or remove the dependency array.", ["335"], "import/no-anonymous-default-export", "Assign instance to a variable before exporting as module default", "ExportDefaultDeclaration", "React Hook useEffect has a missing dependency: 'filterProducts'. Either include it or remove the dependency array.", ["336"], "React Hook useEffect has a missing dependency: 'filterCustomers'. Either include it or remove the dependency array.", ["337"], "React Hook useEffect has a missing dependency: 'filterExpenses'. Either include it or remove the dependency array.", ["338"], "React Hook useEffect has a missing dependency: 'calculateTotals'. Either include it or remove the dependency array.", ["339"], ["340"], {"desc": "341", "fix": "342"}, {"desc": "343", "fix": "344"}, {"desc": "345", "fix": "346"}, {"desc": "347", "fix": "348"}, {"desc": "349", "fix": "350"}, {"desc": "351", "fix": "352"}, {"desc": "351", "fix": "353"}, "Update the dependencies array to be: [filterPurchases, purchases, searchTerm]", {"range": "354", "text": "355"}, "Update the dependencies array to be: [filterSales, sales, searchTerm]", {"range": "356", "text": "357"}, "Update the dependencies array to be: [filterProducts, products, searchTerm]", {"range": "358", "text": "359"}, "Update the dependencies array to be: [customers, filterCustomers, searchTerm]", {"range": "360", "text": "361"}, "Update the dependencies array to be: [expenses, filterExpenses, searchTerm]", {"range": "362", "text": "363"}, "Update the dependencies array to be: [formData.items, formData.discount, calculateTotals]", {"range": "364", "text": "365"}, {"range": "366", "text": "365"}, [1318, 1341], "[filterPurchases, purchases, searchTerm]", [1355, 1374], "[filterSales, sales, searchTerm]", [1417, 1439], "[filterProducts, products, searchTerm]", [1333, 1356], "[customers, filterCustomers, searchTerm]", [1316, 1338], "[expenses, filterExpenses, searchTerm]", [1515, 1550], "[formData.items, formData.discount, calculateTotals]", [1453, 1488]]