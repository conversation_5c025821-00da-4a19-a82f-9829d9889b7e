[{"C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\CompanyManagement.jsx": "3", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\companiesAPI.js": "4", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\button.jsx": "5", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\card.jsx": "6", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\table.jsx": "7", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\dialog.jsx": "8", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\input.jsx": "9", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\label.jsx": "10", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\lib\\utils.js": "11", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\contexts\\AuthContext.js": "12", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Auth\\ProtectedRoute.js": "13", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\LoadingSpinner.jsx": "14", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Login.jsx": "15", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Layout\\Layout.jsx": "16", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Layout\\Sidebar.jsx": "17", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Layout\\Header.jsx": "18", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Dashboard.jsx": "19", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\StatsCard.jsx": "20", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\RecentActivities.jsx": "21", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\CompanyChart.jsx": "22", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\QuickActions.jsx": "23", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Companies.jsx": "24", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Reports.jsx": "25", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Reports\\ReportsChart.jsx": "26", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Reports\\ReportFilters.jsx": "27", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Reports\\ExportOptions.jsx": "28", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Settings.jsx": "29", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Purchases.jsx": "30", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Sales.jsx": "31", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\purchasesAPI.js": "32", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\customersAPI.js": "33", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\salesAPI.js": "34", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\inventoryAPI.js": "35", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Inventory.jsx": "36", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Customers.jsx": "37", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Expenses.jsx": "38", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\expensesAPI.js": "39", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Sales\\SalesForm.jsx": "40", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Sales\\SalesInvoice.jsx": "41", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Purchases\\PurchaseForm.jsx": "42", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Purchases\\PurchaseInvoice.jsx": "43", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Inventory\\ProductForm.jsx": "44", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Customers\\CustomerForm.jsx": "45", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Expenses\\ExpenseForm.jsx": "46", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Inventory\\ProductDetails.jsx": "47", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Customers\\CustomerDetails.jsx": "48", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Expenses\\ExpenseDetails.jsx": "49"}, {"size": 263, "mtime": 1750149838411, "results": "50", "hashOfConfig": "51"}, {"size": 2821, "mtime": 1750163055907, "results": "52", "hashOfConfig": "51"}, {"size": 19179, "mtime": 1750160550738, "results": "53", "hashOfConfig": "51"}, {"size": 2696, "mtime": 1750149893012, "results": "54", "hashOfConfig": "51"}, {"size": 1359, "mtime": 1750160192372, "results": "55", "hashOfConfig": "51"}, {"size": 1508, "mtime": 1750160215710, "results": "56", "hashOfConfig": "51"}, {"size": 2188, "mtime": 1750160228722, "results": "57", "hashOfConfig": "51"}, {"size": 3008, "mtime": 1750160588665, "results": "58", "hashOfConfig": "51"}, {"size": 685, "mtime": 1750160199947, "results": "59", "hashOfConfig": "51"}, {"size": 371, "mtime": 1750160205839, "results": "60", "hashOfConfig": "51"}, {"size": 225, "mtime": 1750160250760, "results": "61", "hashOfConfig": "51"}, {"size": 1905, "mtime": 1750161144315, "results": "62", "hashOfConfig": "51"}, {"size": 640, "mtime": 1750161153757, "results": "63", "hashOfConfig": "51"}, {"size": 889, "mtime": 1750161164023, "results": "64", "hashOfConfig": "51"}, {"size": 6371, "mtime": 1750161191711, "results": "65", "hashOfConfig": "51"}, {"size": 1716, "mtime": 1750161204463, "results": "66", "hashOfConfig": "51"}, {"size": 4127, "mtime": 1750163074848, "results": "67", "hashOfConfig": "51"}, {"size": 7098, "mtime": 1750161255384, "results": "68", "hashOfConfig": "51"}, {"size": 3791, "mtime": 1750161576871, "results": "69", "hashOfConfig": "51"}, {"size": 1649, "mtime": 1750161563760, "results": "70", "hashOfConfig": "51"}, {"size": 3423, "mtime": 1750161315525, "results": "71", "hashOfConfig": "51"}, {"size": 2877, "mtime": 1750161332248, "results": "72", "hashOfConfig": "51"}, {"size": 2654, "mtime": 1750161348353, "results": "73", "hashOfConfig": "51"}, {"size": 385, "mtime": 1750161356392, "results": "74", "hashOfConfig": "51"}, {"size": 8679, "mtime": 1750161394588, "results": "75", "hashOfConfig": "51"}, {"size": 3992, "mtime": 1750161417721, "results": "76", "hashOfConfig": "51"}, {"size": 6035, "mtime": 1750161443223, "results": "77", "hashOfConfig": "51"}, {"size": 7977, "mtime": 1750161474940, "results": "78", "hashOfConfig": "51"}, {"size": 15856, "mtime": 1750161632482, "results": "79", "hashOfConfig": "51"}, {"size": 11548, "mtime": 1750163028092, "results": "80", "hashOfConfig": "51"}, {"size": 11112, "mtime": 1750162984504, "results": "81", "hashOfConfig": "51"}, {"size": 4613, "mtime": 1750162839467, "results": "82", "hashOfConfig": "51"}, {"size": 6415, "mtime": 1750162903194, "results": "83", "hashOfConfig": "51"}, {"size": 4177, "mtime": 1750162815740, "results": "84", "hashOfConfig": "51"}, {"size": 7144, "mtime": 1750162872375, "results": "85", "hashOfConfig": "51"}, {"size": 13389, "mtime": 1750163130963, "results": "86", "hashOfConfig": "51"}, {"size": 12146, "mtime": 1750163173798, "results": "87", "hashOfConfig": "51"}, {"size": 12367, "mtime": 1750163221644, "results": "88", "hashOfConfig": "51"}, {"size": 8399, "mtime": 1750162941810, "results": "89", "hashOfConfig": "51"}, {"size": 15337, "mtime": 1750163276893, "results": "90", "hashOfConfig": "51"}, {"size": 8624, "mtime": 1750163322895, "results": "91", "hashOfConfig": "51"}, {"size": 14994, "mtime": 1750163378240, "results": "92", "hashOfConfig": "51"}, {"size": 8789, "mtime": 1750163415999, "results": "93", "hashOfConfig": "51"}, {"size": 14566, "mtime": 1750163463903, "results": "94", "hashOfConfig": "51"}, {"size": 13625, "mtime": 1750163508364, "results": "95", "hashOfConfig": "51"}, {"size": 15378, "mtime": 1750163560124, "results": "96", "hashOfConfig": "51"}, {"size": 11070, "mtime": 1750163603317, "results": "97", "hashOfConfig": "51"}, {"size": 9819, "mtime": 1750163644888, "results": "98", "hashOfConfig": "51"}, {"size": 10866, "mtime": 1750163692199, "results": "99", "hashOfConfig": "51"}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "7sy7mo", {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\CompanyManagement.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\companiesAPI.js", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\button.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\card.jsx", ["247"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\table.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\dialog.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\input.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\label.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\lib\\utils.js", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Auth\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\LoadingSpinner.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Login.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Layout\\Layout.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Layout\\Sidebar.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Layout\\Header.jsx", ["248"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Dashboard.jsx", ["249", "250"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\StatsCard.jsx", ["251"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\RecentActivities.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\CompanyChart.jsx", ["252", "253"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\QuickActions.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Companies.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Reports.jsx", ["254", "255", "256", "257"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Reports\\ReportsChart.jsx", ["258", "259"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Reports\\ReportFilters.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Reports\\ExportOptions.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Settings.jsx", ["260"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Purchases.jsx", ["261"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Sales.jsx", ["262", "263", "264"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\purchasesAPI.js", ["265"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\customersAPI.js", ["266"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\salesAPI.js", ["267"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\inventoryAPI.js", ["268"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Inventory.jsx", ["269"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Customers.jsx", ["270"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Expenses.jsx", ["271"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\expensesAPI.js", ["272"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Sales\\SalesForm.jsx", ["273"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Sales\\SalesInvoice.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Purchases\\PurchaseForm.jsx", ["274"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Purchases\\PurchaseInvoice.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Inventory\\ProductForm.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Customers\\CustomerForm.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Expenses\\ExpenseForm.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Inventory\\ProductDetails.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Customers\\CustomerDetails.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Expenses\\ExpenseDetails.jsx", [], [], {"ruleId": "275", "severity": 1, "message": "276", "line": 26, "column": 3, "nodeType": "277", "endLine": 33, "endColumn": 5}, {"ruleId": "278", "severity": 1, "message": "279", "line": 11, "column": 10, "nodeType": "280", "messageId": "281", "endLine": 11, "endColumn": 16}, {"ruleId": "278", "severity": 1, "message": "282", "line": 8, "column": 3, "nodeType": "280", "messageId": "281", "endLine": 8, "endColumn": 22}, {"ruleId": "278", "severity": 1, "message": "283", "line": 9, "column": 3, "nodeType": "280", "messageId": "281", "endLine": 9, "endColumn": 24}, {"ruleId": "278", "severity": 1, "message": "284", "line": 2, "column": 10, "nodeType": "280", "messageId": "281", "endLine": 2, "endColumn": 16}, {"ruleId": "278", "severity": 1, "message": "285", "line": 3, "column": 3, "nodeType": "280", "messageId": "281", "endLine": 3, "endColumn": 12}, {"ruleId": "278", "severity": 1, "message": "286", "line": 4, "column": 3, "nodeType": "280", "messageId": "281", "endLine": 4, "endColumn": 7}, {"ruleId": "278", "severity": 1, "message": "287", "line": 6, "column": 3, "nodeType": "280", "messageId": "281", "endLine": 6, "endColumn": 15}, {"ruleId": "278", "severity": 1, "message": "288", "line": 7, "column": 3, "nodeType": "280", "messageId": "281", "endLine": 7, "endColumn": 13}, {"ruleId": "278", "severity": 1, "message": "289", "line": 11, "column": 10, "nodeType": "280", "messageId": "281", "endLine": 11, "endColumn": 15}, {"ruleId": "278", "severity": 1, "message": "290", "line": 12, "column": 10, "nodeType": "280", "messageId": "281", "endLine": 12, "endColumn": 15}, {"ruleId": "278", "severity": 1, "message": "291", "line": 13, "column": 3, "nodeType": "280", "messageId": "281", "endLine": 13, "endColumn": 11}, {"ruleId": "278", "severity": 1, "message": "292", "line": 14, "column": 3, "nodeType": "280", "messageId": "281", "endLine": 14, "endColumn": 6}, {"ruleId": "278", "severity": 1, "message": "293", "line": 7, "column": 3, "nodeType": "280", "messageId": "281", "endLine": 7, "endColumn": 15}, {"ruleId": "294", "severity": 1, "message": "295", "line": 37, "column": 6, "nodeType": "296", "endLine": 37, "endColumn": 29, "suggestions": "297"}, {"ruleId": "278", "severity": 1, "message": "298", "line": 16, "column": 8, "nodeType": "280", "messageId": "281", "endLine": 16, "endColumn": 20}, {"ruleId": "278", "severity": 1, "message": "299", "line": 17, "column": 8, "nodeType": "280", "messageId": "281", "endLine": 17, "endColumn": 20}, {"ruleId": "294", "severity": 1, "message": "300", "line": 39, "column": 6, "nodeType": "296", "endLine": 39, "endColumn": 25, "suggestions": "301"}, {"ruleId": "302", "severity": 1, "message": "303", "line": 140, "column": 1, "nodeType": "304", "endLine": 140, "endColumn": 35}, {"ruleId": "302", "severity": 1, "message": "303", "line": 199, "column": 1, "nodeType": "304", "endLine": 199, "endColumn": 35}, {"ruleId": "302", "severity": 1, "message": "303", "line": 138, "column": 1, "nodeType": "304", "endLine": 138, "endColumn": 31}, {"ruleId": "302", "severity": 1, "message": "303", "line": 223, "column": 1, "nodeType": "304", "endLine": 223, "endColumn": 35}, {"ruleId": "294", "severity": 1, "message": "305", "line": 40, "column": 6, "nodeType": "296", "endLine": 40, "endColumn": 28, "suggestions": "306"}, {"ruleId": "294", "severity": 1, "message": "307", "line": 38, "column": 6, "nodeType": "296", "endLine": 38, "endColumn": 29, "suggestions": "308"}, {"ruleId": "294", "severity": 1, "message": "309", "line": 38, "column": 6, "nodeType": "296", "endLine": 38, "endColumn": 28, "suggestions": "310"}, {"ruleId": "302", "severity": 1, "message": "303", "line": 259, "column": 1, "nodeType": "304", "endLine": 259, "endColumn": 34}, {"ruleId": "294", "severity": 1, "message": "311", "line": 49, "column": 6, "nodeType": "296", "endLine": 49, "endColumn": 41, "suggestions": "312"}, {"ruleId": "294", "severity": 1, "message": "311", "line": 46, "column": 6, "nodeType": "296", "endLine": 46, "endColumn": 41, "suggestions": "313"}, "jsx-a11y/heading-has-content", "Headings must have content and the content must be accessible by a screen reader.", "JSXOpeningElement", "no-unused-vars", "'Button' is defined but never used.", "Identifier", "unusedVar", "'ArrowTrendingUpIcon' is defined but never used.", "'ArrowTrendingDownIcon' is defined but never used.", "'motion' is defined but never used.", "'LineChart' is defined but never used.", "'Line' is defined but never used.", "'CalendarIcon' is defined but never used.", "'FunnelIcon' is defined but never used.", "'Input' is defined but never used.", "'Label' is defined but never used.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'GlobeAltIcon' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'filterPurchases'. Either include it or remove the dependency array.", "ArrayExpression", ["314"], "'customersAPI' is defined but never used.", "'inventoryAPI' is defined but never used.", "React Hook useEffect has a missing dependency: 'filterSales'. Either include it or remove the dependency array.", ["315"], "import/no-anonymous-default-export", "Assign instance to a variable before exporting as module default", "ExportDefaultDeclaration", "React Hook useEffect has a missing dependency: 'filterProducts'. Either include it or remove the dependency array.", ["316"], "React Hook useEffect has a missing dependency: 'filterCustomers'. Either include it or remove the dependency array.", ["317"], "React Hook useEffect has a missing dependency: 'filterExpenses'. Either include it or remove the dependency array.", ["318"], "React Hook useEffect has a missing dependency: 'calculateTotals'. Either include it or remove the dependency array.", ["319"], ["320"], {"desc": "321", "fix": "322"}, {"desc": "323", "fix": "324"}, {"desc": "325", "fix": "326"}, {"desc": "327", "fix": "328"}, {"desc": "329", "fix": "330"}, {"desc": "331", "fix": "332"}, {"desc": "331", "fix": "333"}, "Update the dependencies array to be: [filterPurchases, purchases, searchTerm]", {"range": "334", "text": "335"}, "Update the dependencies array to be: [filterSales, sales, searchTerm]", {"range": "336", "text": "337"}, "Update the dependencies array to be: [filterProducts, products, searchTerm]", {"range": "338", "text": "339"}, "Update the dependencies array to be: [customers, filterCustomers, searchTerm]", {"range": "340", "text": "341"}, "Update the dependencies array to be: [expenses, filterExpenses, searchTerm]", {"range": "342", "text": "343"}, "Update the dependencies array to be: [formData.items, formData.discount, calculateTotals]", {"range": "344", "text": "345"}, {"range": "346", "text": "345"}, [1318, 1341], "[filterPurchases, purchases, searchTerm]", [1355, 1374], "[filterSales, sales, searchTerm]", [1417, 1439], "[filterProducts, products, searchTerm]", [1333, 1356], "[customers, filterCustomers, searchTerm]", [1316, 1338], "[expenses, filterExpenses, searchTerm]", [1515, 1550], "[formData.items, formData.discount, calculateTotals]", [1453, 1488]]