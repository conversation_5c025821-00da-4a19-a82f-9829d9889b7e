[{"C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\CompanyManagement.jsx": "3", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\companiesAPI.js": "4", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\button.jsx": "5", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\card.jsx": "6", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\table.jsx": "7", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\dialog.jsx": "8", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\input.jsx": "9", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\label.jsx": "10", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\lib\\utils.js": "11", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\contexts\\AuthContext.js": "12", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Auth\\ProtectedRoute.js": "13", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\LoadingSpinner.jsx": "14", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Login.jsx": "15", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Layout\\Layout.jsx": "16", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Layout\\Sidebar.jsx": "17", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Layout\\Header.jsx": "18", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Dashboard.jsx": "19", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\StatsCard.jsx": "20", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\RecentActivities.jsx": "21", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\CompanyChart.jsx": "22", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\QuickActions.jsx": "23", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Companies.jsx": "24", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Reports.jsx": "25", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Reports\\ReportsChart.jsx": "26", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Reports\\ReportFilters.jsx": "27", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Reports\\ExportOptions.jsx": "28", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Settings.jsx": "29", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Purchases.jsx": "30", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Sales.jsx": "31", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\purchasesAPI.js": "32", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\customersAPI.js": "33", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\salesAPI.js": "34", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\inventoryAPI.js": "35", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Inventory.jsx": "36", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Customers.jsx": "37", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Expenses.jsx": "38", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\expensesAPI.js": "39", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Sales\\SalesForm.jsx": "40", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Sales\\SalesInvoice.jsx": "41", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Purchases\\PurchaseForm.jsx": "42", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Purchases\\PurchaseInvoice.jsx": "43", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Inventory\\ProductForm.jsx": "44", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Customers\\CustomerForm.jsx": "45", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Expenses\\ExpenseForm.jsx": "46", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Inventory\\ProductDetails.jsx": "47", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Customers\\CustomerDetails.jsx": "48", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Expenses\\ExpenseDetails.jsx": "49", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\QRCode\\InvoiceQRCode.jsx": "50", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\utils\\qrCodeGenerator.js": "51", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Settings\\EInvoiceSettings.jsx": "52", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\QRCodeTest.jsx": "53", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\utils\\qrCodeTester.js": "54"}, {"size": 263, "mtime": 1750149838411, "results": "55", "hashOfConfig": "56"}, {"size": 3097, "mtime": 1750165154783, "results": "57", "hashOfConfig": "56"}, {"size": 19179, "mtime": 1750160550738, "results": "58", "hashOfConfig": "56"}, {"size": 2696, "mtime": 1750149893012, "results": "59", "hashOfConfig": "56"}, {"size": 1359, "mtime": 1750160192372, "results": "60", "hashOfConfig": "56"}, {"size": 1508, "mtime": 1750160215710, "results": "61", "hashOfConfig": "56"}, {"size": 2188, "mtime": 1750160228722, "results": "62", "hashOfConfig": "56"}, {"size": 3008, "mtime": 1750160588665, "results": "63", "hashOfConfig": "56"}, {"size": 685, "mtime": 1750160199947, "results": "64", "hashOfConfig": "56"}, {"size": 371, "mtime": 1750160205839, "results": "65", "hashOfConfig": "56"}, {"size": 225, "mtime": 1750160250760, "results": "66", "hashOfConfig": "56"}, {"size": 1905, "mtime": 1750161144315, "results": "67", "hashOfConfig": "56"}, {"size": 640, "mtime": 1750161153757, "results": "68", "hashOfConfig": "56"}, {"size": 889, "mtime": 1750161164023, "results": "69", "hashOfConfig": "56"}, {"size": 6371, "mtime": 1750161191711, "results": "70", "hashOfConfig": "56"}, {"size": 1716, "mtime": 1750161204463, "results": "71", "hashOfConfig": "56"}, {"size": 4127, "mtime": 1750163074848, "results": "72", "hashOfConfig": "56"}, {"size": 7098, "mtime": 1750161255384, "results": "73", "hashOfConfig": "56"}, {"size": 3791, "mtime": 1750161576871, "results": "74", "hashOfConfig": "56"}, {"size": 1649, "mtime": 1750161563760, "results": "75", "hashOfConfig": "56"}, {"size": 3423, "mtime": 1750161315525, "results": "76", "hashOfConfig": "56"}, {"size": 2877, "mtime": 1750161332248, "results": "77", "hashOfConfig": "56"}, {"size": 2654, "mtime": 1750161348353, "results": "78", "hashOfConfig": "56"}, {"size": 385, "mtime": 1750161356392, "results": "79", "hashOfConfig": "56"}, {"size": 8679, "mtime": 1750161394588, "results": "80", "hashOfConfig": "56"}, {"size": 3992, "mtime": 1750161417721, "results": "81", "hashOfConfig": "56"}, {"size": 6035, "mtime": 1750161443223, "results": "82", "hashOfConfig": "56"}, {"size": 7977, "mtime": 1750161474940, "results": "83", "hashOfConfig": "56"}, {"size": 16091, "mtime": 1750165057799, "results": "84", "hashOfConfig": "56"}, {"size": 11548, "mtime": 1750163028092, "results": "85", "hashOfConfig": "56"}, {"size": 11112, "mtime": 1750162984504, "results": "86", "hashOfConfig": "56"}, {"size": 4613, "mtime": 1750162839467, "results": "87", "hashOfConfig": "56"}, {"size": 6415, "mtime": 1750162903194, "results": "88", "hashOfConfig": "56"}, {"size": 4177, "mtime": 1750162815740, "results": "89", "hashOfConfig": "56"}, {"size": 7144, "mtime": 1750162872375, "results": "90", "hashOfConfig": "56"}, {"size": 13389, "mtime": 1750163130963, "results": "91", "hashOfConfig": "56"}, {"size": 12146, "mtime": 1750163173798, "results": "92", "hashOfConfig": "56"}, {"size": 12367, "mtime": 1750163221644, "results": "93", "hashOfConfig": "56"}, {"size": 8399, "mtime": 1750162941810, "results": "94", "hashOfConfig": "56"}, {"size": 15337, "mtime": 1750163276893, "results": "95", "hashOfConfig": "56"}, {"size": 9309, "mtime": 1750164885488, "results": "96", "hashOfConfig": "56"}, {"size": 14994, "mtime": 1750163378240, "results": "97", "hashOfConfig": "56"}, {"size": 8789, "mtime": 1750163415999, "results": "98", "hashOfConfig": "56"}, {"size": 14566, "mtime": 1750163463903, "results": "99", "hashOfConfig": "56"}, {"size": 13625, "mtime": 1750163508364, "results": "100", "hashOfConfig": "56"}, {"size": 15378, "mtime": 1750163560124, "results": "101", "hashOfConfig": "56"}, {"size": 11070, "mtime": 1750163603317, "results": "102", "hashOfConfig": "56"}, {"size": 9819, "mtime": 1750163644888, "results": "103", "hashOfConfig": "56"}, {"size": 10866, "mtime": 1750163692199, "results": "104", "hashOfConfig": "56"}, {"size": 10131, "mtime": 1750165614940, "results": "105", "hashOfConfig": "56"}, {"size": 7233, "mtime": 1750165521776, "results": "106", "hashOfConfig": "56"}, {"size": 17501, "mtime": 1750164953748, "results": "107", "hashOfConfig": "56"}, {"size": 23864, "mtime": 1750165848689, "results": "108", "hashOfConfig": "56"}, {"size": 9155, "mtime": 1750165744222, "results": "109", "hashOfConfig": "56"}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "7sy7mo", {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\CompanyManagement.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\companiesAPI.js", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\button.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\card.jsx", ["272"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\table.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\dialog.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\input.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\label.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\lib\\utils.js", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Auth\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\LoadingSpinner.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Login.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Layout\\Layout.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Layout\\Sidebar.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Layout\\Header.jsx", ["273"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Dashboard.jsx", ["274", "275"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\StatsCard.jsx", ["276"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\RecentActivities.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\CompanyChart.jsx", ["277", "278"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\QuickActions.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Companies.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Reports.jsx", ["279", "280", "281", "282"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Reports\\ReportsChart.jsx", ["283", "284"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Reports\\ReportFilters.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Reports\\ExportOptions.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Settings.jsx", ["285"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Purchases.jsx", ["286"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Sales.jsx", ["287", "288", "289"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\purchasesAPI.js", ["290"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\customersAPI.js", ["291"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\salesAPI.js", ["292"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\inventoryAPI.js", ["293"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Inventory.jsx", ["294"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Customers.jsx", ["295"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Expenses.jsx", ["296"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\expensesAPI.js", ["297"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Sales\\SalesForm.jsx", ["298"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Sales\\SalesInvoice.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Purchases\\PurchaseForm.jsx", ["299"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Purchases\\PurchaseInvoice.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Inventory\\ProductForm.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Customers\\CustomerForm.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Expenses\\ExpenseForm.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Inventory\\ProductDetails.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Customers\\CustomerDetails.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Expenses\\ExpenseDetails.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\QRCode\\InvoiceQRCode.jsx", ["300"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\utils\\qrCodeGenerator.js", ["301", "302"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Settings\\EInvoiceSettings.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\QRCodeTest.jsx", ["303"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\utils\\qrCodeTester.js", [], [], {"ruleId": "304", "severity": 1, "message": "305", "line": 26, "column": 3, "nodeType": "306", "endLine": 33, "endColumn": 5}, {"ruleId": "307", "severity": 1, "message": "308", "line": 11, "column": 10, "nodeType": "309", "messageId": "310", "endLine": 11, "endColumn": 16}, {"ruleId": "307", "severity": 1, "message": "311", "line": 8, "column": 3, "nodeType": "309", "messageId": "310", "endLine": 8, "endColumn": 22}, {"ruleId": "307", "severity": 1, "message": "312", "line": 9, "column": 3, "nodeType": "309", "messageId": "310", "endLine": 9, "endColumn": 24}, {"ruleId": "307", "severity": 1, "message": "313", "line": 2, "column": 10, "nodeType": "309", "messageId": "310", "endLine": 2, "endColumn": 16}, {"ruleId": "307", "severity": 1, "message": "314", "line": 3, "column": 3, "nodeType": "309", "messageId": "310", "endLine": 3, "endColumn": 12}, {"ruleId": "307", "severity": 1, "message": "315", "line": 4, "column": 3, "nodeType": "309", "messageId": "310", "endLine": 4, "endColumn": 7}, {"ruleId": "307", "severity": 1, "message": "316", "line": 6, "column": 3, "nodeType": "309", "messageId": "310", "endLine": 6, "endColumn": 15}, {"ruleId": "307", "severity": 1, "message": "317", "line": 7, "column": 3, "nodeType": "309", "messageId": "310", "endLine": 7, "endColumn": 13}, {"ruleId": "307", "severity": 1, "message": "318", "line": 11, "column": 10, "nodeType": "309", "messageId": "310", "endLine": 11, "endColumn": 15}, {"ruleId": "307", "severity": 1, "message": "319", "line": 12, "column": 10, "nodeType": "309", "messageId": "310", "endLine": 12, "endColumn": 15}, {"ruleId": "307", "severity": 1, "message": "320", "line": 13, "column": 3, "nodeType": "309", "messageId": "310", "endLine": 13, "endColumn": 11}, {"ruleId": "307", "severity": 1, "message": "321", "line": 14, "column": 3, "nodeType": "309", "messageId": "310", "endLine": 14, "endColumn": 6}, {"ruleId": "307", "severity": 1, "message": "322", "line": 7, "column": 3, "nodeType": "309", "messageId": "310", "endLine": 7, "endColumn": 15}, {"ruleId": "323", "severity": 1, "message": "324", "line": 37, "column": 6, "nodeType": "325", "endLine": 37, "endColumn": 29, "suggestions": "326"}, {"ruleId": "307", "severity": 1, "message": "327", "line": 16, "column": 8, "nodeType": "309", "messageId": "310", "endLine": 16, "endColumn": 20}, {"ruleId": "307", "severity": 1, "message": "328", "line": 17, "column": 8, "nodeType": "309", "messageId": "310", "endLine": 17, "endColumn": 20}, {"ruleId": "323", "severity": 1, "message": "329", "line": 39, "column": 6, "nodeType": "325", "endLine": 39, "endColumn": 25, "suggestions": "330"}, {"ruleId": "331", "severity": 1, "message": "332", "line": 140, "column": 1, "nodeType": "333", "endLine": 140, "endColumn": 35}, {"ruleId": "331", "severity": 1, "message": "332", "line": 199, "column": 1, "nodeType": "333", "endLine": 199, "endColumn": 35}, {"ruleId": "331", "severity": 1, "message": "332", "line": 138, "column": 1, "nodeType": "333", "endLine": 138, "endColumn": 31}, {"ruleId": "331", "severity": 1, "message": "332", "line": 223, "column": 1, "nodeType": "333", "endLine": 223, "endColumn": 35}, {"ruleId": "323", "severity": 1, "message": "334", "line": 40, "column": 6, "nodeType": "325", "endLine": 40, "endColumn": 28, "suggestions": "335"}, {"ruleId": "323", "severity": 1, "message": "336", "line": 38, "column": 6, "nodeType": "325", "endLine": 38, "endColumn": 29, "suggestions": "337"}, {"ruleId": "323", "severity": 1, "message": "338", "line": 38, "column": 6, "nodeType": "325", "endLine": 38, "endColumn": 28, "suggestions": "339"}, {"ruleId": "331", "severity": 1, "message": "332", "line": 259, "column": 1, "nodeType": "333", "endLine": 259, "endColumn": 34}, {"ruleId": "323", "severity": 1, "message": "340", "line": 49, "column": 6, "nodeType": "325", "endLine": 49, "endColumn": 41, "suggestions": "341"}, {"ruleId": "323", "severity": 1, "message": "340", "line": 46, "column": 6, "nodeType": "325", "endLine": 46, "endColumn": 41, "suggestions": "342"}, {"ruleId": "307", "severity": 1, "message": "343", "line": 8, "column": 3, "nodeType": "309", "messageId": "310", "endLine": 8, "endColumn": 19}, {"ruleId": "307", "severity": 1, "message": "344", "line": 9, "column": 7, "nodeType": "309", "messageId": "310", "endLine": 9, "endColumn": 21}, {"ruleId": "345", "severity": 1, "message": "346", "line": 168, "column": 7, "nodeType": "347", "messageId": "348", "endLine": 184, "endColumn": 8}, {"ruleId": "307", "severity": 1, "message": "343", "line": 12, "column": 3, "nodeType": "309", "messageId": "310", "endLine": 12, "endColumn": 19}, "jsx-a11y/heading-has-content", "Headings must have content and the content must be accessible by a screen reader.", "JSXOpeningElement", "no-unused-vars", "'Button' is defined but never used.", "Identifier", "unusedVar", "'ArrowTrendingUpIcon' is defined but never used.", "'ArrowTrendingDownIcon' is defined but never used.", "'motion' is defined but never used.", "'LineChart' is defined but never used.", "'Line' is defined but never used.", "'CalendarIcon' is defined but never used.", "'FunnelIcon' is defined but never used.", "'Input' is defined but never used.", "'Label' is defined but never used.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'GlobeAltIcon' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'filterPurchases'. Either include it or remove the dependency array.", "ArrayExpression", ["349"], "'customersAPI' is defined but never used.", "'inventoryAPI' is defined but never used.", "React Hook useEffect has a missing dependency: 'filterSales'. Either include it or remove the dependency array.", ["350"], "import/no-anonymous-default-export", "Assign instance to a variable before exporting as module default", "ExportDefaultDeclaration", "React Hook useEffect has a missing dependency: 'filterProducts'. Either include it or remove the dependency array.", ["351"], "React Hook useEffect has a missing dependency: 'filterCustomers'. Either include it or remove the dependency array.", ["352"], "React Hook useEffect has a missing dependency: 'filterExpenses'. Either include it or remove the dependency array.", ["353"], "React Hook useEffect has a missing dependency: 'calculateTotals'. Either include it or remove the dependency array.", ["354"], ["355"], "'decodeQRCodeData' is defined but never used.", "'encodeToBase64' is assigned a value but never used.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", {"desc": "356", "fix": "357"}, {"desc": "358", "fix": "359"}, {"desc": "360", "fix": "361"}, {"desc": "362", "fix": "363"}, {"desc": "364", "fix": "365"}, {"desc": "366", "fix": "367"}, {"desc": "366", "fix": "368"}, "Update the dependencies array to be: [filterPurchases, purchases, searchTerm]", {"range": "369", "text": "370"}, "Update the dependencies array to be: [filterSales, sales, searchTerm]", {"range": "371", "text": "372"}, "Update the dependencies array to be: [filterProducts, products, searchTerm]", {"range": "373", "text": "374"}, "Update the dependencies array to be: [customers, filterCustomers, searchTerm]", {"range": "375", "text": "376"}, "Update the dependencies array to be: [expenses, filterExpenses, searchTerm]", {"range": "377", "text": "378"}, "Update the dependencies array to be: [formData.items, formData.discount, calculateTotals]", {"range": "379", "text": "380"}, {"range": "381", "text": "380"}, [1318, 1341], "[filterPurchases, purchases, searchTerm]", [1355, 1374], "[filterSales, sales, searchTerm]", [1417, 1439], "[filterProducts, products, searchTerm]", [1333, 1356], "[customers, filterCustomers, searchTerm]", [1316, 1338], "[expenses, filterExpenses, searchTerm]", [1515, 1550], "[formData.items, formData.discount, calculateTotals]", [1453, 1488]]