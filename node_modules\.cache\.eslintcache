[{"C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\CompanyManagement.jsx": "3", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\companiesAPI.js": "4", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\button.jsx": "5", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\card.jsx": "6", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\table.jsx": "7", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\dialog.jsx": "8", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\input.jsx": "9", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\label.jsx": "10", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\lib\\utils.js": "11", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\contexts\\AuthContext.js": "12", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Auth\\ProtectedRoute.js": "13", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\LoadingSpinner.jsx": "14", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Login.jsx": "15", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Layout\\Layout.jsx": "16", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Layout\\Sidebar.jsx": "17", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Layout\\Header.jsx": "18", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Dashboard.jsx": "19", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\StatsCard.jsx": "20", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\RecentActivities.jsx": "21", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\CompanyChart.jsx": "22", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\QuickActions.jsx": "23", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Companies.jsx": "24", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Reports.jsx": "25", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Reports\\ReportsChart.jsx": "26", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Reports\\ReportFilters.jsx": "27", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Reports\\ExportOptions.jsx": "28", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Settings.jsx": "29", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Purchases.jsx": "30", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Sales.jsx": "31", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\purchasesAPI.js": "32", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\customersAPI.js": "33", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\salesAPI.js": "34", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\inventoryAPI.js": "35", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Inventory.jsx": "36", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Customers.jsx": "37", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Expenses.jsx": "38", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\expensesAPI.js": "39", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Sales\\SalesForm.jsx": "40", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Sales\\SalesInvoice.jsx": "41", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Purchases\\PurchaseForm.jsx": "42", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Purchases\\PurchaseInvoice.jsx": "43", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Inventory\\ProductForm.jsx": "44", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Customers\\CustomerForm.jsx": "45", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Expenses\\ExpenseForm.jsx": "46", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Inventory\\ProductDetails.jsx": "47", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Customers\\CustomerDetails.jsx": "48", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Expenses\\ExpenseDetails.jsx": "49", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\QRCode\\InvoiceQRCode.jsx": "50", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\utils\\qrCodeGenerator.js": "51", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Settings\\EInvoiceSettings.jsx": "52", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\QRCodeTest.jsx": "53", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\utils\\qrCodeTester.js": "54", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Setup\\InitialSetup.jsx": "55", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\config\\companyConfig.js": "56", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Settings\\CompanySettings.jsx": "57"}, {"size": 263, "mtime": 1750149838411, "results": "58", "hashOfConfig": "59"}, {"size": 4249, "mtime": 1750172148371, "results": "60", "hashOfConfig": "59"}, {"size": 19179, "mtime": 1750160550738, "results": "61", "hashOfConfig": "59"}, {"size": 2696, "mtime": 1750149893012, "results": "62", "hashOfConfig": "59"}, {"size": 1359, "mtime": 1750160192372, "results": "63", "hashOfConfig": "59"}, {"size": 1508, "mtime": 1750160215710, "results": "64", "hashOfConfig": "59"}, {"size": 2188, "mtime": 1750160228722, "results": "65", "hashOfConfig": "59"}, {"size": 3008, "mtime": 1750160588665, "results": "66", "hashOfConfig": "59"}, {"size": 685, "mtime": 1750160199947, "results": "67", "hashOfConfig": "59"}, {"size": 371, "mtime": 1750160205839, "results": "68", "hashOfConfig": "59"}, {"size": 225, "mtime": 1750160250760, "results": "69", "hashOfConfig": "59"}, {"size": 1905, "mtime": 1750161144315, "results": "70", "hashOfConfig": "59"}, {"size": 640, "mtime": 1750161153757, "results": "71", "hashOfConfig": "59"}, {"size": 889, "mtime": 1750161164023, "results": "72", "hashOfConfig": "59"}, {"size": 6371, "mtime": 1750161191711, "results": "73", "hashOfConfig": "59"}, {"size": 1716, "mtime": 1750161204463, "results": "74", "hashOfConfig": "59"}, {"size": 4127, "mtime": 1750163074848, "results": "75", "hashOfConfig": "59"}, {"size": 7098, "mtime": 1750161255384, "results": "76", "hashOfConfig": "59"}, {"size": 3791, "mtime": 1750161576871, "results": "77", "hashOfConfig": "59"}, {"size": 1649, "mtime": 1750161563760, "results": "78", "hashOfConfig": "59"}, {"size": 3423, "mtime": 1750161315525, "results": "79", "hashOfConfig": "59"}, {"size": 2877, "mtime": 1750161332248, "results": "80", "hashOfConfig": "59"}, {"size": 2654, "mtime": 1750161348353, "results": "81", "hashOfConfig": "59"}, {"size": 385, "mtime": 1750161356392, "results": "82", "hashOfConfig": "59"}, {"size": 8679, "mtime": 1750161394588, "results": "83", "hashOfConfig": "59"}, {"size": 3992, "mtime": 1750161417721, "results": "84", "hashOfConfig": "59"}, {"size": 6035, "mtime": 1750161443223, "results": "85", "hashOfConfig": "59"}, {"size": 7977, "mtime": 1750161474940, "results": "86", "hashOfConfig": "59"}, {"size": 16323, "mtime": 1750172315669, "results": "87", "hashOfConfig": "59"}, {"size": 11548, "mtime": 1750163028092, "results": "88", "hashOfConfig": "59"}, {"size": 11112, "mtime": 1750162984504, "results": "89", "hashOfConfig": "59"}, {"size": 4613, "mtime": 1750162839467, "results": "90", "hashOfConfig": "59"}, {"size": 6415, "mtime": 1750162903194, "results": "91", "hashOfConfig": "59"}, {"size": 4177, "mtime": 1750162815740, "results": "92", "hashOfConfig": "59"}, {"size": 7144, "mtime": 1750162872375, "results": "93", "hashOfConfig": "59"}, {"size": 13389, "mtime": 1750163130963, "results": "94", "hashOfConfig": "59"}, {"size": 12146, "mtime": 1750163173798, "results": "95", "hashOfConfig": "59"}, {"size": 12367, "mtime": 1750163221644, "results": "96", "hashOfConfig": "59"}, {"size": 8399, "mtime": 1750162941810, "results": "97", "hashOfConfig": "59"}, {"size": 15337, "mtime": 1750163276893, "results": "98", "hashOfConfig": "59"}, {"size": 9309, "mtime": 1750164885488, "results": "99", "hashOfConfig": "59"}, {"size": 14994, "mtime": 1750163378240, "results": "100", "hashOfConfig": "59"}, {"size": 8789, "mtime": 1750163415999, "results": "101", "hashOfConfig": "59"}, {"size": 14566, "mtime": 1750163463903, "results": "102", "hashOfConfig": "59"}, {"size": 13625, "mtime": 1750163508364, "results": "103", "hashOfConfig": "59"}, {"size": 15378, "mtime": 1750163560124, "results": "104", "hashOfConfig": "59"}, {"size": 11070, "mtime": 1750163603317, "results": "105", "hashOfConfig": "59"}, {"size": 9819, "mtime": 1750163644888, "results": "106", "hashOfConfig": "59"}, {"size": 10866, "mtime": 1750163692199, "results": "107", "hashOfConfig": "59"}, {"size": 10131, "mtime": 1750165614940, "results": "108", "hashOfConfig": "59"}, {"size": 7939, "mtime": 1750172177087, "results": "109", "hashOfConfig": "59"}, {"size": 17501, "mtime": 1750164953748, "results": "110", "hashOfConfig": "59"}, {"size": 23864, "mtime": 1750165848689, "results": "111", "hashOfConfig": "59"}, {"size": 9155, "mtime": 1750165744222, "results": "112", "hashOfConfig": "59"}, {"size": 19986, "mtime": 1750172084071, "results": "113", "hashOfConfig": "59"}, {"size": 5119, "mtime": 1750172008105, "results": "114", "hashOfConfig": "59"}, {"size": 20083, "mtime": 1750172254057, "results": "115", "hashOfConfig": "59"}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "7sy7mo", {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\CompanyManagement.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\companiesAPI.js", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\button.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\card.jsx", ["287"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\table.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\dialog.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\input.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\label.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\lib\\utils.js", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Auth\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\LoadingSpinner.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Login.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Layout\\Layout.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Layout\\Sidebar.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Layout\\Header.jsx", ["288"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Dashboard.jsx", ["289", "290"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\StatsCard.jsx", ["291"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\RecentActivities.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\CompanyChart.jsx", ["292", "293"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\QuickActions.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Companies.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Reports.jsx", ["294", "295", "296", "297"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Reports\\ReportsChart.jsx", ["298", "299"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Reports\\ReportFilters.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Reports\\ExportOptions.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Settings.jsx", ["300"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Purchases.jsx", ["301"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Sales.jsx", ["302", "303", "304"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\purchasesAPI.js", ["305"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\customersAPI.js", ["306"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\salesAPI.js", ["307"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\inventoryAPI.js", ["308"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Inventory.jsx", ["309"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Customers.jsx", ["310"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Expenses.jsx", ["311"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\expensesAPI.js", ["312"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Sales\\SalesForm.jsx", ["313"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Sales\\SalesInvoice.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Purchases\\PurchaseForm.jsx", ["314"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Purchases\\PurchaseInvoice.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Inventory\\ProductForm.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Customers\\CustomerForm.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Expenses\\ExpenseForm.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Inventory\\ProductDetails.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Customers\\CustomerDetails.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Expenses\\ExpenseDetails.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\QRCode\\InvoiceQRCode.jsx", ["315"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\utils\\qrCodeGenerator.js", ["316", "317"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Settings\\EInvoiceSettings.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\QRCodeTest.jsx", ["318"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\utils\\qrCodeTester.js", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Setup\\InitialSetup.jsx", ["319"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\config\\companyConfig.js", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Settings\\CompanySettings.jsx", ["320", "321", "322", "323"], [], {"ruleId": "324", "severity": 1, "message": "325", "line": 26, "column": 3, "nodeType": "326", "endLine": 33, "endColumn": 5}, {"ruleId": "327", "severity": 1, "message": "328", "line": 11, "column": 10, "nodeType": "329", "messageId": "330", "endLine": 11, "endColumn": 16}, {"ruleId": "327", "severity": 1, "message": "331", "line": 8, "column": 3, "nodeType": "329", "messageId": "330", "endLine": 8, "endColumn": 22}, {"ruleId": "327", "severity": 1, "message": "332", "line": 9, "column": 3, "nodeType": "329", "messageId": "330", "endLine": 9, "endColumn": 24}, {"ruleId": "327", "severity": 1, "message": "333", "line": 2, "column": 10, "nodeType": "329", "messageId": "330", "endLine": 2, "endColumn": 16}, {"ruleId": "327", "severity": 1, "message": "334", "line": 3, "column": 3, "nodeType": "329", "messageId": "330", "endLine": 3, "endColumn": 12}, {"ruleId": "327", "severity": 1, "message": "335", "line": 4, "column": 3, "nodeType": "329", "messageId": "330", "endLine": 4, "endColumn": 7}, {"ruleId": "327", "severity": 1, "message": "336", "line": 6, "column": 3, "nodeType": "329", "messageId": "330", "endLine": 6, "endColumn": 15}, {"ruleId": "327", "severity": 1, "message": "337", "line": 7, "column": 3, "nodeType": "329", "messageId": "330", "endLine": 7, "endColumn": 13}, {"ruleId": "327", "severity": 1, "message": "338", "line": 11, "column": 10, "nodeType": "329", "messageId": "330", "endLine": 11, "endColumn": 15}, {"ruleId": "327", "severity": 1, "message": "339", "line": 12, "column": 10, "nodeType": "329", "messageId": "330", "endLine": 12, "endColumn": 15}, {"ruleId": "327", "severity": 1, "message": "340", "line": 13, "column": 3, "nodeType": "329", "messageId": "330", "endLine": 13, "endColumn": 11}, {"ruleId": "327", "severity": 1, "message": "341", "line": 14, "column": 3, "nodeType": "329", "messageId": "330", "endLine": 14, "endColumn": 6}, {"ruleId": "327", "severity": 1, "message": "342", "line": 7, "column": 3, "nodeType": "329", "messageId": "330", "endLine": 7, "endColumn": 15}, {"ruleId": "343", "severity": 1, "message": "344", "line": 37, "column": 6, "nodeType": "345", "endLine": 37, "endColumn": 29, "suggestions": "346"}, {"ruleId": "327", "severity": 1, "message": "347", "line": 16, "column": 8, "nodeType": "329", "messageId": "330", "endLine": 16, "endColumn": 20}, {"ruleId": "327", "severity": 1, "message": "348", "line": 17, "column": 8, "nodeType": "329", "messageId": "330", "endLine": 17, "endColumn": 20}, {"ruleId": "343", "severity": 1, "message": "349", "line": 39, "column": 6, "nodeType": "345", "endLine": 39, "endColumn": 25, "suggestions": "350"}, {"ruleId": "351", "severity": 1, "message": "352", "line": 140, "column": 1, "nodeType": "353", "endLine": 140, "endColumn": 35}, {"ruleId": "351", "severity": 1, "message": "352", "line": 199, "column": 1, "nodeType": "353", "endLine": 199, "endColumn": 35}, {"ruleId": "351", "severity": 1, "message": "352", "line": 138, "column": 1, "nodeType": "353", "endLine": 138, "endColumn": 31}, {"ruleId": "351", "severity": 1, "message": "352", "line": 223, "column": 1, "nodeType": "353", "endLine": 223, "endColumn": 35}, {"ruleId": "343", "severity": 1, "message": "354", "line": 40, "column": 6, "nodeType": "345", "endLine": 40, "endColumn": 28, "suggestions": "355"}, {"ruleId": "343", "severity": 1, "message": "356", "line": 38, "column": 6, "nodeType": "345", "endLine": 38, "endColumn": 29, "suggestions": "357"}, {"ruleId": "343", "severity": 1, "message": "358", "line": 38, "column": 6, "nodeType": "345", "endLine": 38, "endColumn": 28, "suggestions": "359"}, {"ruleId": "351", "severity": 1, "message": "352", "line": 259, "column": 1, "nodeType": "353", "endLine": 259, "endColumn": 34}, {"ruleId": "343", "severity": 1, "message": "360", "line": 49, "column": 6, "nodeType": "345", "endLine": 49, "endColumn": 41, "suggestions": "361"}, {"ruleId": "343", "severity": 1, "message": "360", "line": 46, "column": 6, "nodeType": "345", "endLine": 46, "endColumn": 41, "suggestions": "362"}, {"ruleId": "327", "severity": 1, "message": "363", "line": 8, "column": 3, "nodeType": "329", "messageId": "330", "endLine": 8, "endColumn": 19}, {"ruleId": "327", "severity": 1, "message": "364", "line": 9, "column": 7, "nodeType": "329", "messageId": "330", "endLine": 9, "endColumn": 21}, {"ruleId": "365", "severity": 1, "message": "366", "line": 188, "column": 7, "nodeType": "367", "messageId": "368", "endLine": 204, "endColumn": 8}, {"ruleId": "327", "severity": 1, "message": "363", "line": 12, "column": 3, "nodeType": "329", "messageId": "330", "endLine": 12, "endColumn": 19}, {"ruleId": "327", "severity": 1, "message": "369", "line": 8, "column": 3, "nodeType": "329", "messageId": "330", "endLine": 8, "endColumn": 26}, {"ruleId": "327", "severity": 1, "message": "370", "line": 5, "column": 3, "nodeType": "329", "messageId": "330", "endLine": 5, "endColumn": 18}, {"ruleId": "327", "severity": 1, "message": "371", "line": 10, "column": 3, "nodeType": "329", "messageId": "330", "endLine": 10, "endColumn": 15}, {"ruleId": "327", "severity": 1, "message": "372", "line": 12, "column": 41, "nodeType": "329", "messageId": "330", "endLine": 12, "endColumn": 50}, {"ruleId": "327", "severity": 1, "message": "373", "line": 49, "column": 9, "nodeType": "329", "messageId": "330", "endLine": 49, "endColumn": 26}, "jsx-a11y/heading-has-content", "Headings must have content and the content must be accessible by a screen reader.", "JSXOpeningElement", "no-unused-vars", "'Button' is defined but never used.", "Identifier", "unusedVar", "'ArrowTrendingUpIcon' is defined but never used.", "'ArrowTrendingDownIcon' is defined but never used.", "'motion' is defined but never used.", "'LineChart' is defined but never used.", "'Line' is defined but never used.", "'CalendarIcon' is defined but never used.", "'FunnelIcon' is defined but never used.", "'Input' is defined but never used.", "'Label' is defined but never used.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'GlobeAltIcon' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'filterPurchases'. Either include it or remove the dependency array.", "ArrayExpression", ["374"], "'customersAPI' is defined but never used.", "'inventoryAPI' is defined but never used.", "React Hook useEffect has a missing dependency: 'filterSales'. Either include it or remove the dependency array.", ["375"], "import/no-anonymous-default-export", "Assign instance to a variable before exporting as module default", "ExportDefaultDeclaration", "React Hook useEffect has a missing dependency: 'filterProducts'. Either include it or remove the dependency array.", ["376"], "React Hook useEffect has a missing dependency: 'filterCustomers'. Either include it or remove the dependency array.", ["377"], "React Hook useEffect has a missing dependency: 'filterExpenses'. Either include it or remove the dependency array.", ["378"], "React Hook useEffect has a missing dependency: 'calculateTotals'. Either include it or remove the dependency array.", ["379"], ["380"], "'decodeQRCodeData' is defined but never used.", "'encodeToBase64' is assigned a value but never used.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "'ExclamationTriangleIcon' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'EnvelopeIcon' is defined but never used.", "'CardTitle' is defined but never used.", "'handleInputChange' is assigned a value but never used.", {"desc": "381", "fix": "382"}, {"desc": "383", "fix": "384"}, {"desc": "385", "fix": "386"}, {"desc": "387", "fix": "388"}, {"desc": "389", "fix": "390"}, {"desc": "391", "fix": "392"}, {"desc": "391", "fix": "393"}, "Update the dependencies array to be: [filterPurchases, purchases, searchTerm]", {"range": "394", "text": "395"}, "Update the dependencies array to be: [filterSales, sales, searchTerm]", {"range": "396", "text": "397"}, "Update the dependencies array to be: [filterProducts, products, searchTerm]", {"range": "398", "text": "399"}, "Update the dependencies array to be: [customers, filterCustomers, searchTerm]", {"range": "400", "text": "401"}, "Update the dependencies array to be: [expenses, filterExpenses, searchTerm]", {"range": "402", "text": "403"}, "Update the dependencies array to be: [formData.items, formData.discount, calculateTotals]", {"range": "404", "text": "405"}, {"range": "406", "text": "405"}, [1318, 1341], "[filterPurchases, purchases, searchTerm]", [1355, 1374], "[filterSales, sales, searchTerm]", [1417, 1439], "[filterProducts, products, searchTerm]", [1333, 1356], "[customers, filterCustomers, searchTerm]", [1316, 1338], "[expenses, filterExpenses, searchTerm]", [1515, 1550], "[formData.items, formData.discount, calculateTotals]", [1453, 1488]]