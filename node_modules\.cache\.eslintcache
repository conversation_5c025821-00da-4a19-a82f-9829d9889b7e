[{"C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\CompanyManagement.jsx": "3", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\companiesAPI.js": "4", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\button.jsx": "5", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\card.jsx": "6", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\table.jsx": "7", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\dialog.jsx": "8", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\input.jsx": "9", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\label.jsx": "10", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\lib\\utils.js": "11", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\contexts\\AuthContext.js": "12", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Auth\\ProtectedRoute.js": "13", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\LoadingSpinner.jsx": "14", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Login.jsx": "15", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Layout\\Layout.jsx": "16", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Layout\\Sidebar.jsx": "17", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Layout\\Header.jsx": "18", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Dashboard.jsx": "19", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\StatsCard.jsx": "20", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\RecentActivities.jsx": "21", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\CompanyChart.jsx": "22", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\QuickActions.jsx": "23", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Companies.jsx": "24", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Reports.jsx": "25", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Reports\\ReportsChart.jsx": "26", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Reports\\ReportFilters.jsx": "27", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Reports\\ExportOptions.jsx": "28", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Settings.jsx": "29"}, {"size": 263, "mtime": 1750149838411, "results": "30", "hashOfConfig": "31"}, {"size": 2281, "mtime": 1750161127849, "results": "32", "hashOfConfig": "31"}, {"size": 19179, "mtime": 1750160550738, "results": "33", "hashOfConfig": "31"}, {"size": 2696, "mtime": 1750149893012, "results": "34", "hashOfConfig": "31"}, {"size": 1359, "mtime": 1750160192372, "results": "35", "hashOfConfig": "31"}, {"size": 1508, "mtime": 1750160215710, "results": "36", "hashOfConfig": "31"}, {"size": 2188, "mtime": 1750160228722, "results": "37", "hashOfConfig": "31"}, {"size": 3008, "mtime": 1750160588665, "results": "38", "hashOfConfig": "31"}, {"size": 685, "mtime": 1750160199947, "results": "39", "hashOfConfig": "31"}, {"size": 371, "mtime": 1750160205839, "results": "40", "hashOfConfig": "31"}, {"size": 225, "mtime": 1750160250760, "results": "41", "hashOfConfig": "31"}, {"size": 1905, "mtime": 1750161144315, "results": "42", "hashOfConfig": "31"}, {"size": 640, "mtime": 1750161153757, "results": "43", "hashOfConfig": "31"}, {"size": 889, "mtime": 1750161164023, "results": "44", "hashOfConfig": "31"}, {"size": 6371, "mtime": 1750161191711, "results": "45", "hashOfConfig": "31"}, {"size": 1716, "mtime": 1750161204463, "results": "46", "hashOfConfig": "31"}, {"size": 3716, "mtime": 1750161225486, "results": "47", "hashOfConfig": "31"}, {"size": 7098, "mtime": 1750161255384, "results": "48", "hashOfConfig": "31"}, {"size": 3791, "mtime": 1750161576871, "results": "49", "hashOfConfig": "31"}, {"size": 1649, "mtime": 1750161563760, "results": "50", "hashOfConfig": "31"}, {"size": 3423, "mtime": 1750161315525, "results": "51", "hashOfConfig": "31"}, {"size": 2877, "mtime": 1750161332248, "results": "52", "hashOfConfig": "31"}, {"size": 2654, "mtime": 1750161348353, "results": "53", "hashOfConfig": "31"}, {"size": 385, "mtime": 1750161356392, "results": "54", "hashOfConfig": "31"}, {"size": 8679, "mtime": 1750161394588, "results": "55", "hashOfConfig": "31"}, {"size": 3992, "mtime": 1750161417721, "results": "56", "hashOfConfig": "31"}, {"size": 6035, "mtime": 1750161443223, "results": "57", "hashOfConfig": "31"}, {"size": 7977, "mtime": 1750161474940, "results": "58", "hashOfConfig": "31"}, {"size": 15856, "mtime": 1750161632482, "results": "59", "hashOfConfig": "31"}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "7sy7mo", {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\CompanyManagement.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\api\\companiesAPI.js", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\button.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\card.jsx", ["147"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\table.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\dialog.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\input.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\label.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\lib\\utils.js", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Auth\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\ui\\LoadingSpinner.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Login.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Layout\\Layout.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Layout\\Sidebar.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Layout\\Header.jsx", ["148"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Dashboard.jsx", ["149", "150"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\StatsCard.jsx", ["151"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\RecentActivities.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\CompanyChart.jsx", ["152", "153"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Dashboard\\QuickActions.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Companies.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Reports.jsx", ["154", "155", "156", "157"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Reports\\ReportsChart.jsx", ["158", "159"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Reports\\ReportFilters.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\components\\Reports\\ExportOptions.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\src\\pages\\Settings.jsx", ["160"], [], {"ruleId": "161", "severity": 1, "message": "162", "line": 26, "column": 3, "nodeType": "163", "endLine": 33, "endColumn": 5}, {"ruleId": "164", "severity": 1, "message": "165", "line": 11, "column": 10, "nodeType": "166", "messageId": "167", "endLine": 11, "endColumn": 16}, {"ruleId": "164", "severity": 1, "message": "168", "line": 8, "column": 3, "nodeType": "166", "messageId": "167", "endLine": 8, "endColumn": 22}, {"ruleId": "164", "severity": 1, "message": "169", "line": 9, "column": 3, "nodeType": "166", "messageId": "167", "endLine": 9, "endColumn": 24}, {"ruleId": "164", "severity": 1, "message": "170", "line": 2, "column": 10, "nodeType": "166", "messageId": "167", "endLine": 2, "endColumn": 16}, {"ruleId": "164", "severity": 1, "message": "171", "line": 3, "column": 3, "nodeType": "166", "messageId": "167", "endLine": 3, "endColumn": 12}, {"ruleId": "164", "severity": 1, "message": "172", "line": 4, "column": 3, "nodeType": "166", "messageId": "167", "endLine": 4, "endColumn": 7}, {"ruleId": "164", "severity": 1, "message": "173", "line": 6, "column": 3, "nodeType": "166", "messageId": "167", "endLine": 6, "endColumn": 15}, {"ruleId": "164", "severity": 1, "message": "174", "line": 7, "column": 3, "nodeType": "166", "messageId": "167", "endLine": 7, "endColumn": 13}, {"ruleId": "164", "severity": 1, "message": "175", "line": 11, "column": 10, "nodeType": "166", "messageId": "167", "endLine": 11, "endColumn": 15}, {"ruleId": "164", "severity": 1, "message": "176", "line": 12, "column": 10, "nodeType": "166", "messageId": "167", "endLine": 12, "endColumn": 15}, {"ruleId": "164", "severity": 1, "message": "177", "line": 13, "column": 3, "nodeType": "166", "messageId": "167", "endLine": 13, "endColumn": 11}, {"ruleId": "164", "severity": 1, "message": "178", "line": 14, "column": 3, "nodeType": "166", "messageId": "167", "endLine": 14, "endColumn": 6}, {"ruleId": "164", "severity": 1, "message": "179", "line": 7, "column": 3, "nodeType": "166", "messageId": "167", "endLine": 7, "endColumn": 15}, "jsx-a11y/heading-has-content", "Headings must have content and the content must be accessible by a screen reader.", "JSXOpeningElement", "no-unused-vars", "'Button' is defined but never used.", "Identifier", "unusedVar", "'ArrowTrendingUpIcon' is defined but never used.", "'ArrowTrendingDownIcon' is defined but never used.", "'motion' is defined but never used.", "'LineChart' is defined but never used.", "'Line' is defined but never used.", "'CalendarIcon' is defined but never used.", "'FunnelIcon' is defined but never used.", "'Input' is defined but never used.", "'Label' is defined but never used.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'GlobeAltIcon' is defined but never used."]