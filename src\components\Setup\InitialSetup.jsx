import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  BuildingOfficeIcon, 
  CheckCircleIcon,
  ArrowRightIcon,
  ArrowLeftIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { saveCompanyConfig, validateCompanyConfig, createNewCompanyConfig } from '../../config/companyConfig';
import toast from 'react-hot-toast';

const InitialSetup = ({ onComplete }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [companyData, setCompanyData] = useState({
    name: '',
    nameEn: '',
    vatNumber: '',
    crNumber: '',
    licenseNumber: '',
    address: {
      street: '',
      city: '',
      region: '',
      postalCode: '',
      country: 'المملكة العربية السعودية'
    },
    contact: {
      phone: '',
      mobile: '',
      email: '',
      website: '',
      fax: ''
    },
    bankInfo: {
      bankName: '',
      accountNumber: '',
      iban: '',
      swiftCode: ''
    }
  });

  const [errors, setErrors] = useState({});

  const steps = [
    {
      id: 1,
      title: 'معلومات الشركة الأساسية',
      description: 'أدخل المعلومات الأساسية لشركتك'
    },
    {
      id: 2,
      title: 'معلومات الاتصال والعنوان',
      description: 'أدخل عنوان الشركة ومعلومات الاتصال'
    },
    {
      id: 3,
      title: 'المعلومات المصرفية',
      description: 'أدخل معلومات الحساب المصرفي (اختياري)'
    },
    {
      id: 4,
      title: 'مراجعة وإنهاء الإعداد',
      description: 'راجع جميع المعلومات وأكمل الإعداد'
    }
  ];

  const handleInputChange = (section, field, value) => {
    if (section) {
      setCompanyData(prev => ({
        ...prev,
        [section]: {
          ...prev[section],
          [field]: value
        }
      }));
    } else {
      setCompanyData(prev => ({
        ...prev,
        [field]: value
      }));
    }
    
    // إزالة الخطأ عند التعديل
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  const validateStep = (step) => {
    const newErrors = {};
    
    switch (step) {
      case 1:
        if (!companyData.name.trim()) newErrors.name = 'اسم الشركة مطلوب';
        if (!companyData.vatNumber.trim()) {
          newErrors.vatNumber = 'الرقم الضريبي مطلوب';
        } else if (companyData.vatNumber.length !== 15) {
          newErrors.vatNumber = 'الرقم الضريبي يجب أن يكون 15 رقم';
        }
        break;
        
      case 2:
        if (!companyData.contact.phone.trim()) newErrors.phone = 'رقم الهاتف مطلوب';
        if (!companyData.contact.email.trim()) {
          newErrors.email = 'البريد الإلكتروني مطلوب';
        } else if (!/\S+@\S+\.\S+/.test(companyData.contact.email)) {
          newErrors.email = 'البريد الإلكتروني غير صحيح';
        }
        if (!companyData.address.city.trim()) newErrors.city = 'المدينة مطلوبة';
        break;
        
      case 3:
        // المعلومات المصرفية اختيارية
        break;
        
      default:
        break;
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, steps.length));
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const completeSetup = () => {
    const config = createNewCompanyConfig(companyData);
    const validation = validateCompanyConfig(config);
    
    if (validation.isValid) {
      if (saveCompanyConfig(config)) {
        // حفظ حالة الإعداد
        localStorage.setItem('setup_completed', 'true');
        toast.success('تم إعداد الشركة بنجاح!');
        onComplete();
      } else {
        toast.error('حدث خطأ أثناء حفظ البيانات');
      }
    } else {
      toast.error('يرجى مراجعة البيانات المدخلة');
      setErrors(validation.errors.reduce((acc, error) => {
        acc[error] = error;
        return acc;
      }, {}));
    }
  };

  const renderStep1 = () => (
    <div className="space-y-6">
      <div>
        <Label htmlFor="companyName" className="block text-sm font-medium text-gray-700 mb-2">
          اسم الشركة *
        </Label>
        <Input
          id="companyName"
          type="text"
          value={companyData.name}
          onChange={(e) => handleInputChange(null, 'name', e.target.value)}
          placeholder="مثال: شركة التجارة المحدودة"
          className={errors.name ? 'border-red-500' : ''}
        />
        {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
      </div>

      <div>
        <Label htmlFor="companyNameEn" className="block text-sm font-medium text-gray-700 mb-2">
          اسم الشركة بالإنجليزية
        </Label>
        <Input
          id="companyNameEn"
          type="text"
          value={companyData.nameEn}
          onChange={(e) => handleInputChange(null, 'nameEn', e.target.value)}
          placeholder="Example: Trading Company Ltd"
        />
      </div>

      <div>
        <Label htmlFor="vatNumber" className="block text-sm font-medium text-gray-700 mb-2">
          الرقم الضريبي *
        </Label>
        <Input
          id="vatNumber"
          type="text"
          value={companyData.vatNumber}
          onChange={(e) => handleInputChange(null, 'vatNumber', e.target.value)}
          placeholder="300000000000003"
          maxLength={15}
          className={errors.vatNumber ? 'border-red-500' : ''}
        />
        {errors.vatNumber && <p className="text-red-500 text-sm mt-1">{errors.vatNumber}</p>}
        <p className="text-gray-500 text-sm mt-1">يجب أن يكون 15 رقم بالضبط</p>
      </div>

      <div>
        <Label htmlFor="crNumber" className="block text-sm font-medium text-gray-700 mb-2">
          رقم السجل التجاري
        </Label>
        <Input
          id="crNumber"
          type="text"
          value={companyData.crNumber}
          onChange={(e) => handleInputChange(null, 'crNumber', e.target.value)}
          placeholder="**********"
        />
      </div>

      <div>
        <Label htmlFor="licenseNumber" className="block text-sm font-medium text-gray-700 mb-2">
          رقم الرخصة التجارية
        </Label>
        <Input
          id="licenseNumber"
          type="text"
          value={companyData.licenseNumber}
          onChange={(e) => handleInputChange(null, 'licenseNumber', e.target.value)}
          placeholder="رقم الرخصة"
        />
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <Label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
            رقم الهاتف *
          </Label>
          <Input
            id="phone"
            type="tel"
            value={companyData.contact.phone}
            onChange={(e) => handleInputChange('contact', 'phone', e.target.value)}
            placeholder="**********"
            className={errors.phone ? 'border-red-500' : ''}
          />
          {errors.phone && <p className="text-red-500 text-sm mt-1">{errors.phone}</p>}
        </div>

        <div>
          <Label htmlFor="mobile" className="block text-sm font-medium text-gray-700 mb-2">
            رقم الجوال
          </Label>
          <Input
            id="mobile"
            type="tel"
            value={companyData.contact.mobile}
            onChange={(e) => handleInputChange('contact', 'mobile', e.target.value)}
            placeholder="0501234567"
          />
        </div>

        <div>
          <Label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
            البريد الإلكتروني *
          </Label>
          <Input
            id="email"
            type="email"
            value={companyData.contact.email}
            onChange={(e) => handleInputChange('contact', 'email', e.target.value)}
            placeholder="<EMAIL>"
            className={errors.email ? 'border-red-500' : ''}
          />
          {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
        </div>

        <div>
          <Label htmlFor="website" className="block text-sm font-medium text-gray-700 mb-2">
            الموقع الإلكتروني
          </Label>
          <Input
            id="website"
            type="url"
            value={companyData.contact.website}
            onChange={(e) => handleInputChange('contact', 'website', e.target.value)}
            placeholder="www.company.com"
          />
        </div>
      </div>

      <div>
        <Label htmlFor="street" className="block text-sm font-medium text-gray-700 mb-2">
          العنوان التفصيلي
        </Label>
        <textarea
          id="street"
          rows={3}
          value={companyData.address.street}
          onChange={(e) => handleInputChange('address', 'street', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          placeholder="الشارع، الحي، رقم المبنى"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div>
          <Label htmlFor="city" className="block text-sm font-medium text-gray-700 mb-2">
            المدينة *
          </Label>
          <Input
            id="city"
            type="text"
            value={companyData.address.city}
            onChange={(e) => handleInputChange('address', 'city', e.target.value)}
            placeholder="الرياض"
            className={errors.city ? 'border-red-500' : ''}
          />
          {errors.city && <p className="text-red-500 text-sm mt-1">{errors.city}</p>}
        </div>

        <div>
          <Label htmlFor="region" className="block text-sm font-medium text-gray-700 mb-2">
            المنطقة
          </Label>
          <Input
            id="region"
            type="text"
            value={companyData.address.region}
            onChange={(e) => handleInputChange('address', 'region', e.target.value)}
            placeholder="منطقة الرياض"
          />
        </div>

        <div>
          <Label htmlFor="postalCode" className="block text-sm font-medium text-gray-700 mb-2">
            الرمز البريدي
          </Label>
          <Input
            id="postalCode"
            type="text"
            value={companyData.address.postalCode}
            onChange={(e) => handleInputChange('address', 'postalCode', e.target.value)}
            placeholder="12345"
          />
        </div>
      </div>
    </div>
  );

  const renderStep3 = () => (
    <div className="space-y-6">
      <div className="bg-blue-50 p-4 rounded-lg">
        <p className="text-blue-800 text-sm">
          المعلومات المصرفية اختيارية ولكنها مفيدة لإظهارها في الفواتير لتسهيل عملية الدفع على العملاء.
        </p>
      </div>

      <div>
        <Label htmlFor="bankName" className="block text-sm font-medium text-gray-700 mb-2">
          اسم البنك
        </Label>
        <Input
          id="bankName"
          type="text"
          value={companyData.bankInfo.bankName}
          onChange={(e) => handleInputChange('bankInfo', 'bankName', e.target.value)}
          placeholder="البنك الأهلي السعودي"
        />
      </div>

      <div>
        <Label htmlFor="accountNumber" className="block text-sm font-medium text-gray-700 mb-2">
          رقم الحساب
        </Label>
        <Input
          id="accountNumber"
          type="text"
          value={companyData.bankInfo.accountNumber}
          onChange={(e) => handleInputChange('bankInfo', 'accountNumber', e.target.value)}
          placeholder="*********"
        />
      </div>

      <div>
        <Label htmlFor="iban" className="block text-sm font-medium text-gray-700 mb-2">
          رقم الآيبان (IBAN)
        </Label>
        <Input
          id="iban"
          type="text"
          value={companyData.bankInfo.iban}
          onChange={(e) => handleInputChange('bankInfo', 'iban', e.target.value)}
          placeholder="************************"
        />
      </div>

      <div>
        <Label htmlFor="swiftCode" className="block text-sm font-medium text-gray-700 mb-2">
          رمز السويفت (SWIFT)
        </Label>
        <Input
          id="swiftCode"
          type="text"
          value={companyData.bankInfo.swiftCode}
          onChange={(e) => handleInputChange('bankInfo', 'swiftCode', e.target.value)}
          placeholder="NCBKSARI"
        />
      </div>
    </div>
  );

  const renderStep4 = () => (
    <div className="space-y-6">
      <div className="bg-green-50 p-4 rounded-lg">
        <div className="flex items-center">
          <CheckCircleIcon className="w-6 h-6 text-green-600 ml-3" />
          <div>
            <h3 className="text-lg font-semibold text-green-800">جاهز للإنهاء!</h3>
            <p className="text-green-700">راجع المعلومات أدناه وأكمل الإعداد</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white p-4 rounded-lg border">
          <h4 className="font-semibold text-gray-900 mb-3">معلومات الشركة</h4>
          <div className="space-y-2 text-sm">
            <p><span className="font-medium">الاسم:</span> {companyData.name}</p>
            {companyData.nameEn && <p><span className="font-medium">الاسم بالإنجليزية:</span> {companyData.nameEn}</p>}
            <p><span className="font-medium">الرقم الضريبي:</span> {companyData.vatNumber}</p>
            {companyData.crNumber && <p><span className="font-medium">السجل التجاري:</span> {companyData.crNumber}</p>}
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border">
          <h4 className="font-semibold text-gray-900 mb-3">معلومات الاتصال</h4>
          <div className="space-y-2 text-sm">
            <p><span className="font-medium">الهاتف:</span> {companyData.contact.phone}</p>
            <p><span className="font-medium">البريد:</span> {companyData.contact.email}</p>
            <p><span className="font-medium">المدينة:</span> {companyData.address.city}</p>
            {companyData.contact.website && <p><span className="font-medium">الموقع:</span> {companyData.contact.website}</p>}
          </div>
        </div>
      </div>

      {companyData.bankInfo.bankName && (
        <div className="bg-white p-4 rounded-lg border">
          <h4 className="font-semibold text-gray-900 mb-3">المعلومات المصرفية</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <p><span className="font-medium">البنك:</span> {companyData.bankInfo.bankName}</p>
            {companyData.bankInfo.accountNumber && <p><span className="font-medium">رقم الحساب:</span> {companyData.bankInfo.accountNumber}</p>}
            {companyData.bankInfo.iban && <p><span className="font-medium">الآيبان:</span> {companyData.bankInfo.iban}</p>}
          </div>
        </div>
      )}
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="w-full max-w-4xl"
      >
        <Card className="shadow-xl">
          <CardHeader className="text-center bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-t-lg">
            <div className="flex items-center justify-center mb-4">
              <BuildingOfficeIcon className="w-12 h-12" />
            </div>
            <CardTitle className="text-2xl font-bold">إعداد الشركة</CardTitle>
            <p className="text-blue-100 mt-2">أدخل معلومات شركتك لبدء استخدام النظام</p>
          </CardHeader>

          <CardContent className="p-8">
            {/* مؤشر التقدم */}
            <div className="mb-8">
              <div className="flex items-center justify-between mb-4">
                {steps.map((step, index) => (
                  <div key={step.id} className="flex items-center">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium ${
                      currentStep >= step.id 
                        ? 'bg-blue-600 text-white' 
                        : 'bg-gray-200 text-gray-600'
                    }`}>
                      {currentStep > step.id ? (
                        <CheckCircleIcon className="w-6 h-6" />
                      ) : (
                        step.id
                      )}
                    </div>
                    {index < steps.length - 1 && (
                      <div className={`w-16 h-1 mx-2 ${
                        currentStep > step.id ? 'bg-blue-600' : 'bg-gray-200'
                      }`} />
                    )}
                  </div>
                ))}
              </div>
              
              <div className="text-center">
                <h3 className="text-lg font-semibold text-gray-900">
                  {steps[currentStep - 1].title}
                </h3>
                <p className="text-gray-600 text-sm mt-1">
                  {steps[currentStep - 1].description}
                </p>
              </div>
            </div>

            {/* محتوى الخطوة */}
            <div className="mb-8">
              {currentStep === 1 && renderStep1()}
              {currentStep === 2 && renderStep2()}
              {currentStep === 3 && renderStep3()}
              {currentStep === 4 && renderStep4()}
            </div>

            {/* أزرار التنقل */}
            <div className="flex items-center justify-between">
              <Button
                onClick={prevStep}
                disabled={currentStep === 1}
                variant="outline"
                className="flex items-center"
              >
                <ArrowLeftIcon className="w-4 h-4 ml-2" />
                السابق
              </Button>

              {currentStep < steps.length ? (
                <Button
                  onClick={nextStep}
                  className="bg-blue-600 hover:bg-blue-700 text-white flex items-center"
                >
                  التالي
                  <ArrowRightIcon className="w-4 h-4 mr-2" />
                </Button>
              ) : (
                <Button
                  onClick={completeSetup}
                  className="bg-green-600 hover:bg-green-700 text-white flex items-center"
                >
                  إنهاء الإعداد
                  <CheckCircleIcon className="w-4 h-4 mr-2" />
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default InitialSetup;
