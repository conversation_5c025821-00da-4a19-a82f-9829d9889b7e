{"ast": null, "code": "function c() {\n  let o;\n  return {\n    before({\n      doc: e\n    }) {\n      var l;\n      let n = e.documentElement;\n      o = ((l = e.defaultView) != null ? l : window).innerWidth - n.clientWidth;\n    },\n    after({\n      doc: e,\n      d: n\n    }) {\n      let t = e.documentElement,\n        l = t.clientWidth - t.offsetWidth,\n        r = o - l;\n      n.style(t, \"paddingRight\", `${r}px`);\n    }\n  };\n}\nexport { c as adjustScrollbarPadding };", "map": {"version": 3, "names": ["c", "o", "before", "doc", "e", "l", "n", "documentElement", "defaultView", "window", "innerWidth", "clientWidth", "after", "d", "t", "offsetWidth", "r", "style", "adjustScrollbarPadding"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/node_modules/@headlessui/react/dist/hooks/document-overflow/adjust-scrollbar-padding.js"], "sourcesContent": ["function c(){let o;return{before({doc:e}){var l;let n=e.documentElement;o=((l=e.defaultView)!=null?l:window).innerWidth-n.clientWidth},after({doc:e,d:n}){let t=e.documentElement,l=t.clientWidth-t.offsetWidth,r=o-l;n.style(t,\"paddingRight\",`${r}px`)}}}export{c as adjustScrollbarPadding};\n"], "mappings": "AAAA,SAASA,CAACA,CAAA,EAAE;EAAC,IAAIC,CAAC;EAAC,OAAM;IAACC,MAAMA,CAAC;MAACC,GAAG,EAACC;IAAC,CAAC,EAAC;MAAC,IAAIC,CAAC;MAAC,IAAIC,CAAC,GAACF,CAAC,CAACG,eAAe;MAACN,CAAC,GAAC,CAAC,CAACI,CAAC,GAACD,CAAC,CAACI,WAAW,KAAG,IAAI,GAACH,CAAC,GAACI,MAAM,EAAEC,UAAU,GAACJ,CAAC,CAACK,WAAW;IAAA,CAAC;IAACC,KAAKA,CAAC;MAACT,GAAG,EAACC,CAAC;MAACS,CAAC,EAACP;IAAC,CAAC,EAAC;MAAC,IAAIQ,CAAC,GAACV,CAAC,CAACG,eAAe;QAACF,CAAC,GAACS,CAAC,CAACH,WAAW,GAACG,CAAC,CAACC,WAAW;QAACC,CAAC,GAAC<PERSON>,CAAC,GAACI,CAAC;MAACC,CAAC,CAACW,KAAK,CAACH,CAAC,EAAC,cAAc,EAAC,GAAGE,CAAC,IAAI,CAAC;IAAA;EAAC,CAAC;AAAA;AAAC,SAAOhB,CAAC,IAAIkB,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}