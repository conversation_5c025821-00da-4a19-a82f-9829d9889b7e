// مولد QR Code متوافق مع المرحلة الثانية من الفوترة الإلكترونية السعودية
// وفقاً لمعايير هيئة الزكاة والضريبة والجمارك

/**
 * تشفير النص إلى Base64
 * @param {string} text - النص المراد تشفيره
 * @returns {string} النص مشفر بـ Base64
 */
const encodeToBase64 = (text) => {
  return btoa(unescape(encodeURIComponent(text)));
};

/**
 * تحويل إلى TLV (Tag-Length-Value) format بالطريقة الصحيحة
 * @param {number} tag - رقم العلامة
 * @param {string} value - القيمة
 * @returns {Uint8Array} البيانات بصيغة TLV
 */
const toTLV = (tag, value) => {
  const valueBytes = new TextEncoder().encode(value);
  const result = new Uint8Array(2 + valueBytes.length);

  result[0] = tag;
  result[1] = valueBytes.length;
  result.set(valueBytes, 2);

  return result;
};

/**
 * تحويل Uint8Array إلى Base64
 * @param {Uint8Array} bytes - البيانات
 * @returns {string} البيانات مشفرة بـ Base64
 */
const arrayToBase64 = (bytes) => {
  let binary = '';
  for (let i = 0; i < bytes.length; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return btoa(binary);
};

/**
 * إنشاء QR Code متوافق مع المرحلة الثانية
 * @param {Object} invoiceData - بيانات الفاتورة
 * @returns {string} محتوى QR Code
 */
export const generateQRCodeData = (invoiceData) => {
  try {
    // تحميل إعدادات الشركة من التخزين المحلي
    const companyConfig = localStorage.getItem('company_config');
    const eInvoiceSettings = localStorage.getItem('einvoice_settings');

    let companyName = 'شركة إدارة الأعمال';
    let vatNumber = '300000000000003';

    // استخدام بيانات الشركة الحقيقية
    if (companyConfig) {
      try {
        const config = JSON.parse(companyConfig);
        companyName = config.companyInfo?.name || companyName;
        vatNumber = config.companyInfo?.vatNumber || vatNumber;
      } catch (error) {
        console.warn('خطأ في قراءة إعدادات الشركة:', error);
      }
    }

    // استخدام إعدادات الفوترة الإلكترونية إذا وجدت
    if (eInvoiceSettings) {
      try {
        const settings = JSON.parse(eInvoiceSettings);
        companyName = settings.companyName || companyName;
        vatNumber = settings.vatNumber || vatNumber;
      } catch (error) {
        console.warn('خطأ في قراءة إعدادات الفوترة الإلكترونية:', error);
      }
    }

    const companyData = {
      name: companyName,
      vatNumber: vatNumber,
      timestamp: new Date().toISOString(),
      invoiceTotal: invoiceData.total.toFixed(2),
      vatAmount: invoiceData.tax.toFixed(2)
    };

    // إنشاء البيانات بصيغة TLV وفقاً للمعايير السعودية
    const tlvArrays = [];

    // Tag 1: اسم البائع (Seller Name)
    tlvArrays.push(toTLV(1, companyData.name));

    // Tag 2: الرقم الضريبي للبائع (Seller VAT Number)
    tlvArrays.push(toTLV(2, companyData.vatNumber));

    // Tag 3: الطابع الزمني (Timestamp)
    tlvArrays.push(toTLV(3, companyData.timestamp));

    // Tag 4: إجمالي الفاتورة شامل الضريبة (Invoice Total with VAT)
    tlvArrays.push(toTLV(4, companyData.invoiceTotal));

    // Tag 5: إجمالي ضريبة القيمة المضافة (VAT Total)
    tlvArrays.push(toTLV(5, companyData.vatAmount));

    // دمج جميع المصفوفات
    const totalLength = tlvArrays.reduce((sum, arr) => sum + arr.length, 0);
    const combinedArray = new Uint8Array(totalLength);
    let offset = 0;

    for (const arr of tlvArrays) {
      combinedArray.set(arr, offset);
      offset += arr.length;
    }

    // تحويل البيانات إلى Base64
    const base64Data = arrayToBase64(combinedArray);

    return base64Data;

  } catch (error) {
    console.error('خطأ في إنشاء QR Code:', error);
    return '';
  }
};

/**
 * إنشاء QR Code مبسط للاختبار
 * @param {Object} invoiceData - بيانات الفاتورة
 * @returns {string} محتوى QR Code مبسط
 */
export const generateSimpleQRCode = (invoiceData) => {
  const qrData = {
    seller: 'شركة إدارة الأعمال',
    vatNumber: '300000000000003',
    invoiceNumber: invoiceData.invoiceNumber,
    date: invoiceData.date,
    total: invoiceData.total.toFixed(2),
    vat: invoiceData.tax.toFixed(2),
    customer: invoiceData.customerName
  };

  return JSON.stringify(qrData);
};

/**
 * التحقق من صحة الرقم الضريبي السعودي
 * @param {string} vatNumber - الرقم الضريبي
 * @returns {boolean} صحة الرقم الضريبي
 */
export const validateSaudiVATNumber = (vatNumber) => {
  // الرقم الضريبي السعودي يجب أن يكون 15 رقم
  const vatRegex = /^[0-9]{15}$/;
  return vatRegex.test(vatNumber);
};

/**
 * تنسيق التاريخ للفوترة الإلكترونية
 * @param {Date} date - التاريخ
 * @returns {string} التاريخ منسق
 */
export const formatDateForEInvoice = (date) => {
  return new Date(date).toISOString();
};

/**
 * فك تشفير QR Code للتحقق من صحة البيانات
 * @param {string} base64Data - بيانات QR Code مشفرة
 * @returns {Object} بيانات الفاتورة مفكوكة
 */
export const decodeQRCodeData = (base64Data) => {
  try {
    // فك تشفير Base64
    const binaryString = atob(base64Data);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }

    const result = {};
    let offset = 0;

    // قراءة بيانات TLV
    while (offset < bytes.length) {
      const tag = bytes[offset];
      const length = bytes[offset + 1];
      const value = new TextDecoder().decode(bytes.slice(offset + 2, offset + 2 + length));

      switch (tag) {
        case 1:
          result.sellerName = value;
          break;
        case 2:
          result.vatNumber = value;
          break;
        case 3:
          result.timestamp = value;
          break;
        case 4:
          result.invoiceTotal = value;
          break;
        case 5:
          result.vatAmount = value;
          break;
      }

      offset += 2 + length;
    }

    return result;
  } catch (error) {
    console.error('خطأ في فك تشفير QR Code:', error);
    return null;
  }
};

/**
 * حساب hash للفاتورة (للمرحلة الثانية المتقدمة)
 * @param {Object} invoiceData - بيانات الفاتورة
 * @returns {string} hash الفاتورة
 */
export const generateInvoiceHash = (invoiceData) => {
  const dataString = JSON.stringify({
    invoiceNumber: invoiceData.invoiceNumber,
    date: invoiceData.date,
    total: invoiceData.total,
    items: invoiceData.items
  });

  // استخدام hash بسيط (في الإنتاج يجب استخدام SHA-256)
  let hash = 0;
  for (let i = 0; i < dataString.length; i++) {
    const char = dataString.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // تحويل إلى 32bit integer
  }

  return Math.abs(hash).toString(16);
};

/**
 * اختبار قراءة QR Code
 * @param {string} qrData - بيانات QR Code
 * @returns {Object} نتيجة الاختبار
 */
export const testQRCodeReading = (qrData) => {
  const decoded = decodeQRCodeData(qrData);

  return {
    isValid: decoded !== null,
    data: decoded,
    hasAllRequiredFields: decoded &&
      decoded.sellerName &&
      decoded.vatNumber &&
      decoded.timestamp &&
      decoded.invoiceTotal &&
      decoded.vatAmount
  };
};
