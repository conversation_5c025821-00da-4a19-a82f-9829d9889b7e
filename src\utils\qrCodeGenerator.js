// مولد QR Code متوافق مع المرحلة الثانية من الفوترة الإلكترونية السعودية
// وفقاً لمعايير هيئة الزكاة والضريبة والجمارك

/**
 * تشفير النص إلى Base64
 * @param {string} text - النص المراد تشفيره
 * @returns {string} النص مشفر بـ Base64
 */
const encodeToBase64 = (text) => {
  return btoa(unescape(encodeURIComponent(text)));
};

/**
 * تحويل الرقم إلى TLV (Tag-Length-Value) format
 * @param {number} tag - رقم العلامة
 * @param {string} value - القيمة
 * @returns {string} القيمة بصيغة TLV
 */
const toTLV = (tag, value) => {
  const tagHex = tag.toString(16).padStart(2, '0');
  const lengthHex = value.length.toString(16).padStart(2, '0');
  const valueHex = Array.from(value)
    .map(char => char.charCodeAt(0).toString(16).padStart(2, '0'))
    .join('');

  return tagHex + lengthHex + valueHex;
};

/**
 * إنشاء QR Code متوافق مع المرحلة الثانية
 * @param {Object} invoiceData - بيانات الفاتورة
 * @returns {string} محتوى QR Code
 */
export const generateQRCodeData = (invoiceData) => {
  try {
    // تحميل إعدادات الشركة من التخزين المحلي
    const savedSettings = localStorage.getItem('einvoice_settings');
    const defaultSettings = {
      companyName: 'شركة إدارة الأعمال',
      vatNumber: '300000000000003'
    };

    const companySettings = savedSettings ? JSON.parse(savedSettings) : defaultSettings;

    const companyData = {
      name: companySettings.companyName || defaultSettings.companyName,
      vatNumber: companySettings.vatNumber || defaultSettings.vatNumber,
      timestamp: new Date().toISOString(),
      invoiceTotal: invoiceData.total.toFixed(2),
      vatAmount: invoiceData.tax.toFixed(2)
    };

    // إنشاء البيانات بصيغة TLV وفقاً للمعايير السعودية
    let tlvData = '';

    // Tag 1: اسم البائع (Seller Name)
    tlvData += toTLV(1, companyData.name);

    // Tag 2: الرقم الضريبي للبائع (Seller VAT Number)
    tlvData += toTLV(2, companyData.vatNumber);

    // Tag 3: الطابع الزمني (Timestamp)
    tlvData += toTLV(3, companyData.timestamp);

    // Tag 4: إجمالي الفاتورة شامل الضريبة (Invoice Total with VAT)
    tlvData += toTLV(4, companyData.invoiceTotal);

    // Tag 5: إجمالي ضريبة القيمة المضافة (VAT Total)
    tlvData += toTLV(5, companyData.vatAmount);

    // تحويل البيانات إلى Base64
    const base64Data = encodeToBase64(tlvData);

    return base64Data;

  } catch (error) {
    console.error('خطأ في إنشاء QR Code:', error);
    return '';
  }
};

/**
 * إنشاء QR Code مبسط للاختبار
 * @param {Object} invoiceData - بيانات الفاتورة
 * @returns {string} محتوى QR Code مبسط
 */
export const generateSimpleQRCode = (invoiceData) => {
  const qrData = {
    seller: 'شركة إدارة الأعمال',
    vatNumber: '300000000000003',
    invoiceNumber: invoiceData.invoiceNumber,
    date: invoiceData.date,
    total: invoiceData.total.toFixed(2),
    vat: invoiceData.tax.toFixed(2),
    customer: invoiceData.customerName
  };

  return JSON.stringify(qrData);
};

/**
 * التحقق من صحة الرقم الضريبي السعودي
 * @param {string} vatNumber - الرقم الضريبي
 * @returns {boolean} صحة الرقم الضريبي
 */
export const validateSaudiVATNumber = (vatNumber) => {
  // الرقم الضريبي السعودي يجب أن يكون 15 رقم
  const vatRegex = /^[0-9]{15}$/;
  return vatRegex.test(vatNumber);
};

/**
 * تنسيق التاريخ للفوترة الإلكترونية
 * @param {Date} date - التاريخ
 * @returns {string} التاريخ منسق
 */
export const formatDateForEInvoice = (date) => {
  return new Date(date).toISOString();
};

/**
 * حساب hash للفاتورة (للمرحلة الثانية المتقدمة)
 * @param {Object} invoiceData - بيانات الفاتورة
 * @returns {string} hash الفاتورة
 */
export const generateInvoiceHash = (invoiceData) => {
  const dataString = JSON.stringify({
    invoiceNumber: invoiceData.invoiceNumber,
    date: invoiceData.date,
    total: invoiceData.total,
    items: invoiceData.items
  });

  // استخدام hash بسيط (في الإنتاج يجب استخدام SHA-256)
  let hash = 0;
  for (let i = 0; i < dataString.length; i++) {
    const char = dataString.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // تحويل إلى 32bit integer
  }

  return Math.abs(hash).toString(16);
};
