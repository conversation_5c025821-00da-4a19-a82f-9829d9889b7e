{"ast": null, "code": "import leastIndex from \"./leastIndex.js\";\nexport default function scan(values, compare) {\n  const index = leastIndex(values, compare);\n  return index < 0 ? undefined : index;\n}", "map": {"version": 3, "names": ["leastIndex", "scan", "values", "compare", "index", "undefined"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/node_modules/d3-array/src/scan.js"], "sourcesContent": ["import leastIndex from \"./leastIndex.js\";\n\nexport default function scan(values, compare) {\n  const index = leastIndex(values, compare);\n  return index < 0 ? undefined : index;\n}\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,iBAAiB;AAExC,eAAe,SAASC,IAAIA,CAACC,MAAM,EAAEC,OAAO,EAAE;EAC5C,MAAMC,KAAK,GAAGJ,UAAU,CAACE,MAAM,EAAEC,OAAO,CAAC;EACzC,OAAOC,KAAK,GAAG,CAAC,GAAGC,SAAS,GAAGD,KAAK;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}