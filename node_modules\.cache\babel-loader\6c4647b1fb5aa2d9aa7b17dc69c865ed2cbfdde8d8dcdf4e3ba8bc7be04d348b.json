{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u0633\\u0637\\u062D \\u0627\\u0644\\u0645\\u0643\\u062A\\u0628\\\\333\\\\src\\\\pages\\\\Companies.jsx\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport CompanyManagement from '../components/CompanyManagement';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Companies = () => {\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    initial: {\n      opacity: 0,\n      y: 20\n    },\n    animate: {\n      opacity: 1,\n      y: 0\n    },\n    transition: {\n      duration: 0.5\n    },\n    children: /*#__PURE__*/_jsxDEV(CompanyManagement, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n};\n_c = Companies;\nexport default Companies;\nvar _c;\n$RefreshReg$(_c, \"Companies\");", "map": {"version": 3, "names": ["React", "motion", "CompanyManagement", "jsxDEV", "_jsxDEV", "Companies", "div", "initial", "opacity", "y", "animate", "transition", "duration", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/src/pages/Companies.jsx"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport CompanyManagement from '../components/CompanyManagement';\n\nconst Companies = () => {\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5 }}\n    >\n      <CompanyManagement />\n    </motion.div>\n  );\n};\n\nexport default Companies;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAOC,iBAAiB,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAMC,SAAS,GAAGA,CAAA,KAAM;EACtB,oBACED,OAAA,CAACH,MAAM,CAACK,GAAG;IACTC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAE;IAC/BC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAE;IAC9BE,UAAU,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE;IAAAC,QAAA,eAE9BT,OAAA,CAACF,iBAAiB;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACX,CAAC;AAEjB,CAAC;AAACC,EAAA,GAVIb,SAAS;AAYf,eAAeA,SAAS;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}