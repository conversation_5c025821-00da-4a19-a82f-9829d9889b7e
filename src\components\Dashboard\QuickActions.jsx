import React from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import {
  PlusIcon,
  DocumentArrowDownIcon,
  ChartBarIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';

const QuickActions = () => {
  const navigate = useNavigate();

  const actions = [
    {
      title: 'إضافة شركة جديدة',
      description: 'أضف شركة جديدة إلى النظام',
      icon: PlusIcon,
      color: 'bg-blue-500 hover:bg-blue-600',
      action: () => navigate('/companies')
    },
    {
      title: 'تصدير التقارير',
      description: 'تصدير تقارير الشركات',
      icon: DocumentArrowDownIcon,
      color: 'bg-green-500 hover:bg-green-600',
      action: () => navigate('/reports')
    },
    {
      title: 'عرض الإحصائيات',
      description: 'مراجعة الإحصائيات التفصيلية',
      icon: ChartBarIcon,
      color: 'bg-purple-500 hover:bg-purple-600',
      action: () => navigate('/reports')
    },
    {
      title: 'إعدادات النظام',
      description: 'تخصيص إعدادات النظام',
      icon: Cog6ToothIcon,
      color: 'bg-orange-500 hover:bg-orange-600',
      action: () => navigate('/settings')
    }
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>الإجراءات السريعة</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {actions.map((action, index) => (
            <motion.div
              key={action.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Button
                onClick={action.action}
                className={`w-full h-auto p-6 ${action.color} text-white border-0 flex flex-col items-center space-y-3 transition-all duration-200`}
                variant="default"
              >
                <action.icon className="w-8 h-8" />
                <div className="text-center">
                  <h3 className="font-semibold text-sm">{action.title}</h3>
                  <p className="text-xs opacity-90 mt-1">{action.description}</p>
                </div>
              </Button>
            </motion.div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default QuickActions;
