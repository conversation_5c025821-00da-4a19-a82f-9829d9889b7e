{"ast": null, "code": "import { useEffect as t, useLayoutEffect as c } from \"react\";\nimport { env as i } from '../utils/env.js';\nlet l = (e, f) => {\n  i.isServer ? t(e, f) : c(e, f);\n};\nexport { l as useIsoMorphicEffect };", "map": {"version": 3, "names": ["useEffect", "t", "useLayoutEffect", "c", "env", "i", "l", "e", "f", "isServer", "useIsoMorphicEffect"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/node_modules/@headlessui/react/dist/hooks/use-iso-morphic-effect.js"], "sourcesContent": ["import{useEffect as t,useLayoutEffect as c}from\"react\";import{env as i}from'../utils/env.js';let l=(e,f)=>{i.isServer?t(e,f):c(e,f)};export{l as useIsoMorphicEffect};\n"], "mappings": "AAAA,SAAOA,SAAS,IAAIC,CAAC,EAACC,eAAe,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,GAAG,IAAIC,CAAC,QAAK,iBAAiB;AAAC,IAAIC,CAAC,GAACA,CAACC,CAAC,EAACC,CAAC,KAAG;EAACH,CAAC,CAACI,QAAQ,GAACR,CAAC,CAACM,CAAC,EAACC,CAAC,CAAC,GAACL,CAAC,CAACI,CAAC,EAACC,CAAC,CAAC;AAAA,CAAC;AAAC,SAAOF,CAAC,IAAII,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}