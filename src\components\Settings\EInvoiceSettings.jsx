import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  QrCodeIcon, 
  BuildingOfficeIcon, 
  IdentificationIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { validateSaudiVATNumber } from '../../utils/qrCodeGenerator';
import toast from 'react-hot-toast';

const EInvoiceSettings = () => {
  const [settings, setSettings] = useState({
    companyName: 'شركة إدارة الأعمال',
    vatNumber: '300000000000003',
    crNumber: '1010000000',
    address: 'الرياض، المملكة العربية السعودية',
    phone: '0112345678',
    email: '<EMAIL>',
    website: 'www.company.com',
    phase2Enabled: true,
    qrCodeEnabled: true,
    autoGenerateQR: true
  });

  const [isValid, setIsValid] = useState({
    vatNumber: true,
    crNumber: true
  });

  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    // تحميل الإعدادات من التخزين المحلي
    const savedSettings = localStorage.getItem('einvoice_settings');
    if (savedSettings) {
      setSettings(JSON.parse(savedSettings));
    }
  }, []);

  useEffect(() => {
    // التحقق من صحة الرقم الضريبي
    setIsValid(prev => ({
      ...prev,
      vatNumber: validateSaudiVATNumber(settings.vatNumber)
    }));
  }, [settings.vatNumber]);

  const handleInputChange = (field, value) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    setIsSaving(true);
    
    try {
      // التحقق من صحة البيانات
      if (!isValid.vatNumber) {
        toast.error('الرقم الضريبي غير صحيح');
        return;
      }

      if (!settings.companyName.trim()) {
        toast.error('اسم الشركة مطلوب');
        return;
      }

      // حفظ الإعدادات
      localStorage.setItem('einvoice_settings', JSON.stringify(settings));
      toast.success('تم حفظ إعدادات الفوترة الإلكترونية بنجاح');
      
    } catch (error) {
      toast.error('حدث خطأ أثناء حفظ الإعدادات');
    } finally {
      setIsSaving(false);
    }
  };

  const resetToDefaults = () => {
    setSettings({
      companyName: 'شركة إدارة الأعمال',
      vatNumber: '300000000000003',
      crNumber: '1010000000',
      address: 'الرياض، المملكة العربية السعودية',
      phone: '0112345678',
      email: '<EMAIL>',
      website: 'www.company.com',
      phase2Enabled: true,
      qrCodeEnabled: true,
      autoGenerateQR: true
    });
    toast.success('تم إعادة تعيين الإعدادات للقيم الافتراضية');
  };

  return (
    <div className="space-y-6">
      {/* العنوان */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div className="flex items-center mb-6">
          <QrCodeIcon className="w-8 h-8 text-blue-600 ml-3" />
          <div>
            <h2 className="text-2xl font-bold text-gray-900">إعدادات الفوترة الإلكترونية</h2>
            <p className="text-gray-600">إعدادات متوافقة مع المرحلة الثانية من الفوترة الإلكترونية</p>
          </div>
        </div>
      </motion.div>

      {/* حالة التوافق */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card className="border-green-200 bg-green-50">
          <CardContent className="p-6">
            <div className="flex items-center">
              <CheckCircleIcon className="w-6 h-6 text-green-600 ml-3" />
              <div>
                <h3 className="text-lg font-semibold text-green-800">متوافق مع المرحلة الثانية</h3>
                <p className="text-green-700">النظام متوافق مع متطلبات هيئة الزكاة والضريبة والجمارك</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* معلومات الشركة */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BuildingOfficeIcon className="w-5 h-5 ml-2" />
              معلومات الشركة
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="companyName" className="block text-sm font-medium text-gray-700 mb-2">
                  اسم الشركة *
                </Label>
                <Input
                  id="companyName"
                  type="text"
                  value={settings.companyName}
                  onChange={(e) => handleInputChange('companyName', e.target.value)}
                  placeholder="اسم الشركة"
                  required
                />
              </div>

              <div>
                <Label htmlFor="vatNumber" className="block text-sm font-medium text-gray-700 mb-2">
                  الرقم الضريبي *
                </Label>
                <div className="relative">
                  <Input
                    id="vatNumber"
                    type="text"
                    value={settings.vatNumber}
                    onChange={(e) => handleInputChange('vatNumber', e.target.value)}
                    placeholder="300000000000003"
                    className={`${!isValid.vatNumber ? 'border-red-500' : 'border-gray-300'}`}
                    maxLength={15}
                    required
                  />
                  <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                    {isValid.vatNumber ? (
                      <CheckCircleIcon className="w-5 h-5 text-green-500" />
                    ) : (
                      <ExclamationTriangleIcon className="w-5 h-5 text-red-500" />
                    )}
                  </div>
                </div>
                {!isValid.vatNumber && (
                  <p className="text-red-500 text-sm mt-1">الرقم الضريبي يجب أن يكون 15 رقم</p>
                )}
              </div>

              <div>
                <Label htmlFor="crNumber" className="block text-sm font-medium text-gray-700 mb-2">
                  رقم السجل التجاري
                </Label>
                <Input
                  id="crNumber"
                  type="text"
                  value={settings.crNumber}
                  onChange={(e) => handleInputChange('crNumber', e.target.value)}
                  placeholder="1010000000"
                />
              </div>

              <div>
                <Label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                  رقم الهاتف
                </Label>
                <Input
                  id="phone"
                  type="tel"
                  value={settings.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder="0112345678"
                />
              </div>

              <div>
                <Label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  البريد الإلكتروني
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={settings.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <Label htmlFor="website" className="block text-sm font-medium text-gray-700 mb-2">
                  الموقع الإلكتروني
                </Label>
                <Input
                  id="website"
                  type="url"
                  value={settings.website}
                  onChange={(e) => handleInputChange('website', e.target.value)}
                  placeholder="www.company.com"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-2">
                العنوان
              </Label>
              <textarea
                id="address"
                rows={3}
                value={settings.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="العنوان التفصيلي للشركة"
              />
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* إعدادات QR Code */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <QrCodeIcon className="w-5 h-5 ml-2" />
              إعدادات QR Code
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-gray-900">تفعيل المرحلة الثانية</h4>
                  <p className="text-sm text-gray-600">QR Code متوافق مع متطلبات هيئة الزكاة والضريبة</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.phase2Enabled}
                    onChange={(e) => handleInputChange('phase2Enabled', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-gray-900">تفعيل QR Code</h4>
                  <p className="text-sm text-gray-600">إضافة QR Code تلقائياً للفواتير</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.qrCodeEnabled}
                    onChange={(e) => handleInputChange('qrCodeEnabled', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-gray-900">إنشاء تلقائي</h4>
                  <p className="text-sm text-gray-600">إنشاء QR Code تلقائياً عند إنشاء الفاتورة</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.autoGenerateQR}
                    onChange={(e) => handleInputChange('autoGenerateQR', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* معلومات المطابقة */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <IdentificationIcon className="w-5 h-5 ml-2" />
              متطلبات المطابقة
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <h4 className="font-medium text-gray-900">المرحلة الثانية - متطلبات QR Code:</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-center">
                    <CheckCircleIcon className="w-4 h-4 text-green-500 ml-2" />
                    اسم البائع
                  </li>
                  <li className="flex items-center">
                    <CheckCircleIcon className="w-4 h-4 text-green-500 ml-2" />
                    الرقم الضريبي للبائع
                  </li>
                  <li className="flex items-center">
                    <CheckCircleIcon className="w-4 h-4 text-green-500 ml-2" />
                    الطابع الزمني
                  </li>
                  <li className="flex items-center">
                    <CheckCircleIcon className="w-4 h-4 text-green-500 ml-2" />
                    إجمالي الفاتورة شامل الضريبة
                  </li>
                  <li className="flex items-center">
                    <CheckCircleIcon className="w-4 h-4 text-green-500 ml-2" />
                    إجمالي ضريبة القيمة المضافة
                  </li>
                </ul>
              </div>

              <div className="space-y-3">
                <h4 className="font-medium text-gray-900">التشفير والتنسيق:</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-center">
                    <CheckCircleIcon className="w-4 h-4 text-green-500 ml-2" />
                    تنسيق TLV (Tag-Length-Value)
                  </li>
                  <li className="flex items-center">
                    <CheckCircleIcon className="w-4 h-4 text-green-500 ml-2" />
                    تشفير Base64
                  </li>
                  <li className="flex items-center">
                    <CheckCircleIcon className="w-4 h-4 text-green-500 ml-2" />
                    معايير ISO 8601 للتاريخ
                  </li>
                  <li className="flex items-center">
                    <CheckCircleIcon className="w-4 h-4 text-green-500 ml-2" />
                    التحقق من صحة الرقم الضريبي
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* أزرار الحفظ */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="flex items-center justify-end space-x-4 space-x-reverse"
      >
        <Button
          onClick={resetToDefaults}
          variant="outline"
          disabled={isSaving}
        >
          إعادة تعيين
        </Button>
        <Button
          onClick={handleSave}
          disabled={isSaving || !isValid.vatNumber}
          className="bg-blue-600 hover:bg-blue-700 text-white"
        >
          {isSaving ? (
            <div className="flex items-center">
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ml-2"></div>
              جاري الحفظ...
            </div>
          ) : (
            'حفظ الإعدادات'
          )}
        </Button>
      </motion.div>
    </div>
  );
};

export default EInvoiceSettings;
