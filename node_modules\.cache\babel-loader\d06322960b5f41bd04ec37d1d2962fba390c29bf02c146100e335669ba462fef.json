{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u0633\\u0637\\u062D \\u0627\\u0644\\u0645\\u0643\\u062A\\u0628\\\\333\\\\src\\\\components\\\\Sales\\\\SalesForm.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { XMarkIcon, PlusIcon, TrashIcon } from '@heroicons/react/24/outline';\nimport { Button } from '../ui/button';\nimport { Input } from '../ui/input';\nimport { Label } from '../ui/label';\nimport customersAPI from '../../api/customersAPI';\nimport inventoryAPI from '../../api/inventoryAPI';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SalesForm = ({\n  sale,\n  isEditing,\n  onSave,\n  onClose\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    customerId: '',\n    customerName: '',\n    items: [{\n      productId: '',\n      productName: '',\n      quantity: 1,\n      price: 0,\n      total: 0\n    }],\n    subtotal: 0,\n    tax: 0,\n    discount: 0,\n    total: 0,\n    paymentMethod: 'نقدي',\n    notes: ''\n  });\n  const [customers, setCustomers] = useState([]);\n  const [products, setProducts] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  useEffect(() => {\n    loadCustomers();\n    loadProducts();\n    if (isEditing && sale) {\n      setFormData({\n        customerId: sale.customerId,\n        customerName: sale.customerName,\n        items: sale.items || [{\n          productId: '',\n          productName: '',\n          quantity: 1,\n          price: 0,\n          total: 0\n        }],\n        subtotal: sale.subtotal,\n        tax: sale.tax,\n        discount: sale.discount,\n        total: sale.total,\n        paymentMethod: sale.paymentMethod || 'نقدي',\n        notes: sale.notes || ''\n      });\n    }\n  }, [isEditing, sale]);\n  useEffect(() => {\n    calculateTotals();\n  }, [formData.items, formData.discount]);\n  const loadCustomers = () => {\n    const customersData = customersAPI.getAllCustomers();\n    setCustomers(customersData);\n  };\n  const loadProducts = () => {\n    const productsData = inventoryAPI.getAllProducts();\n    setProducts(productsData);\n  };\n  const calculateTotals = () => {\n    const subtotal = formData.items.reduce((sum, item) => sum + item.total, 0);\n    const tax = subtotal * 0.15; // 15% ضريبة القيمة المضافة\n    const total = subtotal + tax - formData.discount;\n    setFormData(prev => ({\n      ...prev,\n      subtotal,\n      tax,\n      total: Math.max(0, total)\n    }));\n  };\n  const handleCustomerChange = customerId => {\n    const customer = customers.find(c => c.id === parseInt(customerId));\n    setFormData(prev => ({\n      ...prev,\n      customerId: parseInt(customerId),\n      customerName: customer ? customer.name : ''\n    }));\n  };\n  const handleProductChange = (index, productId) => {\n    const product = products.find(p => p.id === parseInt(productId));\n    if (product) {\n      const newItems = [...formData.items];\n      newItems[index] = {\n        ...newItems[index],\n        productId: parseInt(productId),\n        productName: product.name,\n        price: product.sellingPrice,\n        total: newItems[index].quantity * product.sellingPrice\n      };\n      setFormData(prev => ({\n        ...prev,\n        items: newItems\n      }));\n    }\n  };\n  const handleQuantityChange = (index, quantity) => {\n    const newItems = [...formData.items];\n    newItems[index] = {\n      ...newItems[index],\n      quantity: parseInt(quantity) || 0,\n      total: (parseInt(quantity) || 0) * newItems[index].price\n    };\n    setFormData(prev => ({\n      ...prev,\n      items: newItems\n    }));\n  };\n  const handlePriceChange = (index, price) => {\n    const newItems = [...formData.items];\n    newItems[index] = {\n      ...newItems[index],\n      price: parseFloat(price) || 0,\n      total: newItems[index].quantity * (parseFloat(price) || 0)\n    };\n    setFormData(prev => ({\n      ...prev,\n      items: newItems\n    }));\n  };\n  const addItem = () => {\n    setFormData(prev => ({\n      ...prev,\n      items: [...prev.items, {\n        productId: '',\n        productName: '',\n        quantity: 1,\n        price: 0,\n        total: 0\n      }]\n    }));\n  };\n  const removeItem = index => {\n    if (formData.items.length > 1) {\n      const newItems = formData.items.filter((_, i) => i !== index);\n      setFormData(prev => ({\n        ...prev,\n        items: newItems\n      }));\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!formData.customerId) {\n      toast.error('يرجى اختيار العميل');\n      return;\n    }\n    if (formData.items.length === 0 || !formData.items[0].productId) {\n      toast.error('يرجى إضافة منتج واحد على الأقل');\n      return;\n    }\n    setIsLoading(true);\n    try {\n      await onSave(formData);\n    } catch (error) {\n      toast.error('حدث خطأ أثناء حفظ الفاتورة');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('ar-SA', {\n      style: 'currency',\n      currency: 'SAR'\n    }).format(amount);\n  };\n  return /*#__PURE__*/_jsxDEV(AnimatePresence, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          scale: 0.95\n        },\n        animate: {\n          opacity: 1,\n          scale: 1\n        },\n        exit: {\n          opacity: 0,\n          scale: 0.95\n        },\n        className: \"bg-white rounded-2xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-gray-900\",\n            children: isEditing ? 'تعديل فاتورة المبيعات' : 'فاتورة مبيعات جديدة'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onClose,\n            className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg\",\n            children: /*#__PURE__*/_jsxDEV(XMarkIcon, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"p-6 space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                htmlFor: \"customer\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"\\u0627\\u0644\\u0639\\u0645\\u064A\\u0644 *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"customer\",\n                value: formData.customerId,\n                onChange: e => handleCustomerChange(e.target.value),\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0639\\u0645\\u064A\\u0644\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 19\n                }, this), customers.map(customer => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: customer.id,\n                  children: [customer.name, \" - \", customer.code]\n                }, customer.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                htmlFor: \"paymentMethod\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"\\u0637\\u0631\\u064A\\u0642\\u0629 \\u0627\\u0644\\u062F\\u0641\\u0639\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"paymentMethod\",\n                value: formData.paymentMethod,\n                onChange: e => setFormData(prev => ({\n                  ...prev,\n                  paymentMethod: e.target.value\n                })),\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\\u0646\\u0642\\u062F\\u064A\",\n                  children: \"\\u0646\\u0642\\u062F\\u064A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\\u0622\\u062C\\u0644\",\n                  children: \"\\u0622\\u062C\\u0644\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\\u0628\\u0637\\u0627\\u0642\\u0629 \\u0627\\u0626\\u062A\\u0645\\u0627\\u0646\",\n                  children: \"\\u0628\\u0637\\u0627\\u0642\\u0629 \\u0627\\u0626\\u062A\\u0645\\u0627\\u0646\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\\u062A\\u062D\\u0648\\u064A\\u0644 \\u0628\\u0646\\u0643\\u064A\",\n                  children: \"\\u062A\\u062D\\u0648\\u064A\\u0644 \\u0628\\u0646\\u0643\\u064A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                className: \"text-sm font-medium text-gray-700\",\n                children: \"\\u0627\\u0644\\u0623\\u0635\\u0646\\u0627\\u0641\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"button\",\n                onClick: addItem,\n                className: \"bg-green-600 hover:bg-green-700 text-white\",\n                size: \"sm\",\n                children: [/*#__PURE__*/_jsxDEV(PlusIcon, {\n                  className: \"w-4 h-4 ml-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 19\n                }, this), \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0635\\u0646\\u0641\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: formData.items.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-12 gap-4 items-end p-4 border border-gray-200 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-span-4\",\n                  children: [/*#__PURE__*/_jsxDEV(Label, {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"\\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: item.productId,\n                    onChange: e => handleProductChange(index, e.target.value),\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                    required: true,\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 254,\n                      columnNumber: 25\n                    }, this), products.map(product => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: product.id,\n                      children: [product.name, \" - \", formatCurrency(product.sellingPrice)]\n                    }, product.id, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 256,\n                      columnNumber: 27\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 248,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-span-2\",\n                  children: [/*#__PURE__*/_jsxDEV(Label, {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"\\u0627\\u0644\\u0643\\u0645\\u064A\\u0629\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 264,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Input, {\n                    type: \"number\",\n                    min: \"1\",\n                    value: item.quantity,\n                    onChange: e => handleQuantityChange(index, e.target.value),\n                    className: \"w-full\",\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-span-2\",\n                  children: [/*#__PURE__*/_jsxDEV(Label, {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"\\u0627\\u0644\\u0633\\u0639\\u0631\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 278,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Input, {\n                    type: \"number\",\n                    step: \"0.01\",\n                    min: \"0\",\n                    value: item.price,\n                    onChange: e => handlePriceChange(index, e.target.value),\n                    className: \"w-full\",\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 281,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-span-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Label, {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"\\u0627\\u0644\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 293,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg\",\n                    children: formatCurrency(item.total)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 296,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-span-1\",\n                  children: formData.items.length > 1 && /*#__PURE__*/_jsxDEV(Button, {\n                    type: \"button\",\n                    onClick: () => removeItem(index),\n                    variant: \"outline\",\n                    size: \"sm\",\n                    className: \"text-red-600 hover:text-red-800\",\n                    children: /*#__PURE__*/_jsxDEV(TrashIcon, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 310,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 303,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-50 p-6 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Label, {\n                  htmlFor: \"discount\",\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"\\u0627\\u0644\\u062E\\u0635\\u0645\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Input, {\n                  id: \"discount\",\n                  type: \"number\",\n                  step: \"0.01\",\n                  min: \"0\",\n                  value: formData.discount,\n                  onChange: e => setFormData(prev => ({\n                    ...prev,\n                    discount: parseFloat(e.target.value) || 0\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Label, {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"\\u0627\\u0644\\u0645\\u062C\\u0645\\u0648\\u0639 \\u0627\\u0644\\u0641\\u0631\\u0639\\u064A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"px-3 py-2 bg-white border border-gray-300 rounded-lg\",\n                  children: formatCurrency(formData.subtotal)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Label, {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"\\u0636\\u0631\\u064A\\u0628\\u0629 \\u0627\\u0644\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0636\\u0627\\u0641\\u0629 (15%)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"px-3 py-2 bg-white border border-gray-300 rounded-lg\",\n                  children: formatCurrency(formData.tax)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4 pt-4 border-t border-gray-200\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg font-semibold text-gray-900\",\n                  children: \"\\u0627\\u0644\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0646\\u0647\\u0627\\u0626\\u064A:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-2xl font-bold text-blue-600\",\n                  children: formatCurrency(formData.total)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              htmlFor: \"notes\",\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"\\u0645\\u0644\\u0627\\u062D\\u0638\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              id: \"notes\",\n              rows: 3,\n              value: formData.notes,\n              onChange: e => setFormData(prev => ({\n                ...prev,\n                notes: e.target.value\n              })),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n              placeholder: \"\\u0623\\u062F\\u062E\\u0644 \\u0623\\u064A \\u0645\\u0644\\u0627\\u062D\\u0638\\u0627\\u062A \\u0625\\u0636\\u0627\\u0641\\u064A\\u0629...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-end space-x-4 space-x-reverse pt-6 border-t border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"button\",\n              variant: \"outline\",\n              onClick: onClose,\n              disabled: isLoading,\n              children: \"\\u0625\\u0644\\u063A\\u0627\\u0621\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"submit\",\n              disabled: isLoading,\n              className: \"bg-blue-600 hover:bg-blue-700 text-white\",\n              children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ml-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 21\n                }, this), \"\\u062C\\u0627\\u0631\\u064A \\u0627\\u0644\\u062D\\u0641\\u0638...\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 19\n              }, this) : isEditing ? 'حفظ التعديلات' : 'حفظ الفاتورة'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 163,\n    columnNumber: 5\n  }, this);\n};\n_s(SalesForm, \"/4h7gHoLDZQHQKMWyJ2RlkDt0Bg=\");\n_c = SalesForm;\nexport default SalesForm;\nvar _c;\n$RefreshReg$(_c, \"SalesForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "AnimatePresence", "XMarkIcon", "PlusIcon", "TrashIcon", "<PERSON><PERSON>", "Input", "Label", "customersAPI", "inventoryAPI", "toast", "jsxDEV", "_jsxDEV", "SalesForm", "sale", "isEditing", "onSave", "onClose", "_s", "formData", "setFormData", "customerId", "customerName", "items", "productId", "productName", "quantity", "price", "total", "subtotal", "tax", "discount", "paymentMethod", "notes", "customers", "setCustomers", "products", "setProducts", "isLoading", "setIsLoading", "loadCustomers", "loadProducts", "calculateTotals", "customersData", "getAllCustomers", "productsData", "getAllProducts", "reduce", "sum", "item", "prev", "Math", "max", "handleCustomerChange", "customer", "find", "c", "id", "parseInt", "name", "handleProductChange", "index", "product", "p", "newItems", "sellingPrice", "handleQuantityChange", "handlePriceChange", "parseFloat", "addItem", "removeItem", "length", "filter", "_", "i", "handleSubmit", "e", "preventDefault", "error", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "children", "className", "div", "initial", "opacity", "scale", "animate", "exit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onSubmit", "htmlFor", "value", "onChange", "target", "required", "map", "code", "type", "size", "min", "step", "variant", "rows", "placeholder", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/src/components/Sales/SalesForm.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { XMarkIcon, PlusIcon, TrashIcon } from '@heroicons/react/24/outline';\nimport { Button } from '../ui/button';\nimport { Input } from '../ui/input';\nimport { Label } from '../ui/label';\nimport customersAPI from '../../api/customersAPI';\nimport inventoryAPI from '../../api/inventoryAPI';\nimport toast from 'react-hot-toast';\n\nconst SalesForm = ({ sale, isEditing, onSave, onClose }) => {\n  const [formData, setFormData] = useState({\n    customerId: '',\n    customerName: '',\n    items: [{ productId: '', productName: '', quantity: 1, price: 0, total: 0 }],\n    subtotal: 0,\n    tax: 0,\n    discount: 0,\n    total: 0,\n    paymentMethod: 'نقدي',\n    notes: ''\n  });\n\n  const [customers, setCustomers] = useState([]);\n  const [products, setProducts] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n\n  useEffect(() => {\n    loadCustomers();\n    loadProducts();\n    \n    if (isEditing && sale) {\n      setFormData({\n        customerId: sale.customerId,\n        customerName: sale.customerName,\n        items: sale.items || [{ productId: '', productName: '', quantity: 1, price: 0, total: 0 }],\n        subtotal: sale.subtotal,\n        tax: sale.tax,\n        discount: sale.discount,\n        total: sale.total,\n        paymentMethod: sale.paymentMethod || 'نقدي',\n        notes: sale.notes || ''\n      });\n    }\n  }, [isEditing, sale]);\n\n  useEffect(() => {\n    calculateTotals();\n  }, [formData.items, formData.discount]);\n\n  const loadCustomers = () => {\n    const customersData = customersAPI.getAllCustomers();\n    setCustomers(customersData);\n  };\n\n  const loadProducts = () => {\n    const productsData = inventoryAPI.getAllProducts();\n    setProducts(productsData);\n  };\n\n  const calculateTotals = () => {\n    const subtotal = formData.items.reduce((sum, item) => sum + item.total, 0);\n    const tax = subtotal * 0.15; // 15% ضريبة القيمة المضافة\n    const total = subtotal + tax - formData.discount;\n\n    setFormData(prev => ({\n      ...prev,\n      subtotal,\n      tax,\n      total: Math.max(0, total)\n    }));\n  };\n\n  const handleCustomerChange = (customerId) => {\n    const customer = customers.find(c => c.id === parseInt(customerId));\n    setFormData(prev => ({\n      ...prev,\n      customerId: parseInt(customerId),\n      customerName: customer ? customer.name : ''\n    }));\n  };\n\n  const handleProductChange = (index, productId) => {\n    const product = products.find(p => p.id === parseInt(productId));\n    if (product) {\n      const newItems = [...formData.items];\n      newItems[index] = {\n        ...newItems[index],\n        productId: parseInt(productId),\n        productName: product.name,\n        price: product.sellingPrice,\n        total: newItems[index].quantity * product.sellingPrice\n      };\n      setFormData(prev => ({ ...prev, items: newItems }));\n    }\n  };\n\n  const handleQuantityChange = (index, quantity) => {\n    const newItems = [...formData.items];\n    newItems[index] = {\n      ...newItems[index],\n      quantity: parseInt(quantity) || 0,\n      total: (parseInt(quantity) || 0) * newItems[index].price\n    };\n    setFormData(prev => ({ ...prev, items: newItems }));\n  };\n\n  const handlePriceChange = (index, price) => {\n    const newItems = [...formData.items];\n    newItems[index] = {\n      ...newItems[index],\n      price: parseFloat(price) || 0,\n      total: newItems[index].quantity * (parseFloat(price) || 0)\n    };\n    setFormData(prev => ({ ...prev, items: newItems }));\n  };\n\n  const addItem = () => {\n    setFormData(prev => ({\n      ...prev,\n      items: [...prev.items, { productId: '', productName: '', quantity: 1, price: 0, total: 0 }]\n    }));\n  };\n\n  const removeItem = (index) => {\n    if (formData.items.length > 1) {\n      const newItems = formData.items.filter((_, i) => i !== index);\n      setFormData(prev => ({ ...prev, items: newItems }));\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!formData.customerId) {\n      toast.error('يرجى اختيار العميل');\n      return;\n    }\n\n    if (formData.items.length === 0 || !formData.items[0].productId) {\n      toast.error('يرجى إضافة منتج واحد على الأقل');\n      return;\n    }\n\n    setIsLoading(true);\n    try {\n      await onSave(formData);\n    } catch (error) {\n      toast.error('حدث خطأ أثناء حفظ الفاتورة');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('ar-SA', {\n      style: 'currency',\n      currency: 'SAR'\n    }).format(amount);\n  };\n\n  return (\n    <AnimatePresence>\n      <div className=\"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50\">\n        <motion.div\n          initial={{ opacity: 0, scale: 0.95 }}\n          animate={{ opacity: 1, scale: 1 }}\n          exit={{ opacity: 0, scale: 0.95 }}\n          className=\"bg-white rounded-2xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\"\n        >\n          {/* الرأس */}\n          <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n            <h2 className=\"text-xl font-semibold text-gray-900\">\n              {isEditing ? 'تعديل فاتورة المبيعات' : 'فاتورة مبيعات جديدة'}\n            </h2>\n            <button\n              onClick={onClose}\n              className=\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg\"\n            >\n              <XMarkIcon className=\"w-5 h-5\" />\n            </button>\n          </div>\n\n          {/* المحتوى */}\n          <form onSubmit={handleSubmit} className=\"p-6 space-y-6\">\n            {/* معلومات العميل */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <Label htmlFor=\"customer\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  العميل *\n                </Label>\n                <select\n                  id=\"customer\"\n                  value={formData.customerId}\n                  onChange={(e) => handleCustomerChange(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  required\n                >\n                  <option value=\"\">اختر العميل</option>\n                  {customers.map((customer) => (\n                    <option key={customer.id} value={customer.id}>\n                      {customer.name} - {customer.code}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              <div>\n                <Label htmlFor=\"paymentMethod\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  طريقة الدفع\n                </Label>\n                <select\n                  id=\"paymentMethod\"\n                  value={formData.paymentMethod}\n                  onChange={(e) => setFormData(prev => ({ ...prev, paymentMethod: e.target.value }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"نقدي\">نقدي</option>\n                  <option value=\"آجل\">آجل</option>\n                  <option value=\"بطاقة ائتمان\">بطاقة ائتمان</option>\n                  <option value=\"تحويل بنكي\">تحويل بنكي</option>\n                </select>\n              </div>\n            </div>\n\n            {/* الأصناف */}\n            <div>\n              <div className=\"flex items-center justify-between mb-4\">\n                <Label className=\"text-sm font-medium text-gray-700\">الأصناف</Label>\n                <Button\n                  type=\"button\"\n                  onClick={addItem}\n                  className=\"bg-green-600 hover:bg-green-700 text-white\"\n                  size=\"sm\"\n                >\n                  <PlusIcon className=\"w-4 h-4 ml-2\" />\n                  إضافة صنف\n                </Button>\n              </div>\n\n              <div className=\"space-y-4\">\n                {formData.items.map((item, index) => (\n                  <div key={index} className=\"grid grid-cols-12 gap-4 items-end p-4 border border-gray-200 rounded-lg\">\n                    <div className=\"col-span-4\">\n                      <Label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        المنتج\n                      </Label>\n                      <select\n                        value={item.productId}\n                        onChange={(e) => handleProductChange(index, e.target.value)}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                        required\n                      >\n                        <option value=\"\">اختر المنتج</option>\n                        {products.map((product) => (\n                          <option key={product.id} value={product.id}>\n                            {product.name} - {formatCurrency(product.sellingPrice)}\n                          </option>\n                        ))}\n                      </select>\n                    </div>\n\n                    <div className=\"col-span-2\">\n                      <Label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        الكمية\n                      </Label>\n                      <Input\n                        type=\"number\"\n                        min=\"1\"\n                        value={item.quantity}\n                        onChange={(e) => handleQuantityChange(index, e.target.value)}\n                        className=\"w-full\"\n                        required\n                      />\n                    </div>\n\n                    <div className=\"col-span-2\">\n                      <Label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        السعر\n                      </Label>\n                      <Input\n                        type=\"number\"\n                        step=\"0.01\"\n                        min=\"0\"\n                        value={item.price}\n                        onChange={(e) => handlePriceChange(index, e.target.value)}\n                        className=\"w-full\"\n                        required\n                      />\n                    </div>\n\n                    <div className=\"col-span-3\">\n                      <Label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        الإجمالي\n                      </Label>\n                      <div className=\"px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg\">\n                        {formatCurrency(item.total)}\n                      </div>\n                    </div>\n\n                    <div className=\"col-span-1\">\n                      {formData.items.length > 1 && (\n                        <Button\n                          type=\"button\"\n                          onClick={() => removeItem(index)}\n                          variant=\"outline\"\n                          size=\"sm\"\n                          className=\"text-red-600 hover:text-red-800\"\n                        >\n                          <TrashIcon className=\"w-4 h-4\" />\n                        </Button>\n                      )}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* الإجماليات */}\n            <div className=\"bg-gray-50 p-6 rounded-lg\">\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                <div>\n                  <Label htmlFor=\"discount\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    الخصم\n                  </Label>\n                  <Input\n                    id=\"discount\"\n                    type=\"number\"\n                    step=\"0.01\"\n                    min=\"0\"\n                    value={formData.discount}\n                    onChange={(e) => setFormData(prev => ({ ...prev, discount: parseFloat(e.target.value) || 0 }))}\n                  />\n                </div>\n\n                <div>\n                  <Label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    المجموع الفرعي\n                  </Label>\n                  <div className=\"px-3 py-2 bg-white border border-gray-300 rounded-lg\">\n                    {formatCurrency(formData.subtotal)}\n                  </div>\n                </div>\n\n                <div>\n                  <Label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    ضريبة القيمة المضافة (15%)\n                  </Label>\n                  <div className=\"px-3 py-2 bg-white border border-gray-300 rounded-lg\">\n                    {formatCurrency(formData.tax)}\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"mt-4 pt-4 border-t border-gray-200\">\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-lg font-semibold text-gray-900\">الإجمالي النهائي:</span>\n                  <span className=\"text-2xl font-bold text-blue-600\">\n                    {formatCurrency(formData.total)}\n                  </span>\n                </div>\n              </div>\n            </div>\n\n            {/* الملاحظات */}\n            <div>\n              <Label htmlFor=\"notes\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                ملاحظات\n              </Label>\n              <textarea\n                id=\"notes\"\n                rows={3}\n                value={formData.notes}\n                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"أدخل أي ملاحظات إضافية...\"\n              />\n            </div>\n\n            {/* الأزرار */}\n            <div className=\"flex items-center justify-end space-x-4 space-x-reverse pt-6 border-t border-gray-200\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={onClose}\n                disabled={isLoading}\n              >\n                إلغاء\n              </Button>\n              <Button\n                type=\"submit\"\n                disabled={isLoading}\n                className=\"bg-blue-600 hover:bg-blue-700 text-white\"\n              >\n                {isLoading ? (\n                  <div className=\"flex items-center\">\n                    <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ml-2\"></div>\n                    جاري الحفظ...\n                  </div>\n                ) : (\n                  isEditing ? 'حفظ التعديلات' : 'حفظ الفاتورة'\n                )}\n              </Button>\n            </div>\n          </form>\n        </motion.div>\n      </div>\n    </AnimatePresence>\n  );\n};\n\nexport default SalesForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,6BAA6B;AAC5E,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,KAAK,QAAQ,aAAa;AACnC,OAAOC,YAAY,MAAM,wBAAwB;AACjD,OAAOC,YAAY,MAAM,wBAAwB;AACjD,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,SAAS,GAAGA,CAAC;EAAEC,IAAI;EAAEC,SAAS;EAAEC,MAAM;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC1D,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC;IACvCuB,UAAU,EAAE,EAAE;IACdC,YAAY,EAAE,EAAE;IAChBC,KAAK,EAAE,CAAC;MAAEC,SAAS,EAAE,EAAE;MAAEC,WAAW,EAAE,EAAE;MAAEC,QAAQ,EAAE,CAAC;MAAEC,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAE,CAAC,CAAC;IAC5EC,QAAQ,EAAE,CAAC;IACXC,GAAG,EAAE,CAAC;IACNC,QAAQ,EAAE,CAAC;IACXH,KAAK,EAAE,CAAC;IACRI,aAAa,EAAE,MAAM;IACrBC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACsC,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACwC,SAAS,EAAEC,YAAY,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAEjDC,SAAS,CAAC,MAAM;IACdyC,aAAa,CAAC,CAAC;IACfC,YAAY,CAAC,CAAC;IAEd,IAAI1B,SAAS,IAAID,IAAI,EAAE;MACrBM,WAAW,CAAC;QACVC,UAAU,EAAEP,IAAI,CAACO,UAAU;QAC3BC,YAAY,EAAER,IAAI,CAACQ,YAAY;QAC/BC,KAAK,EAAET,IAAI,CAACS,KAAK,IAAI,CAAC;UAAEC,SAAS,EAAE,EAAE;UAAEC,WAAW,EAAE,EAAE;UAAEC,QAAQ,EAAE,CAAC;UAAEC,KAAK,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAE,CAAC,CAAC;QAC1FC,QAAQ,EAAEf,IAAI,CAACe,QAAQ;QACvBC,GAAG,EAAEhB,IAAI,CAACgB,GAAG;QACbC,QAAQ,EAAEjB,IAAI,CAACiB,QAAQ;QACvBH,KAAK,EAAEd,IAAI,CAACc,KAAK;QACjBI,aAAa,EAAElB,IAAI,CAACkB,aAAa,IAAI,MAAM;QAC3CC,KAAK,EAAEnB,IAAI,CAACmB,KAAK,IAAI;MACvB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAClB,SAAS,EAAED,IAAI,CAAC,CAAC;EAErBf,SAAS,CAAC,MAAM;IACd2C,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACvB,QAAQ,CAACI,KAAK,EAAEJ,QAAQ,CAACY,QAAQ,CAAC,CAAC;EAEvC,MAAMS,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMG,aAAa,GAAGnC,YAAY,CAACoC,eAAe,CAAC,CAAC;IACpDT,YAAY,CAACQ,aAAa,CAAC;EAC7B,CAAC;EAED,MAAMF,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMI,YAAY,GAAGpC,YAAY,CAACqC,cAAc,CAAC,CAAC;IAClDT,WAAW,CAACQ,YAAY,CAAC;EAC3B,CAAC;EAED,MAAMH,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMb,QAAQ,GAAGV,QAAQ,CAACI,KAAK,CAACwB,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,CAACrB,KAAK,EAAE,CAAC,CAAC;IAC1E,MAAME,GAAG,GAAGD,QAAQ,GAAG,IAAI,CAAC,CAAC;IAC7B,MAAMD,KAAK,GAAGC,QAAQ,GAAGC,GAAG,GAAGX,QAAQ,CAACY,QAAQ;IAEhDX,WAAW,CAAC8B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPrB,QAAQ;MACRC,GAAG;MACHF,KAAK,EAAEuB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAExB,KAAK;IAC1B,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMyB,oBAAoB,GAAIhC,UAAU,IAAK;IAC3C,MAAMiC,QAAQ,GAAGpB,SAAS,CAACqB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKC,QAAQ,CAACrC,UAAU,CAAC,CAAC;IACnED,WAAW,CAAC8B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP7B,UAAU,EAAEqC,QAAQ,CAACrC,UAAU,CAAC;MAChCC,YAAY,EAAEgC,QAAQ,GAAGA,QAAQ,CAACK,IAAI,GAAG;IAC3C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,mBAAmB,GAAGA,CAACC,KAAK,EAAErC,SAAS,KAAK;IAChD,MAAMsC,OAAO,GAAG1B,QAAQ,CAACmB,IAAI,CAACQ,CAAC,IAAIA,CAAC,CAACN,EAAE,KAAKC,QAAQ,CAAClC,SAAS,CAAC,CAAC;IAChE,IAAIsC,OAAO,EAAE;MACX,MAAME,QAAQ,GAAG,CAAC,GAAG7C,QAAQ,CAACI,KAAK,CAAC;MACpCyC,QAAQ,CAACH,KAAK,CAAC,GAAG;QAChB,GAAGG,QAAQ,CAACH,KAAK,CAAC;QAClBrC,SAAS,EAAEkC,QAAQ,CAAClC,SAAS,CAAC;QAC9BC,WAAW,EAAEqC,OAAO,CAACH,IAAI;QACzBhC,KAAK,EAAEmC,OAAO,CAACG,YAAY;QAC3BrC,KAAK,EAAEoC,QAAQ,CAACH,KAAK,CAAC,CAACnC,QAAQ,GAAGoC,OAAO,CAACG;MAC5C,CAAC;MACD7C,WAAW,CAAC8B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE3B,KAAK,EAAEyC;MAAS,CAAC,CAAC,CAAC;IACrD;EACF,CAAC;EAED,MAAME,oBAAoB,GAAGA,CAACL,KAAK,EAAEnC,QAAQ,KAAK;IAChD,MAAMsC,QAAQ,GAAG,CAAC,GAAG7C,QAAQ,CAACI,KAAK,CAAC;IACpCyC,QAAQ,CAACH,KAAK,CAAC,GAAG;MAChB,GAAGG,QAAQ,CAACH,KAAK,CAAC;MAClBnC,QAAQ,EAAEgC,QAAQ,CAAChC,QAAQ,CAAC,IAAI,CAAC;MACjCE,KAAK,EAAE,CAAC8B,QAAQ,CAAChC,QAAQ,CAAC,IAAI,CAAC,IAAIsC,QAAQ,CAACH,KAAK,CAAC,CAAClC;IACrD,CAAC;IACDP,WAAW,CAAC8B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE3B,KAAK,EAAEyC;IAAS,CAAC,CAAC,CAAC;EACrD,CAAC;EAED,MAAMG,iBAAiB,GAAGA,CAACN,KAAK,EAAElC,KAAK,KAAK;IAC1C,MAAMqC,QAAQ,GAAG,CAAC,GAAG7C,QAAQ,CAACI,KAAK,CAAC;IACpCyC,QAAQ,CAACH,KAAK,CAAC,GAAG;MAChB,GAAGG,QAAQ,CAACH,KAAK,CAAC;MAClBlC,KAAK,EAAEyC,UAAU,CAACzC,KAAK,CAAC,IAAI,CAAC;MAC7BC,KAAK,EAAEoC,QAAQ,CAACH,KAAK,CAAC,CAACnC,QAAQ,IAAI0C,UAAU,CAACzC,KAAK,CAAC,IAAI,CAAC;IAC3D,CAAC;IACDP,WAAW,CAAC8B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE3B,KAAK,EAAEyC;IAAS,CAAC,CAAC,CAAC;EACrD,CAAC;EAED,MAAMK,OAAO,GAAGA,CAAA,KAAM;IACpBjD,WAAW,CAAC8B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP3B,KAAK,EAAE,CAAC,GAAG2B,IAAI,CAAC3B,KAAK,EAAE;QAAEC,SAAS,EAAE,EAAE;QAAEC,WAAW,EAAE,EAAE;QAAEC,QAAQ,EAAE,CAAC;QAAEC,KAAK,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAE,CAAC;IAC5F,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM0C,UAAU,GAAIT,KAAK,IAAK;IAC5B,IAAI1C,QAAQ,CAACI,KAAK,CAACgD,MAAM,GAAG,CAAC,EAAE;MAC7B,MAAMP,QAAQ,GAAG7C,QAAQ,CAACI,KAAK,CAACiD,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKb,KAAK,CAAC;MAC7DzC,WAAW,CAAC8B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE3B,KAAK,EAAEyC;MAAS,CAAC,CAAC,CAAC;IACrD;EACF,CAAC;EAED,MAAMW,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAAC1D,QAAQ,CAACE,UAAU,EAAE;MACxBX,KAAK,CAACoE,KAAK,CAAC,oBAAoB,CAAC;MACjC;IACF;IAEA,IAAI3D,QAAQ,CAACI,KAAK,CAACgD,MAAM,KAAK,CAAC,IAAI,CAACpD,QAAQ,CAACI,KAAK,CAAC,CAAC,CAAC,CAACC,SAAS,EAAE;MAC/Dd,KAAK,CAACoE,KAAK,CAAC,gCAAgC,CAAC;MAC7C;IACF;IAEAvC,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF,MAAMvB,MAAM,CAACG,QAAQ,CAAC;IACxB,CAAC,CAAC,OAAO2D,KAAK,EAAE;MACdpE,KAAK,CAACoE,KAAK,CAAC,4BAA4B,CAAC;IAC3C,CAAC,SAAS;MACRvC,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMwC,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,oBACEpE,OAAA,CAACX,eAAe;IAAAqF,QAAA,eACd1E,OAAA;MAAK2E,SAAS,EAAC,gFAAgF;MAAAD,QAAA,eAC7F1E,OAAA,CAACZ,MAAM,CAACwF,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAK,CAAE;QACrCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAE,CAAE;QAClCE,IAAI,EAAE;UAAEH,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAK,CAAE;QAClCJ,SAAS,EAAC,8EAA8E;QAAAD,QAAA,gBAGxF1E,OAAA;UAAK2E,SAAS,EAAC,gEAAgE;UAAAD,QAAA,gBAC7E1E,OAAA;YAAI2E,SAAS,EAAC,qCAAqC;YAAAD,QAAA,EAChDvE,SAAS,GAAG,uBAAuB,GAAG;UAAqB;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,eACLrF,OAAA;YACEsF,OAAO,EAAEjF,OAAQ;YACjBsE,SAAS,EAAC,oEAAoE;YAAAD,QAAA,eAE9E1E,OAAA,CAACV,SAAS;cAACqF,SAAS,EAAC;YAAS;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNrF,OAAA;UAAMuF,QAAQ,EAAExB,YAAa;UAACY,SAAS,EAAC,eAAe;UAAAD,QAAA,gBAErD1E,OAAA;YAAK2E,SAAS,EAAC,uCAAuC;YAAAD,QAAA,gBACpD1E,OAAA;cAAA0E,QAAA,gBACE1E,OAAA,CAACL,KAAK;gBAAC6F,OAAO,EAAC,UAAU;gBAACb,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EAAC;cAEnF;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRrF,OAAA;gBACE6C,EAAE,EAAC,UAAU;gBACb4C,KAAK,EAAElF,QAAQ,CAACE,UAAW;gBAC3BiF,QAAQ,EAAG1B,CAAC,IAAKvB,oBAAoB,CAACuB,CAAC,CAAC2B,MAAM,CAACF,KAAK,CAAE;gBACtDd,SAAS,EAAC,2GAA2G;gBACrHiB,QAAQ;gBAAAlB,QAAA,gBAER1E,OAAA;kBAAQyF,KAAK,EAAC,EAAE;kBAAAf,QAAA,EAAC;gBAAW;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACpC/D,SAAS,CAACuE,GAAG,CAAEnD,QAAQ,iBACtB1C,OAAA;kBAA0ByF,KAAK,EAAE/C,QAAQ,CAACG,EAAG;kBAAA6B,QAAA,GAC1ChC,QAAQ,CAACK,IAAI,EAAC,KAAG,EAACL,QAAQ,CAACoD,IAAI;gBAAA,GADrBpD,QAAQ,CAACG,EAAE;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENrF,OAAA;cAAA0E,QAAA,gBACE1E,OAAA,CAACL,KAAK;gBAAC6F,OAAO,EAAC,eAAe;gBAACb,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,EAAC;cAExF;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRrF,OAAA;gBACE6C,EAAE,EAAC,eAAe;gBAClB4C,KAAK,EAAElF,QAAQ,CAACa,aAAc;gBAC9BsE,QAAQ,EAAG1B,CAAC,IAAKxD,WAAW,CAAC8B,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAElB,aAAa,EAAE4C,CAAC,CAAC2B,MAAM,CAACF;gBAAM,CAAC,CAAC,CAAE;gBACnFd,SAAS,EAAC,2GAA2G;gBAAAD,QAAA,gBAErH1E,OAAA;kBAAQyF,KAAK,EAAC,0BAAM;kBAAAf,QAAA,EAAC;gBAAI;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCrF,OAAA;kBAAQyF,KAAK,EAAC,oBAAK;kBAAAf,QAAA,EAAC;gBAAG;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChCrF,OAAA;kBAAQyF,KAAK,EAAC,qEAAc;kBAAAf,QAAA,EAAC;gBAAY;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClDrF,OAAA;kBAAQyF,KAAK,EAAC,yDAAY;kBAAAf,QAAA,EAAC;gBAAU;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNrF,OAAA;YAAA0E,QAAA,gBACE1E,OAAA;cAAK2E,SAAS,EAAC,wCAAwC;cAAAD,QAAA,gBACrD1E,OAAA,CAACL,KAAK;gBAACgF,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,EAAC;cAAO;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpErF,OAAA,CAACP,MAAM;gBACLsG,IAAI,EAAC,QAAQ;gBACbT,OAAO,EAAE7B,OAAQ;gBACjBkB,SAAS,EAAC,4CAA4C;gBACtDqB,IAAI,EAAC,IAAI;gBAAAtB,QAAA,gBAET1E,OAAA,CAACT,QAAQ;kBAACoF,SAAS,EAAC;gBAAc;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,qDAEvC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENrF,OAAA;cAAK2E,SAAS,EAAC,WAAW;cAAAD,QAAA,EACvBnE,QAAQ,CAACI,KAAK,CAACkF,GAAG,CAAC,CAACxD,IAAI,EAAEY,KAAK,kBAC9BjD,OAAA;gBAAiB2E,SAAS,EAAC,yEAAyE;gBAAAD,QAAA,gBAClG1E,OAAA;kBAAK2E,SAAS,EAAC,YAAY;kBAAAD,QAAA,gBACzB1E,OAAA,CAACL,KAAK;oBAACgF,SAAS,EAAC,8CAA8C;oBAAAD,QAAA,EAAC;kBAEhE;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRrF,OAAA;oBACEyF,KAAK,EAAEpD,IAAI,CAACzB,SAAU;oBACtB8E,QAAQ,EAAG1B,CAAC,IAAKhB,mBAAmB,CAACC,KAAK,EAAEe,CAAC,CAAC2B,MAAM,CAACF,KAAK,CAAE;oBAC5Dd,SAAS,EAAC,2GAA2G;oBACrHiB,QAAQ;oBAAAlB,QAAA,gBAER1E,OAAA;sBAAQyF,KAAK,EAAC,EAAE;sBAAAf,QAAA,EAAC;oBAAW;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EACpC7D,QAAQ,CAACqE,GAAG,CAAE3C,OAAO,iBACpBlD,OAAA;sBAAyByF,KAAK,EAAEvC,OAAO,CAACL,EAAG;sBAAA6B,QAAA,GACxCxB,OAAO,CAACH,IAAI,EAAC,KAAG,EAACoB,cAAc,CAACjB,OAAO,CAACG,YAAY,CAAC;oBAAA,GAD3CH,OAAO,CAACL,EAAE;sBAAAqC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEf,CACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAENrF,OAAA;kBAAK2E,SAAS,EAAC,YAAY;kBAAAD,QAAA,gBACzB1E,OAAA,CAACL,KAAK;oBAACgF,SAAS,EAAC,8CAA8C;oBAAAD,QAAA,EAAC;kBAEhE;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRrF,OAAA,CAACN,KAAK;oBACJqG,IAAI,EAAC,QAAQ;oBACbE,GAAG,EAAC,GAAG;oBACPR,KAAK,EAAEpD,IAAI,CAACvB,QAAS;oBACrB4E,QAAQ,EAAG1B,CAAC,IAAKV,oBAAoB,CAACL,KAAK,EAAEe,CAAC,CAAC2B,MAAM,CAACF,KAAK,CAAE;oBAC7Dd,SAAS,EAAC,QAAQ;oBAClBiB,QAAQ;kBAAA;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENrF,OAAA;kBAAK2E,SAAS,EAAC,YAAY;kBAAAD,QAAA,gBACzB1E,OAAA,CAACL,KAAK;oBAACgF,SAAS,EAAC,8CAA8C;oBAAAD,QAAA,EAAC;kBAEhE;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRrF,OAAA,CAACN,KAAK;oBACJqG,IAAI,EAAC,QAAQ;oBACbG,IAAI,EAAC,MAAM;oBACXD,GAAG,EAAC,GAAG;oBACPR,KAAK,EAAEpD,IAAI,CAACtB,KAAM;oBAClB2E,QAAQ,EAAG1B,CAAC,IAAKT,iBAAiB,CAACN,KAAK,EAAEe,CAAC,CAAC2B,MAAM,CAACF,KAAK,CAAE;oBAC1Dd,SAAS,EAAC,QAAQ;oBAClBiB,QAAQ;kBAAA;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENrF,OAAA;kBAAK2E,SAAS,EAAC,YAAY;kBAAAD,QAAA,gBACzB1E,OAAA,CAACL,KAAK;oBAACgF,SAAS,EAAC,8CAA8C;oBAAAD,QAAA,EAAC;kBAEhE;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRrF,OAAA;oBAAK2E,SAAS,EAAC,wDAAwD;oBAAAD,QAAA,EACpEP,cAAc,CAAC9B,IAAI,CAACrB,KAAK;kBAAC;oBAAAkE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENrF,OAAA;kBAAK2E,SAAS,EAAC,YAAY;kBAAAD,QAAA,EACxBnE,QAAQ,CAACI,KAAK,CAACgD,MAAM,GAAG,CAAC,iBACxB3D,OAAA,CAACP,MAAM;oBACLsG,IAAI,EAAC,QAAQ;oBACbT,OAAO,EAAEA,CAAA,KAAM5B,UAAU,CAACT,KAAK,CAAE;oBACjCkD,OAAO,EAAC,SAAS;oBACjBH,IAAI,EAAC,IAAI;oBACTrB,SAAS,EAAC,iCAAiC;oBAAAD,QAAA,eAE3C1E,OAAA,CAACR,SAAS;sBAACmF,SAAS,EAAC;oBAAS;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B;gBACT;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA,GAtEEpC,KAAK;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAuEV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNrF,OAAA;YAAK2E,SAAS,EAAC,2BAA2B;YAAAD,QAAA,gBACxC1E,OAAA;cAAK2E,SAAS,EAAC,uCAAuC;cAAAD,QAAA,gBACpD1E,OAAA;gBAAA0E,QAAA,gBACE1E,OAAA,CAACL,KAAK;kBAAC6F,OAAO,EAAC,UAAU;kBAACb,SAAS,EAAC,8CAA8C;kBAAAD,QAAA,EAAC;gBAEnF;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRrF,OAAA,CAACN,KAAK;kBACJmD,EAAE,EAAC,UAAU;kBACbkD,IAAI,EAAC,QAAQ;kBACbG,IAAI,EAAC,MAAM;kBACXD,GAAG,EAAC,GAAG;kBACPR,KAAK,EAAElF,QAAQ,CAACY,QAAS;kBACzBuE,QAAQ,EAAG1B,CAAC,IAAKxD,WAAW,CAAC8B,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEnB,QAAQ,EAAEqC,UAAU,CAACQ,CAAC,CAAC2B,MAAM,CAACF,KAAK,CAAC,IAAI;kBAAE,CAAC,CAAC;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENrF,OAAA;gBAAA0E,QAAA,gBACE1E,OAAA,CAACL,KAAK;kBAACgF,SAAS,EAAC,8CAA8C;kBAAAD,QAAA,EAAC;gBAEhE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRrF,OAAA;kBAAK2E,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,EAClEP,cAAc,CAAC5D,QAAQ,CAACU,QAAQ;gBAAC;kBAAAiE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENrF,OAAA;gBAAA0E,QAAA,gBACE1E,OAAA,CAACL,KAAK;kBAACgF,SAAS,EAAC,8CAA8C;kBAAAD,QAAA,EAAC;gBAEhE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRrF,OAAA;kBAAK2E,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,EAClEP,cAAc,CAAC5D,QAAQ,CAACW,GAAG;gBAAC;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENrF,OAAA;cAAK2E,SAAS,EAAC,oCAAoC;cAAAD,QAAA,eACjD1E,OAAA;gBAAK2E,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,gBAChD1E,OAAA;kBAAM2E,SAAS,EAAC,qCAAqC;kBAAAD,QAAA,EAAC;gBAAiB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9ErF,OAAA;kBAAM2E,SAAS,EAAC,kCAAkC;kBAAAD,QAAA,EAC/CP,cAAc,CAAC5D,QAAQ,CAACS,KAAK;gBAAC;kBAAAkE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNrF,OAAA;YAAA0E,QAAA,gBACE1E,OAAA,CAACL,KAAK;cAAC6F,OAAO,EAAC,OAAO;cAACb,SAAS,EAAC,8CAA8C;cAAAD,QAAA,EAAC;YAEhF;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRrF,OAAA;cACE6C,EAAE,EAAC,OAAO;cACVuD,IAAI,EAAE,CAAE;cACRX,KAAK,EAAElF,QAAQ,CAACc,KAAM;cACtBqE,QAAQ,EAAG1B,CAAC,IAAKxD,WAAW,CAAC8B,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEjB,KAAK,EAAE2C,CAAC,CAAC2B,MAAM,CAACF;cAAM,CAAC,CAAC,CAAE;cAC3Ed,SAAS,EAAC,2GAA2G;cACrH0B,WAAW,EAAC;YAA2B;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNrF,OAAA;YAAK2E,SAAS,EAAC,uFAAuF;YAAAD,QAAA,gBACpG1E,OAAA,CAACP,MAAM;cACLsG,IAAI,EAAC,QAAQ;cACbI,OAAO,EAAC,SAAS;cACjBb,OAAO,EAAEjF,OAAQ;cACjBiG,QAAQ,EAAE5E,SAAU;cAAAgD,QAAA,EACrB;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTrF,OAAA,CAACP,MAAM;cACLsG,IAAI,EAAC,QAAQ;cACbO,QAAQ,EAAE5E,SAAU;cACpBiD,SAAS,EAAC,0CAA0C;cAAAD,QAAA,EAEnDhD,SAAS,gBACR1B,OAAA;gBAAK2E,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,gBAChC1E,OAAA;kBAAK2E,SAAS,EAAC;gBAAmF;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,8DAE3G;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,GAENlF,SAAS,GAAG,eAAe,GAAG;YAC/B;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACS,CAAC;AAEtB,CAAC;AAAC/E,EAAA,CA/YIL,SAAS;AAAAsG,EAAA,GAATtG,SAAS;AAiZf,eAAeA,SAAS;AAAC,IAAAsG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}