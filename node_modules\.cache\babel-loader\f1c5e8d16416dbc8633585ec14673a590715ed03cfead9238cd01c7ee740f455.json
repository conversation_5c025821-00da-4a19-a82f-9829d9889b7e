{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u0633\\u0637\\u062D \\u0627\\u0644\\u0645\\u0643\\u062A\\u0628\\\\333\\\\src\\\\pages\\\\Settings.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { UserIcon, BellIcon, ShieldCheckIcon, GlobeAltIcon, PaintBrushIcon, CircleStackIcon, QrCodeIcon } from '@heroicons/react/24/outline';\nimport { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';\nimport { Button } from '../components/ui/button';\nimport { Input } from '../components/ui/input';\nimport { Label } from '../components/ui/label';\nimport { useAuth } from '../contexts/AuthContext';\nimport EInvoiceSettings from '../components/Settings/EInvoiceSettings';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Settings = () => {\n  _s();\n  var _tabs$find;\n  const {\n    user,\n    updateUser\n  } = useAuth();\n  const [activeTab, setActiveTab] = useState('profile');\n  const [settings, setSettings] = useState({\n    profile: {\n      name: (user === null || user === void 0 ? void 0 : user.name) || '',\n      email: (user === null || user === void 0 ? void 0 : user.email) || '',\n      phone: '',\n      position: 'مدير النظام'\n    },\n    notifications: {\n      emailNotifications: true,\n      pushNotifications: true,\n      weeklyReports: true,\n      systemAlerts: true\n    },\n    appearance: {\n      theme: 'light',\n      language: 'ar',\n      dateFormat: 'dd/mm/yyyy'\n    },\n    security: {\n      twoFactorAuth: false,\n      sessionTimeout: '30',\n      passwordExpiry: '90'\n    }\n  });\n  const tabs = [{\n    id: 'profile',\n    name: 'الملف الشخصي',\n    icon: UserIcon\n  }, {\n    id: 'notifications',\n    name: 'الإشعارات',\n    icon: BellIcon\n  }, {\n    id: 'appearance',\n    name: 'المظهر',\n    icon: PaintBrushIcon\n  }, {\n    id: 'security',\n    name: 'الأمان',\n    icon: ShieldCheckIcon\n  }, {\n    id: 'einvoice',\n    name: 'الفوترة الإلكترونية',\n    icon: QrCodeIcon\n  }, {\n    id: 'system',\n    name: 'النظام',\n    icon: CircleStackIcon\n  }];\n  const handleSettingChange = (category, key, value) => {\n    setSettings(prev => ({\n      ...prev,\n      [category]: {\n        ...prev[category],\n        [key]: value\n      }\n    }));\n  };\n  const saveSettings = () => {\n    // حفظ الإعدادات\n    if (activeTab === 'profile') {\n      updateUser({\n        ...user,\n        name: settings.profile.name,\n        email: settings.profile.email\n      });\n    }\n    localStorage.setItem('appSettings', JSON.stringify(settings));\n    toast.success('تم حفظ الإعدادات بنجاح');\n  };\n  const renderProfileSettings = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          htmlFor: \"name\",\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"\\u0627\\u0644\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0643\\u0627\\u0645\\u0644\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"name\",\n          value: settings.profile.name,\n          onChange: e => handleSettingChange('profile', 'name', e.target.value),\n          placeholder: \"\\u0623\\u062F\\u062E\\u0644 \\u0627\\u0633\\u0645\\u0643 \\u0627\\u0644\\u0643\\u0627\\u0645\\u0644\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          htmlFor: \"email\",\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"\\u0627\\u0644\\u0628\\u0631\\u064A\\u062F \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"email\",\n          type: \"email\",\n          value: settings.profile.email,\n          onChange: e => handleSettingChange('profile', 'email', e.target.value),\n          placeholder: \"\\u0623\\u062F\\u062E\\u0644 \\u0628\\u0631\\u064A\\u062F\\u0643 \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          htmlFor: \"phone\",\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0647\\u0627\\u062A\\u0641\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"phone\",\n          value: settings.profile.phone,\n          onChange: e => handleSettingChange('profile', 'phone', e.target.value),\n          placeholder: \"\\u0623\\u062F\\u062E\\u0644 \\u0631\\u0642\\u0645 \\u0647\\u0627\\u062A\\u0641\\u0643\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          htmlFor: \"position\",\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"\\u0627\\u0644\\u0645\\u0646\\u0635\\u0628\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"position\",\n          value: settings.profile.position,\n          onChange: e => handleSettingChange('profile', 'position', e.target.value),\n          placeholder: \"\\u0623\\u062F\\u062E\\u0644 \\u0645\\u0646\\u0635\\u0628\\u0643\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"pt-4 border-t border-gray-200\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-4\",\n        children: \"\\u062A\\u063A\\u064A\\u064A\\u0631 \\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Label, {\n            htmlFor: \"currentPassword\",\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"\\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631 \\u0627\\u0644\\u062D\\u0627\\u0644\\u064A\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            id: \"currentPassword\",\n            type: \"password\",\n            placeholder: \"\\u0623\\u062F\\u062E\\u0644 \\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631 \\u0627\\u0644\\u062D\\u0627\\u0644\\u064A\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Label, {\n            htmlFor: \"newPassword\",\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"\\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631 \\u0627\\u0644\\u062C\\u062F\\u064A\\u062F\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            id: \"newPassword\",\n            type: \"password\",\n            placeholder: \"\\u0623\\u062F\\u062E\\u0644 \\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631 \\u0627\\u0644\\u062C\\u062F\\u064A\\u062F\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 82,\n    columnNumber: 5\n  }, this);\n  const renderNotificationSettings = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: Object.entries(settings.notifications).map(([key, value]) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between py-3 border-b border-gray-200\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-medium text-gray-900\",\n          children: [key === 'emailNotifications' && 'إشعارات البريد الإلكتروني', key === 'pushNotifications' && 'الإشعارات المنبثقة', key === 'weeklyReports' && 'التقارير الأسبوعية', key === 'systemAlerts' && 'تنبيهات النظام']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-600\",\n          children: [key === 'emailNotifications' && 'استقبال الإشعارات عبر البريد الإلكتروني', key === 'pushNotifications' && 'عرض الإشعارات المنبثقة في المتصفح', key === 'weeklyReports' && 'استقبال تقارير أسبوعية عن النشاط', key === 'systemAlerts' && 'تنبيهات حول حالة النظام والأخطاء']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"relative inline-flex items-center cursor-pointer\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"checkbox\",\n          checked: value,\n          onChange: e => handleSettingChange('notifications', key, e.target.checked),\n          className: \"sr-only peer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 11\n      }, this)]\n    }, key, true, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 164,\n    columnNumber: 5\n  }, this);\n  const renderAppearanceSettings = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Label, {\n        className: \"block text-sm font-medium text-gray-700 mb-3\",\n        children: \"\\u0627\\u0644\\u0645\\u0638\\u0647\\u0631\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 gap-4\",\n        children: ['light', 'dark'].map(theme => /*#__PURE__*/_jsxDEV(\"label\", {\n          className: `flex items-center p-4 border rounded-lg cursor-pointer ${settings.appearance.theme === theme ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"radio\",\n            name: \"theme\",\n            value: theme,\n            checked: settings.appearance.theme === theme,\n            onChange: e => handleSettingChange('appearance', 'theme', e.target.value),\n            className: \"sr-only\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center w-full\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-12 h-8 rounded mb-2 mx-auto ${theme === 'light' ? 'bg-white border-2 border-gray-300' : 'bg-gray-800'}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium\",\n              children: theme === 'light' ? 'فاتح' : 'داكن'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 15\n          }, this)]\n        }, theme, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Label, {\n        htmlFor: \"language\",\n        className: \"block text-sm font-medium text-gray-700 mb-2\",\n        children: \"\\u0627\\u0644\\u0644\\u063A\\u0629\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n        id: \"language\",\n        value: settings.appearance.language,\n        onChange: e => handleSettingChange('appearance', 'language', e.target.value),\n        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"ar\",\n          children: \"\\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"en\",\n          children: \"English\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Label, {\n        htmlFor: \"dateFormat\",\n        className: \"block text-sm font-medium text-gray-700 mb-2\",\n        children: \"\\u062A\\u0646\\u0633\\u064A\\u0642 \\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n        id: \"dateFormat\",\n        value: settings.appearance.dateFormat,\n        onChange: e => handleSettingChange('appearance', 'dateFormat', e.target.value),\n        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"dd/mm/yyyy\",\n          children: \"\\u064A\\u0648\\u0645/\\u0634\\u0647\\u0631/\\u0633\\u0646\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"mm/dd/yyyy\",\n          children: \"\\u0634\\u0647\\u0631/\\u064A\\u0648\\u0645/\\u0633\\u0646\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"yyyy-mm-dd\",\n          children: \"\\u0633\\u0646\\u0629-\\u0634\\u0647\\u0631-\\u064A\\u0648\\u0645\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 196,\n    columnNumber: 5\n  }, this);\n  const renderSecuritySettings = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between py-3 border-b border-gray-200\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-medium text-gray-900\",\n          children: \"\\u0627\\u0644\\u0645\\u0635\\u0627\\u062F\\u0642\\u0629 \\u0627\\u0644\\u062B\\u0646\\u0627\\u0626\\u064A\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-600\",\n          children: \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0637\\u0628\\u0642\\u0629 \\u062D\\u0645\\u0627\\u064A\\u0629 \\u0625\\u0636\\u0627\\u0641\\u064A\\u0629 \\u0644\\u062D\\u0633\\u0627\\u0628\\u0643\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"relative inline-flex items-center cursor-pointer\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"checkbox\",\n          checked: settings.security.twoFactorAuth,\n          onChange: e => handleSettingChange('security', 'twoFactorAuth', e.target.checked),\n          className: \"sr-only peer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Label, {\n        htmlFor: \"sessionTimeout\",\n        className: \"block text-sm font-medium text-gray-700 mb-2\",\n        children: \"\\u0627\\u0646\\u062A\\u0647\\u0627\\u0621 \\u0627\\u0644\\u062C\\u0644\\u0633\\u0629 (\\u0628\\u0627\\u0644\\u062F\\u0642\\u0627\\u0626\\u0642)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Input, {\n        id: \"sessionTimeout\",\n        type: \"number\",\n        value: settings.security.sessionTimeout,\n        onChange: e => handleSettingChange('security', 'sessionTimeout', e.target.value),\n        placeholder: \"30\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 281,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Label, {\n        htmlFor: \"passwordExpiry\",\n        className: \"block text-sm font-medium text-gray-700 mb-2\",\n        children: \"\\u0627\\u0646\\u062A\\u0647\\u0627\\u0621 \\u0635\\u0644\\u0627\\u062D\\u064A\\u0629 \\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631 (\\u0628\\u0627\\u0644\\u0623\\u064A\\u0627\\u0645)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Input, {\n        id: \"passwordExpiry\",\n        type: \"number\",\n        value: settings.security.passwordExpiry,\n        onChange: e => handleSettingChange('security', 'passwordExpiry', e.target.value),\n        placeholder: \"90\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 294,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 264,\n    columnNumber: 5\n  }, this);\n  const renderSystemSettings = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-sm font-medium text-yellow-800 mb-2\",\n        children: \"\\u062A\\u062D\\u0630\\u064A\\u0631\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-yellow-700\",\n        children: \"\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645 \\u062A\\u0624\\u062B\\u0631 \\u0639\\u0644\\u0649 \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\\u064A\\u0646. \\u064A\\u0631\\u062C\\u0649 \\u0627\\u0644\\u062D\\u0630\\u0631 \\u0639\\u0646\\u062F \\u062A\\u063A\\u064A\\u064A\\u0631\\u0647\\u0627.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline\",\n        className: \"w-full justify-start\",\n        children: [/*#__PURE__*/_jsxDEV(CircleStackIcon, {\n          className: \"w-4 h-4 ml-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this), \"\\u0646\\u0633\\u062E \\u0627\\u062D\\u062A\\u064A\\u0627\\u0637\\u064A \\u0645\\u0646 \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline\",\n        className: \"w-full justify-start\",\n        children: [/*#__PURE__*/_jsxDEV(CircleStackIcon, {\n          className: \"w-4 h-4 ml-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this), \"\\u0627\\u0633\\u062A\\u064A\\u0631\\u0627\\u062F \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline\",\n        className: \"w-full justify-start text-red-600 border-red-200 hover:bg-red-50\",\n        children: [/*#__PURE__*/_jsxDEV(CircleStackIcon, {\n          className: \"w-4 h-4 ml-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this), \"\\u0645\\u0633\\u062D \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 310,\n    columnNumber: 5\n  }, this);\n  const renderContent = () => {\n    switch (activeTab) {\n      case 'profile':\n        return renderProfileSettings();\n      case 'notifications':\n        return renderNotificationSettings();\n      case 'appearance':\n        return renderAppearanceSettings();\n      case 'security':\n        return renderSecuritySettings();\n      case 'einvoice':\n        return /*#__PURE__*/_jsxDEV(EInvoiceSettings, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 16\n        }, this);\n      case 'system':\n        return renderSystemSettings();\n      default:\n        return renderProfileSettings();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold text-gray-900\",\n        children: \"\\u0627\\u0644\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 mt-2\",\n        children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645 \\u0648\\u0627\\u0644\\u0645\\u0644\\u0641 \\u0627\\u0644\\u0634\\u062E\\u0635\\u064A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 359,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-4 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          x: -20\n        },\n        animate: {\n          opacity: 1,\n          x: 0\n        },\n        transition: {\n          delay: 0.1\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            className: \"p-0\",\n            children: /*#__PURE__*/_jsxDEV(\"nav\", {\n              className: \"space-y-1\",\n              children: tabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setActiveTab(tab.id),\n                className: `w-full flex items-center px-4 py-3 text-sm font-medium text-right transition-colors ${activeTab === tab.id ? 'bg-blue-50 text-blue-700 border-l-4 border-blue-700' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}`,\n                children: [/*#__PURE__*/_jsxDEV(tab.icon, {\n                  className: \"w-5 h-5 ml-3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 21\n                }, this), tab.name]\n              }, tab.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          x: 20\n        },\n        animate: {\n          opacity: 1,\n          x: 0\n        },\n        transition: {\n          delay: 0.2\n        },\n        className: \"lg:col-span-3\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n            children: /*#__PURE__*/_jsxDEV(CardTitle, {\n              children: (_tabs$find = tabs.find(tab => tab.id === activeTab)) === null || _tabs$find === void 0 ? void 0 : _tabs$find.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [renderContent(), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-end space-x-4 space-x-reverse mt-8 pt-6 border-t border-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline\",\n                children: \"\\u0625\\u0644\\u063A\\u0627\\u0621\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                onClick: saveSettings,\n                className: \"bg-blue-600 hover:bg-blue-700 text-white\",\n                children: \"\\u062D\\u0641\\u0638 \\u0627\\u0644\\u062A\\u063A\\u064A\\u064A\\u0631\\u0627\\u062A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 397,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 357,\n    columnNumber: 5\n  }, this);\n};\n_s(Settings, \"uWYgIAa6dGWROVA4SQJyrdyUXyQ=\", false, function () {\n  return [useAuth];\n});\n_c = Settings;\nexport default Settings;\nvar _c;\n$RefreshReg$(_c, \"Settings\");", "map": {"version": 3, "names": ["React", "useState", "motion", "UserIcon", "BellIcon", "ShieldCheckIcon", "GlobeAltIcon", "PaintBrushIcon", "CircleStackIcon", "QrCodeIcon", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "<PERSON><PERSON>", "Input", "Label", "useAuth", "EInvoiceSettings", "toast", "jsxDEV", "_jsxDEV", "Settings", "_s", "_tabs$find", "user", "updateUser", "activeTab", "setActiveTab", "settings", "setSettings", "profile", "name", "email", "phone", "position", "notifications", "emailNotifications", "pushNotifications", "weeklyReports", "systemAlerts", "appearance", "theme", "language", "dateFormat", "security", "twoFactorAuth", "sessionTimeout", "passwordExpiry", "tabs", "id", "icon", "handleSettingChange", "category", "key", "value", "prev", "saveSettings", "localStorage", "setItem", "JSON", "stringify", "success", "renderProfileSettings", "className", "children", "htmlFor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "e", "target", "placeholder", "type", "renderNotificationSettings", "Object", "entries", "map", "checked", "renderAppearanceSettings", "renderSecuritySettings", "renderSystemSettings", "variant", "renderContent", "div", "initial", "opacity", "y", "animate", "x", "transition", "delay", "tab", "onClick", "find", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/src/pages/Settings.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  UserIcon,\n  BellIcon,\n  ShieldCheckIcon,\n  GlobeAltIcon,\n  PaintBrushIcon,\n  CircleStackIcon,\n  QrCodeIcon\n} from '@heroicons/react/24/outline';\nimport { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';\nimport { Button } from '../components/ui/button';\nimport { Input } from '../components/ui/input';\nimport { Label } from '../components/ui/label';\nimport { useAuth } from '../contexts/AuthContext';\nimport EInvoiceSettings from '../components/Settings/EInvoiceSettings';\nimport toast from 'react-hot-toast';\n\nconst Settings = () => {\n  const { user, updateUser } = useAuth();\n  const [activeTab, setActiveTab] = useState('profile');\n  const [settings, setSettings] = useState({\n    profile: {\n      name: user?.name || '',\n      email: user?.email || '',\n      phone: '',\n      position: 'مدير النظام'\n    },\n    notifications: {\n      emailNotifications: true,\n      pushNotifications: true,\n      weeklyReports: true,\n      systemAlerts: true\n    },\n    appearance: {\n      theme: 'light',\n      language: 'ar',\n      dateFormat: 'dd/mm/yyyy'\n    },\n    security: {\n      twoFactorAuth: false,\n      sessionTimeout: '30',\n      passwordExpiry: '90'\n    }\n  });\n\n  const tabs = [\n    { id: 'profile', name: 'الملف الشخصي', icon: UserIcon },\n    { id: 'notifications', name: 'الإشعارات', icon: BellIcon },\n    { id: 'appearance', name: 'المظهر', icon: PaintBrushIcon },\n    { id: 'security', name: 'الأمان', icon: ShieldCheckIcon },\n    { id: 'einvoice', name: 'الفوترة الإلكترونية', icon: QrCodeIcon },\n    { id: 'system', name: 'النظام', icon: CircleStackIcon }\n  ];\n\n  const handleSettingChange = (category, key, value) => {\n    setSettings(prev => ({\n      ...prev,\n      [category]: {\n        ...prev[category],\n        [key]: value\n      }\n    }));\n  };\n\n  const saveSettings = () => {\n    // حفظ الإعدادات\n    if (activeTab === 'profile') {\n      updateUser({\n        ...user,\n        name: settings.profile.name,\n        email: settings.profile.email\n      });\n    }\n\n    localStorage.setItem('appSettings', JSON.stringify(settings));\n    toast.success('تم حفظ الإعدادات بنجاح');\n  };\n\n  const renderProfileSettings = () => (\n    <div className=\"space-y-6\">\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        <div>\n          <Label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            الاسم الكامل\n          </Label>\n          <Input\n            id=\"name\"\n            value={settings.profile.name}\n            onChange={(e) => handleSettingChange('profile', 'name', e.target.value)}\n            placeholder=\"أدخل اسمك الكامل\"\n          />\n        </div>\n\n        <div>\n          <Label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            البريد الإلكتروني\n          </Label>\n          <Input\n            id=\"email\"\n            type=\"email\"\n            value={settings.profile.email}\n            onChange={(e) => handleSettingChange('profile', 'email', e.target.value)}\n            placeholder=\"أدخل بريدك الإلكتروني\"\n          />\n        </div>\n\n        <div>\n          <Label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            رقم الهاتف\n          </Label>\n          <Input\n            id=\"phone\"\n            value={settings.profile.phone}\n            onChange={(e) => handleSettingChange('profile', 'phone', e.target.value)}\n            placeholder=\"أدخل رقم هاتفك\"\n          />\n        </div>\n\n        <div>\n          <Label htmlFor=\"position\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            المنصب\n          </Label>\n          <Input\n            id=\"position\"\n            value={settings.profile.position}\n            onChange={(e) => handleSettingChange('profile', 'position', e.target.value)}\n            placeholder=\"أدخل منصبك\"\n          />\n        </div>\n      </div>\n\n      <div className=\"pt-4 border-t border-gray-200\">\n        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">تغيير كلمة المرور</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div>\n            <Label htmlFor=\"currentPassword\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              كلمة المرور الحالية\n            </Label>\n            <Input\n              id=\"currentPassword\"\n              type=\"password\"\n              placeholder=\"أدخل كلمة المرور الحالية\"\n            />\n          </div>\n\n          <div>\n            <Label htmlFor=\"newPassword\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              كلمة المرور الجديدة\n            </Label>\n            <Input\n              id=\"newPassword\"\n              type=\"password\"\n              placeholder=\"أدخل كلمة المرور الجديدة\"\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderNotificationSettings = () => (\n    <div className=\"space-y-6\">\n      {Object.entries(settings.notifications).map(([key, value]) => (\n        <div key={key} className=\"flex items-center justify-between py-3 border-b border-gray-200\">\n          <div>\n            <h3 className=\"text-sm font-medium text-gray-900\">\n              {key === 'emailNotifications' && 'إشعارات البريد الإلكتروني'}\n              {key === 'pushNotifications' && 'الإشعارات المنبثقة'}\n              {key === 'weeklyReports' && 'التقارير الأسبوعية'}\n              {key === 'systemAlerts' && 'تنبيهات النظام'}\n            </h3>\n            <p className=\"text-sm text-gray-600\">\n              {key === 'emailNotifications' && 'استقبال الإشعارات عبر البريد الإلكتروني'}\n              {key === 'pushNotifications' && 'عرض الإشعارات المنبثقة في المتصفح'}\n              {key === 'weeklyReports' && 'استقبال تقارير أسبوعية عن النشاط'}\n              {key === 'systemAlerts' && 'تنبيهات حول حالة النظام والأخطاء'}\n            </p>\n          </div>\n          <label className=\"relative inline-flex items-center cursor-pointer\">\n            <input\n              type=\"checkbox\"\n              checked={value}\n              onChange={(e) => handleSettingChange('notifications', key, e.target.checked)}\n              className=\"sr-only peer\"\n            />\n            <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n          </label>\n        </div>\n      ))}\n    </div>\n  );\n\n  const renderAppearanceSettings = () => (\n    <div className=\"space-y-6\">\n      <div>\n        <Label className=\"block text-sm font-medium text-gray-700 mb-3\">المظهر</Label>\n        <div className=\"grid grid-cols-2 gap-4\">\n          {['light', 'dark'].map((theme) => (\n            <label\n              key={theme}\n              className={`flex items-center p-4 border rounded-lg cursor-pointer ${\n                settings.appearance.theme === theme\n                  ? 'border-blue-500 bg-blue-50'\n                  : 'border-gray-200 hover:border-gray-300'\n              }`}\n            >\n              <input\n                type=\"radio\"\n                name=\"theme\"\n                value={theme}\n                checked={settings.appearance.theme === theme}\n                onChange={(e) => handleSettingChange('appearance', 'theme', e.target.value)}\n                className=\"sr-only\"\n              />\n              <div className=\"text-center w-full\">\n                <div className={`w-12 h-8 rounded mb-2 mx-auto ${\n                  theme === 'light' ? 'bg-white border-2 border-gray-300' : 'bg-gray-800'\n                }`}></div>\n                <p className=\"text-sm font-medium\">\n                  {theme === 'light' ? 'فاتح' : 'داكن'}\n                </p>\n              </div>\n            </label>\n          ))}\n        </div>\n      </div>\n\n      <div>\n        <Label htmlFor=\"language\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n          اللغة\n        </Label>\n        <select\n          id=\"language\"\n          value={settings.appearance.language}\n          onChange={(e) => handleSettingChange('appearance', 'language', e.target.value)}\n          className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n        >\n          <option value=\"ar\">العربية</option>\n          <option value=\"en\">English</option>\n        </select>\n      </div>\n\n      <div>\n        <Label htmlFor=\"dateFormat\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n          تنسيق التاريخ\n        </Label>\n        <select\n          id=\"dateFormat\"\n          value={settings.appearance.dateFormat}\n          onChange={(e) => handleSettingChange('appearance', 'dateFormat', e.target.value)}\n          className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n        >\n          <option value=\"dd/mm/yyyy\">يوم/شهر/سنة</option>\n          <option value=\"mm/dd/yyyy\">شهر/يوم/سنة</option>\n          <option value=\"yyyy-mm-dd\">سنة-شهر-يوم</option>\n        </select>\n      </div>\n    </div>\n  );\n\n  const renderSecuritySettings = () => (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between py-3 border-b border-gray-200\">\n        <div>\n          <h3 className=\"text-sm font-medium text-gray-900\">المصادقة الثنائية</h3>\n          <p className=\"text-sm text-gray-600\">إضافة طبقة حماية إضافية لحسابك</p>\n        </div>\n        <label className=\"relative inline-flex items-center cursor-pointer\">\n          <input\n            type=\"checkbox\"\n            checked={settings.security.twoFactorAuth}\n            onChange={(e) => handleSettingChange('security', 'twoFactorAuth', e.target.checked)}\n            className=\"sr-only peer\"\n          />\n          <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n        </label>\n      </div>\n\n      <div>\n        <Label htmlFor=\"sessionTimeout\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n          انتهاء الجلسة (بالدقائق)\n        </Label>\n        <Input\n          id=\"sessionTimeout\"\n          type=\"number\"\n          value={settings.security.sessionTimeout}\n          onChange={(e) => handleSettingChange('security', 'sessionTimeout', e.target.value)}\n          placeholder=\"30\"\n        />\n      </div>\n\n      <div>\n        <Label htmlFor=\"passwordExpiry\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n          انتهاء صلاحية كلمة المرور (بالأيام)\n        </Label>\n        <Input\n          id=\"passwordExpiry\"\n          type=\"number\"\n          value={settings.security.passwordExpiry}\n          onChange={(e) => handleSettingChange('security', 'passwordExpiry', e.target.value)}\n          placeholder=\"90\"\n        />\n      </div>\n    </div>\n  );\n\n  const renderSystemSettings = () => (\n    <div className=\"space-y-6\">\n      <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n        <h3 className=\"text-sm font-medium text-yellow-800 mb-2\">تحذير</h3>\n        <p className=\"text-sm text-yellow-700\">\n          إعدادات النظام تؤثر على جميع المستخدمين. يرجى الحذر عند تغييرها.\n        </p>\n      </div>\n\n      <div className=\"space-y-4\">\n        <Button variant=\"outline\" className=\"w-full justify-start\">\n          <CircleStackIcon className=\"w-4 h-4 ml-2\" />\n          نسخ احتياطي من البيانات\n        </Button>\n\n        <Button variant=\"outline\" className=\"w-full justify-start\">\n          <CircleStackIcon className=\"w-4 h-4 ml-2\" />\n          استيراد البيانات\n        </Button>\n\n        <Button variant=\"outline\" className=\"w-full justify-start text-red-600 border-red-200 hover:bg-red-50\">\n          <CircleStackIcon className=\"w-4 h-4 ml-2\" />\n          مسح جميع البيانات\n        </Button>\n      </div>\n    </div>\n  );\n\n  const renderContent = () => {\n    switch (activeTab) {\n      case 'profile':\n        return renderProfileSettings();\n      case 'notifications':\n        return renderNotificationSettings();\n      case 'appearance':\n        return renderAppearanceSettings();\n      case 'security':\n        return renderSecuritySettings();\n      case 'einvoice':\n        return <EInvoiceSettings />;\n      case 'system':\n        return renderSystemSettings();\n      default:\n        return renderProfileSettings();\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* العنوان */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n      >\n        <h1 className=\"text-3xl font-bold text-gray-900\">الإعدادات</h1>\n        <p className=\"text-gray-600 mt-2\">إدارة إعدادات النظام والملف الشخصي</p>\n      </motion.div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-6\">\n        {/* قائمة التبويبات */}\n        <motion.div\n          initial={{ opacity: 0, x: -20 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ delay: 0.1 }}\n        >\n          <Card>\n            <CardContent className=\"p-0\">\n              <nav className=\"space-y-1\">\n                {tabs.map((tab) => (\n                  <button\n                    key={tab.id}\n                    onClick={() => setActiveTab(tab.id)}\n                    className={`w-full flex items-center px-4 py-3 text-sm font-medium text-right transition-colors ${\n                      activeTab === tab.id\n                        ? 'bg-blue-50 text-blue-700 border-l-4 border-blue-700'\n                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                    }`}\n                  >\n                    <tab.icon className=\"w-5 h-5 ml-3\" />\n                    {tab.name}\n                  </button>\n                ))}\n              </nav>\n            </CardContent>\n          </Card>\n        </motion.div>\n\n        {/* محتوى التبويب */}\n        <motion.div\n          initial={{ opacity: 0, x: 20 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ delay: 0.2 }}\n          className=\"lg:col-span-3\"\n        >\n          <Card>\n            <CardHeader>\n              <CardTitle>\n                {tabs.find(tab => tab.id === activeTab)?.name}\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              {renderContent()}\n\n              {/* أزرار الحفظ */}\n              <div className=\"flex items-center justify-end space-x-4 space-x-reverse mt-8 pt-6 border-t border-gray-200\">\n                <Button variant=\"outline\">\n                  إلغاء\n                </Button>\n                <Button onClick={saveSettings} className=\"bg-blue-600 hover:bg-blue-700 text-white\">\n                  حفظ التغييرات\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        </motion.div>\n      </div>\n    </div>\n  );\n};\n\nexport default Settings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,QAAQ,EACRC,QAAQ,EACRC,eAAe,EACfC,YAAY,EACZC,cAAc,EACdC,eAAe,EACfC,UAAU,QACL,6BAA6B;AACpC,SAASC,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,SAAS,QAAQ,uBAAuB;AAChF,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,KAAK,QAAQ,wBAAwB;AAC9C,SAASC,KAAK,QAAQ,wBAAwB;AAC9C,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,gBAAgB,MAAM,yCAAyC;AACtE,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,UAAA;EACrB,MAAM;IAAEC,IAAI;IAAEC;EAAW,CAAC,GAAGT,OAAO,CAAC,CAAC;EACtC,MAAM,CAACU,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAAC4B,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC;IACvC8B,OAAO,EAAE;MACPC,IAAI,EAAE,CAAAP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,IAAI,KAAI,EAAE;MACtBC,KAAK,EAAE,CAAAR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,KAAK,KAAI,EAAE;MACxBC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE;IACZ,CAAC;IACDC,aAAa,EAAE;MACbC,kBAAkB,EAAE,IAAI;MACxBC,iBAAiB,EAAE,IAAI;MACvBC,aAAa,EAAE,IAAI;MACnBC,YAAY,EAAE;IAChB,CAAC;IACDC,UAAU,EAAE;MACVC,KAAK,EAAE,OAAO;MACdC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IACd,CAAC;IACDC,QAAQ,EAAE;MACRC,aAAa,EAAE,KAAK;MACpBC,cAAc,EAAE,IAAI;MACpBC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EAEF,MAAMC,IAAI,GAAG,CACX;IAAEC,EAAE,EAAE,SAAS;IAAElB,IAAI,EAAE,cAAc;IAAEmB,IAAI,EAAEhD;EAAS,CAAC,EACvD;IAAE+C,EAAE,EAAE,eAAe;IAAElB,IAAI,EAAE,WAAW;IAAEmB,IAAI,EAAE/C;EAAS,CAAC,EAC1D;IAAE8C,EAAE,EAAE,YAAY;IAAElB,IAAI,EAAE,QAAQ;IAAEmB,IAAI,EAAE5C;EAAe,CAAC,EAC1D;IAAE2C,EAAE,EAAE,UAAU;IAAElB,IAAI,EAAE,QAAQ;IAAEmB,IAAI,EAAE9C;EAAgB,CAAC,EACzD;IAAE6C,EAAE,EAAE,UAAU;IAAElB,IAAI,EAAE,qBAAqB;IAAEmB,IAAI,EAAE1C;EAAW,CAAC,EACjE;IAAEyC,EAAE,EAAE,QAAQ;IAAElB,IAAI,EAAE,QAAQ;IAAEmB,IAAI,EAAE3C;EAAgB,CAAC,CACxD;EAED,MAAM4C,mBAAmB,GAAGA,CAACC,QAAQ,EAAEC,GAAG,EAAEC,KAAK,KAAK;IACpDzB,WAAW,CAAC0B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,QAAQ,GAAG;QACV,GAAGG,IAAI,CAACH,QAAQ,CAAC;QACjB,CAACC,GAAG,GAAGC;MACT;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzB;IACA,IAAI9B,SAAS,KAAK,SAAS,EAAE;MAC3BD,UAAU,CAAC;QACT,GAAGD,IAAI;QACPO,IAAI,EAAEH,QAAQ,CAACE,OAAO,CAACC,IAAI;QAC3BC,KAAK,EAAEJ,QAAQ,CAACE,OAAO,CAACE;MAC1B,CAAC,CAAC;IACJ;IAEAyB,YAAY,CAACC,OAAO,CAAC,aAAa,EAAEC,IAAI,CAACC,SAAS,CAAChC,QAAQ,CAAC,CAAC;IAC7DV,KAAK,CAAC2C,OAAO,CAAC,wBAAwB,CAAC;EACzC,CAAC;EAED,MAAMC,qBAAqB,GAAGA,CAAA,kBAC5B1C,OAAA;IAAK2C,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB5C,OAAA;MAAK2C,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpD5C,OAAA;QAAA4C,QAAA,gBACE5C,OAAA,CAACL,KAAK;UAACkD,OAAO,EAAC,MAAM;UAACF,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAE/E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRjD,OAAA,CAACN,KAAK;UACJmC,EAAE,EAAC,MAAM;UACTK,KAAK,EAAE1B,QAAQ,CAACE,OAAO,CAACC,IAAK;UAC7BuC,QAAQ,EAAGC,CAAC,IAAKpB,mBAAmB,CAAC,SAAS,EAAE,MAAM,EAAEoB,CAAC,CAACC,MAAM,CAAClB,KAAK,CAAE;UACxEmB,WAAW,EAAC;QAAkB;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENjD,OAAA;QAAA4C,QAAA,gBACE5C,OAAA,CAACL,KAAK;UAACkD,OAAO,EAAC,OAAO;UAACF,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAEhF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRjD,OAAA,CAACN,KAAK;UACJmC,EAAE,EAAC,OAAO;UACVyB,IAAI,EAAC,OAAO;UACZpB,KAAK,EAAE1B,QAAQ,CAACE,OAAO,CAACE,KAAM;UAC9BsC,QAAQ,EAAGC,CAAC,IAAKpB,mBAAmB,CAAC,SAAS,EAAE,OAAO,EAAEoB,CAAC,CAACC,MAAM,CAAClB,KAAK,CAAE;UACzEmB,WAAW,EAAC;QAAuB;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENjD,OAAA;QAAA4C,QAAA,gBACE5C,OAAA,CAACL,KAAK;UAACkD,OAAO,EAAC,OAAO;UAACF,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAEhF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRjD,OAAA,CAACN,KAAK;UACJmC,EAAE,EAAC,OAAO;UACVK,KAAK,EAAE1B,QAAQ,CAACE,OAAO,CAACG,KAAM;UAC9BqC,QAAQ,EAAGC,CAAC,IAAKpB,mBAAmB,CAAC,SAAS,EAAE,OAAO,EAAEoB,CAAC,CAACC,MAAM,CAAClB,KAAK,CAAE;UACzEmB,WAAW,EAAC;QAAgB;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENjD,OAAA;QAAA4C,QAAA,gBACE5C,OAAA,CAACL,KAAK;UAACkD,OAAO,EAAC,UAAU;UAACF,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAEnF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRjD,OAAA,CAACN,KAAK;UACJmC,EAAE,EAAC,UAAU;UACbK,KAAK,EAAE1B,QAAQ,CAACE,OAAO,CAACI,QAAS;UACjCoC,QAAQ,EAAGC,CAAC,IAAKpB,mBAAmB,CAAC,SAAS,EAAE,UAAU,EAAEoB,CAAC,CAACC,MAAM,CAAClB,KAAK,CAAE;UAC5EmB,WAAW,EAAC;QAAY;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENjD,OAAA;MAAK2C,SAAS,EAAC,+BAA+B;MAAAC,QAAA,gBAC5C5C,OAAA;QAAI2C,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7EjD,OAAA;QAAK2C,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpD5C,OAAA;UAAA4C,QAAA,gBACE5C,OAAA,CAACL,KAAK;YAACkD,OAAO,EAAC,iBAAiB;YAACF,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAE1F;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRjD,OAAA,CAACN,KAAK;YACJmC,EAAE,EAAC,iBAAiB;YACpByB,IAAI,EAAC,UAAU;YACfD,WAAW,EAAC;UAA0B;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENjD,OAAA;UAAA4C,QAAA,gBACE5C,OAAA,CAACL,KAAK;YAACkD,OAAO,EAAC,aAAa;YAACF,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEtF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRjD,OAAA,CAACN,KAAK;YACJmC,EAAE,EAAC,aAAa;YAChByB,IAAI,EAAC,UAAU;YACfD,WAAW,EAAC;UAA0B;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMM,0BAA0B,GAAGA,CAAA,kBACjCvD,OAAA;IAAK2C,SAAS,EAAC,WAAW;IAAAC,QAAA,EACvBY,MAAM,CAACC,OAAO,CAACjD,QAAQ,CAACO,aAAa,CAAC,CAAC2C,GAAG,CAAC,CAAC,CAACzB,GAAG,EAAEC,KAAK,CAAC,kBACvDlC,OAAA;MAAe2C,SAAS,EAAC,iEAAiE;MAAAC,QAAA,gBACxF5C,OAAA;QAAA4C,QAAA,gBACE5C,OAAA;UAAI2C,SAAS,EAAC,mCAAmC;UAAAC,QAAA,GAC9CX,GAAG,KAAK,oBAAoB,IAAI,2BAA2B,EAC3DA,GAAG,KAAK,mBAAmB,IAAI,oBAAoB,EACnDA,GAAG,KAAK,eAAe,IAAI,oBAAoB,EAC/CA,GAAG,KAAK,cAAc,IAAI,gBAAgB;QAAA;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACLjD,OAAA;UAAG2C,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GACjCX,GAAG,KAAK,oBAAoB,IAAI,yCAAyC,EACzEA,GAAG,KAAK,mBAAmB,IAAI,mCAAmC,EAClEA,GAAG,KAAK,eAAe,IAAI,kCAAkC,EAC7DA,GAAG,KAAK,cAAc,IAAI,kCAAkC;QAAA;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNjD,OAAA;QAAO2C,SAAS,EAAC,kDAAkD;QAAAC,QAAA,gBACjE5C,OAAA;UACEsD,IAAI,EAAC,UAAU;UACfK,OAAO,EAAEzB,KAAM;UACfgB,QAAQ,EAAGC,CAAC,IAAKpB,mBAAmB,CAAC,eAAe,EAAEE,GAAG,EAAEkB,CAAC,CAACC,MAAM,CAACO,OAAO,CAAE;UAC7EhB,SAAS,EAAC;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eACFjD,OAAA;UAAK2C,SAAS,EAAC;QAAyX;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1Y,CAAC;IAAA,GAvBAhB,GAAG;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAwBR,CACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACN;EAED,MAAMW,wBAAwB,GAAGA,CAAA,kBAC/B5D,OAAA;IAAK2C,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB5C,OAAA;MAAA4C,QAAA,gBACE5C,OAAA,CAACL,KAAK;QAACgD,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAC;MAAM;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC9EjD,OAAA;QAAK2C,SAAS,EAAC,wBAAwB;QAAAC,QAAA,EACpC,CAAC,OAAO,EAAE,MAAM,CAAC,CAACc,GAAG,CAAErC,KAAK,iBAC3BrB,OAAA;UAEE2C,SAAS,EAAE,0DACTnC,QAAQ,CAACY,UAAU,CAACC,KAAK,KAAKA,KAAK,GAC/B,4BAA4B,GAC5B,uCAAuC,EAC1C;UAAAuB,QAAA,gBAEH5C,OAAA;YACEsD,IAAI,EAAC,OAAO;YACZ3C,IAAI,EAAC,OAAO;YACZuB,KAAK,EAAEb,KAAM;YACbsC,OAAO,EAAEnD,QAAQ,CAACY,UAAU,CAACC,KAAK,KAAKA,KAAM;YAC7C6B,QAAQ,EAAGC,CAAC,IAAKpB,mBAAmB,CAAC,YAAY,EAAE,OAAO,EAAEoB,CAAC,CAACC,MAAM,CAAClB,KAAK,CAAE;YAC5ES,SAAS,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eACFjD,OAAA;YAAK2C,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjC5C,OAAA;cAAK2C,SAAS,EAAE,iCACdtB,KAAK,KAAK,OAAO,GAAG,mCAAmC,GAAG,aAAa;YACtE;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACVjD,OAAA;cAAG2C,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAC/BvB,KAAK,KAAK,OAAO,GAAG,MAAM,GAAG;YAAM;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA,GAtBD5B,KAAK;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuBL,CACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENjD,OAAA;MAAA4C,QAAA,gBACE5C,OAAA,CAACL,KAAK;QAACkD,OAAO,EAAC,UAAU;QAACF,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAC;MAEnF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRjD,OAAA;QACE6B,EAAE,EAAC,UAAU;QACbK,KAAK,EAAE1B,QAAQ,CAACY,UAAU,CAACE,QAAS;QACpC4B,QAAQ,EAAGC,CAAC,IAAKpB,mBAAmB,CAAC,YAAY,EAAE,UAAU,EAAEoB,CAAC,CAACC,MAAM,CAAClB,KAAK,CAAE;QAC/ES,SAAS,EAAC,2GAA2G;QAAAC,QAAA,gBAErH5C,OAAA;UAAQkC,KAAK,EAAC,IAAI;UAAAU,QAAA,EAAC;QAAO;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACnCjD,OAAA;UAAQkC,KAAK,EAAC,IAAI;UAAAU,QAAA,EAAC;QAAO;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENjD,OAAA;MAAA4C,QAAA,gBACE5C,OAAA,CAACL,KAAK;QAACkD,OAAO,EAAC,YAAY;QAACF,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAC;MAErF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRjD,OAAA;QACE6B,EAAE,EAAC,YAAY;QACfK,KAAK,EAAE1B,QAAQ,CAACY,UAAU,CAACG,UAAW;QACtC2B,QAAQ,EAAGC,CAAC,IAAKpB,mBAAmB,CAAC,YAAY,EAAE,YAAY,EAAEoB,CAAC,CAACC,MAAM,CAAClB,KAAK,CAAE;QACjFS,SAAS,EAAC,2GAA2G;QAAAC,QAAA,gBAErH5C,OAAA;UAAQkC,KAAK,EAAC,YAAY;UAAAU,QAAA,EAAC;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC/CjD,OAAA;UAAQkC,KAAK,EAAC,YAAY;UAAAU,QAAA,EAAC;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC/CjD,OAAA;UAAQkC,KAAK,EAAC,YAAY;UAAAU,QAAA,EAAC;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMY,sBAAsB,GAAGA,CAAA,kBAC7B7D,OAAA;IAAK2C,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB5C,OAAA;MAAK2C,SAAS,EAAC,iEAAiE;MAAAC,QAAA,gBAC9E5C,OAAA;QAAA4C,QAAA,gBACE5C,OAAA;UAAI2C,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxEjD,OAAA;UAAG2C,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAA8B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CAAC,eACNjD,OAAA;QAAO2C,SAAS,EAAC,kDAAkD;QAAAC,QAAA,gBACjE5C,OAAA;UACEsD,IAAI,EAAC,UAAU;UACfK,OAAO,EAAEnD,QAAQ,CAACgB,QAAQ,CAACC,aAAc;UACzCyB,QAAQ,EAAGC,CAAC,IAAKpB,mBAAmB,CAAC,UAAU,EAAE,eAAe,EAAEoB,CAAC,CAACC,MAAM,CAACO,OAAO,CAAE;UACpFhB,SAAS,EAAC;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eACFjD,OAAA;UAAK2C,SAAS,EAAC;QAAyX;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1Y,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAENjD,OAAA;MAAA4C,QAAA,gBACE5C,OAAA,CAACL,KAAK;QAACkD,OAAO,EAAC,gBAAgB;QAACF,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAC;MAEzF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRjD,OAAA,CAACN,KAAK;QACJmC,EAAE,EAAC,gBAAgB;QACnByB,IAAI,EAAC,QAAQ;QACbpB,KAAK,EAAE1B,QAAQ,CAACgB,QAAQ,CAACE,cAAe;QACxCwB,QAAQ,EAAGC,CAAC,IAAKpB,mBAAmB,CAAC,UAAU,EAAE,gBAAgB,EAAEoB,CAAC,CAACC,MAAM,CAAClB,KAAK,CAAE;QACnFmB,WAAW,EAAC;MAAI;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENjD,OAAA;MAAA4C,QAAA,gBACE5C,OAAA,CAACL,KAAK;QAACkD,OAAO,EAAC,gBAAgB;QAACF,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAC;MAEzF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRjD,OAAA,CAACN,KAAK;QACJmC,EAAE,EAAC,gBAAgB;QACnByB,IAAI,EAAC,QAAQ;QACbpB,KAAK,EAAE1B,QAAQ,CAACgB,QAAQ,CAACG,cAAe;QACxCuB,QAAQ,EAAGC,CAAC,IAAKpB,mBAAmB,CAAC,UAAU,EAAE,gBAAgB,EAAEoB,CAAC,CAACC,MAAM,CAAClB,KAAK,CAAE;QACnFmB,WAAW,EAAC;MAAI;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMa,oBAAoB,GAAGA,CAAA,kBAC3B9D,OAAA;IAAK2C,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB5C,OAAA;MAAK2C,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBACnE5C,OAAA;QAAI2C,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAAC;MAAK;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnEjD,OAAA;QAAG2C,SAAS,EAAC,yBAAyB;QAAAC,QAAA,EAAC;MAEvC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAENjD,OAAA;MAAK2C,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxB5C,OAAA,CAACP,MAAM;QAACsE,OAAO,EAAC,SAAS;QAACpB,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACxD5C,OAAA,CAACb,eAAe;UAACwD,SAAS,EAAC;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,+HAE9C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAETjD,OAAA,CAACP,MAAM;QAACsE,OAAO,EAAC,SAAS;QAACpB,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACxD5C,OAAA,CAACb,eAAe;UAACwD,SAAS,EAAC;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,+FAE9C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAETjD,OAAA,CAACP,MAAM;QAACsE,OAAO,EAAC,SAAS;QAACpB,SAAS,EAAC,kEAAkE;QAAAC,QAAA,gBACpG5C,OAAA,CAACb,eAAe;UAACwD,SAAS,EAAC;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gGAE9C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMe,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQ1D,SAAS;MACf,KAAK,SAAS;QACZ,OAAOoC,qBAAqB,CAAC,CAAC;MAChC,KAAK,eAAe;QAClB,OAAOa,0BAA0B,CAAC,CAAC;MACrC,KAAK,YAAY;QACf,OAAOK,wBAAwB,CAAC,CAAC;MACnC,KAAK,UAAU;QACb,OAAOC,sBAAsB,CAAC,CAAC;MACjC,KAAK,UAAU;QACb,oBAAO7D,OAAA,CAACH,gBAAgB;UAAAiD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7B,KAAK,QAAQ;QACX,OAAOa,oBAAoB,CAAC,CAAC;MAC/B;QACE,OAAOpB,qBAAqB,CAAC,CAAC;IAClC;EACF,CAAC;EAED,oBACE1C,OAAA;IAAK2C,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB5C,OAAA,CAACnB,MAAM,CAACoF,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAxB,QAAA,gBAE9B5C,OAAA;QAAI2C,SAAS,EAAC,kCAAkC;QAAAC,QAAA,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/DjD,OAAA;QAAG2C,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAAkC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D,CAAC,eAEbjD,OAAA;MAAK2C,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpD5C,OAAA,CAACnB,MAAM,CAACoF,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEG,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCD,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEG,CAAC,EAAE;QAAE,CAAE;QAC9BC,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAA5B,QAAA,eAE3B5C,OAAA,CAACX,IAAI;UAAAuD,QAAA,eACH5C,OAAA,CAACV,WAAW;YAACqD,SAAS,EAAC,KAAK;YAAAC,QAAA,eAC1B5C,OAAA;cAAK2C,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBhB,IAAI,CAAC8B,GAAG,CAAEe,GAAG,iBACZzE,OAAA;gBAEE0E,OAAO,EAAEA,CAAA,KAAMnE,YAAY,CAACkE,GAAG,CAAC5C,EAAE,CAAE;gBACpCc,SAAS,EAAE,uFACTrC,SAAS,KAAKmE,GAAG,CAAC5C,EAAE,GAChB,qDAAqD,GACrD,oDAAoD,EACvD;gBAAAe,QAAA,gBAEH5C,OAAA,CAACyE,GAAG,CAAC3C,IAAI;kBAACa,SAAS,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACpCwB,GAAG,CAAC9D,IAAI;cAAA,GATJ8D,GAAG,CAAC5C,EAAE;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUL,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAGbjD,OAAA,CAACnB,MAAM,CAACoF,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEG,CAAC,EAAE;QAAG,CAAE;QAC/BD,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEG,CAAC,EAAE;QAAE,CAAE;QAC9BC,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3B7B,SAAS,EAAC,eAAe;QAAAC,QAAA,eAEzB5C,OAAA,CAACX,IAAI;UAAAuD,QAAA,gBACH5C,OAAA,CAACT,UAAU;YAAAqD,QAAA,eACT5C,OAAA,CAACR,SAAS;cAAAoD,QAAA,GAAAzC,UAAA,GACPyB,IAAI,CAAC+C,IAAI,CAACF,GAAG,IAAIA,GAAG,CAAC5C,EAAE,KAAKvB,SAAS,CAAC,cAAAH,UAAA,uBAAtCA,UAAA,CAAwCQ;YAAI;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACbjD,OAAA,CAACV,WAAW;YAAAsD,QAAA,GACToB,aAAa,CAAC,CAAC,eAGhBhE,OAAA;cAAK2C,SAAS,EAAC,4FAA4F;cAAAC,QAAA,gBACzG5C,OAAA,CAACP,MAAM;gBAACsE,OAAO,EAAC,SAAS;gBAAAnB,QAAA,EAAC;cAE1B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjD,OAAA,CAACP,MAAM;gBAACiF,OAAO,EAAEtC,YAAa;gBAACO,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAEpF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/C,EAAA,CAvZID,QAAQ;EAAA,QACiBL,OAAO;AAAA;AAAAgF,EAAA,GADhC3E,QAAQ;AAyZd,eAAeA,QAAQ;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}