{"ast": null, "code": "// API لإدارة المصاريف\nclass ExpensesAPI {\n  constructor() {\n    this.storageKey = 'expenses_data';\n    this.initializeData();\n  }\n  initializeData() {\n    const existingData = localStorage.getItem(this.storageKey);\n    if (!existingData) {\n      const initialData = {\n        expenses: [{\n          id: 1,\n          code: 'EXP-2024-001',\n          description: 'إيجار المكتب - يناير 2024',\n          category: 'إيجار',\n          amount: 15000,\n          date: '2024-01-01',\n          paymentMethod: 'تحويل بنكي',\n          vendor: 'شركة العقارات المتميزة',\n          reference: 'RENT-JAN-2024',\n          status: 'مدفوع',\n          approvedBy: 'أحمد المدير',\n          notes: 'إيجار شهري منتظم',\n          receiptNumber: 'REC-001',\n          taxAmount: 2250,\n          isRecurring: true,\n          recurringPeriod: 'شهري'\n        }, {\n          id: 2,\n          code: 'EXP-2024-002',\n          description: 'فاتورة كهرباء - ديسمبر 2023',\n          category: 'مرافق',\n          amount: 3500,\n          date: '2024-01-05',\n          paymentMethod: 'نقدي',\n          vendor: 'شركة الكهرباء الوطنية',\n          reference: 'ELEC-DEC-2023',\n          status: 'مدفوع',\n          approvedBy: 'فاطمة المحاسبة',\n          notes: 'فاتورة شهرية',\n          receiptNumber: 'REC-002',\n          taxAmount: 525,\n          isRecurring: true,\n          recurringPeriod: 'شهري'\n        }, {\n          id: 3,\n          code: 'EXP-2024-003',\n          description: 'شراء أدوات مكتبية',\n          category: 'مكتبية',\n          amount: 850,\n          date: '2024-01-10',\n          paymentMethod: 'بطاقة ائتمان',\n          vendor: 'مكتبة الأعمال',\n          reference: 'OFF-SUP-001',\n          status: 'معلق',\n          approvedBy: '',\n          notes: 'أقلام، أوراق، مجلدات',\n          receiptNumber: '',\n          taxAmount: 127.5,\n          isRecurring: false,\n          recurringPeriod: ''\n        }, {\n          id: 4,\n          code: 'EXP-2024-004',\n          description: 'صيانة أجهزة الكمبيوتر',\n          category: 'صيانة',\n          amount: 2200,\n          date: '2024-01-12',\n          paymentMethod: 'نقدي',\n          vendor: 'مركز الصيانة التقني',\n          reference: 'MAINT-001',\n          status: 'مدفوع',\n          approvedBy: 'خالد التقني',\n          notes: 'صيانة دورية للأجهزة',\n          receiptNumber: 'REC-003',\n          taxAmount: 330,\n          isRecurring: false,\n          recurringPeriod: ''\n        }],\n        categories: ['إيجار', 'مرافق', 'مكتبية', 'صيانة', 'رواتب', 'تسويق', 'سفر', 'اتصالات', 'تأمين', 'أخرى'],\n        lastId: 4\n      };\n      localStorage.setItem(this.storageKey, JSON.stringify(initialData));\n    }\n  }\n  getData() {\n    return JSON.parse(localStorage.getItem(this.storageKey));\n  }\n  saveData(data) {\n    localStorage.setItem(this.storageKey, JSON.stringify(data));\n  }\n\n  // الحصول على جميع المصاريف\n  getAllExpenses() {\n    return this.getData().expenses;\n  }\n\n  // إضافة مصروف جديد\n  addExpense(expenseData) {\n    const data = this.getData();\n    const newExpense = {\n      id: data.lastId + 1,\n      code: `EXP-${new Date().getFullYear()}-${String(data.lastId + 1).padStart(3, '0')}`,\n      date: new Date().toISOString().split('T')[0],\n      status: 'معلق',\n      isRecurring: false,\n      ...expenseData\n    };\n    data.expenses.push(newExpense);\n    data.lastId += 1;\n    this.saveData(data);\n    return newExpense;\n  }\n\n  // تحديث مصروف\n  updateExpense(id, expenseData) {\n    const data = this.getData();\n    const index = data.expenses.findIndex(expense => expense.id === id);\n    if (index !== -1) {\n      data.expenses[index] = {\n        ...data.expenses[index],\n        ...expenseData\n      };\n      this.saveData(data);\n      return data.expenses[index];\n    }\n    return null;\n  }\n\n  // حذف مصروف\n  deleteExpense(id) {\n    const data = this.getData();\n    data.expenses = data.expenses.filter(expense => expense.id !== id);\n    this.saveData(data);\n    return true;\n  }\n\n  // الحصول على مصروف بالمعرف\n  getExpenseById(id) {\n    const data = this.getData();\n    return data.expenses.find(expense => expense.id === id);\n  }\n\n  // إحصائيات المصاريف\n  getExpensesStats() {\n    const expenses = this.getAllExpenses();\n    const today = new Date().toISOString().split('T')[0];\n    const thisMonth = new Date().toISOString().slice(0, 7);\n    const thisYear = new Date().getFullYear().toString();\n    return {\n      totalExpenses: expenses.reduce((sum, expense) => sum + expense.amount, 0),\n      todayExpenses: expenses.filter(expense => expense.date === today).reduce((sum, expense) => sum + expense.amount, 0),\n      monthExpenses: expenses.filter(expense => expense.date.startsWith(thisMonth)).reduce((sum, expense) => sum + expense.amount, 0),\n      yearExpenses: expenses.filter(expense => expense.date.startsWith(thisYear)).reduce((sum, expense) => sum + expense.amount, 0),\n      totalCount: expenses.length,\n      paidExpenses: expenses.filter(expense => expense.status === 'مدفوع').length,\n      pendingExpenses: expenses.filter(expense => expense.status === 'معلق').length,\n      recurringExpenses: expenses.filter(expense => expense.isRecurring).length\n    };\n  }\n\n  // المصاريف حسب الفئة\n  getExpensesByCategory() {\n    const expenses = this.getAllExpenses();\n    const categories = {};\n    expenses.forEach(expense => {\n      if (!categories[expense.category]) {\n        categories[expense.category] = {\n          total: 0,\n          count: 0,\n          expenses: []\n        };\n      }\n      categories[expense.category].total += expense.amount;\n      categories[expense.category].count += 1;\n      categories[expense.category].expenses.push(expense);\n    });\n    return categories;\n  }\n\n  // المصاريف المعلقة\n  getPendingExpenses() {\n    const expenses = this.getAllExpenses();\n    return expenses.filter(expense => expense.status === 'معلق');\n  }\n\n  // المصاريف المتكررة\n  getRecurringExpenses() {\n    const expenses = this.getAllExpenses();\n    return expenses.filter(expense => expense.isRecurring);\n  }\n\n  // البحث في المصاريف\n  searchExpenses(query) {\n    const expenses = this.getAllExpenses();\n    return expenses.filter(expense => expense.description.toLowerCase().includes(query.toLowerCase()) || expense.code.toLowerCase().includes(query.toLowerCase()) || expense.category.toLowerCase().includes(query.toLowerCase()) || expense.vendor.toLowerCase().includes(query.toLowerCase()));\n  }\n\n  // الحصول على الفئات\n  getCategories() {\n    return this.getData().categories;\n  }\n\n  // إضافة فئة جديدة\n  addCategory(category) {\n    const data = this.getData();\n    if (!data.categories.includes(category)) {\n      data.categories.push(category);\n      this.saveData(data);\n    }\n    return data.categories;\n  }\n\n  // تقرير المصاريف الشهرية\n  getMonthlyExpensesReport(year) {\n    const expenses = this.getAllExpenses();\n    const monthlyData = {};\n    for (let month = 1; month <= 12; month++) {\n      const monthStr = `${year}-${String(month).padStart(2, '0')}`;\n      monthlyData[monthStr] = {\n        total: 0,\n        count: 0,\n        categories: {}\n      };\n    }\n    expenses.forEach(expense => {\n      const expenseMonth = expense.date.slice(0, 7);\n      if (expenseMonth.startsWith(year)) {\n        monthlyData[expenseMonth].total += expense.amount;\n        monthlyData[expenseMonth].count += 1;\n        if (!monthlyData[expenseMonth].categories[expense.category]) {\n          monthlyData[expenseMonth].categories[expense.category] = 0;\n        }\n        monthlyData[expenseMonth].categories[expense.category] += expense.amount;\n      }\n    });\n    return monthlyData;\n  }\n}\nexport default new ExpensesAPI();", "map": {"version": 3, "names": ["ExpensesAPI", "constructor", "storageKey", "initializeData", "existingData", "localStorage", "getItem", "initialData", "expenses", "id", "code", "description", "category", "amount", "date", "paymentMethod", "vendor", "reference", "status", "approvedBy", "notes", "receiptNumber", "taxAmount", "isRecurring", "<PERSON><PERSON><PERSON><PERSON>", "categories", "lastId", "setItem", "JSON", "stringify", "getData", "parse", "saveData", "data", "getAllExpenses", "addExpense", "expenseData", "newExpense", "Date", "getFullYear", "String", "padStart", "toISOString", "split", "push", "updateExpense", "index", "findIndex", "expense", "deleteExpense", "filter", "getExpenseById", "find", "getExpensesStats", "today", "thisMonth", "slice", "thisYear", "toString", "totalExpenses", "reduce", "sum", "todayExpenses", "monthExpenses", "startsWith", "yearExpenses", "totalCount", "length", "paidExpenses", "pendingExpenses", "recurringExpenses", "getExpensesByCategory", "for<PERSON>ach", "total", "count", "getPendingExpenses", "getRecurringExpenses", "searchExpenses", "query", "toLowerCase", "includes", "getCategories", "addCategory", "getMonthlyExpensesReport", "year", "monthlyData", "month", "monthStr", "expenseMonth"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/src/api/expensesAPI.js"], "sourcesContent": ["// API لإدارة المصاريف\nclass ExpensesAPI {\n  constructor() {\n    this.storageKey = 'expenses_data';\n    this.initializeData();\n  }\n\n  initializeData() {\n    const existingData = localStorage.getItem(this.storageKey);\n    if (!existingData) {\n      const initialData = {\n        expenses: [\n          {\n            id: 1,\n            code: 'EXP-2024-001',\n            description: 'إيجار المكتب - يناير 2024',\n            category: 'إيجار',\n            amount: 15000,\n            date: '2024-01-01',\n            paymentMethod: 'تحويل بنكي',\n            vendor: 'شركة العقارات المتميزة',\n            reference: 'RENT-JAN-2024',\n            status: 'مدفوع',\n            approvedBy: 'أحمد المدير',\n            notes: 'إيجار شهري منتظم',\n            receiptNumber: 'REC-001',\n            taxAmount: 2250,\n            isRecurring: true,\n            recurringPeriod: 'شهري'\n          },\n          {\n            id: 2,\n            code: 'EXP-2024-002',\n            description: 'فاتورة كهرباء - ديسمبر 2023',\n            category: 'مرافق',\n            amount: 3500,\n            date: '2024-01-05',\n            paymentMethod: 'نقدي',\n            vendor: 'شركة الكهرباء الوطنية',\n            reference: 'ELEC-DEC-2023',\n            status: 'مدفوع',\n            approvedBy: 'فاطمة المحاسبة',\n            notes: 'فاتورة شهرية',\n            receiptNumber: 'REC-002',\n            taxAmount: 525,\n            isRecurring: true,\n            recurringPeriod: 'شهري'\n          },\n          {\n            id: 3,\n            code: 'EXP-2024-003',\n            description: 'شراء أدوات مكتبية',\n            category: 'مكتبية',\n            amount: 850,\n            date: '2024-01-10',\n            paymentMethod: 'بطاقة ائتمان',\n            vendor: 'مكتبة الأعمال',\n            reference: 'OFF-SUP-001',\n            status: 'معلق',\n            approvedBy: '',\n            notes: 'أقلام، أوراق، مجلدات',\n            receiptNumber: '',\n            taxAmount: 127.5,\n            isRecurring: false,\n            recurringPeriod: ''\n          },\n          {\n            id: 4,\n            code: 'EXP-2024-004',\n            description: 'صيانة أجهزة الكمبيوتر',\n            category: 'صيانة',\n            amount: 2200,\n            date: '2024-01-12',\n            paymentMethod: 'نقدي',\n            vendor: 'مركز الصيانة التقني',\n            reference: 'MAINT-001',\n            status: 'مدفوع',\n            approvedBy: 'خالد التقني',\n            notes: 'صيانة دورية للأجهزة',\n            receiptNumber: 'REC-003',\n            taxAmount: 330,\n            isRecurring: false,\n            recurringPeriod: ''\n          }\n        ],\n        categories: ['إيجار', 'مرافق', 'مكتبية', 'صيانة', 'رواتب', 'تسويق', 'سفر', 'اتصالات', 'تأمين', 'أخرى'],\n        lastId: 4\n      };\n      localStorage.setItem(this.storageKey, JSON.stringify(initialData));\n    }\n  }\n\n  getData() {\n    return JSON.parse(localStorage.getItem(this.storageKey));\n  }\n\n  saveData(data) {\n    localStorage.setItem(this.storageKey, JSON.stringify(data));\n  }\n\n  // الحصول على جميع المصاريف\n  getAllExpenses() {\n    return this.getData().expenses;\n  }\n\n  // إضافة مصروف جديد\n  addExpense(expenseData) {\n    const data = this.getData();\n    const newExpense = {\n      id: data.lastId + 1,\n      code: `EXP-${new Date().getFullYear()}-${String(data.lastId + 1).padStart(3, '0')}`,\n      date: new Date().toISOString().split('T')[0],\n      status: 'معلق',\n      isRecurring: false,\n      ...expenseData\n    };\n    \n    data.expenses.push(newExpense);\n    data.lastId += 1;\n    this.saveData(data);\n    return newExpense;\n  }\n\n  // تحديث مصروف\n  updateExpense(id, expenseData) {\n    const data = this.getData();\n    const index = data.expenses.findIndex(expense => expense.id === id);\n    if (index !== -1) {\n      data.expenses[index] = { ...data.expenses[index], ...expenseData };\n      this.saveData(data);\n      return data.expenses[index];\n    }\n    return null;\n  }\n\n  // حذف مصروف\n  deleteExpense(id) {\n    const data = this.getData();\n    data.expenses = data.expenses.filter(expense => expense.id !== id);\n    this.saveData(data);\n    return true;\n  }\n\n  // الحصول على مصروف بالمعرف\n  getExpenseById(id) {\n    const data = this.getData();\n    return data.expenses.find(expense => expense.id === id);\n  }\n\n  // إحصائيات المصاريف\n  getExpensesStats() {\n    const expenses = this.getAllExpenses();\n    const today = new Date().toISOString().split('T')[0];\n    const thisMonth = new Date().toISOString().slice(0, 7);\n    const thisYear = new Date().getFullYear().toString();\n    \n    return {\n      totalExpenses: expenses.reduce((sum, expense) => sum + expense.amount, 0),\n      todayExpenses: expenses.filter(expense => expense.date === today).reduce((sum, expense) => sum + expense.amount, 0),\n      monthExpenses: expenses.filter(expense => expense.date.startsWith(thisMonth)).reduce((sum, expense) => sum + expense.amount, 0),\n      yearExpenses: expenses.filter(expense => expense.date.startsWith(thisYear)).reduce((sum, expense) => sum + expense.amount, 0),\n      totalCount: expenses.length,\n      paidExpenses: expenses.filter(expense => expense.status === 'مدفوع').length,\n      pendingExpenses: expenses.filter(expense => expense.status === 'معلق').length,\n      recurringExpenses: expenses.filter(expense => expense.isRecurring).length\n    };\n  }\n\n  // المصاريف حسب الفئة\n  getExpensesByCategory() {\n    const expenses = this.getAllExpenses();\n    const categories = {};\n    \n    expenses.forEach(expense => {\n      if (!categories[expense.category]) {\n        categories[expense.category] = {\n          total: 0,\n          count: 0,\n          expenses: []\n        };\n      }\n      categories[expense.category].total += expense.amount;\n      categories[expense.category].count += 1;\n      categories[expense.category].expenses.push(expense);\n    });\n    \n    return categories;\n  }\n\n  // المصاريف المعلقة\n  getPendingExpenses() {\n    const expenses = this.getAllExpenses();\n    return expenses.filter(expense => expense.status === 'معلق');\n  }\n\n  // المصاريف المتكررة\n  getRecurringExpenses() {\n    const expenses = this.getAllExpenses();\n    return expenses.filter(expense => expense.isRecurring);\n  }\n\n  // البحث في المصاريف\n  searchExpenses(query) {\n    const expenses = this.getAllExpenses();\n    return expenses.filter(expense => \n      expense.description.toLowerCase().includes(query.toLowerCase()) ||\n      expense.code.toLowerCase().includes(query.toLowerCase()) ||\n      expense.category.toLowerCase().includes(query.toLowerCase()) ||\n      expense.vendor.toLowerCase().includes(query.toLowerCase())\n    );\n  }\n\n  // الحصول على الفئات\n  getCategories() {\n    return this.getData().categories;\n  }\n\n  // إضافة فئة جديدة\n  addCategory(category) {\n    const data = this.getData();\n    if (!data.categories.includes(category)) {\n      data.categories.push(category);\n      this.saveData(data);\n    }\n    return data.categories;\n  }\n\n  // تقرير المصاريف الشهرية\n  getMonthlyExpensesReport(year) {\n    const expenses = this.getAllExpenses();\n    const monthlyData = {};\n    \n    for (let month = 1; month <= 12; month++) {\n      const monthStr = `${year}-${String(month).padStart(2, '0')}`;\n      monthlyData[monthStr] = {\n        total: 0,\n        count: 0,\n        categories: {}\n      };\n    }\n    \n    expenses.forEach(expense => {\n      const expenseMonth = expense.date.slice(0, 7);\n      if (expenseMonth.startsWith(year)) {\n        monthlyData[expenseMonth].total += expense.amount;\n        monthlyData[expenseMonth].count += 1;\n        \n        if (!monthlyData[expenseMonth].categories[expense.category]) {\n          monthlyData[expenseMonth].categories[expense.category] = 0;\n        }\n        monthlyData[expenseMonth].categories[expense.category] += expense.amount;\n      }\n    });\n    \n    return monthlyData;\n  }\n}\n\nexport default new ExpensesAPI();\n"], "mappings": "AAAA;AACA,MAAMA,WAAW,CAAC;EAChBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,UAAU,GAAG,eAAe;IACjC,IAAI,CAACC,cAAc,CAAC,CAAC;EACvB;EAEAA,cAAcA,CAAA,EAAG;IACf,MAAMC,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,IAAI,CAACJ,UAAU,CAAC;IAC1D,IAAI,CAACE,YAAY,EAAE;MACjB,MAAMG,WAAW,GAAG;QAClBC,QAAQ,EAAE,CACR;UACEC,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,cAAc;UACpBC,WAAW,EAAE,2BAA2B;UACxCC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE,KAAK;UACbC,IAAI,EAAE,YAAY;UAClBC,aAAa,EAAE,YAAY;UAC3BC,MAAM,EAAE,wBAAwB;UAChCC,SAAS,EAAE,eAAe;UAC1BC,MAAM,EAAE,OAAO;UACfC,UAAU,EAAE,aAAa;UACzBC,KAAK,EAAE,kBAAkB;UACzBC,aAAa,EAAE,SAAS;UACxBC,SAAS,EAAE,IAAI;UACfC,WAAW,EAAE,IAAI;UACjBC,eAAe,EAAE;QACnB,CAAC,EACD;UACEf,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,cAAc;UACpBC,WAAW,EAAE,6BAA6B;UAC1CC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE,IAAI;UACZC,IAAI,EAAE,YAAY;UAClBC,aAAa,EAAE,MAAM;UACrBC,MAAM,EAAE,uBAAuB;UAC/BC,SAAS,EAAE,eAAe;UAC1BC,MAAM,EAAE,OAAO;UACfC,UAAU,EAAE,gBAAgB;UAC5BC,KAAK,EAAE,cAAc;UACrBC,aAAa,EAAE,SAAS;UACxBC,SAAS,EAAE,GAAG;UACdC,WAAW,EAAE,IAAI;UACjBC,eAAe,EAAE;QACnB,CAAC,EACD;UACEf,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,cAAc;UACpBC,WAAW,EAAE,mBAAmB;UAChCC,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAE,GAAG;UACXC,IAAI,EAAE,YAAY;UAClBC,aAAa,EAAE,cAAc;UAC7BC,MAAM,EAAE,eAAe;UACvBC,SAAS,EAAE,aAAa;UACxBC,MAAM,EAAE,MAAM;UACdC,UAAU,EAAE,EAAE;UACdC,KAAK,EAAE,sBAAsB;UAC7BC,aAAa,EAAE,EAAE;UACjBC,SAAS,EAAE,KAAK;UAChBC,WAAW,EAAE,KAAK;UAClBC,eAAe,EAAE;QACnB,CAAC,EACD;UACEf,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,cAAc;UACpBC,WAAW,EAAE,uBAAuB;UACpCC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE,IAAI;UACZC,IAAI,EAAE,YAAY;UAClBC,aAAa,EAAE,MAAM;UACrBC,MAAM,EAAE,qBAAqB;UAC7BC,SAAS,EAAE,WAAW;UACtBC,MAAM,EAAE,OAAO;UACfC,UAAU,EAAE,aAAa;UACzBC,KAAK,EAAE,qBAAqB;UAC5BC,aAAa,EAAE,SAAS;UACxBC,SAAS,EAAE,GAAG;UACdC,WAAW,EAAE,KAAK;UAClBC,eAAe,EAAE;QACnB,CAAC,CACF;QACDC,UAAU,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC;QACtGC,MAAM,EAAE;MACV,CAAC;MACDrB,YAAY,CAACsB,OAAO,CAAC,IAAI,CAACzB,UAAU,EAAE0B,IAAI,CAACC,SAAS,CAACtB,WAAW,CAAC,CAAC;IACpE;EACF;EAEAuB,OAAOA,CAAA,EAAG;IACR,OAAOF,IAAI,CAACG,KAAK,CAAC1B,YAAY,CAACC,OAAO,CAAC,IAAI,CAACJ,UAAU,CAAC,CAAC;EAC1D;EAEA8B,QAAQA,CAACC,IAAI,EAAE;IACb5B,YAAY,CAACsB,OAAO,CAAC,IAAI,CAACzB,UAAU,EAAE0B,IAAI,CAACC,SAAS,CAACI,IAAI,CAAC,CAAC;EAC7D;;EAEA;EACAC,cAAcA,CAAA,EAAG;IACf,OAAO,IAAI,CAACJ,OAAO,CAAC,CAAC,CAACtB,QAAQ;EAChC;;EAEA;EACA2B,UAAUA,CAACC,WAAW,EAAE;IACtB,MAAMH,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC,CAAC;IAC3B,MAAMO,UAAU,GAAG;MACjB5B,EAAE,EAAEwB,IAAI,CAACP,MAAM,GAAG,CAAC;MACnBhB,IAAI,EAAE,OAAO,IAAI4B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,IAAIC,MAAM,CAACP,IAAI,CAACP,MAAM,GAAG,CAAC,CAAC,CAACe,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;MACnF3B,IAAI,EAAE,IAAIwB,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC5CzB,MAAM,EAAE,MAAM;MACdK,WAAW,EAAE,KAAK;MAClB,GAAGa;IACL,CAAC;IAEDH,IAAI,CAACzB,QAAQ,CAACoC,IAAI,CAACP,UAAU,CAAC;IAC9BJ,IAAI,CAACP,MAAM,IAAI,CAAC;IAChB,IAAI,CAACM,QAAQ,CAACC,IAAI,CAAC;IACnB,OAAOI,UAAU;EACnB;;EAEA;EACAQ,aAAaA,CAACpC,EAAE,EAAE2B,WAAW,EAAE;IAC7B,MAAMH,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC,CAAC;IAC3B,MAAMgB,KAAK,GAAGb,IAAI,CAACzB,QAAQ,CAACuC,SAAS,CAACC,OAAO,IAAIA,OAAO,CAACvC,EAAE,KAAKA,EAAE,CAAC;IACnE,IAAIqC,KAAK,KAAK,CAAC,CAAC,EAAE;MAChBb,IAAI,CAACzB,QAAQ,CAACsC,KAAK,CAAC,GAAG;QAAE,GAAGb,IAAI,CAACzB,QAAQ,CAACsC,KAAK,CAAC;QAAE,GAAGV;MAAY,CAAC;MAClE,IAAI,CAACJ,QAAQ,CAACC,IAAI,CAAC;MACnB,OAAOA,IAAI,CAACzB,QAAQ,CAACsC,KAAK,CAAC;IAC7B;IACA,OAAO,IAAI;EACb;;EAEA;EACAG,aAAaA,CAACxC,EAAE,EAAE;IAChB,MAAMwB,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC,CAAC;IAC3BG,IAAI,CAACzB,QAAQ,GAAGyB,IAAI,CAACzB,QAAQ,CAAC0C,MAAM,CAACF,OAAO,IAAIA,OAAO,CAACvC,EAAE,KAAKA,EAAE,CAAC;IAClE,IAAI,CAACuB,QAAQ,CAACC,IAAI,CAAC;IACnB,OAAO,IAAI;EACb;;EAEA;EACAkB,cAAcA,CAAC1C,EAAE,EAAE;IACjB,MAAMwB,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC,CAAC;IAC3B,OAAOG,IAAI,CAACzB,QAAQ,CAAC4C,IAAI,CAACJ,OAAO,IAAIA,OAAO,CAACvC,EAAE,KAAKA,EAAE,CAAC;EACzD;;EAEA;EACA4C,gBAAgBA,CAAA,EAAG;IACjB,MAAM7C,QAAQ,GAAG,IAAI,CAAC0B,cAAc,CAAC,CAAC;IACtC,MAAMoB,KAAK,GAAG,IAAIhB,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACpD,MAAMY,SAAS,GAAG,IAAIjB,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,CAACc,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACtD,MAAMC,QAAQ,GAAG,IAAInB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACmB,QAAQ,CAAC,CAAC;IAEpD,OAAO;MACLC,aAAa,EAAEnD,QAAQ,CAACoD,MAAM,CAAC,CAACC,GAAG,EAAEb,OAAO,KAAKa,GAAG,GAAGb,OAAO,CAACnC,MAAM,EAAE,CAAC,CAAC;MACzEiD,aAAa,EAAEtD,QAAQ,CAAC0C,MAAM,CAACF,OAAO,IAAIA,OAAO,CAAClC,IAAI,KAAKwC,KAAK,CAAC,CAACM,MAAM,CAAC,CAACC,GAAG,EAAEb,OAAO,KAAKa,GAAG,GAAGb,OAAO,CAACnC,MAAM,EAAE,CAAC,CAAC;MACnHkD,aAAa,EAAEvD,QAAQ,CAAC0C,MAAM,CAACF,OAAO,IAAIA,OAAO,CAAClC,IAAI,CAACkD,UAAU,CAACT,SAAS,CAAC,CAAC,CAACK,MAAM,CAAC,CAACC,GAAG,EAAEb,OAAO,KAAKa,GAAG,GAAGb,OAAO,CAACnC,MAAM,EAAE,CAAC,CAAC;MAC/HoD,YAAY,EAAEzD,QAAQ,CAAC0C,MAAM,CAACF,OAAO,IAAIA,OAAO,CAAClC,IAAI,CAACkD,UAAU,CAACP,QAAQ,CAAC,CAAC,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEb,OAAO,KAAKa,GAAG,GAAGb,OAAO,CAACnC,MAAM,EAAE,CAAC,CAAC;MAC7HqD,UAAU,EAAE1D,QAAQ,CAAC2D,MAAM;MAC3BC,YAAY,EAAE5D,QAAQ,CAAC0C,MAAM,CAACF,OAAO,IAAIA,OAAO,CAAC9B,MAAM,KAAK,OAAO,CAAC,CAACiD,MAAM;MAC3EE,eAAe,EAAE7D,QAAQ,CAAC0C,MAAM,CAACF,OAAO,IAAIA,OAAO,CAAC9B,MAAM,KAAK,MAAM,CAAC,CAACiD,MAAM;MAC7EG,iBAAiB,EAAE9D,QAAQ,CAAC0C,MAAM,CAACF,OAAO,IAAIA,OAAO,CAACzB,WAAW,CAAC,CAAC4C;IACrE,CAAC;EACH;;EAEA;EACAI,qBAAqBA,CAAA,EAAG;IACtB,MAAM/D,QAAQ,GAAG,IAAI,CAAC0B,cAAc,CAAC,CAAC;IACtC,MAAMT,UAAU,GAAG,CAAC,CAAC;IAErBjB,QAAQ,CAACgE,OAAO,CAACxB,OAAO,IAAI;MAC1B,IAAI,CAACvB,UAAU,CAACuB,OAAO,CAACpC,QAAQ,CAAC,EAAE;QACjCa,UAAU,CAACuB,OAAO,CAACpC,QAAQ,CAAC,GAAG;UAC7B6D,KAAK,EAAE,CAAC;UACRC,KAAK,EAAE,CAAC;UACRlE,QAAQ,EAAE;QACZ,CAAC;MACH;MACAiB,UAAU,CAACuB,OAAO,CAACpC,QAAQ,CAAC,CAAC6D,KAAK,IAAIzB,OAAO,CAACnC,MAAM;MACpDY,UAAU,CAACuB,OAAO,CAACpC,QAAQ,CAAC,CAAC8D,KAAK,IAAI,CAAC;MACvCjD,UAAU,CAACuB,OAAO,CAACpC,QAAQ,CAAC,CAACJ,QAAQ,CAACoC,IAAI,CAACI,OAAO,CAAC;IACrD,CAAC,CAAC;IAEF,OAAOvB,UAAU;EACnB;;EAEA;EACAkD,kBAAkBA,CAAA,EAAG;IACnB,MAAMnE,QAAQ,GAAG,IAAI,CAAC0B,cAAc,CAAC,CAAC;IACtC,OAAO1B,QAAQ,CAAC0C,MAAM,CAACF,OAAO,IAAIA,OAAO,CAAC9B,MAAM,KAAK,MAAM,CAAC;EAC9D;;EAEA;EACA0D,oBAAoBA,CAAA,EAAG;IACrB,MAAMpE,QAAQ,GAAG,IAAI,CAAC0B,cAAc,CAAC,CAAC;IACtC,OAAO1B,QAAQ,CAAC0C,MAAM,CAACF,OAAO,IAAIA,OAAO,CAACzB,WAAW,CAAC;EACxD;;EAEA;EACAsD,cAAcA,CAACC,KAAK,EAAE;IACpB,MAAMtE,QAAQ,GAAG,IAAI,CAAC0B,cAAc,CAAC,CAAC;IACtC,OAAO1B,QAAQ,CAAC0C,MAAM,CAACF,OAAO,IAC5BA,OAAO,CAACrC,WAAW,CAACoE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC,IAC/D/B,OAAO,CAACtC,IAAI,CAACqE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC,IACxD/B,OAAO,CAACpC,QAAQ,CAACmE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC,IAC5D/B,OAAO,CAAChC,MAAM,CAAC+D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,KAAK,CAACC,WAAW,CAAC,CAAC,CAC3D,CAAC;EACH;;EAEA;EACAE,aAAaA,CAAA,EAAG;IACd,OAAO,IAAI,CAACnD,OAAO,CAAC,CAAC,CAACL,UAAU;EAClC;;EAEA;EACAyD,WAAWA,CAACtE,QAAQ,EAAE;IACpB,MAAMqB,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACG,IAAI,CAACR,UAAU,CAACuD,QAAQ,CAACpE,QAAQ,CAAC,EAAE;MACvCqB,IAAI,CAACR,UAAU,CAACmB,IAAI,CAAChC,QAAQ,CAAC;MAC9B,IAAI,CAACoB,QAAQ,CAACC,IAAI,CAAC;IACrB;IACA,OAAOA,IAAI,CAACR,UAAU;EACxB;;EAEA;EACA0D,wBAAwBA,CAACC,IAAI,EAAE;IAC7B,MAAM5E,QAAQ,GAAG,IAAI,CAAC0B,cAAc,CAAC,CAAC;IACtC,MAAMmD,WAAW,GAAG,CAAC,CAAC;IAEtB,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,IAAI,EAAE,EAAEA,KAAK,EAAE,EAAE;MACxC,MAAMC,QAAQ,GAAG,GAAGH,IAAI,IAAI5C,MAAM,CAAC8C,KAAK,CAAC,CAAC7C,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;MAC5D4C,WAAW,CAACE,QAAQ,CAAC,GAAG;QACtBd,KAAK,EAAE,CAAC;QACRC,KAAK,EAAE,CAAC;QACRjD,UAAU,EAAE,CAAC;MACf,CAAC;IACH;IAEAjB,QAAQ,CAACgE,OAAO,CAACxB,OAAO,IAAI;MAC1B,MAAMwC,YAAY,GAAGxC,OAAO,CAAClC,IAAI,CAAC0C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MAC7C,IAAIgC,YAAY,CAACxB,UAAU,CAACoB,IAAI,CAAC,EAAE;QACjCC,WAAW,CAACG,YAAY,CAAC,CAACf,KAAK,IAAIzB,OAAO,CAACnC,MAAM;QACjDwE,WAAW,CAACG,YAAY,CAAC,CAACd,KAAK,IAAI,CAAC;QAEpC,IAAI,CAACW,WAAW,CAACG,YAAY,CAAC,CAAC/D,UAAU,CAACuB,OAAO,CAACpC,QAAQ,CAAC,EAAE;UAC3DyE,WAAW,CAACG,YAAY,CAAC,CAAC/D,UAAU,CAACuB,OAAO,CAACpC,QAAQ,CAAC,GAAG,CAAC;QAC5D;QACAyE,WAAW,CAACG,YAAY,CAAC,CAAC/D,UAAU,CAACuB,OAAO,CAACpC,QAAQ,CAAC,IAAIoC,OAAO,CAACnC,MAAM;MAC1E;IACF,CAAC,CAAC;IAEF,OAAOwE,WAAW;EACpB;AACF;AAEA,eAAe,IAAIrF,WAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}