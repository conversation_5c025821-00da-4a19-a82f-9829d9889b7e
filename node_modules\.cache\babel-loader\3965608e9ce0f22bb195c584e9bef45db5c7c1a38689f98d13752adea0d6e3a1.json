{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u0633\\u0637\\u062D \\u0627\\u0644\\u0645\\u0643\\u062A\\u0628\\\\333\\\\src\\\\components\\\\Expenses\\\\ExpenseForm.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { XMarkIcon } from '@heroicons/react/24/outline';\nimport { Button } from '../ui/button';\nimport { Input } from '../ui/input';\nimport { Label } from '../ui/label';\nimport expensesAPI from '../../api/expensesAPI';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ExpenseForm = ({\n  expense,\n  isEditing,\n  onSave,\n  onClose\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    description: '',\n    category: '',\n    amount: 0,\n    date: new Date().toISOString().split('T')[0],\n    paymentMethod: 'نقدي',\n    vendor: '',\n    reference: '',\n    approvedBy: '',\n    notes: '',\n    receiptNumber: '',\n    taxAmount: 0,\n    isRecurring: false,\n    recurringPeriod: ''\n  });\n  const [categories, setCategories] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  useEffect(() => {\n    loadCategories();\n    if (isEditing && expense) {\n      setFormData({\n        description: expense.description || '',\n        category: expense.category || '',\n        amount: expense.amount || 0,\n        date: expense.date || new Date().toISOString().split('T')[0],\n        paymentMethod: expense.paymentMethod || 'نقدي',\n        vendor: expense.vendor || '',\n        reference: expense.reference || '',\n        approvedBy: expense.approvedBy || '',\n        notes: expense.notes || '',\n        receiptNumber: expense.receiptNumber || '',\n        taxAmount: expense.taxAmount || 0,\n        isRecurring: expense.isRecurring || false,\n        recurringPeriod: expense.recurringPeriod || ''\n      });\n    }\n  }, [isEditing, expense]);\n  useEffect(() => {\n    // حساب الضريبة تلقائياً (15%)\n    const tax = formData.amount * 0.15;\n    setFormData(prev => ({\n      ...prev,\n      taxAmount: tax\n    }));\n  }, [formData.amount]);\n  const loadCategories = () => {\n    const categoriesData = expensesAPI.getCategories();\n    setCategories(categoriesData);\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!formData.description.trim()) {\n      toast.error('يرجى إدخال وصف المصروف');\n      return;\n    }\n    if (!formData.category.trim()) {\n      toast.error('يرجى اختيار فئة المصروف');\n      return;\n    }\n    if (formData.amount <= 0) {\n      toast.error('يرجى إدخال مبلغ صحيح');\n      return;\n    }\n    setIsLoading(true);\n    try {\n      await onSave(formData);\n    } catch (error) {\n      toast.error('حدث خطأ أثناء حفظ المصروف');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleCategoryChange = category => {\n    setFormData(prev => ({\n      ...prev,\n      category\n    }));\n\n    // إضافة فئة جديدة إذا لم تكن موجودة\n    if (category && !categories.includes(category)) {\n      expensesAPI.addCategory(category);\n      setCategories(prev => [...prev, category]);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(AnimatePresence, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          scale: 0.95\n        },\n        animate: {\n          opacity: 1,\n          scale: 1\n        },\n        exit: {\n          opacity: 0,\n          scale: 0.95\n        },\n        className: \"bg-white rounded-2xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-gray-900\",\n            children: isEditing ? 'تعديل المصروف' : 'مصروف جديد'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onClose,\n            className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg\",\n            children: /*#__PURE__*/_jsxDEV(XMarkIcon, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"p-6 space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"\\u0627\\u0644\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0623\\u0633\\u0627\\u0633\\u064A\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:col-span-2\",\n                children: [/*#__PURE__*/_jsxDEV(Label, {\n                  htmlFor: \"description\",\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"\\u0648\\u0635\\u0641 \\u0627\\u0644\\u0645\\u0635\\u0631\\u0648\\u0641 *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Input, {\n                  id: \"description\",\n                  type: \"text\",\n                  value: formData.description,\n                  onChange: e => setFormData(prev => ({\n                    ...prev,\n                    description: e.target.value\n                  })),\n                  placeholder: \"\\u0623\\u062F\\u062E\\u0644 \\u0648\\u0635\\u0641 \\u0627\\u0644\\u0645\\u0635\\u0631\\u0648\\u0641\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Label, {\n                  htmlFor: \"category\",\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"\\u0627\\u0644\\u0641\\u0626\\u0629 *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"category\",\n                  list: \"categories\",\n                  value: formData.category,\n                  onChange: e => handleCategoryChange(e.target.value),\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500\",\n                  placeholder: \"\\u0627\\u062E\\u062A\\u0631 \\u0623\\u0648 \\u0623\\u062F\\u062E\\u0644 \\u0641\\u0626\\u0629 \\u062C\\u062F\\u064A\\u062F\\u0629\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"datalist\", {\n                  id: \"categories\",\n                  children: categories.map((category, index) => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: category\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 158,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Label, {\n                  htmlFor: \"amount\",\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"\\u0627\\u0644\\u0645\\u0628\\u0644\\u063A *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Input, {\n                  id: \"amount\",\n                  type: \"number\",\n                  step: \"0.01\",\n                  min: \"0\",\n                  value: formData.amount,\n                  onChange: e => setFormData(prev => ({\n                    ...prev,\n                    amount: parseFloat(e.target.value) || 0\n                  })),\n                  placeholder: \"0.00\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Label, {\n                  htmlFor: \"date\",\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Input, {\n                  id: \"date\",\n                  type: \"date\",\n                  value: formData.date,\n                  onChange: e => setFormData(prev => ({\n                    ...prev,\n                    date: e.target.value\n                  })),\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Label, {\n                  htmlFor: \"vendor\",\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"\\u0627\\u0644\\u0645\\u0648\\u0631\\u062F/\\u0627\\u0644\\u062C\\u0647\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Input, {\n                  id: \"vendor\",\n                  type: \"text\",\n                  value: formData.vendor,\n                  onChange: e => setFormData(prev => ({\n                    ...prev,\n                    vendor: e.target.value\n                  })),\n                  placeholder: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F \\u0623\\u0648 \\u0627\\u0644\\u062C\\u0647\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"\\u062A\\u0641\\u0627\\u0635\\u064A\\u0644 \\u0627\\u0644\\u062F\\u0641\\u0639\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Label, {\n                  htmlFor: \"paymentMethod\",\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"\\u0637\\u0631\\u064A\\u0642\\u0629 \\u0627\\u0644\\u062F\\u0641\\u0639\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  id: \"paymentMethod\",\n                  value: formData.paymentMethod,\n                  onChange: e => setFormData(prev => ({\n                    ...prev,\n                    paymentMethod: e.target.value\n                  })),\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\\u0646\\u0642\\u062F\\u064A\",\n                    children: \"\\u0646\\u0642\\u062F\\u064A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 221,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\\u062A\\u062D\\u0648\\u064A\\u0644 \\u0628\\u0646\\u0643\\u064A\",\n                    children: \"\\u062A\\u062D\\u0648\\u064A\\u0644 \\u0628\\u0646\\u0643\\u064A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 222,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\\u0628\\u0637\\u0627\\u0642\\u0629 \\u0627\\u0626\\u062A\\u0645\\u0627\\u0646\",\n                    children: \"\\u0628\\u0637\\u0627\\u0642\\u0629 \\u0627\\u0626\\u062A\\u0645\\u0627\\u0646\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 223,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\\u0634\\u064A\\u0643\",\n                    children: \"\\u0634\\u064A\\u0643\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 224,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Label, {\n                  htmlFor: \"reference\",\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0645\\u0631\\u062C\\u0639\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Input, {\n                  id: \"reference\",\n                  type: \"text\",\n                  value: formData.reference,\n                  onChange: e => setFormData(prev => ({\n                    ...prev,\n                    reference: e.target.value\n                  })),\n                  placeholder: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629 \\u0623\\u0648 \\u0627\\u0644\\u0645\\u0631\\u062C\\u0639\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Label, {\n                  htmlFor: \"receiptNumber\",\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0625\\u064A\\u0635\\u0627\\u0644\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Input, {\n                  id: \"receiptNumber\",\n                  type: \"text\",\n                  value: formData.receiptNumber,\n                  onChange: e => setFormData(prev => ({\n                    ...prev,\n                    receiptNumber: e.target.value\n                  })),\n                  placeholder: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0625\\u064A\\u0635\\u0627\\u0644\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"\\u062A\\u0641\\u0627\\u0635\\u064A\\u0644 \\u0625\\u0636\\u0627\\u0641\\u064A\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Label, {\n                  htmlFor: \"taxAmount\",\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"\\u0645\\u0628\\u0644\\u063A \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u0629 (15%)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg\",\n                  children: new Intl.NumberFormat('ar-SA', {\n                    style: 'currency',\n                    currency: 'SAR'\n                  }).format(formData.taxAmount)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Label, {\n                  htmlFor: \"approvedBy\",\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"\\u0645\\u0639\\u062A\\u0645\\u062F \\u0645\\u0646\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Input, {\n                  id: \"approvedBy\",\n                  type: \"text\",\n                  value: formData.approvedBy,\n                  onChange: e => setFormData(prev => ({\n                    ...prev,\n                    approvedBy: e.target.value\n                  })),\n                  placeholder: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0634\\u062E\\u0635 \\u0627\\u0644\\u0645\\u0639\\u062A\\u0645\\u062F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3 space-x-reverse\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"isRecurring\",\n                  type: \"checkbox\",\n                  checked: formData.isRecurring,\n                  onChange: e => setFormData(prev => ({\n                    ...prev,\n                    isRecurring: e.target.checked\n                  })),\n                  className: \"w-4 h-4 text-red-600 border-gray-300 rounded focus:ring-red-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Label, {\n                  htmlFor: \"isRecurring\",\n                  className: \"text-sm font-medium text-gray-700\",\n                  children: \"\\u0645\\u0635\\u0631\\u0648\\u0641 \\u0645\\u062A\\u0643\\u0631\\u0631\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 17\n              }, this), formData.isRecurring && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-4\",\n                children: [/*#__PURE__*/_jsxDEV(Label, {\n                  htmlFor: \"recurringPeriod\",\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"\\u0641\\u062A\\u0631\\u0629 \\u0627\\u0644\\u062A\\u0643\\u0631\\u0627\\u0631\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  id: \"recurringPeriod\",\n                  value: formData.recurringPeriod,\n                  onChange: e => setFormData(prev => ({\n                    ...prev,\n                    recurringPeriod: e.target.value\n                  })),\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"\\u0627\\u062E\\u062A\\u0631 \\u0641\\u062A\\u0631\\u0629 \\u0627\\u0644\\u062A\\u0643\\u0631\\u0627\\u0631\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 312,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\\u0623\\u0633\\u0628\\u0648\\u0639\\u064A\",\n                    children: \"\\u0623\\u0633\\u0628\\u0648\\u0639\\u064A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\\u0634\\u0647\\u0631\\u064A\",\n                    children: \"\\u0634\\u0647\\u0631\\u064A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\\u0631\\u0628\\u0639 \\u0633\\u0646\\u0648\\u064A\",\n                    children: \"\\u0631\\u0628\\u0639 \\u0633\\u0646\\u0648\\u064A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\\u0646\\u0635\\u0641 \\u0633\\u0646\\u0648\\u064A\",\n                    children: \"\\u0646\\u0635\\u0641 \\u0633\\u0646\\u0648\\u064A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 316,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\\u0633\\u0646\\u0648\\u064A\",\n                    children: \"\\u0633\\u0646\\u0648\\u064A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              htmlFor: \"notes\",\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"\\u0645\\u0644\\u0627\\u062D\\u0638\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              id: \"notes\",\n              rows: 3,\n              value: formData.notes,\n              onChange: e => setFormData(prev => ({\n                ...prev,\n                notes: e.target.value\n              })),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500\",\n              placeholder: \"\\u0623\\u062F\\u062E\\u0644 \\u0623\\u064A \\u0645\\u0644\\u0627\\u062D\\u0638\\u0627\\u062A \\u0625\\u0636\\u0627\\u0641\\u064A\\u0629...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-50 p-6 rounded-lg\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center text-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-semibold text-gray-900\",\n                children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0635\\u0631\\u0648\\u0641 (\\u0634\\u0627\\u0645\\u0644 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u0629):\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-2xl font-bold text-red-600\",\n                children: new Intl.NumberFormat('ar-SA', {\n                  style: 'currency',\n                  currency: 'SAR'\n                }).format(formData.amount + formData.taxAmount)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-end space-x-4 space-x-reverse pt-6 border-t border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"button\",\n              variant: \"outline\",\n              onClick: onClose,\n              disabled: isLoading,\n              children: \"\\u0625\\u0644\\u063A\\u0627\\u0621\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"submit\",\n              disabled: isLoading,\n              className: \"bg-red-600 hover:bg-red-700 text-white\",\n              children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ml-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 369,\n                  columnNumber: 21\n                }, this), \"\\u062C\\u0627\\u0631\\u064A \\u0627\\u0644\\u062D\\u0641\\u0638...\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 19\n              }, this) : isEditing ? 'حفظ التعديلات' : 'حفظ المصروف'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 102,\n    columnNumber: 5\n  }, this);\n};\n_s(ExpenseForm, \"f9AtbF6GSa0wHgeZQfqaWQ2s3Ro=\");\n_c = ExpenseForm;\nexport default ExpenseForm;\nvar _c;\n$RefreshReg$(_c, \"ExpenseForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "AnimatePresence", "XMarkIcon", "<PERSON><PERSON>", "Input", "Label", "expensesAPI", "toast", "jsxDEV", "_jsxDEV", "ExpenseForm", "expense", "isEditing", "onSave", "onClose", "_s", "formData", "setFormData", "description", "category", "amount", "date", "Date", "toISOString", "split", "paymentMethod", "vendor", "reference", "approvedBy", "notes", "receiptNumber", "taxAmount", "isRecurring", "<PERSON><PERSON><PERSON><PERSON>", "categories", "setCategories", "isLoading", "setIsLoading", "loadCategories", "tax", "prev", "categoriesData", "getCategories", "handleSubmit", "e", "preventDefault", "trim", "error", "handleCategoryChange", "includes", "addCategory", "children", "className", "div", "initial", "opacity", "scale", "animate", "exit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onSubmit", "htmlFor", "id", "type", "value", "onChange", "target", "placeholder", "required", "list", "map", "index", "step", "min", "parseFloat", "Intl", "NumberFormat", "style", "currency", "format", "checked", "rows", "variant", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/src/components/Expenses/ExpenseForm.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { XMarkIcon } from '@heroicons/react/24/outline';\nimport { Button } from '../ui/button';\nimport { Input } from '../ui/input';\nimport { Label } from '../ui/label';\nimport expensesAPI from '../../api/expensesAPI';\nimport toast from 'react-hot-toast';\n\nconst ExpenseForm = ({ expense, isEditing, onSave, onClose }) => {\n  const [formData, setFormData] = useState({\n    description: '',\n    category: '',\n    amount: 0,\n    date: new Date().toISOString().split('T')[0],\n    paymentMethod: 'نقدي',\n    vendor: '',\n    reference: '',\n    approvedBy: '',\n    notes: '',\n    receiptNumber: '',\n    taxAmount: 0,\n    isRecurring: false,\n    recurringPeriod: ''\n  });\n\n  const [categories, setCategories] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n\n  useEffect(() => {\n    loadCategories();\n    \n    if (isEditing && expense) {\n      setFormData({\n        description: expense.description || '',\n        category: expense.category || '',\n        amount: expense.amount || 0,\n        date: expense.date || new Date().toISOString().split('T')[0],\n        paymentMethod: expense.paymentMethod || 'نقدي',\n        vendor: expense.vendor || '',\n        reference: expense.reference || '',\n        approvedBy: expense.approvedBy || '',\n        notes: expense.notes || '',\n        receiptNumber: expense.receiptNumber || '',\n        taxAmount: expense.taxAmount || 0,\n        isRecurring: expense.isRecurring || false,\n        recurringPeriod: expense.recurringPeriod || ''\n      });\n    }\n  }, [isEditing, expense]);\n\n  useEffect(() => {\n    // حساب الضريبة تلقائياً (15%)\n    const tax = formData.amount * 0.15;\n    setFormData(prev => ({ ...prev, taxAmount: tax }));\n  }, [formData.amount]);\n\n  const loadCategories = () => {\n    const categoriesData = expensesAPI.getCategories();\n    setCategories(categoriesData);\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!formData.description.trim()) {\n      toast.error('يرجى إدخال وصف المصروف');\n      return;\n    }\n\n    if (!formData.category.trim()) {\n      toast.error('يرجى اختيار فئة المصروف');\n      return;\n    }\n\n    if (formData.amount <= 0) {\n      toast.error('يرجى إدخال مبلغ صحيح');\n      return;\n    }\n\n    setIsLoading(true);\n    try {\n      await onSave(formData);\n    } catch (error) {\n      toast.error('حدث خطأ أثناء حفظ المصروف');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleCategoryChange = (category) => {\n    setFormData(prev => ({ ...prev, category }));\n    \n    // إضافة فئة جديدة إذا لم تكن موجودة\n    if (category && !categories.includes(category)) {\n      expensesAPI.addCategory(category);\n      setCategories(prev => [...prev, category]);\n    }\n  };\n\n  return (\n    <AnimatePresence>\n      <div className=\"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50\">\n        <motion.div\n          initial={{ opacity: 0, scale: 0.95 }}\n          animate={{ opacity: 1, scale: 1 }}\n          exit={{ opacity: 0, scale: 0.95 }}\n          className=\"bg-white rounded-2xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\"\n        >\n          {/* الرأس */}\n          <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n            <h2 className=\"text-xl font-semibold text-gray-900\">\n              {isEditing ? 'تعديل المصروف' : 'مصروف جديد'}\n            </h2>\n            <button\n              onClick={onClose}\n              className=\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg\"\n            >\n              <XMarkIcon className=\"w-5 h-5\" />\n            </button>\n          </div>\n\n          {/* المحتوى */}\n          <form onSubmit={handleSubmit} className=\"p-6 space-y-6\">\n            {/* المعلومات الأساسية */}\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">المعلومات الأساسية</h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div className=\"md:col-span-2\">\n                  <Label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    وصف المصروف *\n                  </Label>\n                  <Input\n                    id=\"description\"\n                    type=\"text\"\n                    value={formData.description}\n                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\n                    placeholder=\"أدخل وصف المصروف\"\n                    required\n                  />\n                </div>\n\n                <div>\n                  <Label htmlFor=\"category\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    الفئة *\n                  </Label>\n                  <input\n                    id=\"category\"\n                    list=\"categories\"\n                    value={formData.category}\n                    onChange={(e) => handleCategoryChange(e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500\"\n                    placeholder=\"اختر أو أدخل فئة جديدة\"\n                    required\n                  />\n                  <datalist id=\"categories\">\n                    {categories.map((category, index) => (\n                      <option key={index} value={category} />\n                    ))}\n                  </datalist>\n                </div>\n\n                <div>\n                  <Label htmlFor=\"amount\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    المبلغ *\n                  </Label>\n                  <Input\n                    id=\"amount\"\n                    type=\"number\"\n                    step=\"0.01\"\n                    min=\"0\"\n                    value={formData.amount}\n                    onChange={(e) => setFormData(prev => ({ ...prev, amount: parseFloat(e.target.value) || 0 }))}\n                    placeholder=\"0.00\"\n                    required\n                  />\n                </div>\n\n                <div>\n                  <Label htmlFor=\"date\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    التاريخ *\n                  </Label>\n                  <Input\n                    id=\"date\"\n                    type=\"date\"\n                    value={formData.date}\n                    onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}\n                    required\n                  />\n                </div>\n\n                <div>\n                  <Label htmlFor=\"vendor\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    المورد/الجهة\n                  </Label>\n                  <Input\n                    id=\"vendor\"\n                    type=\"text\"\n                    value={formData.vendor}\n                    onChange={(e) => setFormData(prev => ({ ...prev, vendor: e.target.value }))}\n                    placeholder=\"اسم المورد أو الجهة\"\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* تفاصيل الدفع */}\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">تفاصيل الدفع</h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                <div>\n                  <Label htmlFor=\"paymentMethod\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    طريقة الدفع\n                  </Label>\n                  <select\n                    id=\"paymentMethod\"\n                    value={formData.paymentMethod}\n                    onChange={(e) => setFormData(prev => ({ ...prev, paymentMethod: e.target.value }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500\"\n                  >\n                    <option value=\"نقدي\">نقدي</option>\n                    <option value=\"تحويل بنكي\">تحويل بنكي</option>\n                    <option value=\"بطاقة ائتمان\">بطاقة ائتمان</option>\n                    <option value=\"شيك\">شيك</option>\n                  </select>\n                </div>\n\n                <div>\n                  <Label htmlFor=\"reference\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    رقم المرجع\n                  </Label>\n                  <Input\n                    id=\"reference\"\n                    type=\"text\"\n                    value={formData.reference}\n                    onChange={(e) => setFormData(prev => ({ ...prev, reference: e.target.value }))}\n                    placeholder=\"رقم الفاتورة أو المرجع\"\n                  />\n                </div>\n\n                <div>\n                  <Label htmlFor=\"receiptNumber\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    رقم الإيصال\n                  </Label>\n                  <Input\n                    id=\"receiptNumber\"\n                    type=\"text\"\n                    value={formData.receiptNumber}\n                    onChange={(e) => setFormData(prev => ({ ...prev, receiptNumber: e.target.value }))}\n                    placeholder=\"رقم الإيصال\"\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* الضريبة والموافقة */}\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">تفاصيل إضافية</h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <Label htmlFor=\"taxAmount\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    مبلغ الضريبة (15%)\n                  </Label>\n                  <div className=\"px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg\">\n                    {new Intl.NumberFormat('ar-SA', {\n                      style: 'currency',\n                      currency: 'SAR'\n                    }).format(formData.taxAmount)}\n                  </div>\n                </div>\n\n                <div>\n                  <Label htmlFor=\"approvedBy\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    معتمد من\n                  </Label>\n                  <Input\n                    id=\"approvedBy\"\n                    type=\"text\"\n                    value={formData.approvedBy}\n                    onChange={(e) => setFormData(prev => ({ ...prev, approvedBy: e.target.value }))}\n                    placeholder=\"اسم الشخص المعتمد\"\n                  />\n                </div>\n              </div>\n\n              {/* المصروف المتكرر */}\n              <div className=\"mt-6\">\n                <div className=\"flex items-center space-x-3 space-x-reverse\">\n                  <input\n                    id=\"isRecurring\"\n                    type=\"checkbox\"\n                    checked={formData.isRecurring}\n                    onChange={(e) => setFormData(prev => ({ ...prev, isRecurring: e.target.checked }))}\n                    className=\"w-4 h-4 text-red-600 border-gray-300 rounded focus:ring-red-500\"\n                  />\n                  <Label htmlFor=\"isRecurring\" className=\"text-sm font-medium text-gray-700\">\n                    مصروف متكرر\n                  </Label>\n                </div>\n\n                {formData.isRecurring && (\n                  <div className=\"mt-4\">\n                    <Label htmlFor=\"recurringPeriod\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      فترة التكرار\n                    </Label>\n                    <select\n                      id=\"recurringPeriod\"\n                      value={formData.recurringPeriod}\n                      onChange={(e) => setFormData(prev => ({ ...prev, recurringPeriod: e.target.value }))}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500\"\n                    >\n                      <option value=\"\">اختر فترة التكرار</option>\n                      <option value=\"أسبوعي\">أسبوعي</option>\n                      <option value=\"شهري\">شهري</option>\n                      <option value=\"ربع سنوي\">ربع سنوي</option>\n                      <option value=\"نصف سنوي\">نصف سنوي</option>\n                      <option value=\"سنوي\">سنوي</option>\n                    </select>\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* الملاحظات */}\n            <div>\n              <Label htmlFor=\"notes\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                ملاحظات\n              </Label>\n              <textarea\n                id=\"notes\"\n                rows={3}\n                value={formData.notes}\n                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500\"\n                placeholder=\"أدخل أي ملاحظات إضافية...\"\n              />\n            </div>\n\n            {/* ملخص المبلغ */}\n            <div className=\"bg-gray-50 p-6 rounded-lg\">\n              <div className=\"flex justify-between items-center text-lg\">\n                <span className=\"font-semibold text-gray-900\">إجمالي المصروف (شامل الضريبة):</span>\n                <span className=\"text-2xl font-bold text-red-600\">\n                  {new Intl.NumberFormat('ar-SA', {\n                    style: 'currency',\n                    currency: 'SAR'\n                  }).format(formData.amount + formData.taxAmount)}\n                </span>\n              </div>\n            </div>\n\n            {/* الأزرار */}\n            <div className=\"flex items-center justify-end space-x-4 space-x-reverse pt-6 border-t border-gray-200\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={onClose}\n                disabled={isLoading}\n              >\n                إلغاء\n              </Button>\n              <Button\n                type=\"submit\"\n                disabled={isLoading}\n                className=\"bg-red-600 hover:bg-red-700 text-white\"\n              >\n                {isLoading ? (\n                  <div className=\"flex items-center\">\n                    <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ml-2\"></div>\n                    جاري الحفظ...\n                  </div>\n                ) : (\n                  isEditing ? 'حفظ التعديلات' : 'حفظ المصروف'\n                )}\n              </Button>\n            </div>\n          </form>\n        </motion.div>\n      </div>\n    </AnimatePresence>\n  );\n};\n\nexport default ExpenseForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,KAAK,QAAQ,aAAa;AACnC,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,WAAW,GAAGA,CAAC;EAAEC,OAAO;EAAEC,SAAS;EAAEC,MAAM;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC/D,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC;IACvCoB,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC5CC,aAAa,EAAE,MAAM;IACrBC,MAAM,EAAE,EAAE;IACVC,SAAS,EAAE,EAAE;IACbC,UAAU,EAAE,EAAE;IACdC,KAAK,EAAE,EAAE;IACTC,aAAa,EAAE,EAAE;IACjBC,SAAS,EAAE,CAAC;IACZC,WAAW,EAAE,KAAK;IAClBC,eAAe,EAAE;EACnB,CAAC,CAAC;EAEF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACsC,SAAS,EAAEC,YAAY,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAEjDC,SAAS,CAAC,MAAM;IACduC,cAAc,CAAC,CAAC;IAEhB,IAAI1B,SAAS,IAAID,OAAO,EAAE;MACxBM,WAAW,CAAC;QACVC,WAAW,EAAEP,OAAO,CAACO,WAAW,IAAI,EAAE;QACtCC,QAAQ,EAAER,OAAO,CAACQ,QAAQ,IAAI,EAAE;QAChCC,MAAM,EAAET,OAAO,CAACS,MAAM,IAAI,CAAC;QAC3BC,IAAI,EAAEV,OAAO,CAACU,IAAI,IAAI,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC5DC,aAAa,EAAEd,OAAO,CAACc,aAAa,IAAI,MAAM;QAC9CC,MAAM,EAAEf,OAAO,CAACe,MAAM,IAAI,EAAE;QAC5BC,SAAS,EAAEhB,OAAO,CAACgB,SAAS,IAAI,EAAE;QAClCC,UAAU,EAAEjB,OAAO,CAACiB,UAAU,IAAI,EAAE;QACpCC,KAAK,EAAElB,OAAO,CAACkB,KAAK,IAAI,EAAE;QAC1BC,aAAa,EAAEnB,OAAO,CAACmB,aAAa,IAAI,EAAE;QAC1CC,SAAS,EAAEpB,OAAO,CAACoB,SAAS,IAAI,CAAC;QACjCC,WAAW,EAAErB,OAAO,CAACqB,WAAW,IAAI,KAAK;QACzCC,eAAe,EAAEtB,OAAO,CAACsB,eAAe,IAAI;MAC9C,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACrB,SAAS,EAAED,OAAO,CAAC,CAAC;EAExBZ,SAAS,CAAC,MAAM;IACd;IACA,MAAMwC,GAAG,GAAGvB,QAAQ,CAACI,MAAM,GAAG,IAAI;IAClCH,WAAW,CAACuB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAET,SAAS,EAAEQ;IAAI,CAAC,CAAC,CAAC;EACpD,CAAC,EAAE,CAACvB,QAAQ,CAACI,MAAM,CAAC,CAAC;EAErB,MAAMkB,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMG,cAAc,GAAGnC,WAAW,CAACoC,aAAa,CAAC,CAAC;IAClDP,aAAa,CAACM,cAAc,CAAC;EAC/B,CAAC;EAED,MAAME,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAAC7B,QAAQ,CAACE,WAAW,CAAC4B,IAAI,CAAC,CAAC,EAAE;MAChCvC,KAAK,CAACwC,KAAK,CAAC,wBAAwB,CAAC;MACrC;IACF;IAEA,IAAI,CAAC/B,QAAQ,CAACG,QAAQ,CAAC2B,IAAI,CAAC,CAAC,EAAE;MAC7BvC,KAAK,CAACwC,KAAK,CAAC,yBAAyB,CAAC;MACtC;IACF;IAEA,IAAI/B,QAAQ,CAACI,MAAM,IAAI,CAAC,EAAE;MACxBb,KAAK,CAACwC,KAAK,CAAC,sBAAsB,CAAC;MACnC;IACF;IAEAV,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF,MAAMxB,MAAM,CAACG,QAAQ,CAAC;IACxB,CAAC,CAAC,OAAO+B,KAAK,EAAE;MACdxC,KAAK,CAACwC,KAAK,CAAC,2BAA2B,CAAC;IAC1C,CAAC,SAAS;MACRV,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMW,oBAAoB,GAAI7B,QAAQ,IAAK;IACzCF,WAAW,CAACuB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAErB;IAAS,CAAC,CAAC,CAAC;;IAE5C;IACA,IAAIA,QAAQ,IAAI,CAACe,UAAU,CAACe,QAAQ,CAAC9B,QAAQ,CAAC,EAAE;MAC9Cb,WAAW,CAAC4C,WAAW,CAAC/B,QAAQ,CAAC;MACjCgB,aAAa,CAACK,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAErB,QAAQ,CAAC,CAAC;IAC5C;EACF,CAAC;EAED,oBACEV,OAAA,CAACR,eAAe;IAAAkD,QAAA,eACd1C,OAAA;MAAK2C,SAAS,EAAC,gFAAgF;MAAAD,QAAA,eAC7F1C,OAAA,CAACT,MAAM,CAACqD,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAK,CAAE;QACrCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAE,CAAE;QAClCE,IAAI,EAAE;UAAEH,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAK,CAAE;QAClCJ,SAAS,EAAC,8EAA8E;QAAAD,QAAA,gBAGxF1C,OAAA;UAAK2C,SAAS,EAAC,gEAAgE;UAAAD,QAAA,gBAC7E1C,OAAA;YAAI2C,SAAS,EAAC,qCAAqC;YAAAD,QAAA,EAChDvC,SAAS,GAAG,eAAe,GAAG;UAAY;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACLrD,OAAA;YACEsD,OAAO,EAAEjD,OAAQ;YACjBsC,SAAS,EAAC,oEAAoE;YAAAD,QAAA,eAE9E1C,OAAA,CAACP,SAAS;cAACkD,SAAS,EAAC;YAAS;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNrD,OAAA;UAAMuD,QAAQ,EAAErB,YAAa;UAACS,SAAS,EAAC,eAAe;UAAAD,QAAA,gBAErD1C,OAAA;YAAA0C,QAAA,gBACE1C,OAAA;cAAI2C,SAAS,EAAC,0CAA0C;cAAAD,QAAA,EAAC;YAAkB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChFrD,OAAA;cAAK2C,SAAS,EAAC,uCAAuC;cAAAD,QAAA,gBACpD1C,OAAA;gBAAK2C,SAAS,EAAC,eAAe;gBAAAD,QAAA,gBAC5B1C,OAAA,CAACJ,KAAK;kBAAC4D,OAAO,EAAC,aAAa;kBAACb,SAAS,EAAC,8CAA8C;kBAAAD,QAAA,EAAC;gBAEtF;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRrD,OAAA,CAACL,KAAK;kBACJ8D,EAAE,EAAC,aAAa;kBAChBC,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAEpD,QAAQ,CAACE,WAAY;kBAC5BmD,QAAQ,EAAGzB,CAAC,IAAK3B,WAAW,CAACuB,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEtB,WAAW,EAAE0B,CAAC,CAAC0B,MAAM,CAACF;kBAAM,CAAC,CAAC,CAAE;kBACjFG,WAAW,EAAC,wFAAkB;kBAC9BC,QAAQ;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENrD,OAAA;gBAAA0C,QAAA,gBACE1C,OAAA,CAACJ,KAAK;kBAAC4D,OAAO,EAAC,UAAU;kBAACb,SAAS,EAAC,8CAA8C;kBAAAD,QAAA,EAAC;gBAEnF;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRrD,OAAA;kBACEyD,EAAE,EAAC,UAAU;kBACbO,IAAI,EAAC,YAAY;kBACjBL,KAAK,EAAEpD,QAAQ,CAACG,QAAS;kBACzBkD,QAAQ,EAAGzB,CAAC,IAAKI,oBAAoB,CAACJ,CAAC,CAAC0B,MAAM,CAACF,KAAK,CAAE;kBACtDhB,SAAS,EAAC,yGAAyG;kBACnHmB,WAAW,EAAC,kHAAwB;kBACpCC,QAAQ;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACFrD,OAAA;kBAAUyD,EAAE,EAAC,YAAY;kBAAAf,QAAA,EACtBjB,UAAU,CAACwC,GAAG,CAAC,CAACvD,QAAQ,EAAEwD,KAAK,kBAC9BlE,OAAA;oBAAoB2D,KAAK,EAAEjD;kBAAS,GAAvBwD,KAAK;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAoB,CACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eAENrD,OAAA;gBAAA0C,QAAA,gBACE1C,OAAA,CAACJ,KAAK;kBAAC4D,OAAO,EAAC,QAAQ;kBAACb,SAAS,EAAC,8CAA8C;kBAAAD,QAAA,EAAC;gBAEjF;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRrD,OAAA,CAACL,KAAK;kBACJ8D,EAAE,EAAC,QAAQ;kBACXC,IAAI,EAAC,QAAQ;kBACbS,IAAI,EAAC,MAAM;kBACXC,GAAG,EAAC,GAAG;kBACPT,KAAK,EAAEpD,QAAQ,CAACI,MAAO;kBACvBiD,QAAQ,EAAGzB,CAAC,IAAK3B,WAAW,CAACuB,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEpB,MAAM,EAAE0D,UAAU,CAAClC,CAAC,CAAC0B,MAAM,CAACF,KAAK,CAAC,IAAI;kBAAE,CAAC,CAAC,CAAE;kBAC7FG,WAAW,EAAC,MAAM;kBAClBC,QAAQ;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENrD,OAAA;gBAAA0C,QAAA,gBACE1C,OAAA,CAACJ,KAAK;kBAAC4D,OAAO,EAAC,MAAM;kBAACb,SAAS,EAAC,8CAA8C;kBAAAD,QAAA,EAAC;gBAE/E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRrD,OAAA,CAACL,KAAK;kBACJ8D,EAAE,EAAC,MAAM;kBACTC,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAEpD,QAAQ,CAACK,IAAK;kBACrBgD,QAAQ,EAAGzB,CAAC,IAAK3B,WAAW,CAACuB,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEnB,IAAI,EAAEuB,CAAC,CAAC0B,MAAM,CAACF;kBAAM,CAAC,CAAC,CAAE;kBAC1EI,QAAQ;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENrD,OAAA;gBAAA0C,QAAA,gBACE1C,OAAA,CAACJ,KAAK;kBAAC4D,OAAO,EAAC,QAAQ;kBAACb,SAAS,EAAC,8CAA8C;kBAAAD,QAAA,EAAC;gBAEjF;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRrD,OAAA,CAACL,KAAK;kBACJ8D,EAAE,EAAC,QAAQ;kBACXC,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAEpD,QAAQ,CAACU,MAAO;kBACvB2C,QAAQ,EAAGzB,CAAC,IAAK3B,WAAW,CAACuB,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEd,MAAM,EAAEkB,CAAC,CAAC0B,MAAM,CAACF;kBAAM,CAAC,CAAC,CAAE;kBAC5EG,WAAW,EAAC;gBAAqB;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNrD,OAAA;YAAA0C,QAAA,gBACE1C,OAAA;cAAI2C,SAAS,EAAC,0CAA0C;cAAAD,QAAA,EAAC;YAAY;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1ErD,OAAA;cAAK2C,SAAS,EAAC,uCAAuC;cAAAD,QAAA,gBACpD1C,OAAA;gBAAA0C,QAAA,gBACE1C,OAAA,CAACJ,KAAK;kBAAC4D,OAAO,EAAC,eAAe;kBAACb,SAAS,EAAC,8CAA8C;kBAAAD,QAAA,EAAC;gBAExF;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRrD,OAAA;kBACEyD,EAAE,EAAC,eAAe;kBAClBE,KAAK,EAAEpD,QAAQ,CAACS,aAAc;kBAC9B4C,QAAQ,EAAGzB,CAAC,IAAK3B,WAAW,CAACuB,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEf,aAAa,EAAEmB,CAAC,CAAC0B,MAAM,CAACF;kBAAM,CAAC,CAAC,CAAE;kBACnFhB,SAAS,EAAC,yGAAyG;kBAAAD,QAAA,gBAEnH1C,OAAA;oBAAQ2D,KAAK,EAAC,0BAAM;oBAAAjB,QAAA,EAAC;kBAAI;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClCrD,OAAA;oBAAQ2D,KAAK,EAAC,yDAAY;oBAAAjB,QAAA,EAAC;kBAAU;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9CrD,OAAA;oBAAQ2D,KAAK,EAAC,qEAAc;oBAAAjB,QAAA,EAAC;kBAAY;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClDrD,OAAA;oBAAQ2D,KAAK,EAAC,oBAAK;oBAAAjB,QAAA,EAAC;kBAAG;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAENrD,OAAA;gBAAA0C,QAAA,gBACE1C,OAAA,CAACJ,KAAK;kBAAC4D,OAAO,EAAC,WAAW;kBAACb,SAAS,EAAC,8CAA8C;kBAAAD,QAAA,EAAC;gBAEpF;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRrD,OAAA,CAACL,KAAK;kBACJ8D,EAAE,EAAC,WAAW;kBACdC,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAEpD,QAAQ,CAACW,SAAU;kBAC1B0C,QAAQ,EAAGzB,CAAC,IAAK3B,WAAW,CAACuB,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEb,SAAS,EAAEiB,CAAC,CAAC0B,MAAM,CAACF;kBAAM,CAAC,CAAC,CAAE;kBAC/EG,WAAW,EAAC;gBAAwB;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENrD,OAAA;gBAAA0C,QAAA,gBACE1C,OAAA,CAACJ,KAAK;kBAAC4D,OAAO,EAAC,eAAe;kBAACb,SAAS,EAAC,8CAA8C;kBAAAD,QAAA,EAAC;gBAExF;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRrD,OAAA,CAACL,KAAK;kBACJ8D,EAAE,EAAC,eAAe;kBAClBC,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAEpD,QAAQ,CAACc,aAAc;kBAC9BuC,QAAQ,EAAGzB,CAAC,IAAK3B,WAAW,CAACuB,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEV,aAAa,EAAEc,CAAC,CAAC0B,MAAM,CAACF;kBAAM,CAAC,CAAC,CAAE;kBACnFG,WAAW,EAAC;gBAAa;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNrD,OAAA;YAAA0C,QAAA,gBACE1C,OAAA;cAAI2C,SAAS,EAAC,0CAA0C;cAAAD,QAAA,EAAC;YAAa;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3ErD,OAAA;cAAK2C,SAAS,EAAC,uCAAuC;cAAAD,QAAA,gBACpD1C,OAAA;gBAAA0C,QAAA,gBACE1C,OAAA,CAACJ,KAAK;kBAAC4D,OAAO,EAAC,WAAW;kBAACb,SAAS,EAAC,8CAA8C;kBAAAD,QAAA,EAAC;gBAEpF;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRrD,OAAA;kBAAK2C,SAAS,EAAC,wDAAwD;kBAAAD,QAAA,EACpE,IAAI4B,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;oBAC9BC,KAAK,EAAE,UAAU;oBACjBC,QAAQ,EAAE;kBACZ,CAAC,CAAC,CAACC,MAAM,CAACnE,QAAQ,CAACe,SAAS;gBAAC;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENrD,OAAA;gBAAA0C,QAAA,gBACE1C,OAAA,CAACJ,KAAK;kBAAC4D,OAAO,EAAC,YAAY;kBAACb,SAAS,EAAC,8CAA8C;kBAAAD,QAAA,EAAC;gBAErF;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRrD,OAAA,CAACL,KAAK;kBACJ8D,EAAE,EAAC,YAAY;kBACfC,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAEpD,QAAQ,CAACY,UAAW;kBAC3ByC,QAAQ,EAAGzB,CAAC,IAAK3B,WAAW,CAACuB,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEZ,UAAU,EAAEgB,CAAC,CAAC0B,MAAM,CAACF;kBAAM,CAAC,CAAC,CAAE;kBAChFG,WAAW,EAAC;gBAAmB;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNrD,OAAA;cAAK2C,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnB1C,OAAA;gBAAK2C,SAAS,EAAC,6CAA6C;gBAAAD,QAAA,gBAC1D1C,OAAA;kBACEyD,EAAE,EAAC,aAAa;kBAChBC,IAAI,EAAC,UAAU;kBACfiB,OAAO,EAAEpE,QAAQ,CAACgB,WAAY;kBAC9BqC,QAAQ,EAAGzB,CAAC,IAAK3B,WAAW,CAACuB,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAER,WAAW,EAAEY,CAAC,CAAC0B,MAAM,CAACc;kBAAQ,CAAC,CAAC,CAAE;kBACnFhC,SAAS,EAAC;gBAAiE;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E,CAAC,eACFrD,OAAA,CAACJ,KAAK;kBAAC4D,OAAO,EAAC,aAAa;kBAACb,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,EAAC;gBAE3E;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,EAEL9C,QAAQ,CAACgB,WAAW,iBACnBvB,OAAA;gBAAK2C,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBACnB1C,OAAA,CAACJ,KAAK;kBAAC4D,OAAO,EAAC,iBAAiB;kBAACb,SAAS,EAAC,8CAA8C;kBAAAD,QAAA,EAAC;gBAE1F;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRrD,OAAA;kBACEyD,EAAE,EAAC,iBAAiB;kBACpBE,KAAK,EAAEpD,QAAQ,CAACiB,eAAgB;kBAChCoC,QAAQ,EAAGzB,CAAC,IAAK3B,WAAW,CAACuB,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEP,eAAe,EAAEW,CAAC,CAAC0B,MAAM,CAACF;kBAAM,CAAC,CAAC,CAAE;kBACrFhB,SAAS,EAAC,yGAAyG;kBAAAD,QAAA,gBAEnH1C,OAAA;oBAAQ2D,KAAK,EAAC,EAAE;oBAAAjB,QAAA,EAAC;kBAAiB;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC3CrD,OAAA;oBAAQ2D,KAAK,EAAC,sCAAQ;oBAAAjB,QAAA,EAAC;kBAAM;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtCrD,OAAA;oBAAQ2D,KAAK,EAAC,0BAAM;oBAAAjB,QAAA,EAAC;kBAAI;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClCrD,OAAA;oBAAQ2D,KAAK,EAAC,6CAAU;oBAAAjB,QAAA,EAAC;kBAAQ;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC1CrD,OAAA;oBAAQ2D,KAAK,EAAC,6CAAU;oBAAAjB,QAAA,EAAC;kBAAQ;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC1CrD,OAAA;oBAAQ2D,KAAK,EAAC,0BAAM;oBAAAjB,QAAA,EAAC;kBAAI;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNrD,OAAA;YAAA0C,QAAA,gBACE1C,OAAA,CAACJ,KAAK;cAAC4D,OAAO,EAAC,OAAO;cAACb,SAAS,EAAC,8CAA8C;cAAAD,QAAA,EAAC;YAEhF;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRrD,OAAA;cACEyD,EAAE,EAAC,OAAO;cACVmB,IAAI,EAAE,CAAE;cACRjB,KAAK,EAAEpD,QAAQ,CAACa,KAAM;cACtBwC,QAAQ,EAAGzB,CAAC,IAAK3B,WAAW,CAACuB,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEX,KAAK,EAAEe,CAAC,CAAC0B,MAAM,CAACF;cAAM,CAAC,CAAC,CAAE;cAC3EhB,SAAS,EAAC,yGAAyG;cACnHmB,WAAW,EAAC;YAA2B;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNrD,OAAA;YAAK2C,SAAS,EAAC,2BAA2B;YAAAD,QAAA,eACxC1C,OAAA;cAAK2C,SAAS,EAAC,2CAA2C;cAAAD,QAAA,gBACxD1C,OAAA;gBAAM2C,SAAS,EAAC,6BAA6B;gBAAAD,QAAA,EAAC;cAA8B;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnFrD,OAAA;gBAAM2C,SAAS,EAAC,iCAAiC;gBAAAD,QAAA,EAC9C,IAAI4B,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;kBAC9BC,KAAK,EAAE,UAAU;kBACjBC,QAAQ,EAAE;gBACZ,CAAC,CAAC,CAACC,MAAM,CAACnE,QAAQ,CAACI,MAAM,GAAGJ,QAAQ,CAACe,SAAS;cAAC;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNrD,OAAA;YAAK2C,SAAS,EAAC,uFAAuF;YAAAD,QAAA,gBACpG1C,OAAA,CAACN,MAAM;cACLgE,IAAI,EAAC,QAAQ;cACbmB,OAAO,EAAC,SAAS;cACjBvB,OAAO,EAAEjD,OAAQ;cACjByE,QAAQ,EAAEnD,SAAU;cAAAe,QAAA,EACrB;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTrD,OAAA,CAACN,MAAM;cACLgE,IAAI,EAAC,QAAQ;cACboB,QAAQ,EAAEnD,SAAU;cACpBgB,SAAS,EAAC,wCAAwC;cAAAD,QAAA,EAEjDf,SAAS,gBACR3B,OAAA;gBAAK2C,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,gBAChC1C,OAAA;kBAAK2C,SAAS,EAAC;gBAAmF;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,8DAE3G;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,GAENlD,SAAS,GAAG,eAAe,GAAG;YAC/B;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACS,CAAC;AAEtB,CAAC;AAAC/C,EAAA,CApXIL,WAAW;AAAA8E,EAAA,GAAX9E,WAAW;AAsXjB,eAAeA,WAAW;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}