# 🏢 نظام إدارة الشركات الاحترافي

نظام إدارة شركات احترافي ومتكامل مبني بأحدث التقنيات. يوفر واجهة حديثة وسهلة الاستخدام لإدارة بيانات الشركات مع دعم كامل للغة العربية ونظام مصادقة متقدم.

## ✨ الميزات الاحترافية

### 🔐 نظام مصادقة متقدم
- تسجيل دخول آمن مع حماية الصفحات
- إدارة الجلسات والصلاحيات
- نظام مستخدمين متعدد المستويات

### 📊 لوحة تحكم شاملة
- إحصائيات فورية ومؤشرات أداء
- رسوم بيانية تفاعلية ومتقدمة
- متابعة الأنشطة الأخيرة
- إجراءات سريعة للمهام الشائعة

### 📈 نظام تقارير متطور
- تقارير مخصصة وقابلة للتفيلتر
- تصدير بصيغ متعددة (PDF, Excel, CSV)
- رسوم بيانية متقدمة وتحليلات عميقة
- فلاتر ذكية وبحث متقدم

### ⚙️ إعدادات متقدمة
- إدارة الملف الشخصي والحساب
- تخصيص المظهر واللغة
- إعدادات الأمان والخصوصية
- إدارة الإشعارات والتنبيهات

### 📱 تصميم متجاوب وحديث
- واجهة مستخدم عصرية وسهلة الاستخدام
- دعم كامل للأجهزة المحمولة واللوحية
- تأثيرات بصرية متقدمة وانتقالات سلسة
- دعم كامل للغة العربية واتجاه RTL

## 🛠️ التقنيات المستخدمة

### 🔧 التقنيات الأساسية
- **React 18.2.0** - مكتبة جافاسكريبت حديثة مع Hooks
- **React Router DOM** - نظام توجيه متقدم
- **TailwindCSS 3.3.3** - إطار عمل CSS متقدم
- **Framer Motion** - مكتبة الحركة والتأثيرات

### 📊 إدارة البيانات والحالة
- **TanStack React Query** - إدارة حالة الخادم والتخزين المؤقت
- **React Hook Form** - إدارة النماذج والتحقق
- **Yup** - مخطط التحقق من البيانات
- **Axios** - عميل HTTP متقدم

### 🎨 واجهة المستخدم والتصميم
- **Headless UI** - مكونات UI قابلة للوصول
- **Heroicons** - أيقونات SVG عالية الجودة
- **Lucide React** - مجموعة أيقونات إضافية
- **React Hot Toast** - نظام إشعارات متقدم

### 📈 التقارير والرسوم البيانية
- **Recharts** - مكتبة رسوم بيانية تفاعلية
- **React PDF** - توليد ملفات PDF
- **jsPDF** - مكتبة إنشاء PDF
- **html2canvas** - تحويل HTML إلى صور

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd company-management-app
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Start the development server**:
   ```bash
   npm start
   ```

4. **Open your browser** and navigate to `http://localhost:3000`

## Project Structure

```
src/
├── api/
│   └── companiesAPI.js          # API client for company operations
├── components/
│   ├── ui/                      # Reusable UI components
│   │   ├── button.jsx
│   │   ├── card.jsx
│   │   ├── dialog.jsx
│   │   ├── input.jsx
│   │   ├── label.jsx
│   │   └── table.jsx
│   └── CompanyManagement.jsx    # Main company management component
├── lib/
│   └── utils.js                 # Utility functions
├── App.js                       # Main application component
├── App.css                      # Application styles
├── index.js                     # Application entry point
└── index.css                    # Global styles and Tailwind imports

public/                          # Static assets
tailwind.config.js              # TailwindCSS configuration
postcss.config.js               # PostCSS configuration
package.json                    # Project dependencies and scripts
```

## Available Scripts

- **`npm start`** - Runs the app in development mode on `http://localhost:3000`
- **`npm build`** - Builds the app for production to the `build` folder
- **`npm test`** - Launches the test runner in interactive watch mode
- **`npm eject`** - Ejects from Create React App (⚠️ irreversible)

## Usage

### Adding a Company
1. Click the "إضافة شركة جديدة" (Add New Company) button
2. Fill in the required fields:
   - Company Name (required)
   - Tax ID (required)
   - Address
   - Contact Person
   - Email
   - Phone
3. Click "إضافة شركة" (Add Company) to save

### Editing a Company
1. Click the edit icon (pencil) next to any company in the table
2. Modify the desired fields in the modal
3. Click "حفظ التعديلات" (Save Changes)

### Deleting a Company
1. Click the delete icon (trash) next to any company
2. Confirm the deletion in the confirmation dialog

### Searching Companies
- Use the search box to filter companies by name, tax ID, or other fields
- Search is performed in real-time as you type

## Data Storage

The application uses browser Local Storage to persist data. Company information is automatically saved and will be available when you return to the application.

## Customization

### Styling
- Modify `tailwind.config.js` to customize the design system
- Update CSS variables in `src/index.css` for theme colors
- Component styles can be customized in individual component files

### Adding Fields
- Update the form structure in `CompanyManagement.jsx`
- Add validation rules in the `validateForm` function
- Update the table columns to display new fields

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

If you encounter any issues or have questions, please open an issue on the GitHub repository.