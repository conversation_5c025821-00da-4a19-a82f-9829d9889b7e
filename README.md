# Company Management System

A comprehensive company management application built with React and TailwindCSS.

## Features

- Create, Read, Update, Delete (CRUD) operations for companies
- Responsive design
- Local storage persistence
- Form validation
- Search functionality
- Toast notifications
- Arabic language support

## Technologies Used

- React
- TailwindCSS
- Heroicons
- Lucide React
- React Hot Toast

## Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Start the development server:
   ```bash
   npm start
   ```

## Project Structure

- `src/` - Main application source
  - `api/` - API client modules
  - `components/` - React components
  - `App.js` - Main application component
  - `index.js` - Application entry point
- `public/` - Static assets
- `tailwind.config.js` - TailwindCSS configuration
- `postcss.config.js` - PostCSS configuration

## Available Scripts

- `npm start` - Runs the app in development mode
- `npm build` - Builds the app for production
- `npm test` - Launches the test runner
- `npm eject` - Ejects from Create React App

## License

MIT