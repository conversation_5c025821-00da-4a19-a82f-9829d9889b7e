// إعدادات الشركة الافتراضية - يجب تحديثها حسب بيانات شركتك
export const DEFAULT_COMPANY_CONFIG = {
  // معلومات الشركة الأساسية
  companyInfo: {
    name: 'اسم شركتك هنا',
    nameEn: 'Your Company Name Here',
    logo: null, // سيتم إضافة الشعار لاحقاً
    
    // معلومات قانونية
    vatNumber: '', // الرقم الضريبي (15 رقم)
    crNumber: '', // رقم السجل التجاري
    licenseNumber: '', // رقم الرخصة التجارية
    
    // معلومات الاتصال
    address: {
      street: '',
      city: '',
      region: '',
      postalCode: '',
      country: 'المملكة العربية السعودية'
    },
    
    contact: {
      phone: '',
      mobile: '',
      email: '',
      website: '',
      fax: ''
    },
    
    // إعدادات البنك
    bankInfo: {
      bankName: '',
      accountNumber: '',
      iban: '',
      swiftCode: ''
    }
  },
  
  // إعدادات النظام
  systemSettings: {
    currency: 'ر.س',
    currencyCode: 'SAR',
    language: 'ar',
    timezone: 'Asia/Riyadh',
    dateFormat: 'DD/MM/YYYY',
    
    // إعدادات الضرائب
    defaultVatRate: 15, // 15% ضريبة القيمة المضافة
    vatEnabled: true,
    
    // إعدادات الفوترة
    invoicePrefix: 'INV',
    purchasePrefix: 'PUR',
    expensePrefix: 'EXP',
    
    // إعدادات التنبيهات
    lowStockThreshold: 10,
    creditLimitWarning: true,
    
    // إعدادات النسخ الاحتياطي
    autoBackup: true,
    backupFrequency: 'daily' // daily, weekly, monthly
  },
  
  // إعدادات الفوترة الإلكترونية
  eInvoiceSettings: {
    enabled: true,
    phase2Enabled: true,
    qrCodeEnabled: true,
    autoGenerateQR: true,
    
    // إعدادات ZATCA
    zatcaEnvironment: 'sandbox', // sandbox أو production
    certificatePath: '',
    privateKeyPath: ''
  },
  
  // إعدادات الطباعة
  printSettings: {
    paperSize: 'A4',
    orientation: 'portrait',
    margins: {
      top: 20,
      bottom: 20,
      left: 20,
      right: 20
    },
    
    // إعدادات الفاتورة
    showLogo: true,
    showQRCode: true,
    showBankInfo: true,
    showTermsAndConditions: true,
    
    // نص الشروط والأحكام
    termsAndConditions: `
الشروط والأحكام:
1. جميع الأسعار شاملة ضريبة القيمة المضافة
2. الدفع مستحق خلال 30 يوم من تاريخ الفاتورة
3. يحق للشركة تحصيل فوائد تأخير 2% شهرياً
4. البضاعة المباعة لا ترد ولا تستبدل إلا بعذر مقبول
5. أي نزاع يحل وفقاً لأنظمة المملكة العربية السعودية
    `.trim()
  }
};

// دالة للحصول على إعدادات الشركة
export const getCompanyConfig = () => {
  const savedConfig = localStorage.getItem('company_config');
  if (savedConfig) {
    try {
      const parsed = JSON.parse(savedConfig);
      // دمج الإعدادات المحفوظة مع الافتراضية
      return {
        ...DEFAULT_COMPANY_CONFIG,
        ...parsed,
        companyInfo: {
          ...DEFAULT_COMPANY_CONFIG.companyInfo,
          ...parsed.companyInfo
        },
        systemSettings: {
          ...DEFAULT_COMPANY_CONFIG.systemSettings,
          ...parsed.systemSettings
        }
      };
    } catch (error) {
      console.error('خطأ في قراءة إعدادات الشركة:', error);
      return DEFAULT_COMPANY_CONFIG;
    }
  }
  return DEFAULT_COMPANY_CONFIG;
};

// دالة لحفظ إعدادات الشركة
export const saveCompanyConfig = (config) => {
  try {
    localStorage.setItem('company_config', JSON.stringify(config));
    return true;
  } catch (error) {
    console.error('خطأ في حفظ إعدادات الشركة:', error);
    return false;
  }
};

// دالة للتحقق من اكتمال إعدادات الشركة
export const validateCompanyConfig = (config) => {
  const errors = [];
  
  if (!config.companyInfo.name.trim()) {
    errors.push('اسم الشركة مطلوب');
  }
  
  if (!config.companyInfo.vatNumber || config.companyInfo.vatNumber.length !== 15) {
    errors.push('الرقم الضريبي يجب أن يكون 15 رقم');
  }
  
  if (!config.companyInfo.contact.phone.trim()) {
    errors.push('رقم الهاتف مطلوب');
  }
  
  if (!config.companyInfo.contact.email.trim()) {
    errors.push('البريد الإلكتروني مطلوب');
  }
  
  if (!config.companyInfo.address.city.trim()) {
    errors.push('المدينة مطلوبة');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// دالة لإنشاء إعدادات شركة جديدة
export const createNewCompanyConfig = (companyData) => {
  return {
    ...DEFAULT_COMPANY_CONFIG,
    companyInfo: {
      ...DEFAULT_COMPANY_CONFIG.companyInfo,
      ...companyData
    }
  };
};
