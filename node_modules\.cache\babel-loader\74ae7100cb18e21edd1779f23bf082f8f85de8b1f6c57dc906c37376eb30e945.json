{"ast": null, "code": "import { useRef as l } from \"react\";\nimport { getTextValue as i } from '../utils/get-text-value.js';\nimport { useEvent as o } from './use-event.js';\nfunction s(c) {\n  let t = l(\"\"),\n    r = l(\"\");\n  return o(() => {\n    let e = c.current;\n    if (!e) return \"\";\n    let u = e.innerText;\n    if (t.current === u) return r.current;\n    let n = i(e).trim().toLowerCase();\n    return t.current = u, r.current = n, n;\n  });\n}\nexport { s as useTextValue };", "map": {"version": 3, "names": ["useRef", "l", "getTextValue", "i", "useEvent", "o", "s", "c", "t", "r", "e", "current", "u", "innerText", "n", "trim", "toLowerCase", "useTextValue"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/node_modules/@headlessui/react/dist/hooks/use-text-value.js"], "sourcesContent": ["import{useRef as l}from\"react\";import{getTextValue as i}from'../utils/get-text-value.js';import{useEvent as o}from'./use-event.js';function s(c){let t=l(\"\"),r=l(\"\");return o(()=>{let e=c.current;if(!e)return\"\";let u=e.innerText;if(t.current===u)return r.current;let n=i(e).trim().toLowerCase();return t.current=u,r.current=n,n})}export{s as useTextValue};\n"], "mappings": "AAAA,SAAOA,MAAM,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,4BAA4B;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,gBAAgB;AAAC,SAASC,CAACA,CAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACP,CAAC,CAAC,EAAE,CAAC;IAACQ,CAAC,GAACR,CAAC,CAAC,EAAE,CAAC;EAAC,OAAOI,CAAC,CAAC,MAAI;IAAC,IAAIK,CAAC,GAACH,CAAC,CAACI,OAAO;IAAC,IAAG,CAACD,CAAC,EAAC,OAAM,EAAE;IAAC,IAAIE,CAAC,GAACF,CAAC,CAACG,SAAS;IAAC,IAAGL,CAAC,CAACG,OAAO,KAAGC,CAAC,EAAC,OAAOH,CAAC,CAACE,OAAO;IAAC,IAAIG,CAAC,GAACX,CAAC,CAACO,CAAC,CAAC,CAACK,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IAAC,OAAOR,CAAC,CAACG,OAAO,GAACC,CAAC,EAACH,CAAC,CAACE,OAAO,GAACG,CAAC,EAACA,CAAC;EAAA,CAAC,CAAC;AAAA;AAAC,SAAOR,CAAC,IAAIW,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}