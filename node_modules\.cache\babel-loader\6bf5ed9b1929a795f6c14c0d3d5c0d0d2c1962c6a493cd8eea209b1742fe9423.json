{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u0633\\u0637\\u062D \\u0627\\u0644\\u0645\\u0643\\u062A\\u0628\\\\333\\\\src\\\\components\\\\Customers\\\\CustomerDetails.jsx\";\nimport React from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { XMarkIcon } from '@heroicons/react/24/outline';\nimport { Button } from '../ui/button';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CustomerDetails = ({\n  customer,\n  onClose\n}) => {\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('ar-SA', {\n      style: 'currency',\n      currency: 'SAR'\n    }).format(amount);\n  };\n  return /*#__PURE__*/_jsxDEV(AnimatePresence, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          scale: 0.95\n        },\n        animate: {\n          opacity: 1,\n          scale: 1\n        },\n        exit: {\n          opacity: 0,\n          scale: 0.95\n        },\n        className: \"bg-white rounded-2xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-gray-900\",\n            children: \"\\u062A\\u0641\\u0627\\u0635\\u064A\\u0644 \\u0627\\u0644\\u0639\\u0645\\u064A\\u0644\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onClose,\n            className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg\",\n            children: /*#__PURE__*/_jsxDEV(XMarkIcon, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                children: \"\\u0627\\u0644\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0623\\u0633\\u0627\\u0633\\u064A\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 39,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-600\",\n                    children: \"\\u0643\\u0648\\u062F \\u0627\\u0644\\u0639\\u0645\\u064A\\u0644:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 42,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-900\",\n                    children: customer.code\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 43,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 41,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-600\",\n                    children: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0639\\u0645\\u064A\\u0644:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 46,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-900 font-semibold\",\n                    children: customer.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 47,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 45,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-600\",\n                    children: \"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u0639\\u0645\\u064A\\u0644:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 50,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-block px-3 py-1 rounded-full text-sm font-medium ${customer.type === 'فرد' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'}`,\n                    children: customer.type\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 51,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 49,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-600\",\n                    children: \"\\u0627\\u0644\\u062D\\u0627\\u0644\\u0629:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 58,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-block px-3 py-1 rounded-full text-sm font-medium ${customer.status === 'نشط' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                    children: customer.status\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 59,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 57,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-600\",\n                    children: \"\\u0639\\u0645\\u064A\\u0644 \\u0645\\u0646\\u0630:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 66,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-900\",\n                    children: new Date(customer.customerSince).toLocaleDateString('ar-SA')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 67,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 65,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                children: \"\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0627\\u062A\\u0635\\u0627\\u0644\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-600\",\n                    children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0647\\u0627\\u062A\\u0641:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 76,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-900 font-mono\",\n                    children: customer.phone\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 77,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 75,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-600\",\n                    children: \"\\u0627\\u0644\\u0628\\u0631\\u064A\\u062F \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 80,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-900\",\n                    children: customer.email || '-'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 81,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 79,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-600\",\n                    children: \"\\u0627\\u0644\\u0634\\u062E\\u0635 \\u0627\\u0644\\u0645\\u0633\\u0624\\u0648\\u0644:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 84,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-900\",\n                    children: customer.contactPerson || '-'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 85,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-600\",\n                    children: \"\\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u064A:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 88,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-900 font-mono\",\n                    children: customer.taxNumber || '-'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 89,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 87,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"\\u0627\\u0644\\u0639\\u0646\\u0648\\u0627\\u0646\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 p-4 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-900\",\n                children: customer.address || 'لم يتم تحديد العنوان'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 17\n              }, this), customer.city && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 mt-2\",\n                children: [customer.city, \", \", customer.country]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"\\u0627\\u0644\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0645\\u0627\\u0644\\u064A\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-blue-50 p-4 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-blue-600\",\n                  children: \"\\u0627\\u0644\\u062D\\u062F \\u0627\\u0644\\u0627\\u0626\\u062A\\u0645\\u0627\\u0646\\u064A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 111,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xl font-bold text-blue-900\",\n                  children: formatCurrency(customer.creditLimit)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 112,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `p-4 rounded-lg ${customer.currentBalance > 0 ? 'bg-red-50' : 'bg-green-50'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `text-sm font-medium ${customer.currentBalance > 0 ? 'text-red-600' : 'text-green-600'}`,\n                  children: \"\\u0627\\u0644\\u0631\\u0635\\u064A\\u062F \\u0627\\u0644\\u062D\\u0627\\u0644\\u064A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 117,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: `text-xl font-bold ${customer.currentBalance > 0 ? 'text-red-900' : 'text-green-900'}`,\n                  children: formatCurrency(customer.currentBalance)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-purple-50 p-4 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-purple-600\",\n                  children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 129,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xl font-bold text-purple-900\",\n                  children: formatCurrency(customer.totalPurchases)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-orange-50 p-4 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-orange-600\",\n                  children: \"\\u0646\\u0633\\u0628\\u0629 \\u0627\\u0644\\u062E\\u0635\\u0645\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xl font-bold text-orange-900\",\n                  children: [customer.discount, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"\\u0634\\u0631\\u0648\\u0637 \\u0627\\u0644\\u062A\\u0639\\u0627\\u0645\\u0644\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"\\u0634\\u0631\\u0648\\u0637 \\u0627\\u0644\\u062F\\u0641\\u0639:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-900\",\n                  children: customer.paymentTerms\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"\\u0622\\u062E\\u0631 \\u0639\\u0645\\u0644\\u064A\\u0629 \\u0634\\u0631\\u0627\\u0621:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-900\",\n                  children: customer.lastPurchase ? new Date(customer.lastPurchase).toLocaleDateString('ar-SA') : 'لا توجد مشتريات'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 149,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this), customer.notes && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"\\u0645\\u0644\\u0627\\u062D\\u0638\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 bg-gray-50 p-4 rounded-lg\",\n              children: customer.notes\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"\\u0625\\u062D\\u0635\\u0627\\u0626\\u064A\\u0627\\u062A \\u0633\\u0631\\u064A\\u0639\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center p-4 bg-gray-50 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-bold text-gray-900\",\n                  children: customer.totalPurchases > 0 ? Math.round(customer.totalPurchases / 12) : 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"\\u0645\\u062A\\u0648\\u0633\\u0637 \\u0627\\u0644\\u0634\\u0631\\u0627\\u0621 \\u0627\\u0644\\u0634\\u0647\\u0631\\u064A (\\u062A\\u0642\\u062F\\u064A\\u0631\\u064A)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center p-4 bg-gray-50 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-bold text-gray-900\",\n                  children: customer.currentBalance > 0 ? Math.ceil(customer.currentBalance / (customer.totalPurchases / 12 || 1)) : 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"\\u0623\\u0634\\u0647\\u0631 \\u0644\\u0644\\u0633\\u062F\\u0627\\u062F (\\u062A\\u0642\\u062F\\u064A\\u0631\\u064A)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center p-4 bg-gray-50 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-bold text-gray-900\",\n                  children: [((customer.creditLimit - customer.currentBalance) / customer.creditLimit * 100).toFixed(0), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"\\u0627\\u0644\\u0627\\u0626\\u062A\\u0645\\u0627\\u0646 \\u0627\\u0644\\u0645\\u062A\\u0627\\u062D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end pt-6 border-t border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              onClick: onClose,\n              variant: \"outline\",\n              children: \"\\u0625\\u063A\\u0644\\u0627\\u0642\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n};\n_c = CustomerDetails;\nexport default CustomerDetails;\nvar _c;\n$RefreshReg$(_c, \"CustomerDetails\");", "map": {"version": 3, "names": ["React", "motion", "AnimatePresence", "XMarkIcon", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "CustomerDetails", "customer", "onClose", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "children", "className", "div", "initial", "opacity", "scale", "animate", "exit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "code", "name", "type", "status", "Date", "customerSince", "toLocaleDateString", "phone", "email", "<PERSON><PERSON><PERSON>", "taxNumber", "address", "city", "country", "creditLimit", "currentBalance", "totalPurchases", "discount", "paymentTerms", "lastPurchase", "notes", "Math", "round", "ceil", "toFixed", "variant", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/src/components/Customers/CustomerDetails.jsx"], "sourcesContent": ["import React from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { XMarkIcon } from '@heroicons/react/24/outline';\nimport { Button } from '../ui/button';\n\nconst CustomerDetails = ({ customer, onClose }) => {\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('ar-SA', {\n      style: 'currency',\n      currency: 'SAR'\n    }).format(amount);\n  };\n\n  return (\n    <AnimatePresence>\n      <div className=\"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50\">\n        <motion.div\n          initial={{ opacity: 0, scale: 0.95 }}\n          animate={{ opacity: 1, scale: 1 }}\n          exit={{ opacity: 0, scale: 0.95 }}\n          className=\"bg-white rounded-2xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\"\n        >\n          {/* الرأس */}\n          <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n            <h2 className=\"text-xl font-semibold text-gray-900\">تفاصيل العميل</h2>\n            <button\n              onClick={onClose}\n              className=\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg\"\n            >\n              <XMarkIcon className=\"w-5 h-5\" />\n            </button>\n          </div>\n\n          {/* المحتوى */}\n          <div className=\"p-6 space-y-6\">\n            {/* المعلومات الأساسية */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">المعلومات الأساسية</h3>\n                <div className=\"space-y-3\">\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-600\">كود العميل:</span>\n                    <p className=\"text-gray-900\">{customer.code}</p>\n                  </div>\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-600\">اسم العميل:</span>\n                    <p className=\"text-gray-900 font-semibold\">{customer.name}</p>\n                  </div>\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-600\">نوع العميل:</span>\n                    <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${\n                      customer.type === 'فرد' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'\n                    }`}>\n                      {customer.type}\n                    </span>\n                  </div>\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-600\">الحالة:</span>\n                    <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${\n                      customer.status === 'نشط' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'\n                    }`}>\n                      {customer.status}\n                    </span>\n                  </div>\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-600\">عميل منذ:</span>\n                    <p className=\"text-gray-900\">{new Date(customer.customerSince).toLocaleDateString('ar-SA')}</p>\n                  </div>\n                </div>\n              </div>\n\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">معلومات الاتصال</h3>\n                <div className=\"space-y-3\">\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-600\">رقم الهاتف:</span>\n                    <p className=\"text-gray-900 font-mono\">{customer.phone}</p>\n                  </div>\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-600\">البريد الإلكتروني:</span>\n                    <p className=\"text-gray-900\">{customer.email || '-'}</p>\n                  </div>\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-600\">الشخص المسؤول:</span>\n                    <p className=\"text-gray-900\">{customer.contactPerson || '-'}</p>\n                  </div>\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-600\">الرقم الضريبي:</span>\n                    <p className=\"text-gray-900 font-mono\">{customer.taxNumber || '-'}</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* العنوان */}\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">العنوان</h3>\n              <div className=\"bg-gray-50 p-4 rounded-lg\">\n                <p className=\"text-gray-900\">{customer.address || 'لم يتم تحديد العنوان'}</p>\n                {customer.city && (\n                  <p className=\"text-gray-600 mt-2\">{customer.city}, {customer.country}</p>\n                )}\n              </div>\n            </div>\n\n            {/* المعلومات المالية */}\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">المعلومات المالية</h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n                <div className=\"bg-blue-50 p-4 rounded-lg\">\n                  <span className=\"text-sm font-medium text-blue-600\">الحد الائتماني</span>\n                  <p className=\"text-xl font-bold text-blue-900\">{formatCurrency(customer.creditLimit)}</p>\n                </div>\n                <div className={`p-4 rounded-lg ${\n                  customer.currentBalance > 0 ? 'bg-red-50' : 'bg-green-50'\n                }`}>\n                  <span className={`text-sm font-medium ${\n                    customer.currentBalance > 0 ? 'text-red-600' : 'text-green-600'\n                  }`}>\n                    الرصيد الحالي\n                  </span>\n                  <p className={`text-xl font-bold ${\n                    customer.currentBalance > 0 ? 'text-red-900' : 'text-green-900'\n                  }`}>\n                    {formatCurrency(customer.currentBalance)}\n                  </p>\n                </div>\n                <div className=\"bg-purple-50 p-4 rounded-lg\">\n                  <span className=\"text-sm font-medium text-purple-600\">إجمالي المشتريات</span>\n                  <p className=\"text-xl font-bold text-purple-900\">{formatCurrency(customer.totalPurchases)}</p>\n                </div>\n                <div className=\"bg-orange-50 p-4 rounded-lg\">\n                  <span className=\"text-sm font-medium text-orange-600\">نسبة الخصم</span>\n                  <p className=\"text-xl font-bold text-orange-900\">{customer.discount}%</p>\n                </div>\n              </div>\n            </div>\n\n            {/* شروط التعامل */}\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">شروط التعامل</h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <span className=\"text-sm font-medium text-gray-600\">شروط الدفع:</span>\n                  <p className=\"text-gray-900\">{customer.paymentTerms}</p>\n                </div>\n                <div>\n                  <span className=\"text-sm font-medium text-gray-600\">آخر عملية شراء:</span>\n                  <p className=\"text-gray-900\">\n                    {customer.lastPurchase ? new Date(customer.lastPurchase).toLocaleDateString('ar-SA') : 'لا توجد مشتريات'}\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* الملاحظات */}\n            {customer.notes && (\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">ملاحظات</h3>\n                <p className=\"text-gray-700 bg-gray-50 p-4 rounded-lg\">{customer.notes}</p>\n              </div>\n            )}\n\n            {/* إحصائيات سريعة */}\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">إحصائيات سريعة</h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n                  <p className=\"text-2xl font-bold text-gray-900\">\n                    {customer.totalPurchases > 0 ? Math.round(customer.totalPurchases / 12) : 0}\n                  </p>\n                  <p className=\"text-sm text-gray-600\">متوسط الشراء الشهري (تقديري)</p>\n                </div>\n                <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n                  <p className=\"text-2xl font-bold text-gray-900\">\n                    {customer.currentBalance > 0 ? Math.ceil(customer.currentBalance / (customer.totalPurchases / 12 || 1)) : 0}\n                  </p>\n                  <p className=\"text-sm text-gray-600\">أشهر للسداد (تقديري)</p>\n                </div>\n                <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n                  <p className=\"text-2xl font-bold text-gray-900\">\n                    {((customer.creditLimit - customer.currentBalance) / customer.creditLimit * 100).toFixed(0)}%\n                  </p>\n                  <p className=\"text-sm text-gray-600\">الائتمان المتاح</p>\n                </div>\n              </div>\n            </div>\n\n            {/* زر الإغلاق */}\n            <div className=\"flex justify-end pt-6 border-t border-gray-200\">\n              <Button onClick={onClose} variant=\"outline\">\n                إغلاق\n              </Button>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </AnimatePresence>\n  );\n};\n\nexport default CustomerDetails;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,MAAM,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,MAAMC,eAAe,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAQ,CAAC,KAAK;EACjD,MAAMC,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,oBACEL,OAAA,CAACJ,eAAe;IAAAe,QAAA,eACdX,OAAA;MAAKY,SAAS,EAAC,gFAAgF;MAAAD,QAAA,eAC7FX,OAAA,CAACL,MAAM,CAACkB,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAK,CAAE;QACrCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAE,CAAE;QAClCE,IAAI,EAAE;UAAEH,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAK,CAAE;QAClCJ,SAAS,EAAC,8EAA8E;QAAAD,QAAA,gBAGxFX,OAAA;UAAKY,SAAS,EAAC,gEAAgE;UAAAD,QAAA,gBAC7EX,OAAA;YAAIY,SAAS,EAAC,qCAAqC;YAAAD,QAAA,EAAC;UAAa;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtEtB,OAAA;YACEuB,OAAO,EAAEpB,OAAQ;YACjBS,SAAS,EAAC,oEAAoE;YAAAD,QAAA,eAE9EX,OAAA,CAACH,SAAS;cAACe,SAAS,EAAC;YAAS;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNtB,OAAA;UAAKY,SAAS,EAAC,eAAe;UAAAD,QAAA,gBAE5BX,OAAA;YAAKY,SAAS,EAAC,uCAAuC;YAAAD,QAAA,gBACpDX,OAAA;cAAAW,QAAA,gBACEX,OAAA;gBAAIY,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAAkB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChFtB,OAAA;gBAAKY,SAAS,EAAC,WAAW;gBAAAD,QAAA,gBACxBX,OAAA;kBAAAW,QAAA,gBACEX,OAAA;oBAAMY,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,EAAC;kBAAW;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtEtB,OAAA;oBAAGY,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAET,QAAQ,CAACsB;kBAAI;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC,eACNtB,OAAA;kBAAAW,QAAA,gBACEX,OAAA;oBAAMY,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,EAAC;kBAAW;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtEtB,OAAA;oBAAGY,SAAS,EAAC,6BAA6B;oBAAAD,QAAA,EAAET,QAAQ,CAACuB;kBAAI;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC,eACNtB,OAAA;kBAAAW,QAAA,gBACEX,OAAA;oBAAMY,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,EAAC;kBAAW;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtEtB,OAAA;oBAAMY,SAAS,EAAE,2DACfV,QAAQ,CAACwB,IAAI,KAAK,KAAK,GAAG,2BAA2B,GAAG,+BAA+B,EACtF;oBAAAf,QAAA,EACAT,QAAQ,CAACwB;kBAAI;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNtB,OAAA;kBAAAW,QAAA,gBACEX,OAAA;oBAAMY,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,EAAC;kBAAO;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClEtB,OAAA;oBAAMY,SAAS,EAAE,2DACfV,QAAQ,CAACyB,MAAM,KAAK,KAAK,GAAG,6BAA6B,GAAG,yBAAyB,EACpF;oBAAAhB,QAAA,EACAT,QAAQ,CAACyB;kBAAM;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNtB,OAAA;kBAAAW,QAAA,gBACEX,OAAA;oBAAMY,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,EAAC;kBAAS;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpEtB,OAAA;oBAAGY,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAE,IAAIiB,IAAI,CAAC1B,QAAQ,CAAC2B,aAAa,CAAC,CAACC,kBAAkB,CAAC,OAAO;kBAAC;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENtB,OAAA;cAAAW,QAAA,gBACEX,OAAA;gBAAIY,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAAe;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7EtB,OAAA;gBAAKY,SAAS,EAAC,WAAW;gBAAAD,QAAA,gBACxBX,OAAA;kBAAAW,QAAA,gBACEX,OAAA;oBAAMY,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,EAAC;kBAAW;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtEtB,OAAA;oBAAGY,SAAS,EAAC,yBAAyB;oBAAAD,QAAA,EAAET,QAAQ,CAAC6B;kBAAK;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC,eACNtB,OAAA;kBAAAW,QAAA,gBACEX,OAAA;oBAAMY,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,EAAC;kBAAkB;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7EtB,OAAA;oBAAGY,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAET,QAAQ,CAAC8B,KAAK,IAAI;kBAAG;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACNtB,OAAA;kBAAAW,QAAA,gBACEX,OAAA;oBAAMY,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,EAAC;kBAAc;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzEtB,OAAA;oBAAGY,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAET,QAAQ,CAAC+B,aAAa,IAAI;kBAAG;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC,eACNtB,OAAA;kBAAAW,QAAA,gBACEX,OAAA;oBAAMY,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,EAAC;kBAAc;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzEtB,OAAA;oBAAGY,SAAS,EAAC,yBAAyB;oBAAAD,QAAA,EAAET,QAAQ,CAACgC,SAAS,IAAI;kBAAG;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNtB,OAAA;YAAAW,QAAA,gBACEX,OAAA;cAAIY,SAAS,EAAC,0CAA0C;cAAAD,QAAA,EAAC;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrEtB,OAAA;cAAKY,SAAS,EAAC,2BAA2B;cAAAD,QAAA,gBACxCX,OAAA;gBAAGY,SAAS,EAAC,eAAe;gBAAAD,QAAA,EAAET,QAAQ,CAACiC,OAAO,IAAI;cAAsB;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EAC5EpB,QAAQ,CAACkC,IAAI,iBACZpC,OAAA;gBAAGY,SAAS,EAAC,oBAAoB;gBAAAD,QAAA,GAAET,QAAQ,CAACkC,IAAI,EAAC,IAAE,EAAClC,QAAQ,CAACmC,OAAO;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CACzE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNtB,OAAA;YAAAW,QAAA,gBACEX,OAAA;cAAIY,SAAS,EAAC,0CAA0C;cAAAD,QAAA,EAAC;YAAiB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/EtB,OAAA;cAAKY,SAAS,EAAC,sDAAsD;cAAAD,QAAA,gBACnEX,OAAA;gBAAKY,SAAS,EAAC,2BAA2B;gBAAAD,QAAA,gBACxCX,OAAA;kBAAMY,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,EAAC;gBAAc;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzEtB,OAAA;kBAAGY,SAAS,EAAC,iCAAiC;kBAAAD,QAAA,EAAEP,cAAc,CAACF,QAAQ,CAACoC,WAAW;gBAAC;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF,CAAC,eACNtB,OAAA;gBAAKY,SAAS,EAAE,kBACdV,QAAQ,CAACqC,cAAc,GAAG,CAAC,GAAG,WAAW,GAAG,aAAa,EACxD;gBAAA5B,QAAA,gBACDX,OAAA;kBAAMY,SAAS,EAAE,uBACfV,QAAQ,CAACqC,cAAc,GAAG,CAAC,GAAG,cAAc,GAAG,gBAAgB,EAC9D;kBAAA5B,QAAA,EAAC;gBAEJ;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACPtB,OAAA;kBAAGY,SAAS,EAAE,qBACZV,QAAQ,CAACqC,cAAc,GAAG,CAAC,GAAG,cAAc,GAAG,gBAAgB,EAC9D;kBAAA5B,QAAA,EACAP,cAAc,CAACF,QAAQ,CAACqC,cAAc;gBAAC;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNtB,OAAA;gBAAKY,SAAS,EAAC,6BAA6B;gBAAAD,QAAA,gBAC1CX,OAAA;kBAAMY,SAAS,EAAC,qCAAqC;kBAAAD,QAAA,EAAC;gBAAgB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7EtB,OAAA;kBAAGY,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,EAAEP,cAAc,CAACF,QAAQ,CAACsC,cAAc;gBAAC;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F,CAAC,eACNtB,OAAA;gBAAKY,SAAS,EAAC,6BAA6B;gBAAAD,QAAA,gBAC1CX,OAAA;kBAAMY,SAAS,EAAC,qCAAqC;kBAAAD,QAAA,EAAC;gBAAU;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvEtB,OAAA;kBAAGY,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,GAAET,QAAQ,CAACuC,QAAQ,EAAC,GAAC;gBAAA;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNtB,OAAA;YAAAW,QAAA,gBACEX,OAAA;cAAIY,SAAS,EAAC,0CAA0C;cAAAD,QAAA,EAAC;YAAY;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1EtB,OAAA;cAAKY,SAAS,EAAC,uCAAuC;cAAAD,QAAA,gBACpDX,OAAA;gBAAAW,QAAA,gBACEX,OAAA;kBAAMY,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,EAAC;gBAAW;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtEtB,OAAA;kBAAGY,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAET,QAAQ,CAACwC;gBAAY;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACNtB,OAAA;gBAAAW,QAAA,gBACEX,OAAA;kBAAMY,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,EAAC;gBAAe;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1EtB,OAAA;kBAAGY,SAAS,EAAC,eAAe;kBAAAD,QAAA,EACzBT,QAAQ,CAACyC,YAAY,GAAG,IAAIf,IAAI,CAAC1B,QAAQ,CAACyC,YAAY,CAAC,CAACb,kBAAkB,CAAC,OAAO,CAAC,GAAG;gBAAiB;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLpB,QAAQ,CAAC0C,KAAK,iBACb5C,OAAA;YAAAW,QAAA,gBACEX,OAAA;cAAIY,SAAS,EAAC,0CAA0C;cAAAD,QAAA,EAAC;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrEtB,OAAA;cAAGY,SAAS,EAAC,yCAAyC;cAAAD,QAAA,EAAET,QAAQ,CAAC0C;YAAK;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CACN,eAGDtB,OAAA;YAAAW,QAAA,gBACEX,OAAA;cAAIY,SAAS,EAAC,0CAA0C;cAAAD,QAAA,EAAC;YAAc;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5EtB,OAAA;cAAKY,SAAS,EAAC,uCAAuC;cAAAD,QAAA,gBACpDX,OAAA;gBAAKY,SAAS,EAAC,uCAAuC;gBAAAD,QAAA,gBACpDX,OAAA;kBAAGY,SAAS,EAAC,kCAAkC;kBAAAD,QAAA,EAC5CT,QAAQ,CAACsC,cAAc,GAAG,CAAC,GAAGK,IAAI,CAACC,KAAK,CAAC5C,QAAQ,CAACsC,cAAc,GAAG,EAAE,CAAC,GAAG;gBAAC;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E,CAAC,eACJtB,OAAA;kBAAGY,SAAS,EAAC,uBAAuB;kBAAAD,QAAA,EAAC;gBAA4B;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CAAC,eACNtB,OAAA;gBAAKY,SAAS,EAAC,uCAAuC;gBAAAD,QAAA,gBACpDX,OAAA;kBAAGY,SAAS,EAAC,kCAAkC;kBAAAD,QAAA,EAC5CT,QAAQ,CAACqC,cAAc,GAAG,CAAC,GAAGM,IAAI,CAACE,IAAI,CAAC7C,QAAQ,CAACqC,cAAc,IAAIrC,QAAQ,CAACsC,cAAc,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG;gBAAC;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1G,CAAC,eACJtB,OAAA;kBAAGY,SAAS,EAAC,uBAAuB;kBAAAD,QAAA,EAAC;gBAAoB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eACNtB,OAAA;gBAAKY,SAAS,EAAC,uCAAuC;gBAAAD,QAAA,gBACpDX,OAAA;kBAAGY,SAAS,EAAC,kCAAkC;kBAAAD,QAAA,GAC5C,CAAC,CAACT,QAAQ,CAACoC,WAAW,GAAGpC,QAAQ,CAACqC,cAAc,IAAIrC,QAAQ,CAACoC,WAAW,GAAG,GAAG,EAAEU,OAAO,CAAC,CAAC,CAAC,EAAC,GAC9F;gBAAA;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJtB,OAAA;kBAAGY,SAAS,EAAC,uBAAuB;kBAAAD,QAAA,EAAC;gBAAe;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNtB,OAAA;YAAKY,SAAS,EAAC,gDAAgD;YAAAD,QAAA,eAC7DX,OAAA,CAACF,MAAM;cAACyB,OAAO,EAAEpB,OAAQ;cAAC8C,OAAO,EAAC,SAAS;cAAAtC,QAAA,EAAC;YAE5C;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACS,CAAC;AAEtB,CAAC;AAAC4B,EAAA,GAlMIjD,eAAe;AAoMrB,eAAeA,eAAe;AAAC,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}