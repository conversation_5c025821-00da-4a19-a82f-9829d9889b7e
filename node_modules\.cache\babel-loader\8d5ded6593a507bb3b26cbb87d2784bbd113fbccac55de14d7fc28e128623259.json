{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u0633\\u0637\\u062D \\u0627\\u0644\\u0645\\u0643\\u062A\\u0628\\\\333\\\\src\\\\components\\\\Purchases\\\\PurchaseInvoice.jsx\";\nimport React from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { XMarkIcon, PrinterIcon, DocumentArrowDownIcon } from '@heroicons/react/24/outline';\nimport { Button } from '../ui/button';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PurchaseInvoice = ({\n  purchase,\n  onClose\n}) => {\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('ar-SA', {\n      style: 'currency',\n      currency: 'SAR'\n    }).format(amount);\n  };\n  const handlePrint = () => {\n    window.print();\n    toast.success('تم إرسال الفاتورة للطباعة');\n  };\n  const handleDownload = () => {\n    toast.success('تم تحميل الفاتورة');\n  };\n  return /*#__PURE__*/_jsxDEV(AnimatePresence, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          scale: 0.95\n        },\n        animate: {\n          opacity: 1,\n          scale: 1\n        },\n        exit: {\n          opacity: 0,\n          scale: 0.95\n        },\n        className: \"bg-white rounded-2xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between p-6 border-b border-gray-200 print:hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-gray-900\",\n            children: \"\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629 \\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4 space-x-reverse\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              onClick: handlePrint,\n              variant: \"outline\",\n              className: \"text-blue-600 hover:text-blue-800\",\n              children: [/*#__PURE__*/_jsxDEV(PrinterIcon, {\n                className: \"w-4 h-4 ml-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 17\n              }, this), \"\\u0637\\u0628\\u0627\\u0639\\u0629\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: handleDownload,\n              variant: \"outline\",\n              className: \"text-green-600 hover:text-green-800\",\n              children: [/*#__PURE__*/_jsxDEV(DocumentArrowDownIcon, {\n                className: \"w-4 h-4 ml-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 17\n              }, this), \"\\u062A\\u062D\\u0645\\u064A\\u0644 PDF\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: onClose,\n              className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(XMarkIcon, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-8 print:p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-3xl font-bold text-gray-900 mb-2\",\n              children: \"\\u0634\\u0631\\u0643\\u0629 \\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0623\\u0639\\u0645\\u0627\\u0644\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"\\u0627\\u0644\\u0631\\u064A\\u0627\\u0636\\u060C \\u0627\\u0644\\u0645\\u0645\\u0644\\u0643\\u0629 \\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629 \\u0627\\u0644\\u0633\\u0639\\u0648\\u062F\\u064A\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"\\u0647\\u0627\\u062A\\u0641: 0112345678 | \\u0627\\u0644\\u0628\\u0631\\u064A\\u062F \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A: <EMAIL>\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4 pt-4 border-t border-gray-200\",\n              children: /*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-semibold text-gray-900\",\n                children: \"\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629 \\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-8 mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                children: \"\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium\",\n                    children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 79,\n                    columnNumber: 22\n                  }, this), \" \", purchase.invoiceNumber]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 79,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium\",\n                    children: \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 80,\n                    columnNumber: 22\n                  }, this), \" \", new Date(purchase.date).toLocaleDateString('ar-SA')]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 80,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium\",\n                    children: \"\\u062A\\u0627\\u0631\\u064A\\u062E \\u0627\\u0644\\u0627\\u0633\\u062A\\u062D\\u0642\\u0627\\u0642:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 81,\n                    columnNumber: 22\n                  }, this), \" \", purchase.dueDate ? new Date(purchase.dueDate).toLocaleDateString('ar-SA') : '-']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 81,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium\",\n                    children: \"\\u0637\\u0631\\u064A\\u0642\\u0629 \\u0627\\u0644\\u062F\\u0641\\u0639:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 82,\n                    columnNumber: 22\n                  }, this), \" \", purchase.paymentMethod]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 82,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium\",\n                    children: \"\\u0627\\u0644\\u062D\\u0627\\u0644\\u0629:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 83,\n                    columnNumber: 22\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `mr-2 px-2 py-1 rounded-full text-xs font-medium ${purchase.status === 'مكتملة' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`,\n                    children: purchase.status\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 84,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                children: \"\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium\",\n                    children: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 96,\n                    columnNumber: 22\n                  }, this), \" \", purchase.supplierName]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 96,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium\",\n                    children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 97,\n                    columnNumber: 22\n                  }, this), \" \", purchase.supplierId]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 97,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"\\u062A\\u0641\\u0627\\u0635\\u064A\\u0644 \\u0627\\u0644\\u0623\\u0635\\u0646\\u0627\\u0641\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"overflow-x-auto\",\n              children: /*#__PURE__*/_jsxDEV(\"table\", {\n                className: \"w-full border-collapse border border-gray-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                  children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                    className: \"bg-gray-50\",\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"border border-gray-300 px-4 py-2 text-right\",\n                      children: \"\\u0627\\u0644\\u0635\\u0646\\u0641\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 109,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"border border-gray-300 px-4 py-2 text-center\",\n                      children: \"\\u0627\\u0644\\u0643\\u0645\\u064A\\u0629\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 110,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"border border-gray-300 px-4 py-2 text-center\",\n                      children: \"\\u0627\\u0644\\u0633\\u0639\\u0631\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 111,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"border border-gray-300 px-4 py-2 text-center\",\n                      children: \"\\u0627\\u0644\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 112,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 108,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 107,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                  children: purchase.items.map((item, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"border border-gray-300 px-4 py-2\",\n                      children: item.productName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 118,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"border border-gray-300 px-4 py-2 text-center\",\n                      children: item.quantity\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 119,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"border border-gray-300 px-4 py-2 text-center\",\n                      children: formatCurrency(item.price)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 120,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"border border-gray-300 px-4 py-2 text-center\",\n                      children: formatCurrency(item.total)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 121,\n                      columnNumber: 25\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 117,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 115,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end mb-8\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full md:w-1/2\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u0627\\u0644\\u0645\\u062C\\u0645\\u0648\\u0639 \\u0627\\u0644\\u0641\\u0631\\u0639\\u064A:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 134,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: formatCurrency(purchase.subtotal)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 135,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u0627\\u0644\\u062E\\u0635\\u0645:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 138,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"-\", formatCurrency(purchase.discount)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 139,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u0636\\u0631\\u064A\\u0628\\u0629 \\u0627\\u0644\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0636\\u0627\\u0641\\u0629 (15%):\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 142,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: formatCurrency(purchase.tax)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 143,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between text-lg font-bold border-t border-gray-300 pt-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u0627\\u0644\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0646\\u0647\\u0627\\u0626\\u064A:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 146,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: formatCurrency(purchase.total)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 147,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this), purchase.notes && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"\\u0645\\u0644\\u0627\\u062D\\u0638\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 bg-gray-50 p-4 rounded-lg\",\n              children: purchase.notes\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-8 mt-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border-t border-gray-300 pt-2 mt-16\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-medium\",\n                  children: \"\\u062A\\u0648\\u0642\\u064A\\u0639 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border-t border-gray-300 pt-2 mt-16\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-medium\",\n                  children: \"\\u062A\\u0648\\u0642\\u064A\\u0639 \\u0648\\u062E\\u062A\\u0645 \\u0627\\u0644\\u0634\\u0631\\u0643\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mt-8 pt-8 border-t border-gray-200 text-sm text-gray-600\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0634\\u0643\\u0631\\u0627\\u064B \\u0644\\u062A\\u0639\\u0627\\u0648\\u0646\\u0643\\u0645 \\u0645\\u0639\\u0646\\u0627\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0647\\u0630\\u0647 \\u0641\\u0627\\u062A\\u0648\\u0631\\u0629 \\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\\u0629 \\u0648\\u0644\\u0627 \\u062A\\u062D\\u062A\\u0627\\u062C \\u0625\\u0644\\u0649 \\u062A\\u0648\\u0642\\u064A\\u0639\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this);\n};\n_c = PurchaseInvoice;\nexport default PurchaseInvoice;\nvar _c;\n$RefreshReg$(_c, \"PurchaseInvoice\");", "map": {"version": 3, "names": ["React", "motion", "AnimatePresence", "XMarkIcon", "PrinterIcon", "DocumentArrowDownIcon", "<PERSON><PERSON>", "toast", "jsxDEV", "_jsxDEV", "PurchaseInvoice", "purchase", "onClose", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "handlePrint", "window", "print", "success", "handleDownload", "children", "className", "div", "initial", "opacity", "scale", "animate", "exit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "variant", "invoiceNumber", "Date", "date", "toLocaleDateString", "dueDate", "paymentMethod", "status", "supplierName", "supplierId", "items", "map", "item", "index", "productName", "quantity", "price", "total", "subtotal", "discount", "tax", "notes", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/src/components/Purchases/PurchaseInvoice.jsx"], "sourcesContent": ["import React from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { XMarkIcon, PrinterIcon, DocumentArrowDownIcon } from '@heroicons/react/24/outline';\nimport { Button } from '../ui/button';\nimport toast from 'react-hot-toast';\n\nconst PurchaseInvoice = ({ purchase, onClose }) => {\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('ar-SA', {\n      style: 'currency',\n      currency: 'SAR'\n    }).format(amount);\n  };\n\n  const handlePrint = () => {\n    window.print();\n    toast.success('تم إرسال الفاتورة للطباعة');\n  };\n\n  const handleDownload = () => {\n    toast.success('تم تحميل الفاتورة');\n  };\n\n  return (\n    <AnimatePresence>\n      <div className=\"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50\">\n        <motion.div\n          initial={{ opacity: 0, scale: 0.95 }}\n          animate={{ opacity: 1, scale: 1 }}\n          exit={{ opacity: 0, scale: 0.95 }}\n          className=\"bg-white rounded-2xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\"\n        >\n          {/* الرأس */}\n          <div className=\"flex items-center justify-between p-6 border-b border-gray-200 print:hidden\">\n            <h2 className=\"text-xl font-semibold text-gray-900\">فاتورة مشتريات</h2>\n            <div className=\"flex items-center space-x-4 space-x-reverse\">\n              <Button\n                onClick={handlePrint}\n                variant=\"outline\"\n                className=\"text-blue-600 hover:text-blue-800\"\n              >\n                <PrinterIcon className=\"w-4 h-4 ml-2\" />\n                طباعة\n              </Button>\n              <Button\n                onClick={handleDownload}\n                variant=\"outline\"\n                className=\"text-green-600 hover:text-green-800\"\n              >\n                <DocumentArrowDownIcon className=\"w-4 h-4 ml-2\" />\n                تحميل PDF\n              </Button>\n              <button\n                onClick={onClose}\n                className=\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg\"\n              >\n                <XMarkIcon className=\"w-5 h-5\" />\n              </button>\n            </div>\n          </div>\n\n          {/* محتوى الفاتورة */}\n          <div className=\"p-8 print:p-4\">\n            {/* رأس الفاتورة */}\n            <div className=\"text-center mb-8\">\n              <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">شركة إدارة الأعمال</h1>\n              <p className=\"text-gray-600\">الرياض، المملكة العربية السعودية</p>\n              <p className=\"text-gray-600\">هاتف: 0112345678 | البريد الإلكتروني: <EMAIL></p>\n              <div className=\"mt-4 pt-4 border-t border-gray-200\">\n                <h2 className=\"text-xl font-semibold text-gray-900\">فاتورة مشتريات</h2>\n              </div>\n            </div>\n\n            {/* معلومات الفاتورة والمورد */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 mb-8\">\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">معلومات الفاتورة</h3>\n                <div className=\"space-y-2\">\n                  <p><span className=\"font-medium\">رقم الفاتورة:</span> {purchase.invoiceNumber}</p>\n                  <p><span className=\"font-medium\">التاريخ:</span> {new Date(purchase.date).toLocaleDateString('ar-SA')}</p>\n                  <p><span className=\"font-medium\">تاريخ الاستحقاق:</span> {purchase.dueDate ? new Date(purchase.dueDate).toLocaleDateString('ar-SA') : '-'}</p>\n                  <p><span className=\"font-medium\">طريقة الدفع:</span> {purchase.paymentMethod}</p>\n                  <p><span className=\"font-medium\">الحالة:</span> \n                    <span className={`mr-2 px-2 py-1 rounded-full text-xs font-medium ${\n                      purchase.status === 'مكتملة' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'\n                    }`}>\n                      {purchase.status}\n                    </span>\n                  </p>\n                </div>\n              </div>\n\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">بيانات المورد</h3>\n                <div className=\"space-y-2\">\n                  <p><span className=\"font-medium\">اسم المورد:</span> {purchase.supplierName}</p>\n                  <p><span className=\"font-medium\">رقم المورد:</span> {purchase.supplierId}</p>\n                </div>\n              </div>\n            </div>\n\n            {/* جدول الأصناف */}\n            <div className=\"mb-8\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">تفاصيل الأصناف</h3>\n              <div className=\"overflow-x-auto\">\n                <table className=\"w-full border-collapse border border-gray-300\">\n                  <thead>\n                    <tr className=\"bg-gray-50\">\n                      <th className=\"border border-gray-300 px-4 py-2 text-right\">الصنف</th>\n                      <th className=\"border border-gray-300 px-4 py-2 text-center\">الكمية</th>\n                      <th className=\"border border-gray-300 px-4 py-2 text-center\">السعر</th>\n                      <th className=\"border border-gray-300 px-4 py-2 text-center\">الإجمالي</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    {purchase.items.map((item, index) => (\n                      <tr key={index}>\n                        <td className=\"border border-gray-300 px-4 py-2\">{item.productName}</td>\n                        <td className=\"border border-gray-300 px-4 py-2 text-center\">{item.quantity}</td>\n                        <td className=\"border border-gray-300 px-4 py-2 text-center\">{formatCurrency(item.price)}</td>\n                        <td className=\"border border-gray-300 px-4 py-2 text-center\">{formatCurrency(item.total)}</td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            </div>\n\n            {/* الإجماليات */}\n            <div className=\"flex justify-end mb-8\">\n              <div className=\"w-full md:w-1/2\">\n                <div className=\"space-y-2\">\n                  <div className=\"flex justify-between\">\n                    <span>المجموع الفرعي:</span>\n                    <span>{formatCurrency(purchase.subtotal)}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span>الخصم:</span>\n                    <span>-{formatCurrency(purchase.discount)}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span>ضريبة القيمة المضافة (15%):</span>\n                    <span>{formatCurrency(purchase.tax)}</span>\n                  </div>\n                  <div className=\"flex justify-between text-lg font-bold border-t border-gray-300 pt-2\">\n                    <span>الإجمالي النهائي:</span>\n                    <span>{formatCurrency(purchase.total)}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* الملاحظات */}\n            {purchase.notes && (\n              <div className=\"mb-8\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">ملاحظات</h3>\n                <p className=\"text-gray-700 bg-gray-50 p-4 rounded-lg\">{purchase.notes}</p>\n              </div>\n            )}\n\n            {/* التوقيع والختم */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 mt-12\">\n              <div className=\"text-center\">\n                <div className=\"border-t border-gray-300 pt-2 mt-16\">\n                  <p className=\"font-medium\">توقيع المورد</p>\n                </div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"border-t border-gray-300 pt-2 mt-16\">\n                  <p className=\"font-medium\">توقيع وختم الشركة</p>\n                </div>\n              </div>\n            </div>\n\n            {/* تذييل الفاتورة */}\n            <div className=\"text-center mt-8 pt-8 border-t border-gray-200 text-sm text-gray-600\">\n              <p>شكراً لتعاونكم معنا</p>\n              <p>هذه فاتورة إلكترونية ولا تحتاج إلى توقيع</p>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </AnimatePresence>\n  );\n};\n\nexport default PurchaseInvoice;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,SAAS,EAAEC,WAAW,EAAEC,qBAAqB,QAAQ,6BAA6B;AAC3F,SAASC,MAAM,QAAQ,cAAc;AACrC,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,eAAe,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAQ,CAAC,KAAK;EACjD,MAAMC,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,MAAMM,WAAW,GAAGA,CAAA,KAAM;IACxBC,MAAM,CAACC,KAAK,CAAC,CAAC;IACdf,KAAK,CAACgB,OAAO,CAAC,2BAA2B,CAAC;EAC5C,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3BjB,KAAK,CAACgB,OAAO,CAAC,mBAAmB,CAAC;EACpC,CAAC;EAED,oBACEd,OAAA,CAACP,eAAe;IAAAuB,QAAA,eACdhB,OAAA;MAAKiB,SAAS,EAAC,gFAAgF;MAAAD,QAAA,eAC7FhB,OAAA,CAACR,MAAM,CAAC0B,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAK,CAAE;QACrCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAE,CAAE;QAClCE,IAAI,EAAE;UAAEH,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAK,CAAE;QAClCJ,SAAS,EAAC,8EAA8E;QAAAD,QAAA,gBAGxFhB,OAAA;UAAKiB,SAAS,EAAC,6EAA6E;UAAAD,QAAA,gBAC1FhB,OAAA;YAAIiB,SAAS,EAAC,qCAAqC;YAAAD,QAAA,EAAC;UAAc;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvE3B,OAAA;YAAKiB,SAAS,EAAC,6CAA6C;YAAAD,QAAA,gBAC1DhB,OAAA,CAACH,MAAM;cACL+B,OAAO,EAAEjB,WAAY;cACrBkB,OAAO,EAAC,SAAS;cACjBZ,SAAS,EAAC,mCAAmC;cAAAD,QAAA,gBAE7ChB,OAAA,CAACL,WAAW;gBAACsB,SAAS,EAAC;cAAc;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,kCAE1C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT3B,OAAA,CAACH,MAAM;cACL+B,OAAO,EAAEb,cAAe;cACxBc,OAAO,EAAC,SAAS;cACjBZ,SAAS,EAAC,qCAAqC;cAAAD,QAAA,gBAE/ChB,OAAA,CAACJ,qBAAqB;gBAACqB,SAAS,EAAC;cAAc;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,sCAEpD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT3B,OAAA;cACE4B,OAAO,EAAEzB,OAAQ;cACjBc,SAAS,EAAC,oEAAoE;cAAAD,QAAA,eAE9EhB,OAAA,CAACN,SAAS;gBAACuB,SAAS,EAAC;cAAS;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN3B,OAAA;UAAKiB,SAAS,EAAC,eAAe;UAAAD,QAAA,gBAE5BhB,OAAA;YAAKiB,SAAS,EAAC,kBAAkB;YAAAD,QAAA,gBAC/BhB,OAAA;cAAIiB,SAAS,EAAC,uCAAuC;cAAAD,QAAA,EAAC;YAAkB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7E3B,OAAA;cAAGiB,SAAS,EAAC,eAAe;cAAAD,QAAA,EAAC;YAAgC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACjE3B,OAAA;cAAGiB,SAAS,EAAC,eAAe;cAAAD,QAAA,EAAC;YAAsD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACvF3B,OAAA;cAAKiB,SAAS,EAAC,oCAAoC;cAAAD,QAAA,eACjDhB,OAAA;gBAAIiB,SAAS,EAAC,qCAAqC;gBAAAD,QAAA,EAAC;cAAc;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN3B,OAAA;YAAKiB,SAAS,EAAC,4CAA4C;YAAAD,QAAA,gBACzDhB,OAAA;cAAAgB,QAAA,gBACEhB,OAAA;gBAAIiB,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAAgB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9E3B,OAAA;gBAAKiB,SAAS,EAAC,WAAW;gBAAAD,QAAA,gBACxBhB,OAAA;kBAAAgB,QAAA,gBAAGhB,OAAA;oBAAMiB,SAAS,EAAC,aAAa;oBAAAD,QAAA,EAAC;kBAAa;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,KAAC,EAACzB,QAAQ,CAAC4B,aAAa;gBAAA;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClF3B,OAAA;kBAAAgB,QAAA,gBAAGhB,OAAA;oBAAMiB,SAAS,EAAC,aAAa;oBAAAD,QAAA,EAAC;kBAAQ;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,KAAC,EAAC,IAAII,IAAI,CAAC7B,QAAQ,CAAC8B,IAAI,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAC;gBAAA;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1G3B,OAAA;kBAAAgB,QAAA,gBAAGhB,OAAA;oBAAMiB,SAAS,EAAC,aAAa;oBAAAD,QAAA,EAAC;kBAAgB;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,KAAC,EAACzB,QAAQ,CAACgC,OAAO,GAAG,IAAIH,IAAI,CAAC7B,QAAQ,CAACgC,OAAO,CAAC,CAACD,kBAAkB,CAAC,OAAO,CAAC,GAAG,GAAG;gBAAA;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9I3B,OAAA;kBAAAgB,QAAA,gBAAGhB,OAAA;oBAAMiB,SAAS,EAAC,aAAa;oBAAAD,QAAA,EAAC;kBAAY;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,KAAC,EAACzB,QAAQ,CAACiC,aAAa;gBAAA;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjF3B,OAAA;kBAAAgB,QAAA,gBAAGhB,OAAA;oBAAMiB,SAAS,EAAC,aAAa;oBAAAD,QAAA,EAAC;kBAAO;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7C3B,OAAA;oBAAMiB,SAAS,EAAE,mDACff,QAAQ,CAACkC,MAAM,KAAK,QAAQ,GAAG,6BAA6B,GAAG,+BAA+B,EAC7F;oBAAApB,QAAA,EACAd,QAAQ,CAACkC;kBAAM;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN3B,OAAA;cAAAgB,QAAA,gBACEhB,OAAA;gBAAIiB,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAAa;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3E3B,OAAA;gBAAKiB,SAAS,EAAC,WAAW;gBAAAD,QAAA,gBACxBhB,OAAA;kBAAAgB,QAAA,gBAAGhB,OAAA;oBAAMiB,SAAS,EAAC,aAAa;oBAAAD,QAAA,EAAC;kBAAW;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,KAAC,EAACzB,QAAQ,CAACmC,YAAY;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/E3B,OAAA;kBAAAgB,QAAA,gBAAGhB,OAAA;oBAAMiB,SAAS,EAAC,aAAa;oBAAAD,QAAA,EAAC;kBAAW;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,KAAC,EAACzB,QAAQ,CAACoC,UAAU;gBAAA;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN3B,OAAA;YAAKiB,SAAS,EAAC,MAAM;YAAAD,QAAA,gBACnBhB,OAAA;cAAIiB,SAAS,EAAC,0CAA0C;cAAAD,QAAA,EAAC;YAAc;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5E3B,OAAA;cAAKiB,SAAS,EAAC,iBAAiB;cAAAD,QAAA,eAC9BhB,OAAA;gBAAOiB,SAAS,EAAC,+CAA+C;gBAAAD,QAAA,gBAC9DhB,OAAA;kBAAAgB,QAAA,eACEhB,OAAA;oBAAIiB,SAAS,EAAC,YAAY;oBAAAD,QAAA,gBACxBhB,OAAA;sBAAIiB,SAAS,EAAC,6CAA6C;sBAAAD,QAAA,EAAC;oBAAK;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACtE3B,OAAA;sBAAIiB,SAAS,EAAC,8CAA8C;sBAAAD,QAAA,EAAC;oBAAM;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACxE3B,OAAA;sBAAIiB,SAAS,EAAC,8CAA8C;sBAAAD,QAAA,EAAC;oBAAK;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACvE3B,OAAA;sBAAIiB,SAAS,EAAC,8CAA8C;sBAAAD,QAAA,EAAC;oBAAQ;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACR3B,OAAA;kBAAAgB,QAAA,EACGd,QAAQ,CAACqC,KAAK,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC9B1C,OAAA;oBAAAgB,QAAA,gBACEhB,OAAA;sBAAIiB,SAAS,EAAC,kCAAkC;sBAAAD,QAAA,EAAEyB,IAAI,CAACE;oBAAW;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACxE3B,OAAA;sBAAIiB,SAAS,EAAC,8CAA8C;sBAAAD,QAAA,EAAEyB,IAAI,CAACG;oBAAQ;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACjF3B,OAAA;sBAAIiB,SAAS,EAAC,8CAA8C;sBAAAD,QAAA,EAAEZ,cAAc,CAACqC,IAAI,CAACI,KAAK;oBAAC;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC9F3B,OAAA;sBAAIiB,SAAS,EAAC,8CAA8C;sBAAAD,QAAA,EAAEZ,cAAc,CAACqC,IAAI,CAACK,KAAK;oBAAC;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA,GAJvFe,KAAK;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAKV,CACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN3B,OAAA;YAAKiB,SAAS,EAAC,uBAAuB;YAAAD,QAAA,eACpChB,OAAA;cAAKiB,SAAS,EAAC,iBAAiB;cAAAD,QAAA,eAC9BhB,OAAA;gBAAKiB,SAAS,EAAC,WAAW;gBAAAD,QAAA,gBACxBhB,OAAA;kBAAKiB,SAAS,EAAC,sBAAsB;kBAAAD,QAAA,gBACnChB,OAAA;oBAAAgB,QAAA,EAAM;kBAAe;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5B3B,OAAA;oBAAAgB,QAAA,EAAOZ,cAAc,CAACF,QAAQ,CAAC6C,QAAQ;kBAAC;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC,eACN3B,OAAA;kBAAKiB,SAAS,EAAC,sBAAsB;kBAAAD,QAAA,gBACnChB,OAAA;oBAAAgB,QAAA,EAAM;kBAAM;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnB3B,OAAA;oBAAAgB,QAAA,GAAM,GAAC,EAACZ,cAAc,CAACF,QAAQ,CAAC8C,QAAQ,CAAC;kBAAA;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,eACN3B,OAAA;kBAAKiB,SAAS,EAAC,sBAAsB;kBAAAD,QAAA,gBACnChB,OAAA;oBAAAgB,QAAA,EAAM;kBAA2B;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACxC3B,OAAA;oBAAAgB,QAAA,EAAOZ,cAAc,CAACF,QAAQ,CAAC+C,GAAG;kBAAC;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACN3B,OAAA;kBAAKiB,SAAS,EAAC,sEAAsE;kBAAAD,QAAA,gBACnFhB,OAAA;oBAAAgB,QAAA,EAAM;kBAAiB;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC9B3B,OAAA;oBAAAgB,QAAA,EAAOZ,cAAc,CAACF,QAAQ,CAAC4C,KAAK;kBAAC;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLzB,QAAQ,CAACgD,KAAK,iBACblD,OAAA;YAAKiB,SAAS,EAAC,MAAM;YAAAD,QAAA,gBACnBhB,OAAA;cAAIiB,SAAS,EAAC,0CAA0C;cAAAD,QAAA,EAAC;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrE3B,OAAA;cAAGiB,SAAS,EAAC,yCAAyC;cAAAD,QAAA,EAAEd,QAAQ,CAACgD;YAAK;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CACN,eAGD3B,OAAA;YAAKiB,SAAS,EAAC,6CAA6C;YAAAD,QAAA,gBAC1DhB,OAAA;cAAKiB,SAAS,EAAC,aAAa;cAAAD,QAAA,eAC1BhB,OAAA;gBAAKiB,SAAS,EAAC,qCAAqC;gBAAAD,QAAA,eAClDhB,OAAA;kBAAGiB,SAAS,EAAC,aAAa;kBAAAD,QAAA,EAAC;gBAAY;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN3B,OAAA;cAAKiB,SAAS,EAAC,aAAa;cAAAD,QAAA,eAC1BhB,OAAA;gBAAKiB,SAAS,EAAC,qCAAqC;gBAAAD,QAAA,eAClDhB,OAAA;kBAAGiB,SAAS,EAAC,aAAa;kBAAAD,QAAA,EAAC;gBAAiB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN3B,OAAA;YAAKiB,SAAS,EAAC,sEAAsE;YAAAD,QAAA,gBACnFhB,OAAA;cAAAgB,QAAA,EAAG;YAAmB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC1B3B,OAAA;cAAAgB,QAAA,EAAG;YAAwC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACS,CAAC;AAEtB,CAAC;AAACwB,EAAA,GAlLIlD,eAAe;AAoLrB,eAAeA,eAAe;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}