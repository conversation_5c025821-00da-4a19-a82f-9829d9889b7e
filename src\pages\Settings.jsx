import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  UserIcon,
  BellIcon,
  ShieldCheckIcon,
  GlobeAltIcon,
  PaintBrushIcon,
  CircleStackIcon,
  QrCodeIcon,
  BuildingOfficeIcon
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { useAuth } from '../contexts/AuthContext';
import EInvoiceSettings from '../components/Settings/EInvoiceSettings';
import CompanySettings from '../components/Settings/CompanySettings';
import toast from 'react-hot-toast';

const Settings = () => {
  const { user, updateUser } = useAuth();
  const [activeTab, setActiveTab] = useState('profile');
  const [settings, setSettings] = useState({
    profile: {
      name: user?.name || '',
      email: user?.email || '',
      phone: '',
      position: 'مدير النظام'
    },
    notifications: {
      emailNotifications: true,
      pushNotifications: true,
      weeklyReports: true,
      systemAlerts: true
    },
    appearance: {
      theme: 'light',
      language: 'ar',
      dateFormat: 'dd/mm/yyyy'
    },
    security: {
      twoFactorAuth: false,
      sessionTimeout: '30',
      passwordExpiry: '90'
    }
  });

  const tabs = [
    { id: 'company', name: 'إعدادات الشركة', icon: BuildingOfficeIcon },
    { id: 'profile', name: 'الملف الشخصي', icon: UserIcon },
    { id: 'notifications', name: 'الإشعارات', icon: BellIcon },
    { id: 'appearance', name: 'المظهر', icon: PaintBrushIcon },
    { id: 'security', name: 'الأمان', icon: ShieldCheckIcon },
    { id: 'einvoice', name: 'الفوترة الإلكترونية', icon: QrCodeIcon },
    { id: 'system', name: 'النظام', icon: CircleStackIcon }
  ];

  const handleSettingChange = (category, key, value) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }));
  };

  const saveSettings = () => {
    // حفظ الإعدادات
    if (activeTab === 'profile') {
      updateUser({
        ...user,
        name: settings.profile.name,
        email: settings.profile.email
      });
    }

    localStorage.setItem('appSettings', JSON.stringify(settings));
    toast.success('تم حفظ الإعدادات بنجاح');
  };

  const renderProfileSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <Label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
            الاسم الكامل
          </Label>
          <Input
            id="name"
            value={settings.profile.name}
            onChange={(e) => handleSettingChange('profile', 'name', e.target.value)}
            placeholder="أدخل اسمك الكامل"
          />
        </div>

        <div>
          <Label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
            البريد الإلكتروني
          </Label>
          <Input
            id="email"
            type="email"
            value={settings.profile.email}
            onChange={(e) => handleSettingChange('profile', 'email', e.target.value)}
            placeholder="أدخل بريدك الإلكتروني"
          />
        </div>

        <div>
          <Label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
            رقم الهاتف
          </Label>
          <Input
            id="phone"
            value={settings.profile.phone}
            onChange={(e) => handleSettingChange('profile', 'phone', e.target.value)}
            placeholder="أدخل رقم هاتفك"
          />
        </div>

        <div>
          <Label htmlFor="position" className="block text-sm font-medium text-gray-700 mb-2">
            المنصب
          </Label>
          <Input
            id="position"
            value={settings.profile.position}
            onChange={(e) => handleSettingChange('profile', 'position', e.target.value)}
            placeholder="أدخل منصبك"
          />
        </div>
      </div>

      <div className="pt-4 border-t border-gray-200">
        <h3 className="text-lg font-medium text-gray-900 mb-4">تغيير كلمة المرور</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="currentPassword" className="block text-sm font-medium text-gray-700 mb-2">
              كلمة المرور الحالية
            </Label>
            <Input
              id="currentPassword"
              type="password"
              placeholder="أدخل كلمة المرور الحالية"
            />
          </div>

          <div>
            <Label htmlFor="newPassword" className="block text-sm font-medium text-gray-700 mb-2">
              كلمة المرور الجديدة
            </Label>
            <Input
              id="newPassword"
              type="password"
              placeholder="أدخل كلمة المرور الجديدة"
            />
          </div>
        </div>
      </div>
    </div>
  );

  const renderNotificationSettings = () => (
    <div className="space-y-6">
      {Object.entries(settings.notifications).map(([key, value]) => (
        <div key={key} className="flex items-center justify-between py-3 border-b border-gray-200">
          <div>
            <h3 className="text-sm font-medium text-gray-900">
              {key === 'emailNotifications' && 'إشعارات البريد الإلكتروني'}
              {key === 'pushNotifications' && 'الإشعارات المنبثقة'}
              {key === 'weeklyReports' && 'التقارير الأسبوعية'}
              {key === 'systemAlerts' && 'تنبيهات النظام'}
            </h3>
            <p className="text-sm text-gray-600">
              {key === 'emailNotifications' && 'استقبال الإشعارات عبر البريد الإلكتروني'}
              {key === 'pushNotifications' && 'عرض الإشعارات المنبثقة في المتصفح'}
              {key === 'weeklyReports' && 'استقبال تقارير أسبوعية عن النشاط'}
              {key === 'systemAlerts' && 'تنبيهات حول حالة النظام والأخطاء'}
            </p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={value}
              onChange={(e) => handleSettingChange('notifications', key, e.target.checked)}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
          </label>
        </div>
      ))}
    </div>
  );

  const renderAppearanceSettings = () => (
    <div className="space-y-6">
      <div>
        <Label className="block text-sm font-medium text-gray-700 mb-3">المظهر</Label>
        <div className="grid grid-cols-2 gap-4">
          {['light', 'dark'].map((theme) => (
            <label
              key={theme}
              className={`flex items-center p-4 border rounded-lg cursor-pointer ${
                settings.appearance.theme === theme
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <input
                type="radio"
                name="theme"
                value={theme}
                checked={settings.appearance.theme === theme}
                onChange={(e) => handleSettingChange('appearance', 'theme', e.target.value)}
                className="sr-only"
              />
              <div className="text-center w-full">
                <div className={`w-12 h-8 rounded mb-2 mx-auto ${
                  theme === 'light' ? 'bg-white border-2 border-gray-300' : 'bg-gray-800'
                }`}></div>
                <p className="text-sm font-medium">
                  {theme === 'light' ? 'فاتح' : 'داكن'}
                </p>
              </div>
            </label>
          ))}
        </div>
      </div>

      <div>
        <Label htmlFor="language" className="block text-sm font-medium text-gray-700 mb-2">
          اللغة
        </Label>
        <select
          id="language"
          value={settings.appearance.language}
          onChange={(e) => handleSettingChange('appearance', 'language', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="ar">العربية</option>
          <option value="en">English</option>
        </select>
      </div>

      <div>
        <Label htmlFor="dateFormat" className="block text-sm font-medium text-gray-700 mb-2">
          تنسيق التاريخ
        </Label>
        <select
          id="dateFormat"
          value={settings.appearance.dateFormat}
          onChange={(e) => handleSettingChange('appearance', 'dateFormat', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="dd/mm/yyyy">يوم/شهر/سنة</option>
          <option value="mm/dd/yyyy">شهر/يوم/سنة</option>
          <option value="yyyy-mm-dd">سنة-شهر-يوم</option>
        </select>
      </div>
    </div>
  );

  const renderSecuritySettings = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between py-3 border-b border-gray-200">
        <div>
          <h3 className="text-sm font-medium text-gray-900">المصادقة الثنائية</h3>
          <p className="text-sm text-gray-600">إضافة طبقة حماية إضافية لحسابك</p>
        </div>
        <label className="relative inline-flex items-center cursor-pointer">
          <input
            type="checkbox"
            checked={settings.security.twoFactorAuth}
            onChange={(e) => handleSettingChange('security', 'twoFactorAuth', e.target.checked)}
            className="sr-only peer"
          />
          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
        </label>
      </div>

      <div>
        <Label htmlFor="sessionTimeout" className="block text-sm font-medium text-gray-700 mb-2">
          انتهاء الجلسة (بالدقائق)
        </Label>
        <Input
          id="sessionTimeout"
          type="number"
          value={settings.security.sessionTimeout}
          onChange={(e) => handleSettingChange('security', 'sessionTimeout', e.target.value)}
          placeholder="30"
        />
      </div>

      <div>
        <Label htmlFor="passwordExpiry" className="block text-sm font-medium text-gray-700 mb-2">
          انتهاء صلاحية كلمة المرور (بالأيام)
        </Label>
        <Input
          id="passwordExpiry"
          type="number"
          value={settings.security.passwordExpiry}
          onChange={(e) => handleSettingChange('security', 'passwordExpiry', e.target.value)}
          placeholder="90"
        />
      </div>
    </div>
  );

  const renderSystemSettings = () => (
    <div className="space-y-6">
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h3 className="text-sm font-medium text-yellow-800 mb-2">تحذير</h3>
        <p className="text-sm text-yellow-700">
          إعدادات النظام تؤثر على جميع المستخدمين. يرجى الحذر عند تغييرها.
        </p>
      </div>

      <div className="space-y-4">
        <Button variant="outline" className="w-full justify-start">
          <CircleStackIcon className="w-4 h-4 ml-2" />
          نسخ احتياطي من البيانات
        </Button>

        <Button variant="outline" className="w-full justify-start">
          <CircleStackIcon className="w-4 h-4 ml-2" />
          استيراد البيانات
        </Button>

        <Button variant="outline" className="w-full justify-start text-red-600 border-red-200 hover:bg-red-50">
          <CircleStackIcon className="w-4 h-4 ml-2" />
          مسح جميع البيانات
        </Button>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'company':
        return <CompanySettings />;
      case 'profile':
        return renderProfileSettings();
      case 'notifications':
        return renderNotificationSettings();
      case 'appearance':
        return renderAppearanceSettings();
      case 'security':
        return renderSecuritySettings();
      case 'einvoice':
        return <EInvoiceSettings />;
      case 'system':
        return renderSystemSettings();
      default:
        return <CompanySettings />;
    }
  };

  return (
    <div className="space-y-6">
      {/* العنوان */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <h1 className="text-3xl font-bold text-gray-900">الإعدادات</h1>
        <p className="text-gray-600 mt-2">إدارة إعدادات النظام والملف الشخصي</p>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* قائمة التبويبات */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card>
            <CardContent className="p-0">
              <nav className="space-y-1">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center px-4 py-3 text-sm font-medium text-right transition-colors ${
                      activeTab === tab.id
                        ? 'bg-blue-50 text-blue-700 border-l-4 border-blue-700'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                  >
                    <tab.icon className="w-5 h-5 ml-3" />
                    {tab.name}
                  </button>
                ))}
              </nav>
            </CardContent>
          </Card>
        </motion.div>

        {/* محتوى التبويب */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
          className="lg:col-span-3"
        >
          <Card>
            <CardHeader>
              <CardTitle>
                {tabs.find(tab => tab.id === activeTab)?.name}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {renderContent()}

              {/* أزرار الحفظ */}
              <div className="flex items-center justify-end space-x-4 space-x-reverse mt-8 pt-6 border-t border-gray-200">
                <Button variant="outline">
                  إلغاء
                </Button>
                <Button onClick={saveSettings} className="bg-blue-600 hover:bg-blue-700 text-white">
                  حفظ التغييرات
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default Settings;
