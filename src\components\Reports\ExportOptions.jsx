import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  XMarkIcon,
  DocumentArrowDownIcon,
  DocumentTextIcon,
  TableCellsIcon,
  DocumentIcon
} from '@heroicons/react/24/outline';
import { Button } from '../ui/button';
import { Label } from '../ui/label';
import toast from 'react-hot-toast';

const ExportOptions = ({ onClose }) => {
  const [selectedFormat, setSelectedFormat] = useState('pdf');
  const [selectedData, setSelectedData] = useState('all');
  const [isExporting, setIsExporting] = useState(false);

  const exportFormats = [
    {
      id: 'pdf',
      name: 'PDF',
      description: 'ملف PDF للطباعة والمشاركة',
      icon: DocumentTextIcon,
      color: 'text-red-600'
    },
    {
      id: 'excel',
      name: 'Excel',
      description: 'جدول بيانات Excel',
      icon: TableCellsIcon,
      color: 'text-green-600'
    },
    {
      id: 'csv',
      name: 'CSV',
      description: 'ملف CSV للاستيراد',
      icon: DocumentIcon,
      color: 'text-blue-600'
    }
  ];

  const dataOptions = [
    { id: 'all', name: 'جميع البيانات', description: 'تصدير جميع بيانات الشركات' },
    { id: 'filtered', name: 'البيانات المفلترة', description: 'تصدير البيانات المفلترة فقط' },
    { id: 'summary', name: 'ملخص الإحصائيات', description: 'تصدير ملخص الإحصائيات فقط' }
  ];

  const handleExport = async () => {
    setIsExporting(true);
    
    try {
      // محاكاة عملية التصدير
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // هنا يمكن إضافة منطق التصدير الفعلي
      console.log('تصدير:', { format: selectedFormat, data: selectedData });
      
      toast.success(`تم تصدير التقرير بصيغة ${exportFormats.find(f => f.id === selectedFormat)?.name} بنجاح`);
      onClose();
    } catch (error) {
      toast.error('فشل في تصدير التقرير');
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        {/* الخلفية */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 bg-black bg-opacity-50"
          onClick={onClose}
        />
        
        {/* المودال */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="relative bg-white rounded-2xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto"
        >
          {/* الرأس */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900 flex items-center">
              <DocumentArrowDownIcon className="w-6 h-6 ml-2 text-blue-600" />
              تصدير التقرير
            </h2>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>

          {/* المحتوى */}
          <div className="p-6 space-y-6">
            {/* اختيار صيغة التصدير */}
            <div>
              <Label className="block text-sm font-medium text-gray-900 mb-3">
                صيغة التصدير
              </Label>
              <div className="space-y-3">
                {exportFormats.map((format) => (
                  <label
                    key={format.id}
                    className={`flex items-center p-3 border rounded-lg cursor-pointer transition-all ${
                      selectedFormat === format.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <input
                      type="radio"
                      name="format"
                      value={format.id}
                      checked={selectedFormat === format.id}
                      onChange={(e) => setSelectedFormat(e.target.value)}
                      className="sr-only"
                    />
                    <format.icon className={`w-6 h-6 ml-3 ${format.color}`} />
                    <div className="flex-1">
                      <p className="font-medium text-gray-900">{format.name}</p>
                      <p className="text-sm text-gray-600">{format.description}</p>
                    </div>
                    {selectedFormat === format.id && (
                      <div className="w-4 h-4 bg-blue-600 rounded-full flex items-center justify-center">
                        <div className="w-2 h-2 bg-white rounded-full"></div>
                      </div>
                    )}
                  </label>
                ))}
              </div>
            </div>

            {/* اختيار البيانات */}
            <div>
              <Label className="block text-sm font-medium text-gray-900 mb-3">
                البيانات المراد تصديرها
              </Label>
              <div className="space-y-3">
                {dataOptions.map((option) => (
                  <label
                    key={option.id}
                    className={`flex items-start p-3 border rounded-lg cursor-pointer transition-all ${
                      selectedData === option.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <input
                      type="radio"
                      name="data"
                      value={option.id}
                      checked={selectedData === option.id}
                      onChange={(e) => setSelectedData(e.target.value)}
                      className="mt-1 sr-only"
                    />
                    <div className="flex-1">
                      <p className="font-medium text-gray-900">{option.name}</p>
                      <p className="text-sm text-gray-600">{option.description}</p>
                    </div>
                    {selectedData === option.id && (
                      <div className="w-4 h-4 bg-blue-600 rounded-full flex items-center justify-center mt-1">
                        <div className="w-2 h-2 bg-white rounded-full"></div>
                      </div>
                    )}
                  </label>
                ))}
              </div>
            </div>
          </div>

          {/* الأزرار */}
          <div className="flex items-center justify-end space-x-4 space-x-reverse p-6 border-t border-gray-200">
            <Button
              variant="outline"
              onClick={onClose}
              disabled={isExporting}
            >
              إلغاء
            </Button>
            <Button
              onClick={handleExport}
              disabled={isExporting}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              {isExporting ? (
                <div className="flex items-center">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ml-2"></div>
                  جاري التصدير...
                </div>
              ) : (
                <>
                  <DocumentArrowDownIcon className="w-4 h-4 ml-2" />
                  تصدير
                </>
              )}
            </Button>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default ExportOptions;
