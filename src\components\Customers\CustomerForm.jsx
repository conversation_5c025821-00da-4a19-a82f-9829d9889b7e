import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import toast from 'react-hot-toast';

const CustomerForm = ({ customer, isEditing, onSave, onClose }) => {
  const [formData, setFormData] = useState({
    name: '',
    type: 'فرد',
    phone: '',
    email: '',
    address: '',
    city: '',
    country: 'السعودية',
    taxNumber: '',
    creditLimit: 0,
    contactPerson: '',
    paymentTerms: 'نقدي',
    discount: 0,
    notes: ''
  });

  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (isEditing && customer) {
      setFormData({
        name: customer.name || '',
        type: customer.type || 'فرد',
        phone: customer.phone || '',
        email: customer.email || '',
        address: customer.address || '',
        city: customer.city || '',
        country: customer.country || 'السعودية',
        taxNumber: customer.taxNumber || '',
        creditLimit: customer.creditLimit || 0,
        contactPerson: customer.contactPerson || '',
        paymentTerms: customer.paymentTerms || 'نقدي',
        discount: customer.discount || 0,
        notes: customer.notes || ''
      });
    }
  }, [isEditing, customer]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast.error('يرجى إدخال اسم العميل');
      return;
    }

    if (!formData.phone.trim()) {
      toast.error('يرجى إدخال رقم الهاتف');
      return;
    }

    setIsLoading(true);
    try {
      await onSave(formData);
    } catch (error) {
      toast.error('حدث خطأ أثناء حفظ العميل');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="bg-white rounded-2xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
        >
          {/* الرأس */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">
              {isEditing ? 'تعديل العميل' : 'عميل جديد'}
            </h2>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>

          {/* المحتوى */}
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* المعلومات الأساسية */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">المعلومات الأساسية</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                    اسم العميل *
                  </Label>
                  <Input
                    id="name"
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="أدخل اسم العميل"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-2">
                    نوع العميل
                  </Label>
                  <select
                    id="type"
                    value={formData.type}
                    onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  >
                    <option value="فرد">فرد</option>
                    <option value="شركة">شركة</option>
                  </select>
                </div>

                <div>
                  <Label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                    رقم الهاتف *
                  </Label>
                  <Input
                    id="phone"
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                    placeholder="05xxxxxxxx"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                    البريد الإلكتروني
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <Label htmlFor="contactPerson" className="block text-sm font-medium text-gray-700 mb-2">
                    الشخص المسؤول
                  </Label>
                  <Input
                    id="contactPerson"
                    type="text"
                    value={formData.contactPerson}
                    onChange={(e) => setFormData(prev => ({ ...prev, contactPerson: e.target.value }))}
                    placeholder="اسم الشخص المسؤول"
                  />
                </div>

                <div>
                  <Label htmlFor="taxNumber" className="block text-sm font-medium text-gray-700 mb-2">
                    الرقم الضريبي
                  </Label>
                  <Input
                    id="taxNumber"
                    type="text"
                    value={formData.taxNumber}
                    onChange={(e) => setFormData(prev => ({ ...prev, taxNumber: e.target.value }))}
                    placeholder="الرقم الضريبي"
                  />
                </div>
              </div>
            </div>

            {/* العنوان */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">العنوان</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="md:col-span-2">
                  <Label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-2">
                    العنوان التفصيلي
                  </Label>
                  <textarea
                    id="address"
                    rows={3}
                    value={formData.address}
                    onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="أدخل العنوان التفصيلي"
                  />
                </div>

                <div>
                  <Label htmlFor="city" className="block text-sm font-medium text-gray-700 mb-2">
                    المدينة
                  </Label>
                  <Input
                    id="city"
                    type="text"
                    value={formData.city}
                    onChange={(e) => setFormData(prev => ({ ...prev, city: e.target.value }))}
                    placeholder="اسم المدينة"
                  />
                </div>

                <div>
                  <Label htmlFor="country" className="block text-sm font-medium text-gray-700 mb-2">
                    الدولة
                  </Label>
                  <select
                    id="country"
                    value={formData.country}
                    onChange={(e) => setFormData(prev => ({ ...prev, country: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  >
                    <option value="السعودية">السعودية</option>
                    <option value="الإمارات">الإمارات</option>
                    <option value="الكويت">الكويت</option>
                    <option value="قطر">قطر</option>
                    <option value="البحرين">البحرين</option>
                    <option value="عمان">عمان</option>
                    <option value="الأردن">الأردن</option>
                    <option value="مصر">مصر</option>
                  </select>
                </div>
              </div>
            </div>

            {/* الشروط المالية */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">الشروط المالية</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <Label htmlFor="creditLimit" className="block text-sm font-medium text-gray-700 mb-2">
                    الحد الائتماني
                  </Label>
                  <Input
                    id="creditLimit"
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.creditLimit}
                    onChange={(e) => setFormData(prev => ({ ...prev, creditLimit: parseFloat(e.target.value) || 0 }))}
                    placeholder="0.00"
                  />
                </div>

                <div>
                  <Label htmlFor="paymentTerms" className="block text-sm font-medium text-gray-700 mb-2">
                    شروط الدفع
                  </Label>
                  <select
                    id="paymentTerms"
                    value={formData.paymentTerms}
                    onChange={(e) => setFormData(prev => ({ ...prev, paymentTerms: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  >
                    <option value="نقدي">نقدي</option>
                    <option value="آجل 15 يوم">آجل 15 يوم</option>
                    <option value="آجل 30 يوم">آجل 30 يوم</option>
                    <option value="آجل 45 يوم">آجل 45 يوم</option>
                    <option value="آجل 60 يوم">آجل 60 يوم</option>
                  </select>
                </div>

                <div>
                  <Label htmlFor="discount" className="block text-sm font-medium text-gray-700 mb-2">
                    نسبة الخصم (%)
                  </Label>
                  <Input
                    id="discount"
                    type="number"
                    min="0"
                    max="100"
                    step="0.1"
                    value={formData.discount}
                    onChange={(e) => setFormData(prev => ({ ...prev, discount: parseFloat(e.target.value) || 0 }))}
                    placeholder="0"
                  />
                </div>
              </div>
            </div>

            {/* الملاحظات */}
            <div>
              <Label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
                ملاحظات
              </Label>
              <textarea
                id="notes"
                rows={3}
                value={formData.notes}
                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="أدخل أي ملاحظات إضافية..."
              />
            </div>

            {/* الأزرار */}
            <div className="flex items-center justify-end space-x-4 space-x-reverse pt-6 border-t border-gray-200">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isLoading}
              >
                إلغاء
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
                className="bg-indigo-600 hover:bg-indigo-700 text-white"
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ml-2"></div>
                    جاري الحفظ...
                  </div>
                ) : (
                  isEditing ? 'حفظ التعديلات' : 'حفظ العميل'
                )}
              </Button>
            </div>
          </form>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default CustomerForm;
