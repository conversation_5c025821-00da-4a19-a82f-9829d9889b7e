{"ast": null, "code": "// مولد QR Code متوافق مع المرحلة الثانية من الفوترة الإلكترونية السعودية\n// وفقاً لمعايير هيئة الزكاة والضريبة والجمارك\n\n/**\n * تشفير النص إلى Base64\n * @param {string} text - النص المراد تشفيره\n * @returns {string} النص مشفر بـ Base64\n */\nconst encodeToBase64 = text => {\n  return btoa(unescape(encodeURIComponent(text)));\n};\n\n/**\n * تحويل إلى TLV (Tag-Length-Value) format بالطريقة الصحيحة\n * @param {number} tag - رقم العلامة\n * @param {string} value - القيمة\n * @returns {Uint8Array} البيانات بصيغة TLV\n */\nconst toTLV = (tag, value) => {\n  const valueBytes = new TextEncoder().encode(value);\n  const result = new Uint8Array(2 + valueBytes.length);\n  result[0] = tag;\n  result[1] = valueBytes.length;\n  result.set(valueBytes, 2);\n  return result;\n};\n\n/**\n * تحويل Uint8Array إلى Base64\n * @param {Uint8Array} bytes - البيانات\n * @returns {string} البيانات مشفرة بـ Base64\n */\nconst arrayToBase64 = bytes => {\n  let binary = '';\n  for (let i = 0; i < bytes.length; i++) {\n    binary += String.fromCharCode(bytes[i]);\n  }\n  return btoa(binary);\n};\n\n/**\n * إنشاء QR Code متوافق مع المرحلة الثانية\n * @param {Object} invoiceData - بيانات الفاتورة\n * @returns {string} محتوى QR Code\n */\nexport const generateQRCodeData = invoiceData => {\n  try {\n    // تحميل إعدادات الشركة من التخزين المحلي\n    const savedSettings = localStorage.getItem('einvoice_settings');\n    const defaultSettings = {\n      companyName: 'شركة إدارة الأعمال',\n      vatNumber: '300000000000003'\n    };\n    const companySettings = savedSettings ? JSON.parse(savedSettings) : defaultSettings;\n    const companyData = {\n      name: companySettings.companyName || defaultSettings.companyName,\n      vatNumber: companySettings.vatNumber || defaultSettings.vatNumber,\n      timestamp: new Date().toISOString(),\n      invoiceTotal: invoiceData.total.toFixed(2),\n      vatAmount: invoiceData.tax.toFixed(2)\n    };\n\n    // إنشاء البيانات بصيغة TLV وفقاً للمعايير السعودية\n    const tlvArrays = [];\n\n    // Tag 1: اسم البائع (Seller Name)\n    tlvArrays.push(toTLV(1, companyData.name));\n\n    // Tag 2: الرقم الضريبي للبائع (Seller VAT Number)\n    tlvArrays.push(toTLV(2, companyData.vatNumber));\n\n    // Tag 3: الطابع الزمني (Timestamp)\n    tlvArrays.push(toTLV(3, companyData.timestamp));\n\n    // Tag 4: إجمالي الفاتورة شامل الضريبة (Invoice Total with VAT)\n    tlvArrays.push(toTLV(4, companyData.invoiceTotal));\n\n    // Tag 5: إجمالي ضريبة القيمة المضافة (VAT Total)\n    tlvArrays.push(toTLV(5, companyData.vatAmount));\n\n    // دمج جميع المصفوفات\n    const totalLength = tlvArrays.reduce((sum, arr) => sum + arr.length, 0);\n    const combinedArray = new Uint8Array(totalLength);\n    let offset = 0;\n    for (const arr of tlvArrays) {\n      combinedArray.set(arr, offset);\n      offset += arr.length;\n    }\n\n    // تحويل البيانات إلى Base64\n    const base64Data = arrayToBase64(combinedArray);\n    return base64Data;\n  } catch (error) {\n    console.error('خطأ في إنشاء QR Code:', error);\n    return '';\n  }\n};\n\n/**\n * إنشاء QR Code مبسط للاختبار\n * @param {Object} invoiceData - بيانات الفاتورة\n * @returns {string} محتوى QR Code مبسط\n */\nexport const generateSimpleQRCode = invoiceData => {\n  const qrData = {\n    seller: 'شركة إدارة الأعمال',\n    vatNumber: '300000000000003',\n    invoiceNumber: invoiceData.invoiceNumber,\n    date: invoiceData.date,\n    total: invoiceData.total.toFixed(2),\n    vat: invoiceData.tax.toFixed(2),\n    customer: invoiceData.customerName\n  };\n  return JSON.stringify(qrData);\n};\n\n/**\n * التحقق من صحة الرقم الضريبي السعودي\n * @param {string} vatNumber - الرقم الضريبي\n * @returns {boolean} صحة الرقم الضريبي\n */\nexport const validateSaudiVATNumber = vatNumber => {\n  // الرقم الضريبي السعودي يجب أن يكون 15 رقم\n  const vatRegex = /^[0-9]{15}$/;\n  return vatRegex.test(vatNumber);\n};\n\n/**\n * تنسيق التاريخ للفوترة الإلكترونية\n * @param {Date} date - التاريخ\n * @returns {string} التاريخ منسق\n */\nexport const formatDateForEInvoice = date => {\n  return new Date(date).toISOString();\n};\n\n/**\n * حساب hash للفاتورة (للمرحلة الثانية المتقدمة)\n * @param {Object} invoiceData - بيانات الفاتورة\n * @returns {string} hash الفاتورة\n */\nexport const generateInvoiceHash = invoiceData => {\n  const dataString = JSON.stringify({\n    invoiceNumber: invoiceData.invoiceNumber,\n    date: invoiceData.date,\n    total: invoiceData.total,\n    items: invoiceData.items\n  });\n\n  // استخدام hash بسيط (في الإنتاج يجب استخدام SHA-256)\n  let hash = 0;\n  for (let i = 0; i < dataString.length; i++) {\n    const char = dataString.charCodeAt(i);\n    hash = (hash << 5) - hash + char;\n    hash = hash & hash; // تحويل إلى 32bit integer\n  }\n  return Math.abs(hash).toString(16);\n};", "map": {"version": 3, "names": ["encodeToBase64", "text", "btoa", "unescape", "encodeURIComponent", "toTLV", "tag", "value", "valueBytes", "TextEncoder", "encode", "result", "Uint8Array", "length", "set", "arrayToBase64", "bytes", "binary", "i", "String", "fromCharCode", "generateQRCodeData", "invoiceData", "savedSettings", "localStorage", "getItem", "defaultSettings", "companyName", "vatNumber", "companySettings", "JSON", "parse", "companyData", "name", "timestamp", "Date", "toISOString", "invoiceTotal", "total", "toFixed", "vatAmount", "tax", "tlvArrays", "push", "totalLength", "reduce", "sum", "arr", "combinedArray", "offset", "base64Data", "error", "console", "generateSimpleQRCode", "qrData", "seller", "invoiceNumber", "date", "vat", "customer", "customerName", "stringify", "validateSaudiVATNumber", "vatRegex", "test", "formatDateForEInvoice", "generateInvoiceHash", "dataString", "items", "hash", "char", "charCodeAt", "Math", "abs", "toString"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/src/utils/qrCodeGenerator.js"], "sourcesContent": ["// مولد QR Code متوافق مع المرحلة الثانية من الفوترة الإلكترونية السعودية\n// وفقاً لمعايير هيئة الزكاة والضريبة والجمارك\n\n/**\n * تشفير النص إلى Base64\n * @param {string} text - النص المراد تشفيره\n * @returns {string} النص مشفر بـ Base64\n */\nconst encodeToBase64 = (text) => {\n  return btoa(unescape(encodeURIComponent(text)));\n};\n\n/**\n * تحويل إلى TLV (Tag-Length-Value) format بالطريقة الصحيحة\n * @param {number} tag - رقم العلامة\n * @param {string} value - القيمة\n * @returns {Uint8Array} البيانات بصيغة TLV\n */\nconst toTLV = (tag, value) => {\n  const valueBytes = new TextEncoder().encode(value);\n  const result = new Uint8Array(2 + valueBytes.length);\n\n  result[0] = tag;\n  result[1] = valueBytes.length;\n  result.set(valueBytes, 2);\n\n  return result;\n};\n\n/**\n * تحويل Uint8Array إلى Base64\n * @param {Uint8Array} bytes - البيانات\n * @returns {string} البيانات مشفرة بـ Base64\n */\nconst arrayToBase64 = (bytes) => {\n  let binary = '';\n  for (let i = 0; i < bytes.length; i++) {\n    binary += String.fromCharCode(bytes[i]);\n  }\n  return btoa(binary);\n};\n\n/**\n * إنشاء QR Code متوافق مع المرحلة الثانية\n * @param {Object} invoiceData - بيانات الفاتورة\n * @returns {string} محتوى QR Code\n */\nexport const generateQRCodeData = (invoiceData) => {\n  try {\n    // تحميل إعدادات الشركة من التخزين المحلي\n    const savedSettings = localStorage.getItem('einvoice_settings');\n    const defaultSettings = {\n      companyName: 'شركة إدارة الأعمال',\n      vatNumber: '300000000000003'\n    };\n\n    const companySettings = savedSettings ? JSON.parse(savedSettings) : defaultSettings;\n\n    const companyData = {\n      name: companySettings.companyName || defaultSettings.companyName,\n      vatNumber: companySettings.vatNumber || defaultSettings.vatNumber,\n      timestamp: new Date().toISOString(),\n      invoiceTotal: invoiceData.total.toFixed(2),\n      vatAmount: invoiceData.tax.toFixed(2)\n    };\n\n    // إنشاء البيانات بصيغة TLV وفقاً للمعايير السعودية\n    const tlvArrays = [];\n\n    // Tag 1: اسم البائع (Seller Name)\n    tlvArrays.push(toTLV(1, companyData.name));\n\n    // Tag 2: الرقم الضريبي للبائع (Seller VAT Number)\n    tlvArrays.push(toTLV(2, companyData.vatNumber));\n\n    // Tag 3: الطابع الزمني (Timestamp)\n    tlvArrays.push(toTLV(3, companyData.timestamp));\n\n    // Tag 4: إجمالي الفاتورة شامل الضريبة (Invoice Total with VAT)\n    tlvArrays.push(toTLV(4, companyData.invoiceTotal));\n\n    // Tag 5: إجمالي ضريبة القيمة المضافة (VAT Total)\n    tlvArrays.push(toTLV(5, companyData.vatAmount));\n\n    // دمج جميع المصفوفات\n    const totalLength = tlvArrays.reduce((sum, arr) => sum + arr.length, 0);\n    const combinedArray = new Uint8Array(totalLength);\n    let offset = 0;\n\n    for (const arr of tlvArrays) {\n      combinedArray.set(arr, offset);\n      offset += arr.length;\n    }\n\n    // تحويل البيانات إلى Base64\n    const base64Data = arrayToBase64(combinedArray);\n\n    return base64Data;\n\n  } catch (error) {\n    console.error('خطأ في إنشاء QR Code:', error);\n    return '';\n  }\n};\n\n/**\n * إنشاء QR Code مبسط للاختبار\n * @param {Object} invoiceData - بيانات الفاتورة\n * @returns {string} محتوى QR Code مبسط\n */\nexport const generateSimpleQRCode = (invoiceData) => {\n  const qrData = {\n    seller: 'شركة إدارة الأعمال',\n    vatNumber: '300000000000003',\n    invoiceNumber: invoiceData.invoiceNumber,\n    date: invoiceData.date,\n    total: invoiceData.total.toFixed(2),\n    vat: invoiceData.tax.toFixed(2),\n    customer: invoiceData.customerName\n  };\n\n  return JSON.stringify(qrData);\n};\n\n/**\n * التحقق من صحة الرقم الضريبي السعودي\n * @param {string} vatNumber - الرقم الضريبي\n * @returns {boolean} صحة الرقم الضريبي\n */\nexport const validateSaudiVATNumber = (vatNumber) => {\n  // الرقم الضريبي السعودي يجب أن يكون 15 رقم\n  const vatRegex = /^[0-9]{15}$/;\n  return vatRegex.test(vatNumber);\n};\n\n/**\n * تنسيق التاريخ للفوترة الإلكترونية\n * @param {Date} date - التاريخ\n * @returns {string} التاريخ منسق\n */\nexport const formatDateForEInvoice = (date) => {\n  return new Date(date).toISOString();\n};\n\n/**\n * حساب hash للفاتورة (للمرحلة الثانية المتقدمة)\n * @param {Object} invoiceData - بيانات الفاتورة\n * @returns {string} hash الفاتورة\n */\nexport const generateInvoiceHash = (invoiceData) => {\n  const dataString = JSON.stringify({\n    invoiceNumber: invoiceData.invoiceNumber,\n    date: invoiceData.date,\n    total: invoiceData.total,\n    items: invoiceData.items\n  });\n\n  // استخدام hash بسيط (في الإنتاج يجب استخدام SHA-256)\n  let hash = 0;\n  for (let i = 0; i < dataString.length; i++) {\n    const char = dataString.charCodeAt(i);\n    hash = ((hash << 5) - hash) + char;\n    hash = hash & hash; // تحويل إلى 32bit integer\n  }\n\n  return Math.abs(hash).toString(16);\n};\n"], "mappings": "AAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMA,cAAc,GAAIC,IAAI,IAAK;EAC/B,OAAOC,IAAI,CAACC,QAAQ,CAACC,kBAAkB,CAACH,IAAI,CAAC,CAAC,CAAC;AACjD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,KAAK,GAAGA,CAACC,GAAG,EAAEC,KAAK,KAAK;EAC5B,MAAMC,UAAU,GAAG,IAAIC,WAAW,CAAC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;EAClD,MAAMI,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC,GAAGJ,UAAU,CAACK,MAAM,CAAC;EAEpDF,MAAM,CAAC,CAAC,CAAC,GAAGL,GAAG;EACfK,MAAM,CAAC,CAAC,CAAC,GAAGH,UAAU,CAACK,MAAM;EAC7BF,MAAM,CAACG,GAAG,CAACN,UAAU,EAAE,CAAC,CAAC;EAEzB,OAAOG,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,MAAMI,aAAa,GAAIC,KAAK,IAAK;EAC/B,IAAIC,MAAM,GAAG,EAAE;EACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACH,MAAM,EAAEK,CAAC,EAAE,EAAE;IACrCD,MAAM,IAAIE,MAAM,CAACC,YAAY,CAACJ,KAAK,CAACE,CAAC,CAAC,CAAC;EACzC;EACA,OAAOhB,IAAI,CAACe,MAAM,CAAC;AACrB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMI,kBAAkB,GAAIC,WAAW,IAAK;EACjD,IAAI;IACF;IACA,MAAMC,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,mBAAmB,CAAC;IAC/D,MAAMC,eAAe,GAAG;MACtBC,WAAW,EAAE,oBAAoB;MACjCC,SAAS,EAAE;IACb,CAAC;IAED,MAAMC,eAAe,GAAGN,aAAa,GAAGO,IAAI,CAACC,KAAK,CAACR,aAAa,CAAC,GAAGG,eAAe;IAEnF,MAAMM,WAAW,GAAG;MAClBC,IAAI,EAAEJ,eAAe,CAACF,WAAW,IAAID,eAAe,CAACC,WAAW;MAChEC,SAAS,EAAEC,eAAe,CAACD,SAAS,IAAIF,eAAe,CAACE,SAAS;MACjEM,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnCC,YAAY,EAAEf,WAAW,CAACgB,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC;MAC1CC,SAAS,EAAElB,WAAW,CAACmB,GAAG,CAACF,OAAO,CAAC,CAAC;IACtC,CAAC;;IAED;IACA,MAAMG,SAAS,GAAG,EAAE;;IAEpB;IACAA,SAAS,CAACC,IAAI,CAACtC,KAAK,CAAC,CAAC,EAAE2B,WAAW,CAACC,IAAI,CAAC,CAAC;;IAE1C;IACAS,SAAS,CAACC,IAAI,CAACtC,KAAK,CAAC,CAAC,EAAE2B,WAAW,CAACJ,SAAS,CAAC,CAAC;;IAE/C;IACAc,SAAS,CAACC,IAAI,CAACtC,KAAK,CAAC,CAAC,EAAE2B,WAAW,CAACE,SAAS,CAAC,CAAC;;IAE/C;IACAQ,SAAS,CAACC,IAAI,CAACtC,KAAK,CAAC,CAAC,EAAE2B,WAAW,CAACK,YAAY,CAAC,CAAC;;IAElD;IACAK,SAAS,CAACC,IAAI,CAACtC,KAAK,CAAC,CAAC,EAAE2B,WAAW,CAACQ,SAAS,CAAC,CAAC;;IAE/C;IACA,MAAMI,WAAW,GAAGF,SAAS,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAAClC,MAAM,EAAE,CAAC,CAAC;IACvE,MAAMmC,aAAa,GAAG,IAAIpC,UAAU,CAACgC,WAAW,CAAC;IACjD,IAAIK,MAAM,GAAG,CAAC;IAEd,KAAK,MAAMF,GAAG,IAAIL,SAAS,EAAE;MAC3BM,aAAa,CAAClC,GAAG,CAACiC,GAAG,EAAEE,MAAM,CAAC;MAC9BA,MAAM,IAAIF,GAAG,CAAClC,MAAM;IACtB;;IAEA;IACA,MAAMqC,UAAU,GAAGnC,aAAa,CAACiC,aAAa,CAAC;IAE/C,OAAOE,UAAU;EAEnB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC7C,OAAO,EAAE;EACX;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,oBAAoB,GAAI/B,WAAW,IAAK;EACnD,MAAMgC,MAAM,GAAG;IACbC,MAAM,EAAE,oBAAoB;IAC5B3B,SAAS,EAAE,iBAAiB;IAC5B4B,aAAa,EAAElC,WAAW,CAACkC,aAAa;IACxCC,IAAI,EAAEnC,WAAW,CAACmC,IAAI;IACtBnB,KAAK,EAAEhB,WAAW,CAACgB,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC;IACnCmB,GAAG,EAAEpC,WAAW,CAACmB,GAAG,CAACF,OAAO,CAAC,CAAC,CAAC;IAC/BoB,QAAQ,EAAErC,WAAW,CAACsC;EACxB,CAAC;EAED,OAAO9B,IAAI,CAAC+B,SAAS,CAACP,MAAM,CAAC;AAC/B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMQ,sBAAsB,GAAIlC,SAAS,IAAK;EACnD;EACA,MAAMmC,QAAQ,GAAG,aAAa;EAC9B,OAAOA,QAAQ,CAACC,IAAI,CAACpC,SAAS,CAAC;AACjC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMqC,qBAAqB,GAAIR,IAAI,IAAK;EAC7C,OAAO,IAAItB,IAAI,CAACsB,IAAI,CAAC,CAACrB,WAAW,CAAC,CAAC;AACrC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM8B,mBAAmB,GAAI5C,WAAW,IAAK;EAClD,MAAM6C,UAAU,GAAGrC,IAAI,CAAC+B,SAAS,CAAC;IAChCL,aAAa,EAAElC,WAAW,CAACkC,aAAa;IACxCC,IAAI,EAAEnC,WAAW,CAACmC,IAAI;IACtBnB,KAAK,EAAEhB,WAAW,CAACgB,KAAK;IACxB8B,KAAK,EAAE9C,WAAW,CAAC8C;EACrB,CAAC,CAAC;;EAEF;EACA,IAAIC,IAAI,GAAG,CAAC;EACZ,KAAK,IAAInD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiD,UAAU,CAACtD,MAAM,EAAEK,CAAC,EAAE,EAAE;IAC1C,MAAMoD,IAAI,GAAGH,UAAU,CAACI,UAAU,CAACrD,CAAC,CAAC;IACrCmD,IAAI,GAAI,CAACA,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAIC,IAAI;IAClCD,IAAI,GAAGA,IAAI,GAAGA,IAAI,CAAC,CAAC;EACtB;EAEA,OAAOG,IAAI,CAACC,GAAG,CAACJ,IAAI,CAAC,CAACK,QAAQ,CAAC,EAAE,CAAC;AACpC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}