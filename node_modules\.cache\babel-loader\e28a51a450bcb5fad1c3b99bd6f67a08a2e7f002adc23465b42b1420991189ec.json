{"ast": null, "code": "import { useSyncExternalStore as r } from '../use-sync-external-store-shim/index.js';\nfunction S(t) {\n  return r(t.subscribe, t.getSnapshot, t.getSnapshot);\n}\nexport { S as useStore };", "map": {"version": 3, "names": ["useSyncExternalStore", "r", "S", "t", "subscribe", "getSnapshot", "useStore"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/node_modules/@headlessui/react/dist/hooks/use-store.js"], "sourcesContent": ["import{useSyncExternalStore as r}from'../use-sync-external-store-shim/index.js';function S(t){return r(t.subscribe,t.getSnapshot,t.getSnapshot)}export{S as useStore};\n"], "mappings": "AAAA,SAAOA,oBAAoB,IAAIC,CAAC,QAAK,0CAA0C;AAAC,SAASC,CAACA,CAACC,CAAC,EAAC;EAAC,OAAOF,CAAC,CAACE,CAAC,CAACC,SAAS,EAACD,CAAC,CAACE,WAAW,EAACF,CAAC,CAACE,WAAW,CAAC;AAAA;AAAC,SAAOH,CAAC,IAAII,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}