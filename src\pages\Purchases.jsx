import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  ShoppingCartIcon
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../components/ui/table';
import purchasesAPI from '../api/purchasesAPI';
import PurchaseForm from '../components/Purchases/PurchaseForm';
import PurchaseInvoice from '../components/Purchases/PurchaseInvoice';
import toast from 'react-hot-toast';

const Purchases = () => {
  const [purchases, setPurchases] = useState([]);
  const [filteredPurchases, setFilteredPurchases] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [showForm, setShowForm] = useState(false);
  const [showInvoice, setShowInvoice] = useState(false);
  const [selectedPurchase, setSelectedPurchase] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [stats, setStats] = useState({});

  useEffect(() => {
    loadPurchases();
    loadStats();
  }, []);

  useEffect(() => {
    filterPurchases();
  }, [purchases, searchTerm]);

  const loadPurchases = () => {
    const purchasesData = purchasesAPI.getAllPurchases();
    setPurchases(purchasesData);
  };

  const loadStats = () => {
    const purchasesStats = purchasesAPI.getPurchasesStats();
    setStats(purchasesStats);
  };

  const filterPurchases = () => {
    if (!searchTerm) {
      setFilteredPurchases(purchases);
    } else {
      const filtered = purchasesAPI.searchPurchases(searchTerm);
      setFilteredPurchases(filtered);
    }
  };

  const handleAddPurchase = () => {
    setSelectedPurchase(null);
    setIsEditing(false);
    setShowForm(true);
  };

  const handleEditPurchase = (purchase) => {
    setSelectedPurchase(purchase);
    setIsEditing(true);
    setShowForm(true);
  };

  const handleViewInvoice = (purchase) => {
    setSelectedPurchase(purchase);
    setShowInvoice(true);
  };

  const handleDeletePurchase = (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
      purchasesAPI.deletePurchase(id);
      loadPurchases();
      loadStats();
      toast.success('تم حذف الفاتورة بنجاح');
    }
  };

  const handleSavePurchase = (purchaseData) => {
    try {
      if (isEditing) {
        purchasesAPI.updatePurchase(selectedPurchase.id, purchaseData);
        toast.success('تم تحديث الفاتورة بنجاح');
      } else {
        purchasesAPI.addPurchase(purchaseData);
        toast.success('تم إضافة الفاتورة بنجاح');
      }
      
      loadPurchases();
      loadStats();
      setShowForm(false);
    } catch (error) {
      toast.error('حدث خطأ أثناء حفظ الفاتورة');
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'مكتملة':
        return 'bg-green-100 text-green-800';
      case 'معلقة':
        return 'bg-yellow-100 text-yellow-800';
      case 'ملغية':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* العنوان والإحصائيات */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة المشتريات</h1>
            <p className="text-gray-600 mt-2">إدارة فواتير المشتريات والموردين</p>
          </div>
          <Button
            onClick={handleAddPurchase}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            <PlusIcon className="w-4 h-4 ml-2" />
            فاتورة شراء جديدة
          </Button>
        </div>

        {/* بطاقات الإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">إجمالي المشتريات</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(stats.totalPurchases || 0)}
                  </p>
                </div>
                <div className="p-3 bg-green-100 rounded-full">
                  <ShoppingCartIcon className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">مشتريات اليوم</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(stats.todayPurchases || 0)}
                  </p>
                </div>
                <div className="p-3 bg-blue-100 rounded-full">
                  <ShoppingCartIcon className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">عدد الفواتير</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalInvoices || 0}</p>
                </div>
                <div className="p-3 bg-purple-100 rounded-full">
                  <ShoppingCartIcon className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">فواتير معلقة</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.pendingPurchases || 0}</p>
                </div>
                <div className="p-3 bg-orange-100 rounded-full">
                  <ShoppingCartIcon className="w-6 h-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </motion.div>

      {/* البحث والجدول */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>فواتير المشتريات</CardTitle>
              <div className="flex items-center space-x-4 space-x-reverse">
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="البحث في الفواتير..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10 w-64"
                  />
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>رقم الفاتورة</TableHead>
                    <TableHead>المورد</TableHead>
                    <TableHead>التاريخ</TableHead>
                    <TableHead>تاريخ الاستحقاق</TableHead>
                    <TableHead>المبلغ الإجمالي</TableHead>
                    <TableHead>الحالة</TableHead>
                    <TableHead>طريقة الدفع</TableHead>
                    <TableHead className="text-center">الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredPurchases.map((purchase) => (
                    <TableRow key={purchase.id}>
                      <TableCell className="font-medium">{purchase.invoiceNumber}</TableCell>
                      <TableCell>{purchase.supplierName}</TableCell>
                      <TableCell>{new Date(purchase.date).toLocaleDateString('ar-SA')}</TableCell>
                      <TableCell>{purchase.dueDate ? new Date(purchase.dueDate).toLocaleDateString('ar-SA') : '-'}</TableCell>
                      <TableCell>{formatCurrency(purchase.total)}</TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(purchase.status)}`}>
                          {purchase.status}
                        </span>
                      </TableCell>
                      <TableCell>{purchase.paymentMethod}</TableCell>
                      <TableCell>
                        <div className="flex items-center justify-center space-x-2 space-x-reverse">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleViewInvoice(purchase)}
                          >
                            <EyeIcon className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditPurchase(purchase)}
                          >
                            <PencilIcon className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeletePurchase(purchase.id)}
                            className="text-red-600 hover:text-red-800"
                          >
                            <TrashIcon className="w-4 h-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* نموذج إضافة/تعديل الفاتورة */}
      {showForm && (
        <PurchaseForm
          purchase={selectedPurchase}
          isEditing={isEditing}
          onSave={handleSavePurchase}
          onClose={() => setShowForm(false)}
        />
      )}

      {/* عرض الفاتورة */}
      {showInvoice && selectedPurchase && (
        <PurchaseInvoice
          purchase={selectedPurchase}
          onClose={() => setShowInvoice(false)}
        />
      )}
    </div>
  );
};

export default Purchases;
