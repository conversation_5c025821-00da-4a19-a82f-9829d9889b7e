// API لإدارة العملاء
class CustomersAPI {
  constructor() {
    this.storageKey = 'customers_data';
    this.initializeData();
  }

  initializeData() {
    const existingData = localStorage.getItem(this.storageKey);
    if (!existingData) {
      const initialData = {
        customers: [
          {
            id: 1,
            code: 'CUST-001',
            name: 'أحمد محمد علي',
            type: 'فرد',
            phone: '0501234567',
            email: '<EMAIL>',
            address: 'الرياض، حي النخيل، شارع الملك فهد',
            city: 'الرياض',
            country: 'السعودية',
            taxNumber: '*********',
            creditLimit: 50000,
            currentBalance: 8712.5,
            status: 'نشط',
            customerSince: '2023-06-15',
            lastPurchase: '2024-01-15',
            totalPurchases: 25000,
            notes: 'عميل مميز - يحصل على خصم 5%',
            contactPerson: 'أحمد محمد',
            paymentTerms: 'نقدي',
            discount: 5
          },
          {
            id: 2,
            code: 'CUST-002',
            name: 'فاطمة أحمد',
            type: 'فرد',
            phone: '0507654321',
            email: '<EMAIL>',
            address: 'جدة، حي الصفا، شارع التحلية',
            city: 'جدة',
            country: 'السعودية',
            taxNumber: '*********',
            creditLimit: 30000,
            currentBalance: 920,
            status: 'نشط',
            customerSince: '2023-08-20',
            lastPurchase: '2024-01-16',
            totalPurchases: 15000,
            notes: 'عميلة منتظمة',
            contactPerson: 'فاطمة أحمد',
            paymentTerms: 'آجل 30 يوم',
            discount: 0
          },
          {
            id: 3,
            code: 'CUST-003',
            name: 'شركة التقنية الحديثة',
            type: 'شركة',
            phone: '0112345678',
            email: '<EMAIL>',
            address: 'الدمام، حي الشاطئ، مجمع الأعمال',
            city: 'الدمام',
            country: 'السعودية',
            taxNumber: '*********',
            creditLimit: 100000,
            currentBalance: 0,
            status: 'نشط',
            customerSince: '2023-03-10',
            lastPurchase: '2023-12-28',
            totalPurchases: 85000,
            notes: 'عميل مؤسسي - طلبات كبيرة',
            contactPerson: 'خالد السعد',
            paymentTerms: 'آجل 45 يوم',
            discount: 10
          }
        ],
        lastId: 3
      };
      localStorage.setItem(this.storageKey, JSON.stringify(initialData));
    }
  }

  getData() {
    return JSON.parse(localStorage.getItem(this.storageKey));
  }

  saveData(data) {
    localStorage.setItem(this.storageKey, JSON.stringify(data));
  }

  // الحصول على جميع العملاء
  getAllCustomers() {
    return this.getData().customers;
  }

  // إضافة عميل جديد
  addCustomer(customerData) {
    const data = this.getData();
    const newCustomer = {
      id: data.lastId + 1,
      code: `CUST-${String(data.lastId + 1).padStart(3, '0')}`,
      status: 'نشط',
      customerSince: new Date().toISOString().split('T')[0],
      currentBalance: 0,
      totalPurchases: 0,
      ...customerData
    };
    
    data.customers.push(newCustomer);
    data.lastId += 1;
    this.saveData(data);
    return newCustomer;
  }

  // تحديث عميل
  updateCustomer(id, customerData) {
    const data = this.getData();
    const index = data.customers.findIndex(customer => customer.id === id);
    if (index !== -1) {
      data.customers[index] = { ...data.customers[index], ...customerData };
      this.saveData(data);
      return data.customers[index];
    }
    return null;
  }

  // حذف عميل
  deleteCustomer(id) {
    const data = this.getData();
    data.customers = data.customers.filter(customer => customer.id !== id);
    this.saveData(data);
    return true;
  }

  // الحصول على عميل بالمعرف
  getCustomerById(id) {
    const data = this.getData();
    return data.customers.find(customer => customer.id === id);
  }

  // تحديث رصيد العميل
  updateCustomerBalance(customerId, amount) {
    const data = this.getData();
    const customer = data.customers.find(c => c.id === customerId);
    if (customer) {
      customer.currentBalance += amount;
      customer.totalPurchases += Math.max(0, amount);
      customer.lastPurchase = new Date().toISOString().split('T')[0];
      this.saveData(data);
      return customer;
    }
    return null;
  }

  // إحصائيات العملاء
  getCustomersStats() {
    const customers = this.getAllCustomers();
    
    return {
      totalCustomers: customers.length,
      activeCustomers: customers.filter(customer => customer.status === 'نشط').length,
      individualCustomers: customers.filter(customer => customer.type === 'فرد').length,
      corporateCustomers: customers.filter(customer => customer.type === 'شركة').length,
      totalBalance: customers.reduce((sum, customer) => sum + customer.currentBalance, 0),
      totalSales: customers.reduce((sum, customer) => sum + customer.totalPurchases, 0),
      averagePurchase: customers.length > 0 ? customers.reduce((sum, customer) => sum + customer.totalPurchases, 0) / customers.length : 0
    };
  }

  // العملاء المدينون
  getDebtorCustomers() {
    const customers = this.getAllCustomers();
    return customers.filter(customer => customer.currentBalance > 0);
  }

  // أفضل العملاء
  getTopCustomers(limit = 10) {
    const customers = this.getAllCustomers();
    return customers
      .sort((a, b) => b.totalPurchases - a.totalPurchases)
      .slice(0, limit);
  }

  // البحث في العملاء
  searchCustomers(query) {
    const customers = this.getAllCustomers();
    return customers.filter(customer => 
      customer.name.toLowerCase().includes(query.toLowerCase()) ||
      customer.code.toLowerCase().includes(query.toLowerCase()) ||
      customer.phone.includes(query) ||
      customer.email.toLowerCase().includes(query.toLowerCase())
    );
  }
}

export default new CustomersAPI();
