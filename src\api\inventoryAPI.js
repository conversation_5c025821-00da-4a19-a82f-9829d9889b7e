// API لإدارة المخزون
class InventoryAPI {
  constructor() {
    this.storageKey = 'inventory_data';
    this.initializeData();
  }

  initializeData() {
    const existingData = localStorage.getItem(this.storageKey);
    if (!existingData) {
      const initialData = {
        products: [
          {
            id: 1,
            code: 'PROD-001',
            name: 'لابتوب Dell Inspiron 15',
            category: 'أجهزة كمبيوتر',
            brand: 'Dell',
            description: 'لابتوب Dell Inspiron 15 - معالج Intel Core i5',
            costPrice: 3000,
            sellingPrice: 3500,
            currentStock: 25,
            minStock: 5,
            maxStock: 100,
            unit: 'قطعة',
            location: 'مخزن A - رف 1',
            supplier: 'شركة التقنية المتقدمة',
            barcode: '1234567890123',
            status: 'نشط',
            lastUpdated: '2024-01-15',
            movements: [
              { date: '2024-01-10', type: 'شراء', quantity: 10, reference: 'PUR-2024-001' },
              { date: '2024-01-15', type: 'بيع', quantity: -2, reference: 'INV-2024-001' }
            ]
          },
          {
            id: 2,
            code: 'PROD-002',
            name: 'ماوس لاسلكي Logitech',
            category: 'ملحقات',
            brand: 'Logitech',
            description: 'ماوس لاسلكي بتقنية البلوتوث',
            costPrice: 100,
            sellingPrice: 150,
            currentStock: 45,
            minStock: 10,
            maxStock: 200,
            unit: 'قطعة',
            location: 'مخزن B - رف 2',
            supplier: 'شركة التقنية المتقدمة',
            barcode: '1234567890124',
            status: 'نشط',
            lastUpdated: '2024-01-15',
            movements: [
              { date: '2024-01-10', type: 'شراء', quantity: 50, reference: 'PUR-2024-001' },
              { date: '2024-01-15', type: 'بيع', quantity: -5, reference: 'INV-2024-001' }
            ]
          },
          {
            id: 3,
            code: 'PROD-003',
            name: 'طابعة HP LaserJet',
            category: 'طابعات',
            brand: 'HP',
            description: 'طابعة ليزر أحادية اللون',
            costPrice: 600,
            sellingPrice: 800,
            currentStock: 4,
            minStock: 2,
            maxStock: 20,
            unit: 'قطعة',
            location: 'مخزن A - رف 3',
            supplier: 'مؤسسة الإلكترونيات',
            barcode: '1234567890125',
            status: 'نشط',
            lastUpdated: '2024-01-16',
            movements: [
              { date: '2024-01-12', type: 'شراء', quantity: 5, reference: 'PUR-2024-002' },
              { date: '2024-01-16', type: 'بيع', quantity: -1, reference: 'INV-2024-002' }
            ]
          }
        ],
        categories: ['أجهزة كمبيوتر', 'ملحقات', 'طابعات', 'شاشات', 'أجهزة شبكات'],
        lastId: 3
      };
      localStorage.setItem(this.storageKey, JSON.stringify(initialData));
    }
  }

  getData() {
    return JSON.parse(localStorage.getItem(this.storageKey));
  }

  saveData(data) {
    localStorage.setItem(this.storageKey, JSON.stringify(data));
  }

  // الحصول على جميع المنتجات
  getAllProducts() {
    return this.getData().products;
  }

  // إضافة منتج جديد
  addProduct(productData) {
    const data = this.getData();
    const newProduct = {
      id: data.lastId + 1,
      code: `PROD-${String(data.lastId + 1).padStart(3, '0')}`,
      status: 'نشط',
      lastUpdated: new Date().toISOString().split('T')[0],
      movements: [],
      ...productData
    };
    
    data.products.push(newProduct);
    data.lastId += 1;
    this.saveData(data);
    return newProduct;
  }

  // تحديث منتج
  updateProduct(id, productData) {
    const data = this.getData();
    const index = data.products.findIndex(product => product.id === id);
    if (index !== -1) {
      data.products[index] = { 
        ...data.products[index], 
        ...productData,
        lastUpdated: new Date().toISOString().split('T')[0]
      };
      this.saveData(data);
      return data.products[index];
    }
    return null;
  }

  // حذف منتج
  deleteProduct(id) {
    const data = this.getData();
    data.products = data.products.filter(product => product.id !== id);
    this.saveData(data);
    return true;
  }

  // الحصول على منتج بالمعرف
  getProductById(id) {
    const data = this.getData();
    return data.products.find(product => product.id === id);
  }

  // تحديث المخزون
  updateStock(productId, quantity, type, reference) {
    const data = this.getData();
    const product = data.products.find(p => p.id === productId);
    if (product) {
      product.currentStock += quantity;
      product.movements.push({
        date: new Date().toISOString().split('T')[0],
        type,
        quantity,
        reference
      });
      product.lastUpdated = new Date().toISOString().split('T')[0];
      this.saveData(data);
      return product;
    }
    return null;
  }

  // إحصائيات المخزون
  getInventoryStats() {
    const products = this.getAllProducts();
    
    return {
      totalProducts: products.length,
      totalValue: products.reduce((sum, product) => sum + (product.currentStock * product.costPrice), 0),
      lowStockProducts: products.filter(product => product.currentStock <= product.minStock).length,
      outOfStockProducts: products.filter(product => product.currentStock === 0).length,
      activeProducts: products.filter(product => product.status === 'نشط').length,
      totalCategories: [...new Set(products.map(product => product.category))].length
    };
  }

  // المنتجات منخفضة المخزون
  getLowStockProducts() {
    const products = this.getAllProducts();
    return products.filter(product => product.currentStock <= product.minStock);
  }

  // المنتجات نفدت من المخزون
  getOutOfStockProducts() {
    const products = this.getAllProducts();
    return products.filter(product => product.currentStock === 0);
  }

  // البحث في المنتجات
  searchProducts(query) {
    const products = this.getAllProducts();
    return products.filter(product => 
      product.name.toLowerCase().includes(query.toLowerCase()) ||
      product.code.toLowerCase().includes(query.toLowerCase()) ||
      product.category.toLowerCase().includes(query.toLowerCase()) ||
      product.brand.toLowerCase().includes(query.toLowerCase())
    );
  }

  // الحصول على الفئات
  getCategories() {
    return this.getData().categories;
  }

  // إضافة فئة جديدة
  addCategory(category) {
    const data = this.getData();
    if (!data.categories.includes(category)) {
      data.categories.push(category);
      this.saveData(data);
    }
    return data.categories;
  }
}

export default new InventoryAPI();
