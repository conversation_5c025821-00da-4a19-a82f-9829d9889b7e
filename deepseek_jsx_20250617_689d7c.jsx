import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from '@/components/ui/dialog';
import { PencilIcon, TrashIcon, PlusIcon } from '@heroicons/react/24/outline';
import { Loader2 } from 'lucide-react';
import toast, { Toaster } from 'react-hot-toast';

const CompanyManagement = () => {
  // تخزين البيانات في localStorage لمحاكاة قاعدة البيانات
  const getLocalStorageData = () => {
    const savedData = localStorage.getItem('companyManagementData');
    return savedData ? JSON.parse(savedData) : {
      companies: [],
      lastId: 0
    };
  };

  const saveLocalStorageData = (data) => {
    localStorage.setItem('companyManagementData', JSON.stringify(data));
  };

  // حالة النظام
  const [companies, setCompanies] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentCompany, setCurrentCompany] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    tax_id: '',
    address: '',
    contact_person: '',
    email: '',
    phone: ''
  });
  const [formErrors, setFormErrors] = useState({});
  const [submitting, setSubmitting] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isAdmin] = useState(true); // يمكنك تغيير هذه القيمة لتجربة صلاحيات مختلفة

  // جلب البيانات الأولية
  useEffect(() => {
    const fetchData = () => {
      try {
        const data = getLocalStorageData();
        setCompanies(data.companies);
        
        // إضافة بيانات تجريبية إذا لم يكن هناك بيانات
        if (data.companies.length === 0) {
          const demoData = {
            companies: [
              {
                company_id: 1,
                name: 'شركة التقنية المتقدمة',
                tax_id: '1234567890',
                address: 'الرياض، شارع الملك فهد',
                contact_person: 'أحمد محمد',
                email: '<EMAIL>',
                phone: '+966112233445'
              },
              {
                company_id: 2,
                name: 'شركة التطوير الحديثة',
                tax_id: '0987654321',
                address: 'جدة، حي الصفا',
                contact_person: 'سارة عبدالله',
                email: '<EMAIL>',
                phone: '+966556677889'
              },
              {
                company_id: 3,
                name: 'شركة الحلول الذكية',
                tax_id: '4567890123',
                address: 'الدمام، طريق الملك عبدالعزيز',
                contact_person: 'خالد سعيد',
                email: '<EMAIL>',
                phone: '+966990011223'
              }
            ],
            lastId: 3
          };
          saveLocalStorageData(demoData);
          setCompanies(demoData.companies);
        }
      } catch (err) {
        toast.error('حدث خطأ في تحميل البيانات');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, []);

  // عرض رسالة
  const showToast = (message, type = 'success') => {
    if (type === 'success') {
      toast.success(message);
    } else {
      toast.error(message);
    }
  };

  // التحقق من صحة النموذج
  const validateForm = () => {
    const errors = {};
    if (!formData.name.trim()) errors.name = 'اسم الشركة مطلوب';
    if (!formData.tax_id.trim()) errors.tax_id = 'الرقم الضريبي مطلوب';
    if (formData.email && !/^\S+@\S+\.\S+$/.test(formData.email)) errors.email = 'بريد إلكتروني غير صالح';
    if (formData.phone && !/^\+?[0-9\s-]{7,}$/.test(formData.phone)) errors.phone = 'رقم هاتف غير صالح';
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (e) => {
    const { id, value } = e.target;
    setFormData(prev => ({ ...prev, [id]: value }));
    
    // مسح الخطأ عند الكتابة
    if (formErrors[id]) {
      setFormErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[id];
        return newErrors;
      });
    }
  };

  // حفظ الشركة
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) return;
    
    setSubmitting(true);
    
    try {
      const data = getLocalStorageData();
      
      if (currentCompany) {
        // تحديث الشركة
        const updatedCompanies = data.companies.map(c => 
          c.company_id === currentCompany.company_id ? { ...formData, company_id: currentCompany.company_id } : c
        );
        
        saveLocalStorageData({
          companies: updatedCompanies,
          lastId: data.lastId
        });
        
        setCompanies(updatedCompanies);
        showToast('تم تحديث بيانات الشركة بنجاح');
      } else {
        // إضافة شركة جديدة
        const newCompany = { 
          ...formData, 
          company_id: data.lastId + 1 
        };
        
        saveLocalStorageData({
          companies: [...data.companies, newCompany],
          lastId: data.lastId + 1
        });
        
        setCompanies([...data.companies, newCompany]);
        showToast('تم إضافة الشركة بنجاح');
      }
      
      closeModal();
    } catch (err) {
      showToast('فشل في حفظ بيانات الشركة', 'error');
    } finally {
      setSubmitting(false);
    }
  };

  // حذف الشركة
  const handleDelete = (companyId) => {
    if (!window.confirm('هل أنت متأكد أنك تريد حذف هذه الشركة؟')) return;
    
    try {
      const data = getLocalStorageData();
      const updatedCompanies = data.companies.filter(c => c.company_id !== companyId);
      
      saveLocalStorageData({
        companies: updatedCompanies,
        lastId: data.lastId
      });
      
      setCompanies(updatedCompanies);
      showToast('تم حذف الشركة بنجاح');
    } catch (err) {
      showToast('فشل في حذف الشركة', 'error');
    }
  };

  // فتح نموذج التعديل
  const openEditModal = (company) => {
    setCurrentCompany(company);
    setFormData({
      name: company.name,
      tax_id: company.tax_id,
      address: company.address,
      contact_person: company.contact_person,
      email: company.email,
      phone: company.phone
    });
    setIsModalOpen(true);
  };

  // فتح نموذج الإضافة
  const openAddModal = () => {
    setCurrentCompany(null);
    setFormData({
      name: '',
      tax_id: '',
      address: '',
      contact_person: '',
      email: '',
      phone: ''
    });
    setFormErrors({});
    setIsModalOpen(true);
  };

  // إغلاق النموذج
  const closeModal = () => {
    setIsModalOpen(false);
    setCurrentCompany(null);
    setFormErrors({});
  };

  // فلترة الشركات حسب البحث
  const filteredCompanies = companies.filter(company => 
    company.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    company.tax_id.includes(searchTerm) ||
    (company.contact_person && company.contact_person.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (company.email && company.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (company.phone && company.phone.includes(searchTerm))
  );

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto mb-4" />
          <p className="text-lg text-gray-600">جاري تحميل بيانات الشركات...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 md:p-6 max-w-6xl">
      <Toaster position="top-right" />
      
      <Card className="border rounded-xl shadow-lg">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-t-xl">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
              <CardTitle className="text-2xl md:text-3xl font-bold text-gray-800">إدارة الشركات</CardTitle>
              <CardDescription className="mt-2 text-gray-600">
                قم بإدارة بيانات الشركات المسجلة في النظام
              </CardDescription>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-3 w-full md:w-auto">
              <div className="relative w-full">
                <Input 
                  type="text" 
                  placeholder="ابحث عن شركة..." 
                  className="pl-10 pr-4 py-2 w-full"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                <svg 
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24" 
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth="2" 
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  ></path>
                </svg>
              </div>
              
              {isAdmin && (
                <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
                  <DialogTrigger asChild>
                    <Button 
                      onClick={openAddModal}
                      className="bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700 hover:to-indigo-800 text-white"
                    >
                      <PlusIcon className="h-5 w-5 ml-2" /> إضافة شركة جديدة
                    </Button>
                  </DialogTrigger>
                </Dialog>
              )}
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="p-0">
          {filteredCompanies.length === 0 ? (
            <div className="text-center py-16 border rounded-lg m-6">
              <div className="mx-auto bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mb-4">
                <svg className="w-8 h-8 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
              </div>
              <p className="text-gray-500 text-lg">لا توجد شركات مسجلة</p>
              {isAdmin && (
                <Button 
                  onClick={openAddModal} 
                  className="mt-4 bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700 hover:to-indigo-800"
                >
                  <PlusIcon className="h-5 w-5 ml-2" /> إضافة شركة جديدة
                </Button>
              )}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table className="min-w-full">
                <TableHeader className="bg-gray-50">
                  <TableRow>
                    <TableHead className="w-1/4">اسم الشركة</TableHead>
                    <TableHead>الرقم الضريبي</TableHead>
                    <TableHead>شخص الاتصال</TableHead>
                    <TableHead>البريد الإلكتروني</TableHead>
                    <TableHead>الهاتف</TableHead>
                    {isAdmin && <TableHead className="w-32 text-center">الإجراءات</TableHead>}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredCompanies.map((company) => (
                    <TableRow key={company.company_id} className="hover:bg-gray-50 transition-colors">
                      <TableCell className="font-medium text-gray-900">{company.name}</TableCell>
                      <TableCell className="text-gray-700">{company.tax_id}</TableCell>
                      <TableCell className="text-gray-700">{company.contact_person}</TableCell>
                      <TableCell className="text-gray-700">
                        <a href={`mailto:${company.email}`} className="text-blue-600 hover:underline">
                          {company.email}
                        </a>
                      </TableCell>
                      <TableCell className="text-gray-700">
                        <a href={`tel:${company.phone}`} className="text-blue-600 hover:underline">
                          {company.phone}
                        </a>
                      </TableCell>
                      {isAdmin && (
                        <TableCell className="flex justify-center space-x-2 py-3">
                          <Button 
                            variant="outline" 
                            size="icon"
                            onClick={() => openEditModal(company)}
                            className="text-blue-600 border-blue-200 hover:bg-blue-50"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="outline" 
                            size="icon"
                            onClick={() => handleDelete(company.company_id)}
                            className="text-red-600 border-red-200 hover:bg-red-50"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      )}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* نموذج إضافة/تعديل الشركة */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="sm:max-w-xl rounded-lg shadow-xl">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold text-gray-800">
              {currentCompany ? 'تعديل شركة' : 'إضافة شركة جديدة'}
            </DialogTitle>
            <DialogDescription className="text-gray-600">
              {currentCompany ? 'قم بتعديل بيانات الشركة' : 'قم بإضافة شركة جديدة إلى النظام'}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-4 py-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name" className="block text-gray-700">اسم الشركة *</Label>
                <Input 
                  id="name" 
                  value={formData.name} 
                  onChange={handleInputChange} 
                  className={`${formErrors.name ? 'border-red-500' : ''}`}
                />
                {formErrors.name && <p className="text-red-500 text-sm mt-1">{formErrors.name}</p>}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="tax_id" className="block text-gray-700">الرقم الضريبي *</Label>
                <Input 
                  id="tax_id" 
                  value={formData.tax_id} 
                  onChange={handleInputChange} 
                  className={`${formErrors.tax_id ? 'border-red-500' : ''}`}
                />
                {formErrors.tax_id && <p className="text-red-500 text-sm mt-1">{formErrors.tax_id}</p>}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="address" className="block text-gray-700">العنوان</Label>
                <Input 
                  id="address" 
                  value={formData.address} 
                  onChange={handleInputChange} 
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="contact_person" className="block text-gray-700">شخص الاتصال</Label>
                <Input 
                  id="contact_person" 
                  value={formData.contact_person} 
                  onChange={handleInputChange} 
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="email" className="block text-gray-700">البريد الإلكتروني</Label>
                <Input 
                  id="email" 
                  type="email"
                  value={formData.email} 
                  onChange={handleInputChange} 
                  className={`${formErrors.email ? 'border-red-500' : ''}`}
                />
                {formErrors.email && <p className="text-red-500 text-sm mt-1">{formErrors.email}</p>}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="phone" className="block text-gray-700">رقم الهاتف</Label>
                <Input 
                  id="phone" 
                  value={formData.phone} 
                  onChange={handleInputChange} 
                  className={`${formErrors.phone ? 'border-red-500' : ''}`}
                />
                {formErrors.phone && <p className="text-red-500 text-sm mt-1">{formErrors.phone}</p>}
              </div>
            </div>
            
            <DialogFooter className="pt-4">
              <Button 
                type="button" 
                variant="outline" 
                onClick={closeModal}
                className="text-gray-700 hover:bg-gray-100"
              >
                إلغاء
              </Button>
              <Button 
                type="submit" 
                disabled={submitting}
                className="bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700 hover:to-indigo-800"
              >
                {submitting ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    جاري الحفظ...
                  </>
                ) : currentCompany ? 'حفظ التعديلات' : 'إضافة شركة'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CompanyManagement;