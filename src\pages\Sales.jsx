import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  DocumentArrowDownIcon
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../components/ui/table';
import salesAPI from '../api/salesAPI';
import customersAPI from '../api/customersAPI';
import inventoryAPI from '../api/inventoryAPI';
import SalesForm from '../components/Sales/SalesForm';
import SalesInvoice from '../components/Sales/SalesInvoice';
import toast from 'react-hot-toast';

const Sales = () => {
  const [sales, setSales] = useState([]);
  const [filteredSales, setFilteredSales] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [showForm, setShowForm] = useState(false);
  const [showInvoice, setShowInvoice] = useState(false);
  const [selectedSale, setSelectedSale] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [stats, setStats] = useState({});

  useEffect(() => {
    loadSales();
    loadStats();
  }, []);

  useEffect(() => {
    filterSales();
  }, [sales, searchTerm]);

  const loadSales = () => {
    const salesData = salesAPI.getAllSales();
    setSales(salesData);
  };

  const loadStats = () => {
    const salesStats = salesAPI.getSalesStats();
    setStats(salesStats);
  };

  const filterSales = () => {
    if (!searchTerm) {
      setFilteredSales(sales);
    } else {
      const filtered = salesAPI.searchSales(searchTerm);
      setFilteredSales(filtered);
    }
  };

  const handleAddSale = () => {
    setSelectedSale(null);
    setIsEditing(false);
    setShowForm(true);
  };

  const handleEditSale = (sale) => {
    setSelectedSale(sale);
    setIsEditing(true);
    setShowForm(true);
  };

  const handleViewInvoice = (sale) => {
    setSelectedSale(sale);
    setShowInvoice(true);
  };

  const handleDeleteSale = (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
      salesAPI.deleteSale(id);
      loadSales();
      loadStats();
      toast.success('تم حذف الفاتورة بنجاح');
    }
  };

  const handleSaveSale = (saleData) => {
    try {
      if (isEditing) {
        salesAPI.updateSale(selectedSale.id, saleData);
        toast.success('تم تحديث الفاتورة بنجاح');
      } else {
        salesAPI.addSale(saleData);
        toast.success('تم إضافة الفاتورة بنجاح');
      }
      
      loadSales();
      loadStats();
      setShowForm(false);
    } catch (error) {
      toast.error('حدث خطأ أثناء حفظ الفاتورة');
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'مكتملة':
        return 'bg-green-100 text-green-800';
      case 'معلقة':
        return 'bg-yellow-100 text-yellow-800';
      case 'ملغية':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* العنوان والإحصائيات */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة المبيعات</h1>
            <p className="text-gray-600 mt-2">إدارة فواتير المبيعات والعملاء</p>
          </div>
          <Button
            onClick={handleAddSale}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            <PlusIcon className="w-4 h-4 ml-2" />
            فاتورة جديدة
          </Button>
        </div>

        {/* بطاقات الإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">إجمالي المبيعات</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(stats.totalSales || 0)}
                  </p>
                </div>
                <div className="p-3 bg-blue-100 rounded-full">
                  <DocumentArrowDownIcon className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">مبيعات اليوم</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(stats.todaySales || 0)}
                  </p>
                </div>
                <div className="p-3 bg-green-100 rounded-full">
                  <DocumentArrowDownIcon className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">عدد الفواتير</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalInvoices || 0}</p>
                </div>
                <div className="p-3 bg-purple-100 rounded-full">
                  <DocumentArrowDownIcon className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">فواتير معلقة</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.pendingSales || 0}</p>
                </div>
                <div className="p-3 bg-orange-100 rounded-full">
                  <DocumentArrowDownIcon className="w-6 h-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </motion.div>

      {/* البحث والجدول */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>فواتير المبيعات</CardTitle>
              <div className="flex items-center space-x-4 space-x-reverse">
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="البحث في الفواتير..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10 w-64"
                  />
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>رقم الفاتورة</TableHead>
                    <TableHead>العميل</TableHead>
                    <TableHead>التاريخ</TableHead>
                    <TableHead>المبلغ الإجمالي</TableHead>
                    <TableHead>الحالة</TableHead>
                    <TableHead>طريقة الدفع</TableHead>
                    <TableHead className="text-center">الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredSales.map((sale) => (
                    <TableRow key={sale.id}>
                      <TableCell className="font-medium">{sale.invoiceNumber}</TableCell>
                      <TableCell>{sale.customerName}</TableCell>
                      <TableCell>{new Date(sale.date).toLocaleDateString('ar-SA')}</TableCell>
                      <TableCell>{formatCurrency(sale.total)}</TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(sale.status)}`}>
                          {sale.status}
                        </span>
                      </TableCell>
                      <TableCell>{sale.paymentMethod}</TableCell>
                      <TableCell>
                        <div className="flex items-center justify-center space-x-2 space-x-reverse">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleViewInvoice(sale)}
                          >
                            <EyeIcon className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditSale(sale)}
                          >
                            <PencilIcon className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteSale(sale.id)}
                            className="text-red-600 hover:text-red-800"
                          >
                            <TrashIcon className="w-4 h-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* نموذج إضافة/تعديل الفاتورة */}
      {showForm && (
        <SalesForm
          sale={selectedSale}
          isEditing={isEditing}
          onSave={handleSaveSale}
          onClose={() => setShowForm(false)}
        />
      )}

      {/* عرض الفاتورة */}
      {showInvoice && selectedSale && (
        <SalesInvoice
          sale={selectedSale}
          onClose={() => setShowInvoice(false)}
        />
      )}
    </div>
  );
};

export default Sales;
