{"ast": null, "code": "import { formatDecimalParts } from \"./formatDecimal.js\";\nexport default function (x, p) {\n  var d = formatDecimalParts(x, p);\n  if (!d) return x + \"\";\n  var coefficient = d[0],\n    exponent = d[1];\n  return exponent < 0 ? \"0.\" + new Array(-exponent).join(\"0\") + coefficient : coefficient.length > exponent + 1 ? coefficient.slice(0, exponent + 1) + \".\" + coefficient.slice(exponent + 1) : coefficient + new Array(exponent - coefficient.length + 2).join(\"0\");\n}", "map": {"version": 3, "names": ["formatDecimalParts", "x", "p", "d", "coefficient", "exponent", "Array", "join", "length", "slice"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/node_modules/d3-format/src/formatRounded.js"], "sourcesContent": ["import {formatDecimalParts} from \"./formatDecimal.js\";\n\nexport default function(x, p) {\n  var d = formatDecimalParts(x, p);\n  if (!d) return x + \"\";\n  var coefficient = d[0],\n      exponent = d[1];\n  return exponent < 0 ? \"0.\" + new Array(-exponent).join(\"0\") + coefficient\n      : coefficient.length > exponent + 1 ? coefficient.slice(0, exponent + 1) + \".\" + coefficient.slice(exponent + 1)\n      : coefficient + new Array(exponent - coefficient.length + 2).join(\"0\");\n}\n"], "mappings": "AAAA,SAAQA,kBAAkB,QAAO,oBAAoB;AAErD,eAAe,UAASC,CAAC,EAAEC,CAAC,EAAE;EAC5B,IAAIC,CAAC,GAAGH,kBAAkB,CAACC,CAAC,EAAEC,CAAC,CAAC;EAChC,IAAI,CAACC,CAAC,EAAE,OAAOF,CAAC,GAAG,EAAE;EACrB,IAAIG,WAAW,GAAGD,CAAC,CAAC,CAAC,CAAC;IAClBE,QAAQ,GAAGF,CAAC,CAAC,CAAC,CAAC;EACnB,OAAOE,QAAQ,GAAG,CAAC,GAAG,IAAI,GAAG,IAAIC,KAAK,CAAC,CAACD,QAAQ,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC,GAAGH,WAAW,GACnEA,WAAW,CAACI,MAAM,GAAGH,QAAQ,GAAG,CAAC,GAAGD,WAAW,CAACK,KAAK,CAAC,CAAC,EAAEJ,QAAQ,GAAG,CAAC,CAAC,GAAG,GAAG,GAAGD,WAAW,CAACK,KAAK,CAACJ,QAAQ,GAAG,CAAC,CAAC,GAC9GD,WAAW,GAAG,IAAIE,KAAK,CAACD,QAAQ,GAAGD,WAAW,CAACI,MAAM,GAAG,CAAC,CAAC,CAACD,IAAI,CAAC,GAAG,CAAC;AAC5E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}