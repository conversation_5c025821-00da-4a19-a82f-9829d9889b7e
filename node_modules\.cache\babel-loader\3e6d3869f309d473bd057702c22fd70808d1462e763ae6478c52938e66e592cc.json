{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u0633\\u0637\\u062D \\u0627\\u0644\\u0645\\u0643\\u062A\\u0628\\\\333\\\\src\\\\components\\\\Inventory\\\\ProductDetails.jsx\";\nimport React from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { XMarkIcon } from '@heroicons/react/24/outline';\nimport { Button } from '../ui/button';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductDetails = ({\n  product,\n  onClose\n}) => {\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('ar-SA', {\n      style: 'currency',\n      currency: 'SAR'\n    }).format(amount);\n  };\n  const getStockStatus = () => {\n    if (product.currentStock === 0) {\n      return {\n        text: 'نفد المخزون',\n        color: 'text-red-600 bg-red-100'\n      };\n    } else if (product.currentStock <= product.minStock) {\n      return {\n        text: 'مخزون منخفض',\n        color: 'text-yellow-600 bg-yellow-100'\n      };\n    } else {\n      return {\n        text: 'متوفر',\n        color: 'text-green-600 bg-green-100'\n      };\n    }\n  };\n  const stockStatus = getStockStatus();\n  return /*#__PURE__*/_jsxDEV(AnimatePresence, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          scale: 0.95\n        },\n        animate: {\n          opacity: 1,\n          scale: 1\n        },\n        exit: {\n          opacity: 0,\n          scale: 0.95\n        },\n        className: \"bg-white rounded-2xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-gray-900\",\n            children: \"\\u062A\\u0641\\u0627\\u0635\\u064A\\u0644 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onClose,\n            className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg\",\n            children: /*#__PURE__*/_jsxDEV(XMarkIcon, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                children: \"\\u0627\\u0644\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0623\\u0633\\u0627\\u0633\\u064A\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-600\",\n                    children: \"\\u0643\\u0648\\u062F \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 54,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-900\",\n                    children: product.code\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 55,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 53,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-600\",\n                    children: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 58,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-900\",\n                    children: product.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 59,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 57,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-600\",\n                    children: \"\\u0627\\u0644\\u0641\\u0626\\u0629:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 62,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-900\",\n                    children: product.category\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 63,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 61,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-600\",\n                    children: \"\\u0627\\u0644\\u0639\\u0644\\u0627\\u0645\\u0629 \\u0627\\u0644\\u062A\\u062C\\u0627\\u0631\\u064A\\u0629:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 66,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-900\",\n                    children: product.brand || '-'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 67,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 65,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-600\",\n                    children: \"\\u0627\\u0644\\u0648\\u062D\\u062F\\u0629:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 70,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-900\",\n                    children: product.unit\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 71,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 69,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 52,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                children: \"\\u062D\\u0627\\u0644\\u0629 \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-600\",\n                    children: \"\\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646 \\u0627\\u0644\\u062D\\u0627\\u0644\\u064A:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 80,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-2xl font-bold text-gray-900\",\n                    children: [product.currentStock, \" \", product.unit]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 81,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 79,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-600\",\n                    children: \"\\u0627\\u0644\\u062D\\u0627\\u0644\\u0629:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 86,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-block px-3 py-1 rounded-full text-sm font-medium ${stockStatus.color}`,\n                    children: stockStatus.text\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 87,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 85,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-600\",\n                    children: \"\\u0627\\u0644\\u062D\\u062F \\u0627\\u0644\\u0623\\u062F\\u0646\\u0649:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 92,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-900\",\n                    children: [product.minStock, \" \", product.unit]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 93,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 91,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-600\",\n                    children: \"\\u0627\\u0644\\u062D\\u062F \\u0627\\u0644\\u0623\\u0642\\u0635\\u0649:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 96,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-900\",\n                    children: [product.maxStock, \" \", product.unit]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 97,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 95,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"\\u0627\\u0644\\u0623\\u0633\\u0639\\u0627\\u0631\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-blue-50 p-4 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-blue-600\",\n                  children: \"\\u0633\\u0639\\u0631 \\u0627\\u0644\\u062A\\u0643\\u0644\\u0641\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 108,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xl font-bold text-blue-900\",\n                  children: formatCurrency(product.costPrice)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 109,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-green-50 p-4 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-green-600\",\n                  children: \"\\u0633\\u0639\\u0631 \\u0627\\u0644\\u0628\\u064A\\u0639\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 112,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xl font-bold text-green-900\",\n                  children: formatCurrency(product.sellingPrice)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 113,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-purple-50 p-4 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-purple-600\",\n                  children: \"\\u0647\\u0627\\u0645\\u0634 \\u0627\\u0644\\u0631\\u0628\\u062D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xl font-bold text-purple-900\",\n                  children: [((product.sellingPrice - product.costPrice) / product.costPrice * 100).toFixed(2), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 117,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gray-50 p-4 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646 (\\u0628\\u0633\\u0639\\u0631 \\u0627\\u0644\\u062A\\u0643\\u0644\\u0641\\u0629)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 129,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xl font-bold text-gray-900\",\n                  children: formatCurrency(product.currentStock * product.costPrice)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gray-50 p-4 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646 (\\u0628\\u0633\\u0639\\u0631 \\u0627\\u0644\\u0628\\u064A\\u0639)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xl font-bold text-gray-900\",\n                  children: formatCurrency(product.currentStock * product.sellingPrice)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0625\\u0636\\u0627\\u0641\\u064A\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"\\u0645\\u0648\\u0642\\u0639 \\u0627\\u0644\\u062A\\u062E\\u0632\\u064A\\u0646:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-900\",\n                  children: product.location || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 149,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"\\u0627\\u0644\\u0645\\u0648\\u0631\\u062F:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-900\",\n                  children: product.supplier || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"\\u0627\\u0644\\u0628\\u0627\\u0631\\u0643\\u0648\\u062F:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-900 font-mono\",\n                  children: product.barcode || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"\\u0622\\u062E\\u0631 \\u062A\\u062D\\u062F\\u064A\\u062B:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-900\",\n                  children: new Date(product.lastUpdated).toLocaleDateString('ar-SA')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this), product.description && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"\\u0627\\u0644\\u0648\\u0635\\u0641\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 bg-gray-50 p-4 rounded-lg\",\n              children: product.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 15\n          }, this), product.movements && product.movements.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"\\u0622\\u062E\\u0631 \\u062D\\u0631\\u0643\\u0627\\u062A \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"overflow-x-auto\",\n              children: /*#__PURE__*/_jsxDEV(\"table\", {\n                className: \"w-full border-collapse border border-gray-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                  children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                    className: \"bg-gray-50\",\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"border border-gray-300 px-4 py-2 text-right\",\n                      children: \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 182,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"border border-gray-300 px-4 py-2 text-center\",\n                      children: \"\\u0627\\u0644\\u0646\\u0648\\u0639\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 183,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"border border-gray-300 px-4 py-2 text-center\",\n                      children: \"\\u0627\\u0644\\u0643\\u0645\\u064A\\u0629\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 184,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"border border-gray-300 px-4 py-2 text-center\",\n                      children: \"\\u0627\\u0644\\u0645\\u0631\\u062C\\u0639\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 185,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 181,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                  children: product.movements.slice(-5).reverse().map((movement, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"border border-gray-300 px-4 py-2\",\n                      children: new Date(movement.date).toLocaleDateString('ar-SA')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 191,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"border border-gray-300 px-4 py-2 text-center\",\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `px-2 py-1 rounded-full text-xs font-medium ${movement.type === 'شراء' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                        children: movement.type\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 195,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 194,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"border border-gray-300 px-4 py-2 text-center\",\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: movement.quantity > 0 ? 'text-green-600' : 'text-red-600',\n                        children: [movement.quantity > 0 ? '+' : '', movement.quantity]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 202,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 201,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"border border-gray-300 px-4 py-2 text-center\",\n                      children: movement.reference\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 206,\n                      columnNumber: 27\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 190,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end pt-6 border-t border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              onClick: onClose,\n              variant: \"outline\",\n              children: \"\\u0625\\u063A\\u0644\\u0627\\u0642\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this);\n};\n_c = ProductDetails;\nexport default ProductDetails;\nvar _c;\n$RefreshReg$(_c, \"ProductDetails\");", "map": {"version": 3, "names": ["React", "motion", "AnimatePresence", "XMarkIcon", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "ProductDetails", "product", "onClose", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "getStockStatus", "currentStock", "text", "color", "minStock", "stockStatus", "children", "className", "div", "initial", "opacity", "scale", "animate", "exit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "code", "name", "category", "brand", "unit", "maxStock", "costPrice", "sellingPrice", "toFixed", "location", "supplier", "barcode", "Date", "lastUpdated", "toLocaleDateString", "description", "movements", "length", "slice", "reverse", "map", "movement", "index", "date", "type", "quantity", "reference", "variant", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/src/components/Inventory/ProductDetails.jsx"], "sourcesContent": ["import React from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { XMarkIcon } from '@heroicons/react/24/outline';\nimport { Button } from '../ui/button';\n\nconst ProductDetails = ({ product, onClose }) => {\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('ar-SA', {\n      style: 'currency',\n      currency: 'SAR'\n    }).format(amount);\n  };\n\n  const getStockStatus = () => {\n    if (product.currentStock === 0) {\n      return { text: 'نفد المخزون', color: 'text-red-600 bg-red-100' };\n    } else if (product.currentStock <= product.minStock) {\n      return { text: 'مخزون منخفض', color: 'text-yellow-600 bg-yellow-100' };\n    } else {\n      return { text: 'متوفر', color: 'text-green-600 bg-green-100' };\n    }\n  };\n\n  const stockStatus = getStockStatus();\n\n  return (\n    <AnimatePresence>\n      <div className=\"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50\">\n        <motion.div\n          initial={{ opacity: 0, scale: 0.95 }}\n          animate={{ opacity: 1, scale: 1 }}\n          exit={{ opacity: 0, scale: 0.95 }}\n          className=\"bg-white rounded-2xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\"\n        >\n          {/* الرأس */}\n          <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n            <h2 className=\"text-xl font-semibold text-gray-900\">تفاصيل المنتج</h2>\n            <button\n              onClick={onClose}\n              className=\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg\"\n            >\n              <XMarkIcon className=\"w-5 h-5\" />\n            </button>\n          </div>\n\n          {/* المحتوى */}\n          <div className=\"p-6 space-y-6\">\n            {/* المعلومات الأساسية */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">المعلومات الأساسية</h3>\n                <div className=\"space-y-3\">\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-600\">كود المنتج:</span>\n                    <p className=\"text-gray-900\">{product.code}</p>\n                  </div>\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-600\">اسم المنتج:</span>\n                    <p className=\"text-gray-900\">{product.name}</p>\n                  </div>\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-600\">الفئة:</span>\n                    <p className=\"text-gray-900\">{product.category}</p>\n                  </div>\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-600\">العلامة التجارية:</span>\n                    <p className=\"text-gray-900\">{product.brand || '-'}</p>\n                  </div>\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-600\">الوحدة:</span>\n                    <p className=\"text-gray-900\">{product.unit}</p>\n                  </div>\n                </div>\n              </div>\n\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">حالة المخزون</h3>\n                <div className=\"space-y-3\">\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-600\">المخزون الحالي:</span>\n                    <p className=\"text-2xl font-bold text-gray-900\">\n                      {product.currentStock} {product.unit}\n                    </p>\n                  </div>\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-600\">الحالة:</span>\n                    <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${stockStatus.color}`}>\n                      {stockStatus.text}\n                    </span>\n                  </div>\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-600\">الحد الأدنى:</span>\n                    <p className=\"text-gray-900\">{product.minStock} {product.unit}</p>\n                  </div>\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-600\">الحد الأقصى:</span>\n                    <p className=\"text-gray-900\">{product.maxStock} {product.unit}</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* الأسعار */}\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">الأسعار</h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                <div className=\"bg-blue-50 p-4 rounded-lg\">\n                  <span className=\"text-sm font-medium text-blue-600\">سعر التكلفة</span>\n                  <p className=\"text-xl font-bold text-blue-900\">{formatCurrency(product.costPrice)}</p>\n                </div>\n                <div className=\"bg-green-50 p-4 rounded-lg\">\n                  <span className=\"text-sm font-medium text-green-600\">سعر البيع</span>\n                  <p className=\"text-xl font-bold text-green-900\">{formatCurrency(product.sellingPrice)}</p>\n                </div>\n                <div className=\"bg-purple-50 p-4 rounded-lg\">\n                  <span className=\"text-sm font-medium text-purple-600\">هامش الربح</span>\n                  <p className=\"text-xl font-bold text-purple-900\">\n                    {((product.sellingPrice - product.costPrice) / product.costPrice * 100).toFixed(2)}%\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* قيمة المخزون */}\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">قيمة المخزون</h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div className=\"bg-gray-50 p-4 rounded-lg\">\n                  <span className=\"text-sm font-medium text-gray-600\">قيمة المخزون (بسعر التكلفة)</span>\n                  <p className=\"text-xl font-bold text-gray-900\">\n                    {formatCurrency(product.currentStock * product.costPrice)}\n                  </p>\n                </div>\n                <div className=\"bg-gray-50 p-4 rounded-lg\">\n                  <span className=\"text-sm font-medium text-gray-600\">قيمة المخزون (بسعر البيع)</span>\n                  <p className=\"text-xl font-bold text-gray-900\">\n                    {formatCurrency(product.currentStock * product.sellingPrice)}\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* معلومات إضافية */}\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">معلومات إضافية</h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <span className=\"text-sm font-medium text-gray-600\">موقع التخزين:</span>\n                  <p className=\"text-gray-900\">{product.location || '-'}</p>\n                </div>\n                <div>\n                  <span className=\"text-sm font-medium text-gray-600\">المورد:</span>\n                  <p className=\"text-gray-900\">{product.supplier || '-'}</p>\n                </div>\n                <div>\n                  <span className=\"text-sm font-medium text-gray-600\">الباركود:</span>\n                  <p className=\"text-gray-900 font-mono\">{product.barcode || '-'}</p>\n                </div>\n                <div>\n                  <span className=\"text-sm font-medium text-gray-600\">آخر تحديث:</span>\n                  <p className=\"text-gray-900\">{new Date(product.lastUpdated).toLocaleDateString('ar-SA')}</p>\n                </div>\n              </div>\n            </div>\n\n            {/* الوصف */}\n            {product.description && (\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">الوصف</h3>\n                <p className=\"text-gray-700 bg-gray-50 p-4 rounded-lg\">{product.description}</p>\n              </div>\n            )}\n\n            {/* حركات المخزون */}\n            {product.movements && product.movements.length > 0 && (\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">آخر حركات المخزون</h3>\n                <div className=\"overflow-x-auto\">\n                  <table className=\"w-full border-collapse border border-gray-300\">\n                    <thead>\n                      <tr className=\"bg-gray-50\">\n                        <th className=\"border border-gray-300 px-4 py-2 text-right\">التاريخ</th>\n                        <th className=\"border border-gray-300 px-4 py-2 text-center\">النوع</th>\n                        <th className=\"border border-gray-300 px-4 py-2 text-center\">الكمية</th>\n                        <th className=\"border border-gray-300 px-4 py-2 text-center\">المرجع</th>\n                      </tr>\n                    </thead>\n                    <tbody>\n                      {product.movements.slice(-5).reverse().map((movement, index) => (\n                        <tr key={index}>\n                          <td className=\"border border-gray-300 px-4 py-2\">\n                            {new Date(movement.date).toLocaleDateString('ar-SA')}\n                          </td>\n                          <td className=\"border border-gray-300 px-4 py-2 text-center\">\n                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                              movement.type === 'شراء' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'\n                            }`}>\n                              {movement.type}\n                            </span>\n                          </td>\n                          <td className=\"border border-gray-300 px-4 py-2 text-center\">\n                            <span className={movement.quantity > 0 ? 'text-green-600' : 'text-red-600'}>\n                              {movement.quantity > 0 ? '+' : ''}{movement.quantity}\n                            </span>\n                          </td>\n                          <td className=\"border border-gray-300 px-4 py-2 text-center\">\n                            {movement.reference}\n                          </td>\n                        </tr>\n                      ))}\n                    </tbody>\n                  </table>\n                </div>\n              </div>\n            )}\n\n            {/* زر الإغلاق */}\n            <div className=\"flex justify-end pt-6 border-t border-gray-200\">\n              <Button onClick={onClose} variant=\"outline\">\n                إغلاق\n              </Button>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </AnimatePresence>\n  );\n};\n\nexport default ProductDetails;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,MAAM,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,MAAMC,cAAc,GAAGA,CAAC;EAAEC,OAAO;EAAEC;AAAQ,CAAC,KAAK;EAC/C,MAAMC,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,MAAMM,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIT,OAAO,CAACU,YAAY,KAAK,CAAC,EAAE;MAC9B,OAAO;QAAEC,IAAI,EAAE,aAAa;QAAEC,KAAK,EAAE;MAA0B,CAAC;IAClE,CAAC,MAAM,IAAIZ,OAAO,CAACU,YAAY,IAAIV,OAAO,CAACa,QAAQ,EAAE;MACnD,OAAO;QAAEF,IAAI,EAAE,aAAa;QAAEC,KAAK,EAAE;MAAgC,CAAC;IACxE,CAAC,MAAM;MACL,OAAO;QAAED,IAAI,EAAE,OAAO;QAAEC,KAAK,EAAE;MAA8B,CAAC;IAChE;EACF,CAAC;EAED,MAAME,WAAW,GAAGL,cAAc,CAAC,CAAC;EAEpC,oBACEX,OAAA,CAACJ,eAAe;IAAAqB,QAAA,eACdjB,OAAA;MAAKkB,SAAS,EAAC,gFAAgF;MAAAD,QAAA,eAC7FjB,OAAA,CAACL,MAAM,CAACwB,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAK,CAAE;QACrCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAE,CAAE;QAClCE,IAAI,EAAE;UAAEH,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAK,CAAE;QAClCJ,SAAS,EAAC,8EAA8E;QAAAD,QAAA,gBAGxFjB,OAAA;UAAKkB,SAAS,EAAC,gEAAgE;UAAAD,QAAA,gBAC7EjB,OAAA;YAAIkB,SAAS,EAAC,qCAAqC;YAAAD,QAAA,EAAC;UAAa;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtE5B,OAAA;YACE6B,OAAO,EAAE1B,OAAQ;YACjBe,SAAS,EAAC,oEAAoE;YAAAD,QAAA,eAE9EjB,OAAA,CAACH,SAAS;cAACqB,SAAS,EAAC;YAAS;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN5B,OAAA;UAAKkB,SAAS,EAAC,eAAe;UAAAD,QAAA,gBAE5BjB,OAAA;YAAKkB,SAAS,EAAC,uCAAuC;YAAAD,QAAA,gBACpDjB,OAAA;cAAAiB,QAAA,gBACEjB,OAAA;gBAAIkB,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAAkB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChF5B,OAAA;gBAAKkB,SAAS,EAAC,WAAW;gBAAAD,QAAA,gBACxBjB,OAAA;kBAAAiB,QAAA,gBACEjB,OAAA;oBAAMkB,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,EAAC;kBAAW;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtE5B,OAAA;oBAAGkB,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAEf,OAAO,CAAC4B;kBAAI;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,eACN5B,OAAA;kBAAAiB,QAAA,gBACEjB,OAAA;oBAAMkB,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,EAAC;kBAAW;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtE5B,OAAA;oBAAGkB,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAEf,OAAO,CAAC6B;kBAAI;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,eACN5B,OAAA;kBAAAiB,QAAA,gBACEjB,OAAA;oBAAMkB,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,EAAC;kBAAM;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjE5B,OAAA;oBAAGkB,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAEf,OAAO,CAAC8B;kBAAQ;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACN5B,OAAA;kBAAAiB,QAAA,gBACEjB,OAAA;oBAAMkB,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,EAAC;kBAAiB;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5E5B,OAAA;oBAAGkB,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAEf,OAAO,CAAC+B,KAAK,IAAI;kBAAG;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC,eACN5B,OAAA;kBAAAiB,QAAA,gBACEjB,OAAA;oBAAMkB,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,EAAC;kBAAO;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClE5B,OAAA;oBAAGkB,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAEf,OAAO,CAACgC;kBAAI;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN5B,OAAA;cAAAiB,QAAA,gBACEjB,OAAA;gBAAIkB,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAAY;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1E5B,OAAA;gBAAKkB,SAAS,EAAC,WAAW;gBAAAD,QAAA,gBACxBjB,OAAA;kBAAAiB,QAAA,gBACEjB,OAAA;oBAAMkB,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,EAAC;kBAAe;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC1E5B,OAAA;oBAAGkB,SAAS,EAAC,kCAAkC;oBAAAD,QAAA,GAC5Cf,OAAO,CAACU,YAAY,EAAC,GAAC,EAACV,OAAO,CAACgC,IAAI;kBAAA;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACN5B,OAAA;kBAAAiB,QAAA,gBACEjB,OAAA;oBAAMkB,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,EAAC;kBAAO;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClE5B,OAAA;oBAAMkB,SAAS,EAAE,2DAA2DF,WAAW,CAACF,KAAK,EAAG;oBAAAG,QAAA,EAC7FD,WAAW,CAACH;kBAAI;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACN5B,OAAA;kBAAAiB,QAAA,gBACEjB,OAAA;oBAAMkB,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,EAAC;kBAAY;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvE5B,OAAA;oBAAGkB,SAAS,EAAC,eAAe;oBAAAD,QAAA,GAAEf,OAAO,CAACa,QAAQ,EAAC,GAAC,EAACb,OAAO,CAACgC,IAAI;kBAAA;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/D,CAAC,eACN5B,OAAA;kBAAAiB,QAAA,gBACEjB,OAAA;oBAAMkB,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,EAAC;kBAAY;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvE5B,OAAA;oBAAGkB,SAAS,EAAC,eAAe;oBAAAD,QAAA,GAAEf,OAAO,CAACiC,QAAQ,EAAC,GAAC,EAACjC,OAAO,CAACgC,IAAI;kBAAA;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN5B,OAAA;YAAAiB,QAAA,gBACEjB,OAAA;cAAIkB,SAAS,EAAC,0CAA0C;cAAAD,QAAA,EAAC;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrE5B,OAAA;cAAKkB,SAAS,EAAC,uCAAuC;cAAAD,QAAA,gBACpDjB,OAAA;gBAAKkB,SAAS,EAAC,2BAA2B;gBAAAD,QAAA,gBACxCjB,OAAA;kBAAMkB,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,EAAC;gBAAW;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtE5B,OAAA;kBAAGkB,SAAS,EAAC,iCAAiC;kBAAAD,QAAA,EAAEb,cAAc,CAACF,OAAO,CAACkC,SAAS;gBAAC;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnF,CAAC,eACN5B,OAAA;gBAAKkB,SAAS,EAAC,4BAA4B;gBAAAD,QAAA,gBACzCjB,OAAA;kBAAMkB,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,EAAC;gBAAS;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrE5B,OAAA;kBAAGkB,SAAS,EAAC,kCAAkC;kBAAAD,QAAA,EAAEb,cAAc,CAACF,OAAO,CAACmC,YAAY;gBAAC;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvF,CAAC,eACN5B,OAAA;gBAAKkB,SAAS,EAAC,6BAA6B;gBAAAD,QAAA,gBAC1CjB,OAAA;kBAAMkB,SAAS,EAAC,qCAAqC;kBAAAD,QAAA,EAAC;gBAAU;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvE5B,OAAA;kBAAGkB,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,GAC7C,CAAC,CAACf,OAAO,CAACmC,YAAY,GAAGnC,OAAO,CAACkC,SAAS,IAAIlC,OAAO,CAACkC,SAAS,GAAG,GAAG,EAAEE,OAAO,CAAC,CAAC,CAAC,EAAC,GACrF;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN5B,OAAA;YAAAiB,QAAA,gBACEjB,OAAA;cAAIkB,SAAS,EAAC,0CAA0C;cAAAD,QAAA,EAAC;YAAY;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1E5B,OAAA;cAAKkB,SAAS,EAAC,uCAAuC;cAAAD,QAAA,gBACpDjB,OAAA;gBAAKkB,SAAS,EAAC,2BAA2B;gBAAAD,QAAA,gBACxCjB,OAAA;kBAAMkB,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,EAAC;gBAA2B;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtF5B,OAAA;kBAAGkB,SAAS,EAAC,iCAAiC;kBAAAD,QAAA,EAC3Cb,cAAc,CAACF,OAAO,CAACU,YAAY,GAAGV,OAAO,CAACkC,SAAS;gBAAC;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACN5B,OAAA;gBAAKkB,SAAS,EAAC,2BAA2B;gBAAAD,QAAA,gBACxCjB,OAAA;kBAAMkB,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,EAAC;gBAAyB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpF5B,OAAA;kBAAGkB,SAAS,EAAC,iCAAiC;kBAAAD,QAAA,EAC3Cb,cAAc,CAACF,OAAO,CAACU,YAAY,GAAGV,OAAO,CAACmC,YAAY;gBAAC;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN5B,OAAA;YAAAiB,QAAA,gBACEjB,OAAA;cAAIkB,SAAS,EAAC,0CAA0C;cAAAD,QAAA,EAAC;YAAc;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5E5B,OAAA;cAAKkB,SAAS,EAAC,uCAAuC;cAAAD,QAAA,gBACpDjB,OAAA;gBAAAiB,QAAA,gBACEjB,OAAA;kBAAMkB,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,EAAC;gBAAa;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxE5B,OAAA;kBAAGkB,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAEf,OAAO,CAACqC,QAAQ,IAAI;gBAAG;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACN5B,OAAA;gBAAAiB,QAAA,gBACEjB,OAAA;kBAAMkB,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,EAAC;gBAAO;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClE5B,OAAA;kBAAGkB,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAEf,OAAO,CAACsC,QAAQ,IAAI;gBAAG;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACN5B,OAAA;gBAAAiB,QAAA,gBACEjB,OAAA;kBAAMkB,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,EAAC;gBAAS;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpE5B,OAAA;kBAAGkB,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EAAEf,OAAO,CAACuC,OAAO,IAAI;gBAAG;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC,eACN5B,OAAA;gBAAAiB,QAAA,gBACEjB,OAAA;kBAAMkB,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,EAAC;gBAAU;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrE5B,OAAA;kBAAGkB,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAE,IAAIyB,IAAI,CAACxC,OAAO,CAACyC,WAAW,CAAC,CAACC,kBAAkB,CAAC,OAAO;gBAAC;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGL1B,OAAO,CAAC2C,WAAW,iBAClB7C,OAAA;YAAAiB,QAAA,gBACEjB,OAAA;cAAIkB,SAAS,EAAC,0CAA0C;cAAAD,QAAA,EAAC;YAAK;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnE5B,OAAA;cAAGkB,SAAS,EAAC,yCAAyC;cAAAD,QAAA,EAAEf,OAAO,CAAC2C;YAAW;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CACN,EAGA1B,OAAO,CAAC4C,SAAS,IAAI5C,OAAO,CAAC4C,SAAS,CAACC,MAAM,GAAG,CAAC,iBAChD/C,OAAA;YAAAiB,QAAA,gBACEjB,OAAA;cAAIkB,SAAS,EAAC,0CAA0C;cAAAD,QAAA,EAAC;YAAiB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/E5B,OAAA;cAAKkB,SAAS,EAAC,iBAAiB;cAAAD,QAAA,eAC9BjB,OAAA;gBAAOkB,SAAS,EAAC,+CAA+C;gBAAAD,QAAA,gBAC9DjB,OAAA;kBAAAiB,QAAA,eACEjB,OAAA;oBAAIkB,SAAS,EAAC,YAAY;oBAAAD,QAAA,gBACxBjB,OAAA;sBAAIkB,SAAS,EAAC,6CAA6C;sBAAAD,QAAA,EAAC;oBAAO;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACxE5B,OAAA;sBAAIkB,SAAS,EAAC,8CAA8C;sBAAAD,QAAA,EAAC;oBAAK;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACvE5B,OAAA;sBAAIkB,SAAS,EAAC,8CAA8C;sBAAAD,QAAA,EAAC;oBAAM;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACxE5B,OAAA;sBAAIkB,SAAS,EAAC,8CAA8C;sBAAAD,QAAA,EAAC;oBAAM;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACR5B,OAAA;kBAAAiB,QAAA,EACGf,OAAO,CAAC4C,SAAS,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,kBACzDpD,OAAA;oBAAAiB,QAAA,gBACEjB,OAAA;sBAAIkB,SAAS,EAAC,kCAAkC;sBAAAD,QAAA,EAC7C,IAAIyB,IAAI,CAACS,QAAQ,CAACE,IAAI,CAAC,CAACT,kBAAkB,CAAC,OAAO;oBAAC;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClD,CAAC,eACL5B,OAAA;sBAAIkB,SAAS,EAAC,8CAA8C;sBAAAD,QAAA,eAC1DjB,OAAA;wBAAMkB,SAAS,EAAE,8CACfiC,QAAQ,CAACG,IAAI,KAAK,MAAM,GAAG,6BAA6B,GAAG,yBAAyB,EACnF;wBAAArC,QAAA,EACAkC,QAAQ,CAACG;sBAAI;wBAAA7B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACL5B,OAAA;sBAAIkB,SAAS,EAAC,8CAA8C;sBAAAD,QAAA,eAC1DjB,OAAA;wBAAMkB,SAAS,EAAEiC,QAAQ,CAACI,QAAQ,GAAG,CAAC,GAAG,gBAAgB,GAAG,cAAe;wBAAAtC,QAAA,GACxEkC,QAAQ,CAACI,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEJ,QAAQ,CAACI,QAAQ;sBAAA;wBAAA9B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACL5B,OAAA;sBAAIkB,SAAS,EAAC,8CAA8C;sBAAAD,QAAA,EACzDkC,QAAQ,CAACK;oBAAS;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC;kBAAA,GAlBEwB,KAAK;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAmBV,CACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGD5B,OAAA;YAAKkB,SAAS,EAAC,gDAAgD;YAAAD,QAAA,eAC7DjB,OAAA,CAACF,MAAM;cAAC+B,OAAO,EAAE1B,OAAQ;cAACsD,OAAO,EAAC,SAAS;cAAAxC,QAAA,EAAC;YAE5C;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACS,CAAC;AAEtB,CAAC;AAAC8B,EAAA,GA9NIzD,cAAc;AAgOpB,eAAeA,cAAc;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}