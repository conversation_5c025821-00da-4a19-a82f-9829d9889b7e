{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u0633\\u0637\\u062D \\u0627\\u0644\\u0645\\u0643\\u062A\\u0628\\\\333\\\\src\\\\components\\\\ui\\\\label.jsx\";\nimport React from 'react';\nimport { cn } from '../../lib/utils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Label = /*#__PURE__*/React.forwardRef(_c = ({\n  className,\n  ...props\n}, ref) => /*#__PURE__*/_jsxDEV(\"label\", {\n  ref: ref,\n  className: cn(\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\", className),\n  ...props\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 5,\n  columnNumber: 3\n}, this));\n_c2 = Label;\nLabel.displayName = \"Label\";\nexport { Label };\nvar _c, _c2;\n$RefreshReg$(_c, \"Label$React.forwardRef\");\n$RefreshReg$(_c2, \"Label\");", "map": {"version": 3, "names": ["React", "cn", "jsxDEV", "_jsxDEV", "Label", "forwardRef", "_c", "className", "props", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c2", "displayName", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/src/components/ui/label.jsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '../../lib/utils';\n\nconst Label = React.forwardRef(({ className, ...props }, ref) => (\n  <label\n    ref={ref}\n    className={cn(\n      \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\n      className\n    )}\n    {...props}\n  />\n));\n\nLabel.displayName = \"Label\";\n\nexport { Label };\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,EAAE,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,KAAK,gBAAGJ,KAAK,CAACK,UAAU,CAAAC,EAAA,GAACA,CAAC;EAAEC,SAAS;EAAE,GAAGC;AAAM,CAAC,EAAEC,GAAG,kBAC1DN,OAAA;EACEM,GAAG,EAAEA,GAAI;EACTF,SAAS,EAAEN,EAAE,CACX,4FAA4F,EAC5FM,SACF,CAAE;EAAA,GACEC;AAAK;EAAAE,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACV,CACF,CAAC;AAACC,GAAA,GATGV,KAAK;AAWXA,KAAK,CAACW,WAAW,GAAG,OAAO;AAE3B,SAASX,KAAK;AAAG,IAAAE,EAAA,EAAAQ,GAAA;AAAAE,YAAA,CAAAV,EAAA;AAAAU,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}