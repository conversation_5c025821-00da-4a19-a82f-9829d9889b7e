{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u0633\\u0637\\u062D \\u0627\\u0644\\u0645\\u0643\\u062A\\u0628\\\\333\\\\src\\\\components\\\\Dashboard\\\\StatsCard.jsx\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { ArrowTrendingUpIcon, ArrowTrendingDownIcon } from '@heroicons/react/24/outline';\nimport { Card, CardContent } from '../ui/card';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StatsCard = ({\n  title,\n  value,\n  change,\n  changeType,\n  icon: Icon,\n  color\n}) => {\n  const colorClasses = {\n    blue: 'bg-blue-500',\n    green: 'bg-green-500',\n    purple: 'bg-purple-500',\n    orange: 'bg-orange-500',\n    red: 'bg-red-500'\n  };\n  const changeColorClasses = {\n    increase: 'text-green-600 bg-green-100',\n    decrease: 'text-red-600 bg-red-100'\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    className: \"hover:shadow-lg transition-shadow duration-200\",\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      className: \"p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm font-medium text-gray-600 mb-1\",\n            children: title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `inline-flex items-center px-2 py-1 rounded-full text-xs font-medium mt-2 ${changeColorClasses[changeType]}`,\n            children: [changeType === 'increase' ? /*#__PURE__*/_jsxDEV(ArrowTrendingUpIcon, {\n              className: \"w-3 h-3 ml-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(ArrowTrendingDownIcon, {\n              className: \"w-3 h-3 ml-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 17\n            }, this), change]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `p-3 rounded-full ${colorClasses[color]}`,\n          children: /*#__PURE__*/_jsxDEV(Icon, {\n            className: \"w-6 h-6 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this);\n};\n_c = StatsCard;\nexport default StatsCard;\nvar _c;\n$RefreshReg$(_c, \"StatsCard\");", "map": {"version": 3, "names": ["React", "motion", "ArrowTrendingUpIcon", "ArrowTrendingDownIcon", "Card", "<PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "StatsCard", "title", "value", "change", "changeType", "icon", "Icon", "color", "colorClasses", "blue", "green", "purple", "orange", "red", "changeColorClasses", "increase", "decrease", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/src/components/Dashboard/StatsCard.jsx"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { ArrowTrendingUpIcon, ArrowTrendingDownIcon } from '@heroicons/react/24/outline';\nimport { Card, CardContent } from '../ui/card';\n\nconst StatsCard = ({ title, value, change, changeType, icon: Icon, color }) => {\n  const colorClasses = {\n    blue: 'bg-blue-500',\n    green: 'bg-green-500',\n    purple: 'bg-purple-500',\n    orange: 'bg-orange-500',\n    red: 'bg-red-500'\n  };\n\n  const changeColorClasses = {\n    increase: 'text-green-600 bg-green-100',\n    decrease: 'text-red-600 bg-red-100'\n  };\n\n  return (\n    <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n      <CardContent className=\"p-6\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex-1\">\n            <p className=\"text-sm font-medium text-gray-600 mb-1\">{title}</p>\n            <p className=\"text-2xl font-bold text-gray-900\">{value}</p>\n\n            {/* مؤشر التغيير */}\n            <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium mt-2 ${changeColorClasses[changeType]}`}>\n              {changeType === 'increase' ? (\n                <ArrowTrendingUpIcon className=\"w-3 h-3 ml-1\" />\n              ) : (\n                <ArrowTrendingDownIcon className=\"w-3 h-3 ml-1\" />\n              )}\n              {change}\n            </div>\n          </div>\n\n          {/* الأيقونة */}\n          <div className={`p-3 rounded-full ${colorClasses[color]}`}>\n            <Icon className=\"w-6 h-6 text-white\" />\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default StatsCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,mBAAmB,EAAEC,qBAAqB,QAAQ,6BAA6B;AACxF,SAASC,IAAI,EAAEC,WAAW,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,SAAS,GAAGA,CAAC;EAAEC,KAAK;EAAEC,KAAK;EAAEC,MAAM;EAAEC,UAAU;EAAEC,IAAI,EAAEC,IAAI;EAAEC;AAAM,CAAC,KAAK;EAC7E,MAAMC,YAAY,GAAG;IACnBC,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE,cAAc;IACrBC,MAAM,EAAE,eAAe;IACvBC,MAAM,EAAE,eAAe;IACvBC,GAAG,EAAE;EACP,CAAC;EAED,MAAMC,kBAAkB,GAAG;IACzBC,QAAQ,EAAE,6BAA6B;IACvCC,QAAQ,EAAE;EACZ,CAAC;EAED,oBACEjB,OAAA,CAACH,IAAI;IAACqB,SAAS,EAAC,gDAAgD;IAAAC,QAAA,eAC9DnB,OAAA,CAACF,WAAW;MAACoB,SAAS,EAAC,KAAK;MAAAC,QAAA,eAC1BnB,OAAA;QAAKkB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDnB,OAAA;UAAKkB,SAAS,EAAC,QAAQ;UAAAC,QAAA,gBACrBnB,OAAA;YAAGkB,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAEjB;UAAK;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjEvB,OAAA;YAAGkB,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAEhB;UAAK;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAG3DvB,OAAA;YAAKkB,SAAS,EAAE,4EAA4EH,kBAAkB,CAACV,UAAU,CAAC,EAAG;YAAAc,QAAA,GAC1Hd,UAAU,KAAK,UAAU,gBACxBL,OAAA,CAACL,mBAAmB;cAACuB,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAEhDvB,OAAA,CAACJ,qBAAqB;cAACsB,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAClD,EACAnB,MAAM;UAAA;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNvB,OAAA;UAAKkB,SAAS,EAAE,oBAAoBT,YAAY,CAACD,KAAK,CAAC,EAAG;UAAAW,QAAA,eACxDnB,OAAA,CAACO,IAAI;YAACW,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEX,CAAC;AAACC,EAAA,GAzCIvB,SAAS;AA2Cf,eAAeA,SAAS;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}