{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u0633\\u0637\\u062D \\u0627\\u0644\\u0645\\u0643\\u062A\\u0628\\\\333\\\\src\\\\components\\\\Settings\\\\CompanySettings.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { BuildingOfficeIcon, CheckCircleIcon, ExclamationTriangleIcon, DocumentTextIcon, BanknotesIcon, PhoneIcon, EnvelopeIcon } from '@heroicons/react/24/outline';\nimport { Card, CardContent, CardHeader, CardTitle } from '../ui/card';\nimport { Button } from '../ui/button';\nimport { Input } from '../ui/input';\nimport { Label } from '../ui/label';\nimport { getCompanyConfig, saveCompanyConfig, validateCompanyConfig } from '../../config/companyConfig';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CompanySettings = () => {\n  _s();\n  const [config, setConfig] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [isSaving, setIsSaving] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [activeTab, setActiveTab] = useState('basic');\n  const tabs = [{\n    id: 'basic',\n    name: 'المعلومات الأساسية',\n    icon: BuildingOfficeIcon\n  }, {\n    id: 'contact',\n    name: 'الاتصال والعنوان',\n    icon: PhoneIcon\n  }, {\n    id: 'bank',\n    name: 'المعلومات المصرفية',\n    icon: BanknotesIcon\n  }, {\n    id: 'system',\n    name: 'إعدادات النظام',\n    icon: DocumentTextIcon\n  }];\n  useEffect(() => {\n    loadConfig();\n  }, []);\n  const loadConfig = () => {\n    try {\n      const companyConfig = getCompanyConfig();\n      setConfig(companyConfig);\n    } catch (error) {\n      toast.error('خطأ في تحميل إعدادات الشركة');\n      console.error(error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleInputChange = (section, field, value) => {\n    setConfig(prev => {\n      const newConfig = {\n        ...prev\n      };\n      if (section) {\n        newConfig[section] = {\n          ...newConfig[section],\n          [field]: value\n        };\n      } else {\n        newConfig[field] = value;\n      }\n      return newConfig;\n    });\n\n    // إزالة الخطأ عند التعديل\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: null\n      }));\n    }\n  };\n  const handleNestedInputChange = (section, subsection, field, value) => {\n    setConfig(prev => ({\n      ...prev,\n      [section]: {\n        ...prev[section],\n        [subsection]: {\n          ...prev[section][subsection],\n          [field]: value\n        }\n      }\n    }));\n  };\n  const handleSave = async () => {\n    setIsSaving(true);\n    try {\n      const validation = validateCompanyConfig(config);\n      if (!validation.isValid) {\n        const errorObj = {};\n        validation.errors.forEach(error => {\n          errorObj[error] = error;\n        });\n        setErrors(errorObj);\n        toast.error('يرجى تصحيح الأخطاء المذكورة');\n        return;\n      }\n      if (saveCompanyConfig(config)) {\n        toast.success('تم حفظ إعدادات الشركة بنجاح');\n        setErrors({});\n      } else {\n        toast.error('حدث خطأ أثناء حفظ الإعدادات');\n      }\n    } catch (error) {\n      toast.error('حدث خطأ غير متوقع');\n      console.error(error);\n    } finally {\n      setIsSaving(false);\n    }\n  };\n  const resetToDefaults = () => {\n    if (window.confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {\n      localStorage.removeItem('company_config');\n      loadConfig();\n      toast.success('تم إعادة تعيين الإعدادات');\n    }\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center p-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this);\n  }\n  if (!config) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center p-8\",\n      children: [/*#__PURE__*/_jsxDEV(ExclamationTriangleIcon, {\n        className: \"w-12 h-12 text-red-500 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: \"\\u062E\\u0637\\u0623 \\u0641\\u064A \\u062A\\u062D\\u0645\\u064A\\u0644 \\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0634\\u0631\\u0643\\u0629\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: loadConfig,\n        className: \"mt-4\",\n        children: \"\\u0625\\u0639\\u0627\\u062F\\u0629 \\u0627\\u0644\\u0645\\u062D\\u0627\\u0648\\u0644\\u0629\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this);\n  }\n  const renderBasicInfo = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          htmlFor: \"companyName\",\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0634\\u0631\\u0643\\u0629 *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"companyName\",\n          type: \"text\",\n          value: config.companyInfo.name,\n          onChange: e => handleNestedInputChange('companyInfo', null, 'name', e.target.value),\n          placeholder: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0634\\u0631\\u0643\\u0629\",\n          className: errors.name ? 'border-red-500' : ''\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this), errors.name && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-500 text-sm mt-1\",\n          children: errors.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 27\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          htmlFor: \"companyNameEn\",\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0634\\u0631\\u0643\\u0629 \\u0628\\u0627\\u0644\\u0625\\u0646\\u062C\\u0644\\u064A\\u0632\\u064A\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"companyNameEn\",\n          type: \"text\",\n          value: config.companyInfo.nameEn || '',\n          onChange: e => handleNestedInputChange('companyInfo', null, 'nameEn', e.target.value),\n          placeholder: \"Company Name in English\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          htmlFor: \"vatNumber\",\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"\\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u064A *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"vatNumber\",\n          type: \"text\",\n          value: config.companyInfo.vatNumber,\n          onChange: e => handleNestedInputChange('companyInfo', null, 'vatNumber', e.target.value),\n          placeholder: \"300000000000003\",\n          maxLength: 15,\n          className: errors.vatNumber ? 'border-red-500' : ''\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this), errors.vatNumber && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-500 text-sm mt-1\",\n          children: errors.vatNumber\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 32\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          htmlFor: \"crNumber\",\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0633\\u062C\\u0644 \\u0627\\u0644\\u062A\\u062C\\u0627\\u0631\\u064A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"crNumber\",\n          type: \"text\",\n          value: config.companyInfo.crNumber || '',\n          onChange: e => handleNestedInputChange('companyInfo', null, 'crNumber', e.target.value),\n          placeholder: \"**********\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          htmlFor: \"licenseNumber\",\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0631\\u062E\\u0635\\u0629 \\u0627\\u0644\\u062A\\u062C\\u0627\\u0631\\u064A\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"licenseNumber\",\n          type: \"text\",\n          value: config.companyInfo.licenseNumber || '',\n          onChange: e => handleNestedInputChange('companyInfo', null, 'licenseNumber', e.target.value),\n          placeholder: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0631\\u062E\\u0635\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 146,\n    columnNumber: 5\n  }, this);\n  const renderContactInfo = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          htmlFor: \"phone\",\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0647\\u0627\\u062A\\u0641 *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"phone\",\n          type: \"tel\",\n          value: config.companyInfo.contact.phone,\n          onChange: e => handleNestedInputChange('companyInfo', 'contact', 'phone', e.target.value),\n          placeholder: \"**********\",\n          className: errors.phone ? 'border-red-500' : ''\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this), errors.phone && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-500 text-sm mt-1\",\n          children: errors.phone\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 28\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          htmlFor: \"mobile\",\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u062C\\u0648\\u0627\\u0644\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"mobile\",\n          type: \"tel\",\n          value: config.companyInfo.contact.mobile || '',\n          onChange: e => handleNestedInputChange('companyInfo', 'contact', 'mobile', e.target.value),\n          placeholder: \"0501234567\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          htmlFor: \"email\",\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"\\u0627\\u0644\\u0628\\u0631\\u064A\\u062F \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"email\",\n          type: \"email\",\n          value: config.companyInfo.contact.email,\n          onChange: e => handleNestedInputChange('companyInfo', 'contact', 'email', e.target.value),\n          placeholder: \"<EMAIL>\",\n          className: errors.email ? 'border-red-500' : ''\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this), errors.email && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-500 text-sm mt-1\",\n          children: errors.email\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 28\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          htmlFor: \"website\",\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"\\u0627\\u0644\\u0645\\u0648\\u0642\\u0639 \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"website\",\n          type: \"url\",\n          value: config.companyInfo.contact.website || '',\n          onChange: e => handleNestedInputChange('companyInfo', 'contact', 'website', e.target.value),\n          placeholder: \"www.company.com\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          htmlFor: \"fax\",\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0641\\u0627\\u0643\\u0633\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"fax\",\n          type: \"tel\",\n          value: config.companyInfo.contact.fax || '',\n          onChange: e => handleNestedInputChange('companyInfo', 'contact', 'fax', e.target.value),\n          placeholder: \"0112345679\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 223,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Label, {\n        htmlFor: \"street\",\n        className: \"block text-sm font-medium text-gray-700 mb-2\",\n        children: \"\\u0627\\u0644\\u0639\\u0646\\u0648\\u0627\\u0646 \\u0627\\u0644\\u062A\\u0641\\u0635\\u064A\\u0644\\u064A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n        id: \"street\",\n        rows: 3,\n        value: config.companyInfo.address.street || '',\n        onChange: e => handleNestedInputChange('companyInfo', 'address', 'street', e.target.value),\n        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n        placeholder: \"\\u0627\\u0644\\u0634\\u0627\\u0631\\u0639\\u060C \\u0627\\u0644\\u062D\\u064A\\u060C \\u0631\\u0642\\u0645 \\u0627\\u0644\\u0645\\u0628\\u0646\\u0649\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 294,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          htmlFor: \"city\",\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"\\u0627\\u0644\\u0645\\u062F\\u064A\\u0646\\u0629 *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"city\",\n          type: \"text\",\n          value: config.companyInfo.address.city,\n          onChange: e => handleNestedInputChange('companyInfo', 'address', 'city', e.target.value),\n          placeholder: \"\\u0627\\u0644\\u0631\\u064A\\u0627\\u0636\",\n          className: errors.city ? 'border-red-500' : ''\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this), errors.city && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-500 text-sm mt-1\",\n          children: errors.city\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 27\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          htmlFor: \"region\",\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"\\u0627\\u0644\\u0645\\u0646\\u0637\\u0642\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"region\",\n          type: \"text\",\n          value: config.companyInfo.address.region || '',\n          onChange: e => handleNestedInputChange('companyInfo', 'address', 'region', e.target.value),\n          placeholder: \"\\u0645\\u0646\\u0637\\u0642\\u0629 \\u0627\\u0644\\u0631\\u064A\\u0627\\u0636\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          htmlFor: \"postalCode\",\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"\\u0627\\u0644\\u0631\\u0645\\u0632 \\u0627\\u0644\\u0628\\u0631\\u064A\\u062F\\u064A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"postalCode\",\n          type: \"text\",\n          value: config.companyInfo.address.postalCode || '',\n          onChange: e => handleNestedInputChange('companyInfo', 'address', 'postalCode', e.target.value),\n          placeholder: \"12345\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 222,\n    columnNumber: 5\n  }, this);\n  const renderBankInfo = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-blue-50 p-4 rounded-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-blue-800 text-sm\",\n        children: \"\\u0627\\u0644\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0645\\u0635\\u0631\\u0641\\u064A\\u0629 \\u0633\\u062A\\u0638\\u0647\\u0631 \\u0641\\u064A \\u0627\\u0644\\u0641\\u0648\\u0627\\u062A\\u064A\\u0631 \\u0644\\u062A\\u0633\\u0647\\u064A\\u0644 \\u0639\\u0645\\u0644\\u064A\\u0629 \\u0627\\u0644\\u062F\\u0641\\u0639 \\u0639\\u0644\\u0649 \\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 355,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          htmlFor: \"bankName\",\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0628\\u0646\\u0643\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"bankName\",\n          type: \"text\",\n          value: config.companyInfo.bankInfo.bankName || '',\n          onChange: e => handleNestedInputChange('companyInfo', 'bankInfo', 'bankName', e.target.value),\n          placeholder: \"\\u0627\\u0644\\u0628\\u0646\\u0643 \\u0627\\u0644\\u0623\\u0647\\u0644\\u064A \\u0627\\u0644\\u0633\\u0639\\u0648\\u062F\\u064A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          htmlFor: \"accountNumber\",\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u062D\\u0633\\u0627\\u0628\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"accountNumber\",\n          type: \"text\",\n          value: config.companyInfo.bankInfo.accountNumber || '',\n          onChange: e => handleNestedInputChange('companyInfo', 'bankInfo', 'accountNumber', e.target.value),\n          placeholder: \"*********\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          htmlFor: \"iban\",\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0622\\u064A\\u0628\\u0627\\u0646 (IBAN)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"iban\",\n          type: \"text\",\n          value: config.companyInfo.bankInfo.iban || '',\n          onChange: e => handleNestedInputChange('companyInfo', 'bankInfo', 'iban', e.target.value),\n          placeholder: \"************************\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          htmlFor: \"swiftCode\",\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"\\u0631\\u0645\\u0632 \\u0627\\u0644\\u0633\\u0648\\u064A\\u0641\\u062A (SWIFT)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"swiftCode\",\n          type: \"text\",\n          value: config.companyInfo.bankInfo.swiftCode || '',\n          onChange: e => handleNestedInputChange('companyInfo', 'bankInfo', 'swiftCode', e.target.value),\n          placeholder: \"NCBKSARI\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 401,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 361,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 354,\n    columnNumber: 5\n  }, this);\n  const renderSystemSettings = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          htmlFor: \"currency\",\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"\\u0627\\u0644\\u0639\\u0645\\u0644\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"currency\",\n          type: \"text\",\n          value: config.systemSettings.currency,\n          onChange: e => handleNestedInputChange('systemSettings', null, 'currency', e.target.value),\n          placeholder: \"\\u0631.\\u0633\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          htmlFor: \"vatRate\",\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"\\u0646\\u0633\\u0628\\u0629 \\u0636\\u0631\\u064A\\u0628\\u0629 \\u0627\\u0644\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0636\\u0627\\u0641\\u0629 (%)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"vatRate\",\n          type: \"number\",\n          value: config.systemSettings.defaultVatRate,\n          onChange: e => handleNestedInputChange('systemSettings', null, 'defaultVatRate', parseFloat(e.target.value)),\n          placeholder: \"15\",\n          min: \"0\",\n          max: \"100\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 433,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          htmlFor: \"invoicePrefix\",\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"\\u0628\\u0627\\u062F\\u0626\\u0629 \\u0631\\u0642\\u0645 \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"invoicePrefix\",\n          type: \"text\",\n          value: config.systemSettings.invoicePrefix,\n          onChange: e => handleNestedInputChange('systemSettings', null, 'invoicePrefix', e.target.value),\n          placeholder: \"INV\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 452,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 448,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          htmlFor: \"lowStockThreshold\",\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"\\u062D\\u062F \\u062A\\u0646\\u0628\\u064A\\u0647 \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646 \\u0627\\u0644\\u0645\\u0646\\u062E\\u0641\\u0636\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"lowStockThreshold\",\n          type: \"number\",\n          value: config.systemSettings.lowStockThreshold,\n          onChange: e => handleNestedInputChange('systemSettings', null, 'lowStockThreshold', parseInt(e.target.value)),\n          placeholder: \"10\",\n          min: \"0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 465,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 461,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 419,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Label, {\n        htmlFor: \"termsAndConditions\",\n        className: \"block text-sm font-medium text-gray-700 mb-2\",\n        children: \"\\u0627\\u0644\\u0634\\u0631\\u0648\\u0637 \\u0648\\u0627\\u0644\\u0623\\u062D\\u0643\\u0627\\u0645\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 477,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n        id: \"termsAndConditions\",\n        rows: 6,\n        value: config.printSettings.termsAndConditions,\n        onChange: e => handleNestedInputChange('printSettings', null, 'termsAndConditions', e.target.value),\n        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n        placeholder: \"\\u0623\\u062F\\u062E\\u0644 \\u0627\\u0644\\u0634\\u0631\\u0648\\u0637 \\u0648\\u0627\\u0644\\u0623\\u062D\\u0643\\u0627\\u0645 \\u0627\\u0644\\u062A\\u064A \\u0633\\u062A\\u0638\\u0647\\u0631 \\u0641\\u064A \\u0627\\u0644\\u0641\\u0648\\u0627\\u062A\\u064A\\u0631\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 480,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 476,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 418,\n    columnNumber: 5\n  }, this);\n  const renderContent = () => {\n    switch (activeTab) {\n      case 'basic':\n        return renderBasicInfo();\n      case 'contact':\n        return renderContactInfo();\n      case 'bank':\n        return renderBankInfo();\n      case 'system':\n        return renderSystemSettings();\n      default:\n        return renderBasicInfo();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(BuildingOfficeIcon, {\n            className: \"w-8 h-8 text-blue-600 ml-3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0634\\u0631\\u0643\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0634\\u0631\\u0643\\u0629 \\u0648\\u0627\\u0644\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0639\\u0627\\u0645\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 515,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3 space-x-reverse\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: resetToDefaults,\n            variant: \"outline\",\n            disabled: isSaving,\n            children: \"\\u0625\\u0639\\u0627\\u062F\\u0629 \\u062A\\u0639\\u064A\\u064A\\u0646\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 524,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: isSaving,\n            className: \"bg-blue-600 hover:bg-blue-700 text-white\",\n            children: isSaving ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ml-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 19\n              }, this), \"\\u062C\\u0627\\u0631\\u064A \\u0627\\u0644\\u062D\\u0641\\u0638...\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 537,\n              columnNumber: 17\n            }, this) : 'حفظ التغييرات'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 531,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 523,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 514,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 510,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.1\n      },\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-1 space-x-reverse\",\n            children: tabs.map(tab => {\n              const Icon = tab.icon;\n              return /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setActiveTab(tab.id),\n                className: `flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors ${activeTab === tab.id ? 'bg-blue-100 text-blue-700 border border-blue-200' : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'}`,\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  className: \"w-4 h-4 ml-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 21\n                }, this), tab.name]\n              }, tab.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 557,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 556,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n          children: renderContent()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 578,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 555,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 550,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 508,\n    columnNumber: 5\n  }, this);\n};\n_s(CompanySettings, \"5sqTUgEWXymKxQAgkhG8z+RTg8I=\");\n_c = CompanySettings;\nexport default CompanySettings;\nvar _c;\n$RefreshReg$(_c, \"CompanySettings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "BuildingOfficeIcon", "CheckCircleIcon", "ExclamationTriangleIcon", "DocumentTextIcon", "BanknotesIcon", "PhoneIcon", "EnvelopeIcon", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "<PERSON><PERSON>", "Input", "Label", "getCompanyConfig", "saveCompanyConfig", "validateCompanyConfig", "toast", "jsxDEV", "_jsxDEV", "CompanySettings", "_s", "config", "setConfig", "isLoading", "setIsLoading", "isSaving", "setIsSaving", "errors", "setErrors", "activeTab", "setActiveTab", "tabs", "id", "name", "icon", "loadConfig", "companyConfig", "error", "console", "handleInputChange", "section", "field", "value", "prev", "newConfig", "handleNestedInputChange", "subsection", "handleSave", "validation", "<PERSON><PERSON><PERSON><PERSON>", "errorObj", "for<PERSON>ach", "success", "resetToDefaults", "window", "confirm", "localStorage", "removeItem", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "renderBasicInfo", "htmlFor", "type", "companyInfo", "onChange", "e", "target", "placeholder", "nameEn", "vatNumber", "max<PERSON><PERSON><PERSON>", "crNumber", "licenseNumber", "renderContactInfo", "contact", "phone", "mobile", "email", "website", "fax", "rows", "address", "street", "city", "region", "postalCode", "renderBankInfo", "bankInfo", "bankName", "accountNumber", "iban", "swiftCode", "renderSystemSettings", "systemSettings", "currency", "defaultVatRate", "parseFloat", "min", "max", "invoicePrefix", "lowStockThreshold", "parseInt", "printSettings", "termsAndConditions", "renderContent", "div", "initial", "opacity", "y", "animate", "variant", "disabled", "transition", "delay", "map", "tab", "Icon", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/src/components/Settings/CompanySettings.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { \n  BuildingOfficeIcon, \n  CheckCircleIcon,\n  ExclamationTriangleIcon,\n  DocumentTextIcon,\n  BanknotesIcon,\n  PhoneIcon,\n  EnvelopeIcon\n} from '@heroicons/react/24/outline';\nimport { Card, CardContent, CardHeader, CardTitle } from '../ui/card';\nimport { Button } from '../ui/button';\nimport { Input } from '../ui/input';\nimport { Label } from '../ui/label';\nimport { getCompanyConfig, saveCompanyConfig, validateCompanyConfig } from '../../config/companyConfig';\nimport toast from 'react-hot-toast';\n\nconst CompanySettings = () => {\n  const [config, setConfig] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [isSaving, setIsSaving] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [activeTab, setActiveTab] = useState('basic');\n\n  const tabs = [\n    { id: 'basic', name: 'المعلومات الأساسية', icon: BuildingOfficeIcon },\n    { id: 'contact', name: 'الاتصال والعنوان', icon: PhoneIcon },\n    { id: 'bank', name: 'المعلومات المصرفية', icon: BanknotesIcon },\n    { id: 'system', name: 'إعدادات النظام', icon: DocumentTextIcon }\n  ];\n\n  useEffect(() => {\n    loadConfig();\n  }, []);\n\n  const loadConfig = () => {\n    try {\n      const companyConfig = getCompanyConfig();\n      setConfig(companyConfig);\n    } catch (error) {\n      toast.error('خطأ في تحميل إعدادات الشركة');\n      console.error(error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleInputChange = (section, field, value) => {\n    setConfig(prev => {\n      const newConfig = { ...prev };\n      \n      if (section) {\n        newConfig[section] = {\n          ...newConfig[section],\n          [field]: value\n        };\n      } else {\n        newConfig[field] = value;\n      }\n      \n      return newConfig;\n    });\n\n    // إزالة الخطأ عند التعديل\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: null\n      }));\n    }\n  };\n\n  const handleNestedInputChange = (section, subsection, field, value) => {\n    setConfig(prev => ({\n      ...prev,\n      [section]: {\n        ...prev[section],\n        [subsection]: {\n          ...prev[section][subsection],\n          [field]: value\n        }\n      }\n    }));\n  };\n\n  const handleSave = async () => {\n    setIsSaving(true);\n    \n    try {\n      const validation = validateCompanyConfig(config);\n      \n      if (!validation.isValid) {\n        const errorObj = {};\n        validation.errors.forEach(error => {\n          errorObj[error] = error;\n        });\n        setErrors(errorObj);\n        toast.error('يرجى تصحيح الأخطاء المذكورة');\n        return;\n      }\n\n      if (saveCompanyConfig(config)) {\n        toast.success('تم حفظ إعدادات الشركة بنجاح');\n        setErrors({});\n      } else {\n        toast.error('حدث خطأ أثناء حفظ الإعدادات');\n      }\n    } catch (error) {\n      toast.error('حدث خطأ غير متوقع');\n      console.error(error);\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  const resetToDefaults = () => {\n    if (window.confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {\n      localStorage.removeItem('company_config');\n      loadConfig();\n      toast.success('تم إعادة تعيين الإعدادات');\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center p-8\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!config) {\n    return (\n      <div className=\"text-center p-8\">\n        <ExclamationTriangleIcon className=\"w-12 h-12 text-red-500 mx-auto mb-4\" />\n        <p className=\"text-gray-600\">خطأ في تحميل إعدادات الشركة</p>\n        <Button onClick={loadConfig} className=\"mt-4\">\n          إعادة المحاولة\n        </Button>\n      </div>\n    );\n  }\n\n  const renderBasicInfo = () => (\n    <div className=\"space-y-6\">\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        <div>\n          <Label htmlFor=\"companyName\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            اسم الشركة *\n          </Label>\n          <Input\n            id=\"companyName\"\n            type=\"text\"\n            value={config.companyInfo.name}\n            onChange={(e) => handleNestedInputChange('companyInfo', null, 'name', e.target.value)}\n            placeholder=\"اسم الشركة\"\n            className={errors.name ? 'border-red-500' : ''}\n          />\n          {errors.name && <p className=\"text-red-500 text-sm mt-1\">{errors.name}</p>}\n        </div>\n\n        <div>\n          <Label htmlFor=\"companyNameEn\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            اسم الشركة بالإنجليزية\n          </Label>\n          <Input\n            id=\"companyNameEn\"\n            type=\"text\"\n            value={config.companyInfo.nameEn || ''}\n            onChange={(e) => handleNestedInputChange('companyInfo', null, 'nameEn', e.target.value)}\n            placeholder=\"Company Name in English\"\n          />\n        </div>\n\n        <div>\n          <Label htmlFor=\"vatNumber\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            الرقم الضريبي *\n          </Label>\n          <Input\n            id=\"vatNumber\"\n            type=\"text\"\n            value={config.companyInfo.vatNumber}\n            onChange={(e) => handleNestedInputChange('companyInfo', null, 'vatNumber', e.target.value)}\n            placeholder=\"300000000000003\"\n            maxLength={15}\n            className={errors.vatNumber ? 'border-red-500' : ''}\n          />\n          {errors.vatNumber && <p className=\"text-red-500 text-sm mt-1\">{errors.vatNumber}</p>}\n        </div>\n\n        <div>\n          <Label htmlFor=\"crNumber\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            رقم السجل التجاري\n          </Label>\n          <Input\n            id=\"crNumber\"\n            type=\"text\"\n            value={config.companyInfo.crNumber || ''}\n            onChange={(e) => handleNestedInputChange('companyInfo', null, 'crNumber', e.target.value)}\n            placeholder=\"**********\"\n          />\n        </div>\n\n        <div>\n          <Label htmlFor=\"licenseNumber\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            رقم الرخصة التجارية\n          </Label>\n          <Input\n            id=\"licenseNumber\"\n            type=\"text\"\n            value={config.companyInfo.licenseNumber || ''}\n            onChange={(e) => handleNestedInputChange('companyInfo', null, 'licenseNumber', e.target.value)}\n            placeholder=\"رقم الرخصة\"\n          />\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderContactInfo = () => (\n    <div className=\"space-y-6\">\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        <div>\n          <Label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            رقم الهاتف *\n          </Label>\n          <Input\n            id=\"phone\"\n            type=\"tel\"\n            value={config.companyInfo.contact.phone}\n            onChange={(e) => handleNestedInputChange('companyInfo', 'contact', 'phone', e.target.value)}\n            placeholder=\"**********\"\n            className={errors.phone ? 'border-red-500' : ''}\n          />\n          {errors.phone && <p className=\"text-red-500 text-sm mt-1\">{errors.phone}</p>}\n        </div>\n\n        <div>\n          <Label htmlFor=\"mobile\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            رقم الجوال\n          </Label>\n          <Input\n            id=\"mobile\"\n            type=\"tel\"\n            value={config.companyInfo.contact.mobile || ''}\n            onChange={(e) => handleNestedInputChange('companyInfo', 'contact', 'mobile', e.target.value)}\n            placeholder=\"0501234567\"\n          />\n        </div>\n\n        <div>\n          <Label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            البريد الإلكتروني *\n          </Label>\n          <Input\n            id=\"email\"\n            type=\"email\"\n            value={config.companyInfo.contact.email}\n            onChange={(e) => handleNestedInputChange('companyInfo', 'contact', 'email', e.target.value)}\n            placeholder=\"<EMAIL>\"\n            className={errors.email ? 'border-red-500' : ''}\n          />\n          {errors.email && <p className=\"text-red-500 text-sm mt-1\">{errors.email}</p>}\n        </div>\n\n        <div>\n          <Label htmlFor=\"website\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            الموقع الإلكتروني\n          </Label>\n          <Input\n            id=\"website\"\n            type=\"url\"\n            value={config.companyInfo.contact.website || ''}\n            onChange={(e) => handleNestedInputChange('companyInfo', 'contact', 'website', e.target.value)}\n            placeholder=\"www.company.com\"\n          />\n        </div>\n\n        <div>\n          <Label htmlFor=\"fax\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            رقم الفاكس\n          </Label>\n          <Input\n            id=\"fax\"\n            type=\"tel\"\n            value={config.companyInfo.contact.fax || ''}\n            onChange={(e) => handleNestedInputChange('companyInfo', 'contact', 'fax', e.target.value)}\n            placeholder=\"0112345679\"\n          />\n        </div>\n      </div>\n\n      <div>\n        <Label htmlFor=\"street\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n          العنوان التفصيلي\n        </Label>\n        <textarea\n          id=\"street\"\n          rows={3}\n          value={config.companyInfo.address.street || ''}\n          onChange={(e) => handleNestedInputChange('companyInfo', 'address', 'street', e.target.value)}\n          className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n          placeholder=\"الشارع، الحي، رقم المبنى\"\n        />\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <div>\n          <Label htmlFor=\"city\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            المدينة *\n          </Label>\n          <Input\n            id=\"city\"\n            type=\"text\"\n            value={config.companyInfo.address.city}\n            onChange={(e) => handleNestedInputChange('companyInfo', 'address', 'city', e.target.value)}\n            placeholder=\"الرياض\"\n            className={errors.city ? 'border-red-500' : ''}\n          />\n          {errors.city && <p className=\"text-red-500 text-sm mt-1\">{errors.city}</p>}\n        </div>\n\n        <div>\n          <Label htmlFor=\"region\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            المنطقة\n          </Label>\n          <Input\n            id=\"region\"\n            type=\"text\"\n            value={config.companyInfo.address.region || ''}\n            onChange={(e) => handleNestedInputChange('companyInfo', 'address', 'region', e.target.value)}\n            placeholder=\"منطقة الرياض\"\n          />\n        </div>\n\n        <div>\n          <Label htmlFor=\"postalCode\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            الرمز البريدي\n          </Label>\n          <Input\n            id=\"postalCode\"\n            type=\"text\"\n            value={config.companyInfo.address.postalCode || ''}\n            onChange={(e) => handleNestedInputChange('companyInfo', 'address', 'postalCode', e.target.value)}\n            placeholder=\"12345\"\n          />\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderBankInfo = () => (\n    <div className=\"space-y-6\">\n      <div className=\"bg-blue-50 p-4 rounded-lg\">\n        <p className=\"text-blue-800 text-sm\">\n          المعلومات المصرفية ستظهر في الفواتير لتسهيل عملية الدفع على العملاء.\n        </p>\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        <div>\n          <Label htmlFor=\"bankName\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            اسم البنك\n          </Label>\n          <Input\n            id=\"bankName\"\n            type=\"text\"\n            value={config.companyInfo.bankInfo.bankName || ''}\n            onChange={(e) => handleNestedInputChange('companyInfo', 'bankInfo', 'bankName', e.target.value)}\n            placeholder=\"البنك الأهلي السعودي\"\n          />\n        </div>\n\n        <div>\n          <Label htmlFor=\"accountNumber\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            رقم الحساب\n          </Label>\n          <Input\n            id=\"accountNumber\"\n            type=\"text\"\n            value={config.companyInfo.bankInfo.accountNumber || ''}\n            onChange={(e) => handleNestedInputChange('companyInfo', 'bankInfo', 'accountNumber', e.target.value)}\n            placeholder=\"*********\"\n          />\n        </div>\n\n        <div>\n          <Label htmlFor=\"iban\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            رقم الآيبان (IBAN)\n          </Label>\n          <Input\n            id=\"iban\"\n            type=\"text\"\n            value={config.companyInfo.bankInfo.iban || ''}\n            onChange={(e) => handleNestedInputChange('companyInfo', 'bankInfo', 'iban', e.target.value)}\n            placeholder=\"************************\"\n          />\n        </div>\n\n        <div>\n          <Label htmlFor=\"swiftCode\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            رمز السويفت (SWIFT)\n          </Label>\n          <Input\n            id=\"swiftCode\"\n            type=\"text\"\n            value={config.companyInfo.bankInfo.swiftCode || ''}\n            onChange={(e) => handleNestedInputChange('companyInfo', 'bankInfo', 'swiftCode', e.target.value)}\n            placeholder=\"NCBKSARI\"\n          />\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderSystemSettings = () => (\n    <div className=\"space-y-6\">\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        <div>\n          <Label htmlFor=\"currency\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            العملة\n          </Label>\n          <Input\n            id=\"currency\"\n            type=\"text\"\n            value={config.systemSettings.currency}\n            onChange={(e) => handleNestedInputChange('systemSettings', null, 'currency', e.target.value)}\n            placeholder=\"ر.س\"\n          />\n        </div>\n\n        <div>\n          <Label htmlFor=\"vatRate\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            نسبة ضريبة القيمة المضافة (%)\n          </Label>\n          <Input\n            id=\"vatRate\"\n            type=\"number\"\n            value={config.systemSettings.defaultVatRate}\n            onChange={(e) => handleNestedInputChange('systemSettings', null, 'defaultVatRate', parseFloat(e.target.value))}\n            placeholder=\"15\"\n            min=\"0\"\n            max=\"100\"\n          />\n        </div>\n\n        <div>\n          <Label htmlFor=\"invoicePrefix\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            بادئة رقم الفاتورة\n          </Label>\n          <Input\n            id=\"invoicePrefix\"\n            type=\"text\"\n            value={config.systemSettings.invoicePrefix}\n            onChange={(e) => handleNestedInputChange('systemSettings', null, 'invoicePrefix', e.target.value)}\n            placeholder=\"INV\"\n          />\n        </div>\n\n        <div>\n          <Label htmlFor=\"lowStockThreshold\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            حد تنبيه المخزون المنخفض\n          </Label>\n          <Input\n            id=\"lowStockThreshold\"\n            type=\"number\"\n            value={config.systemSettings.lowStockThreshold}\n            onChange={(e) => handleNestedInputChange('systemSettings', null, 'lowStockThreshold', parseInt(e.target.value))}\n            placeholder=\"10\"\n            min=\"0\"\n          />\n        </div>\n      </div>\n\n      <div>\n        <Label htmlFor=\"termsAndConditions\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n          الشروط والأحكام\n        </Label>\n        <textarea\n          id=\"termsAndConditions\"\n          rows={6}\n          value={config.printSettings.termsAndConditions}\n          onChange={(e) => handleNestedInputChange('printSettings', null, 'termsAndConditions', e.target.value)}\n          className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n          placeholder=\"أدخل الشروط والأحكام التي ستظهر في الفواتير\"\n        />\n      </div>\n    </div>\n  );\n\n  const renderContent = () => {\n    switch (activeTab) {\n      case 'basic':\n        return renderBasicInfo();\n      case 'contact':\n        return renderContactInfo();\n      case 'bank':\n        return renderBankInfo();\n      case 'system':\n        return renderSystemSettings();\n      default:\n        return renderBasicInfo();\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* العنوان */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n      >\n        <div className=\"flex items-center justify-between mb-6\">\n          <div className=\"flex items-center\">\n            <BuildingOfficeIcon className=\"w-8 h-8 text-blue-600 ml-3\" />\n            <div>\n              <h2 className=\"text-2xl font-bold text-gray-900\">إعدادات الشركة</h2>\n              <p className=\"text-gray-600\">إدارة معلومات الشركة والإعدادات العامة</p>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center space-x-3 space-x-reverse\">\n            <Button\n              onClick={resetToDefaults}\n              variant=\"outline\"\n              disabled={isSaving}\n            >\n              إعادة تعيين\n            </Button>\n            <Button\n              onClick={handleSave}\n              disabled={isSaving}\n              className=\"bg-blue-600 hover:bg-blue-700 text-white\"\n            >\n              {isSaving ? (\n                <div className=\"flex items-center\">\n                  <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ml-2\"></div>\n                  جاري الحفظ...\n                </div>\n              ) : (\n                'حفظ التغييرات'\n              )}\n            </Button>\n          </div>\n        </div>\n      </motion.div>\n\n      {/* التبويبات */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.1 }}\n      >\n        <Card>\n          <CardHeader>\n            <div className=\"flex space-x-1 space-x-reverse\">\n              {tabs.map((tab) => {\n                const Icon = tab.icon;\n                return (\n                  <button\n                    key={tab.id}\n                    onClick={() => setActiveTab(tab.id)}\n                    className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors ${\n                      activeTab === tab.id\n                        ? 'bg-blue-100 text-blue-700 border border-blue-200'\n                        : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'\n                    }`}\n                  >\n                    <Icon className=\"w-4 h-4 ml-2\" />\n                    {tab.name}\n                  </button>\n                );\n              })}\n            </div>\n          </CardHeader>\n\n          <CardContent>\n            {renderContent()}\n          </CardContent>\n        </Card>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default CompanySettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,kBAAkB,EAClBC,eAAe,EACfC,uBAAuB,EACvBC,gBAAgB,EAChBC,aAAa,EACbC,SAAS,EACTC,YAAY,QACP,6BAA6B;AACpC,SAASC,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,SAAS,QAAQ,YAAY;AACrE,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,gBAAgB,EAAEC,iBAAiB,EAAEC,qBAAqB,QAAQ,4BAA4B;AACvG,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC6B,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC+B,MAAM,EAAEC,SAAS,CAAC,GAAGhC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,OAAO,CAAC;EAEnD,MAAMmC,IAAI,GAAG,CACX;IAAEC,EAAE,EAAE,OAAO;IAAEC,IAAI,EAAE,oBAAoB;IAAEC,IAAI,EAAEnC;EAAmB,CAAC,EACrE;IAAEiC,EAAE,EAAE,SAAS;IAAEC,IAAI,EAAE,kBAAkB;IAAEC,IAAI,EAAE9B;EAAU,CAAC,EAC5D;IAAE4B,EAAE,EAAE,MAAM;IAAEC,IAAI,EAAE,oBAAoB;IAAEC,IAAI,EAAE/B;EAAc,CAAC,EAC/D;IAAE6B,EAAE,EAAE,QAAQ;IAAEC,IAAI,EAAE,gBAAgB;IAAEC,IAAI,EAAEhC;EAAiB,CAAC,CACjE;EAEDL,SAAS,CAAC,MAAM;IACdsC,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI;MACF,MAAMC,aAAa,GAAGvB,gBAAgB,CAAC,CAAC;MACxCS,SAAS,CAACc,aAAa,CAAC;IAC1B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdrB,KAAK,CAACqB,KAAK,CAAC,6BAA6B,CAAC;MAC1CC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;IACtB,CAAC,SAAS;MACRb,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMe,iBAAiB,GAAGA,CAACC,OAAO,EAAEC,KAAK,EAAEC,KAAK,KAAK;IACnDpB,SAAS,CAACqB,IAAI,IAAI;MAChB,MAAMC,SAAS,GAAG;QAAE,GAAGD;MAAK,CAAC;MAE7B,IAAIH,OAAO,EAAE;QACXI,SAAS,CAACJ,OAAO,CAAC,GAAG;UACnB,GAAGI,SAAS,CAACJ,OAAO,CAAC;UACrB,CAACC,KAAK,GAAGC;QACX,CAAC;MACH,CAAC,MAAM;QACLE,SAAS,CAACH,KAAK,CAAC,GAAGC,KAAK;MAC1B;MAEA,OAAOE,SAAS;IAClB,CAAC,CAAC;;IAEF;IACA,IAAIjB,MAAM,CAACc,KAAK,CAAC,EAAE;MACjBb,SAAS,CAACe,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACF,KAAK,GAAG;MACX,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMI,uBAAuB,GAAGA,CAACL,OAAO,EAAEM,UAAU,EAAEL,KAAK,EAAEC,KAAK,KAAK;IACrEpB,SAAS,CAACqB,IAAI,KAAK;MACjB,GAAGA,IAAI;MACP,CAACH,OAAO,GAAG;QACT,GAAGG,IAAI,CAACH,OAAO,CAAC;QAChB,CAACM,UAAU,GAAG;UACZ,GAAGH,IAAI,CAACH,OAAO,CAAC,CAACM,UAAU,CAAC;UAC5B,CAACL,KAAK,GAAGC;QACX;MACF;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMK,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7BrB,WAAW,CAAC,IAAI,CAAC;IAEjB,IAAI;MACF,MAAMsB,UAAU,GAAGjC,qBAAqB,CAACM,MAAM,CAAC;MAEhD,IAAI,CAAC2B,UAAU,CAACC,OAAO,EAAE;QACvB,MAAMC,QAAQ,GAAG,CAAC,CAAC;QACnBF,UAAU,CAACrB,MAAM,CAACwB,OAAO,CAACd,KAAK,IAAI;UACjCa,QAAQ,CAACb,KAAK,CAAC,GAAGA,KAAK;QACzB,CAAC,CAAC;QACFT,SAAS,CAACsB,QAAQ,CAAC;QACnBlC,KAAK,CAACqB,KAAK,CAAC,6BAA6B,CAAC;QAC1C;MACF;MAEA,IAAIvB,iBAAiB,CAACO,MAAM,CAAC,EAAE;QAC7BL,KAAK,CAACoC,OAAO,CAAC,6BAA6B,CAAC;QAC5CxB,SAAS,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,MAAM;QACLZ,KAAK,CAACqB,KAAK,CAAC,6BAA6B,CAAC;MAC5C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdrB,KAAK,CAACqB,KAAK,CAAC,mBAAmB,CAAC;MAChCC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;IACtB,CAAC,SAAS;MACRX,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAM2B,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIC,MAAM,CAACC,OAAO,CAAC,6CAA6C,CAAC,EAAE;MACjEC,YAAY,CAACC,UAAU,CAAC,gBAAgB,CAAC;MACzCtB,UAAU,CAAC,CAAC;MACZnB,KAAK,CAACoC,OAAO,CAAC,0BAA0B,CAAC;IAC3C;EACF,CAAC;EAED,IAAI7B,SAAS,EAAE;IACb,oBACEL,OAAA;MAAKwC,SAAS,EAAC,sCAAsC;MAAAC,QAAA,eACnDzC,OAAA;QAAKwC,SAAS,EAAC;MAA8D;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjF,CAAC;EAEV;EAEA,IAAI,CAAC1C,MAAM,EAAE;IACX,oBACEH,OAAA;MAAKwC,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BzC,OAAA,CAACjB,uBAAuB;QAACyD,SAAS,EAAC;MAAqC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3E7C,OAAA;QAAGwC,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAA2B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC5D7C,OAAA,CAACR,MAAM;QAACsD,OAAO,EAAE7B,UAAW;QAACuB,SAAS,EAAC,MAAM;QAAAC,QAAA,EAAC;MAE9C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,MAAME,eAAe,GAAGA,CAAA,kBACtB/C,OAAA;IAAKwC,SAAS,EAAC,WAAW;IAAAC,QAAA,eACxBzC,OAAA;MAAKwC,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpDzC,OAAA;QAAAyC,QAAA,gBACEzC,OAAA,CAACN,KAAK;UAACsD,OAAO,EAAC,aAAa;UAACR,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAEtF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR7C,OAAA,CAACP,KAAK;UACJqB,EAAE,EAAC,aAAa;UAChBmC,IAAI,EAAC,MAAM;UACXzB,KAAK,EAAErB,MAAM,CAAC+C,WAAW,CAACnC,IAAK;UAC/BoC,QAAQ,EAAGC,CAAC,IAAKzB,uBAAuB,CAAC,aAAa,EAAE,IAAI,EAAE,MAAM,EAAEyB,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;UACtF8B,WAAW,EAAC,yDAAY;UACxBd,SAAS,EAAE/B,MAAM,CAACM,IAAI,GAAG,gBAAgB,GAAG;QAAG;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,EACDpC,MAAM,CAACM,IAAI,iBAAIf,OAAA;UAAGwC,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAEhC,MAAM,CAACM;QAAI;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC,eAEN7C,OAAA;QAAAyC,QAAA,gBACEzC,OAAA,CAACN,KAAK;UAACsD,OAAO,EAAC,eAAe;UAACR,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAExF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR7C,OAAA,CAACP,KAAK;UACJqB,EAAE,EAAC,eAAe;UAClBmC,IAAI,EAAC,MAAM;UACXzB,KAAK,EAAErB,MAAM,CAAC+C,WAAW,CAACK,MAAM,IAAI,EAAG;UACvCJ,QAAQ,EAAGC,CAAC,IAAKzB,uBAAuB,CAAC,aAAa,EAAE,IAAI,EAAE,QAAQ,EAAEyB,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;UACxF8B,WAAW,EAAC;QAAyB;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN7C,OAAA;QAAAyC,QAAA,gBACEzC,OAAA,CAACN,KAAK;UAACsD,OAAO,EAAC,WAAW;UAACR,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAEpF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR7C,OAAA,CAACP,KAAK;UACJqB,EAAE,EAAC,WAAW;UACdmC,IAAI,EAAC,MAAM;UACXzB,KAAK,EAAErB,MAAM,CAAC+C,WAAW,CAACM,SAAU;UACpCL,QAAQ,EAAGC,CAAC,IAAKzB,uBAAuB,CAAC,aAAa,EAAE,IAAI,EAAE,WAAW,EAAEyB,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;UAC3F8B,WAAW,EAAC,iBAAiB;UAC7BG,SAAS,EAAE,EAAG;UACdjB,SAAS,EAAE/B,MAAM,CAAC+C,SAAS,GAAG,gBAAgB,GAAG;QAAG;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,EACDpC,MAAM,CAAC+C,SAAS,iBAAIxD,OAAA;UAAGwC,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAEhC,MAAM,CAAC+C;QAAS;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjF,CAAC,eAEN7C,OAAA;QAAAyC,QAAA,gBACEzC,OAAA,CAACN,KAAK;UAACsD,OAAO,EAAC,UAAU;UAACR,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAEnF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR7C,OAAA,CAACP,KAAK;UACJqB,EAAE,EAAC,UAAU;UACbmC,IAAI,EAAC,MAAM;UACXzB,KAAK,EAAErB,MAAM,CAAC+C,WAAW,CAACQ,QAAQ,IAAI,EAAG;UACzCP,QAAQ,EAAGC,CAAC,IAAKzB,uBAAuB,CAAC,aAAa,EAAE,IAAI,EAAE,UAAU,EAAEyB,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;UAC1F8B,WAAW,EAAC;QAAY;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN7C,OAAA;QAAAyC,QAAA,gBACEzC,OAAA,CAACN,KAAK;UAACsD,OAAO,EAAC,eAAe;UAACR,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAExF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR7C,OAAA,CAACP,KAAK;UACJqB,EAAE,EAAC,eAAe;UAClBmC,IAAI,EAAC,MAAM;UACXzB,KAAK,EAAErB,MAAM,CAAC+C,WAAW,CAACS,aAAa,IAAI,EAAG;UAC9CR,QAAQ,EAAGC,CAAC,IAAKzB,uBAAuB,CAAC,aAAa,EAAE,IAAI,EAAE,eAAe,EAAEyB,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;UAC/F8B,WAAW,EAAC;QAAY;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMe,iBAAiB,GAAGA,CAAA,kBACxB5D,OAAA;IAAKwC,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBzC,OAAA;MAAKwC,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpDzC,OAAA;QAAAyC,QAAA,gBACEzC,OAAA,CAACN,KAAK;UAACsD,OAAO,EAAC,OAAO;UAACR,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAEhF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR7C,OAAA,CAACP,KAAK;UACJqB,EAAE,EAAC,OAAO;UACVmC,IAAI,EAAC,KAAK;UACVzB,KAAK,EAAErB,MAAM,CAAC+C,WAAW,CAACW,OAAO,CAACC,KAAM;UACxCX,QAAQ,EAAGC,CAAC,IAAKzB,uBAAuB,CAAC,aAAa,EAAE,SAAS,EAAE,OAAO,EAAEyB,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;UAC5F8B,WAAW,EAAC,YAAY;UACxBd,SAAS,EAAE/B,MAAM,CAACqD,KAAK,GAAG,gBAAgB,GAAG;QAAG;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,EACDpC,MAAM,CAACqD,KAAK,iBAAI9D,OAAA;UAAGwC,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAEhC,MAAM,CAACqD;QAAK;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE,CAAC,eAEN7C,OAAA;QAAAyC,QAAA,gBACEzC,OAAA,CAACN,KAAK;UAACsD,OAAO,EAAC,QAAQ;UAACR,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAEjF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR7C,OAAA,CAACP,KAAK;UACJqB,EAAE,EAAC,QAAQ;UACXmC,IAAI,EAAC,KAAK;UACVzB,KAAK,EAAErB,MAAM,CAAC+C,WAAW,CAACW,OAAO,CAACE,MAAM,IAAI,EAAG;UAC/CZ,QAAQ,EAAGC,CAAC,IAAKzB,uBAAuB,CAAC,aAAa,EAAE,SAAS,EAAE,QAAQ,EAAEyB,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;UAC7F8B,WAAW,EAAC;QAAY;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN7C,OAAA;QAAAyC,QAAA,gBACEzC,OAAA,CAACN,KAAK;UAACsD,OAAO,EAAC,OAAO;UAACR,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAEhF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR7C,OAAA,CAACP,KAAK;UACJqB,EAAE,EAAC,OAAO;UACVmC,IAAI,EAAC,OAAO;UACZzB,KAAK,EAAErB,MAAM,CAAC+C,WAAW,CAACW,OAAO,CAACG,KAAM;UACxCb,QAAQ,EAAGC,CAAC,IAAKzB,uBAAuB,CAAC,aAAa,EAAE,SAAS,EAAE,OAAO,EAAEyB,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;UAC5F8B,WAAW,EAAC,kBAAkB;UAC9Bd,SAAS,EAAE/B,MAAM,CAACuD,KAAK,GAAG,gBAAgB,GAAG;QAAG;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,EACDpC,MAAM,CAACuD,KAAK,iBAAIhE,OAAA;UAAGwC,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAEhC,MAAM,CAACuD;QAAK;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE,CAAC,eAEN7C,OAAA;QAAAyC,QAAA,gBACEzC,OAAA,CAACN,KAAK;UAACsD,OAAO,EAAC,SAAS;UAACR,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAElF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR7C,OAAA,CAACP,KAAK;UACJqB,EAAE,EAAC,SAAS;UACZmC,IAAI,EAAC,KAAK;UACVzB,KAAK,EAAErB,MAAM,CAAC+C,WAAW,CAACW,OAAO,CAACI,OAAO,IAAI,EAAG;UAChDd,QAAQ,EAAGC,CAAC,IAAKzB,uBAAuB,CAAC,aAAa,EAAE,SAAS,EAAE,SAAS,EAAEyB,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;UAC9F8B,WAAW,EAAC;QAAiB;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN7C,OAAA;QAAAyC,QAAA,gBACEzC,OAAA,CAACN,KAAK;UAACsD,OAAO,EAAC,KAAK;UAACR,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAE9E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR7C,OAAA,CAACP,KAAK;UACJqB,EAAE,EAAC,KAAK;UACRmC,IAAI,EAAC,KAAK;UACVzB,KAAK,EAAErB,MAAM,CAAC+C,WAAW,CAACW,OAAO,CAACK,GAAG,IAAI,EAAG;UAC5Cf,QAAQ,EAAGC,CAAC,IAAKzB,uBAAuB,CAAC,aAAa,EAAE,SAAS,EAAE,KAAK,EAAEyB,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;UAC1F8B,WAAW,EAAC;QAAY;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN7C,OAAA;MAAAyC,QAAA,gBACEzC,OAAA,CAACN,KAAK;QAACsD,OAAO,EAAC,QAAQ;QAACR,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAC;MAEjF;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACR7C,OAAA;QACEc,EAAE,EAAC,QAAQ;QACXqD,IAAI,EAAE,CAAE;QACR3C,KAAK,EAAErB,MAAM,CAAC+C,WAAW,CAACkB,OAAO,CAACC,MAAM,IAAI,EAAG;QAC/ClB,QAAQ,EAAGC,CAAC,IAAKzB,uBAAuB,CAAC,aAAa,EAAE,SAAS,EAAE,QAAQ,EAAEyB,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;QAC7FgB,SAAS,EAAC,2GAA2G;QACrHc,WAAW,EAAC;MAA0B;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEN7C,OAAA;MAAKwC,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpDzC,OAAA;QAAAyC,QAAA,gBACEzC,OAAA,CAACN,KAAK;UAACsD,OAAO,EAAC,MAAM;UAACR,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAE/E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR7C,OAAA,CAACP,KAAK;UACJqB,EAAE,EAAC,MAAM;UACTmC,IAAI,EAAC,MAAM;UACXzB,KAAK,EAAErB,MAAM,CAAC+C,WAAW,CAACkB,OAAO,CAACE,IAAK;UACvCnB,QAAQ,EAAGC,CAAC,IAAKzB,uBAAuB,CAAC,aAAa,EAAE,SAAS,EAAE,MAAM,EAAEyB,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;UAC3F8B,WAAW,EAAC,sCAAQ;UACpBd,SAAS,EAAE/B,MAAM,CAAC6D,IAAI,GAAG,gBAAgB,GAAG;QAAG;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,EACDpC,MAAM,CAAC6D,IAAI,iBAAItE,OAAA;UAAGwC,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAEhC,MAAM,CAAC6D;QAAI;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC,eAEN7C,OAAA;QAAAyC,QAAA,gBACEzC,OAAA,CAACN,KAAK;UAACsD,OAAO,EAAC,QAAQ;UAACR,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAEjF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR7C,OAAA,CAACP,KAAK;UACJqB,EAAE,EAAC,QAAQ;UACXmC,IAAI,EAAC,MAAM;UACXzB,KAAK,EAAErB,MAAM,CAAC+C,WAAW,CAACkB,OAAO,CAACG,MAAM,IAAI,EAAG;UAC/CpB,QAAQ,EAAGC,CAAC,IAAKzB,uBAAuB,CAAC,aAAa,EAAE,SAAS,EAAE,QAAQ,EAAEyB,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;UAC7F8B,WAAW,EAAC;QAAc;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN7C,OAAA;QAAAyC,QAAA,gBACEzC,OAAA,CAACN,KAAK;UAACsD,OAAO,EAAC,YAAY;UAACR,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAErF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR7C,OAAA,CAACP,KAAK;UACJqB,EAAE,EAAC,YAAY;UACfmC,IAAI,EAAC,MAAM;UACXzB,KAAK,EAAErB,MAAM,CAAC+C,WAAW,CAACkB,OAAO,CAACI,UAAU,IAAI,EAAG;UACnDrB,QAAQ,EAAGC,CAAC,IAAKzB,uBAAuB,CAAC,aAAa,EAAE,SAAS,EAAE,YAAY,EAAEyB,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;UACjG8B,WAAW,EAAC;QAAO;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAM4B,cAAc,GAAGA,CAAA,kBACrBzE,OAAA;IAAKwC,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBzC,OAAA;MAAKwC,SAAS,EAAC,2BAA2B;MAAAC,QAAA,eACxCzC,OAAA;QAAGwC,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EAAC;MAErC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAEN7C,OAAA;MAAKwC,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpDzC,OAAA;QAAAyC,QAAA,gBACEzC,OAAA,CAACN,KAAK;UAACsD,OAAO,EAAC,UAAU;UAACR,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAEnF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR7C,OAAA,CAACP,KAAK;UACJqB,EAAE,EAAC,UAAU;UACbmC,IAAI,EAAC,MAAM;UACXzB,KAAK,EAAErB,MAAM,CAAC+C,WAAW,CAACwB,QAAQ,CAACC,QAAQ,IAAI,EAAG;UAClDxB,QAAQ,EAAGC,CAAC,IAAKzB,uBAAuB,CAAC,aAAa,EAAE,UAAU,EAAE,UAAU,EAAEyB,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;UAChG8B,WAAW,EAAC;QAAsB;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN7C,OAAA;QAAAyC,QAAA,gBACEzC,OAAA,CAACN,KAAK;UAACsD,OAAO,EAAC,eAAe;UAACR,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAExF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR7C,OAAA,CAACP,KAAK;UACJqB,EAAE,EAAC,eAAe;UAClBmC,IAAI,EAAC,MAAM;UACXzB,KAAK,EAAErB,MAAM,CAAC+C,WAAW,CAACwB,QAAQ,CAACE,aAAa,IAAI,EAAG;UACvDzB,QAAQ,EAAGC,CAAC,IAAKzB,uBAAuB,CAAC,aAAa,EAAE,UAAU,EAAE,eAAe,EAAEyB,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;UACrG8B,WAAW,EAAC;QAAW;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN7C,OAAA;QAAAyC,QAAA,gBACEzC,OAAA,CAACN,KAAK;UAACsD,OAAO,EAAC,MAAM;UAACR,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAE/E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR7C,OAAA,CAACP,KAAK;UACJqB,EAAE,EAAC,MAAM;UACTmC,IAAI,EAAC,MAAM;UACXzB,KAAK,EAAErB,MAAM,CAAC+C,WAAW,CAACwB,QAAQ,CAACG,IAAI,IAAI,EAAG;UAC9C1B,QAAQ,EAAGC,CAAC,IAAKzB,uBAAuB,CAAC,aAAa,EAAE,UAAU,EAAE,MAAM,EAAEyB,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;UAC5F8B,WAAW,EAAC;QAA0B;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN7C,OAAA;QAAAyC,QAAA,gBACEzC,OAAA,CAACN,KAAK;UAACsD,OAAO,EAAC,WAAW;UAACR,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAEpF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR7C,OAAA,CAACP,KAAK;UACJqB,EAAE,EAAC,WAAW;UACdmC,IAAI,EAAC,MAAM;UACXzB,KAAK,EAAErB,MAAM,CAAC+C,WAAW,CAACwB,QAAQ,CAACI,SAAS,IAAI,EAAG;UACnD3B,QAAQ,EAAGC,CAAC,IAAKzB,uBAAuB,CAAC,aAAa,EAAE,UAAU,EAAE,WAAW,EAAEyB,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;UACjG8B,WAAW,EAAC;QAAU;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMkC,oBAAoB,GAAGA,CAAA,kBAC3B/E,OAAA;IAAKwC,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBzC,OAAA;MAAKwC,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpDzC,OAAA;QAAAyC,QAAA,gBACEzC,OAAA,CAACN,KAAK;UAACsD,OAAO,EAAC,UAAU;UAACR,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAEnF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR7C,OAAA,CAACP,KAAK;UACJqB,EAAE,EAAC,UAAU;UACbmC,IAAI,EAAC,MAAM;UACXzB,KAAK,EAAErB,MAAM,CAAC6E,cAAc,CAACC,QAAS;UACtC9B,QAAQ,EAAGC,CAAC,IAAKzB,uBAAuB,CAAC,gBAAgB,EAAE,IAAI,EAAE,UAAU,EAAEyB,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;UAC7F8B,WAAW,EAAC;QAAK;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN7C,OAAA;QAAAyC,QAAA,gBACEzC,OAAA,CAACN,KAAK;UAACsD,OAAO,EAAC,SAAS;UAACR,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAElF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR7C,OAAA,CAACP,KAAK;UACJqB,EAAE,EAAC,SAAS;UACZmC,IAAI,EAAC,QAAQ;UACbzB,KAAK,EAAErB,MAAM,CAAC6E,cAAc,CAACE,cAAe;UAC5C/B,QAAQ,EAAGC,CAAC,IAAKzB,uBAAuB,CAAC,gBAAgB,EAAE,IAAI,EAAE,gBAAgB,EAAEwD,UAAU,CAAC/B,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAC,CAAE;UAC/G8B,WAAW,EAAC,IAAI;UAChB8B,GAAG,EAAC,GAAG;UACPC,GAAG,EAAC;QAAK;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN7C,OAAA;QAAAyC,QAAA,gBACEzC,OAAA,CAACN,KAAK;UAACsD,OAAO,EAAC,eAAe;UAACR,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAExF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR7C,OAAA,CAACP,KAAK;UACJqB,EAAE,EAAC,eAAe;UAClBmC,IAAI,EAAC,MAAM;UACXzB,KAAK,EAAErB,MAAM,CAAC6E,cAAc,CAACM,aAAc;UAC3CnC,QAAQ,EAAGC,CAAC,IAAKzB,uBAAuB,CAAC,gBAAgB,EAAE,IAAI,EAAE,eAAe,EAAEyB,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;UAClG8B,WAAW,EAAC;QAAK;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN7C,OAAA;QAAAyC,QAAA,gBACEzC,OAAA,CAACN,KAAK;UAACsD,OAAO,EAAC,mBAAmB;UAACR,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAE5F;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR7C,OAAA,CAACP,KAAK;UACJqB,EAAE,EAAC,mBAAmB;UACtBmC,IAAI,EAAC,QAAQ;UACbzB,KAAK,EAAErB,MAAM,CAAC6E,cAAc,CAACO,iBAAkB;UAC/CpC,QAAQ,EAAGC,CAAC,IAAKzB,uBAAuB,CAAC,gBAAgB,EAAE,IAAI,EAAE,mBAAmB,EAAE6D,QAAQ,CAACpC,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAC,CAAE;UAChH8B,WAAW,EAAC,IAAI;UAChB8B,GAAG,EAAC;QAAG;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN7C,OAAA;MAAAyC,QAAA,gBACEzC,OAAA,CAACN,KAAK;QAACsD,OAAO,EAAC,oBAAoB;QAACR,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAC;MAE7F;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACR7C,OAAA;QACEc,EAAE,EAAC,oBAAoB;QACvBqD,IAAI,EAAE,CAAE;QACR3C,KAAK,EAAErB,MAAM,CAACsF,aAAa,CAACC,kBAAmB;QAC/CvC,QAAQ,EAAGC,CAAC,IAAKzB,uBAAuB,CAAC,eAAe,EAAE,IAAI,EAAE,oBAAoB,EAAEyB,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;QACtGgB,SAAS,EAAC,2GAA2G;QACrHc,WAAW,EAAC;MAA6C;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAM8C,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQhF,SAAS;MACf,KAAK,OAAO;QACV,OAAOoC,eAAe,CAAC,CAAC;MAC1B,KAAK,SAAS;QACZ,OAAOa,iBAAiB,CAAC,CAAC;MAC5B,KAAK,MAAM;QACT,OAAOa,cAAc,CAAC,CAAC;MACzB,KAAK,QAAQ;QACX,OAAOM,oBAAoB,CAAC,CAAC;MAC/B;QACE,OAAOhC,eAAe,CAAC,CAAC;IAC5B;EACF,CAAC;EAED,oBACE/C,OAAA;IAAKwC,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBzC,OAAA,CAACpB,MAAM,CAACgH,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAtD,QAAA,eAE9BzC,OAAA;QAAKwC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDzC,OAAA;UAAKwC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCzC,OAAA,CAACnB,kBAAkB;YAAC2D,SAAS,EAAC;UAA4B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7D7C,OAAA;YAAAyC,QAAA,gBACEzC,OAAA;cAAIwC,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpE7C,OAAA;cAAGwC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAsC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7C,OAAA;UAAKwC,SAAS,EAAC,6CAA6C;UAAAC,QAAA,gBAC1DzC,OAAA,CAACR,MAAM;YACLsD,OAAO,EAAEX,eAAgB;YACzB8D,OAAO,EAAC,SAAS;YACjBC,QAAQ,EAAE3F,QAAS;YAAAkC,QAAA,EACpB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT7C,OAAA,CAACR,MAAM;YACLsD,OAAO,EAAEjB,UAAW;YACpBqE,QAAQ,EAAE3F,QAAS;YACnBiC,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAEnDlC,QAAQ,gBACPP,OAAA;cAAKwC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCzC,OAAA;gBAAKwC,SAAS,EAAC;cAAmF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,8DAE3G;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GAEN;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGb7C,OAAA,CAACpB,MAAM,CAACgH,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BI,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAAA3D,QAAA,eAE3BzC,OAAA,CAACZ,IAAI;QAAAqD,QAAA,gBACHzC,OAAA,CAACV,UAAU;UAAAmD,QAAA,eACTzC,OAAA;YAAKwC,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAC5C5B,IAAI,CAACwF,GAAG,CAAEC,GAAG,IAAK;cACjB,MAAMC,IAAI,GAAGD,GAAG,CAACtF,IAAI;cACrB,oBACEhB,OAAA;gBAEE8C,OAAO,EAAEA,CAAA,KAAMlC,YAAY,CAAC0F,GAAG,CAACxF,EAAE,CAAE;gBACpC0B,SAAS,EAAE,gFACT7B,SAAS,KAAK2F,GAAG,CAACxF,EAAE,GAChB,kDAAkD,GAClD,qDAAqD,EACxD;gBAAA2B,QAAA,gBAEHzC,OAAA,CAACuG,IAAI;kBAAC/D,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAChCyD,GAAG,CAACvF,IAAI;cAAA,GATJuF,GAAG,CAACxF,EAAE;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUL,CAAC;YAEb,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAEb7C,OAAA,CAACX,WAAW;UAAAoD,QAAA,EACTkD,aAAa,CAAC;QAAC;UAAAjD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAAC3C,EAAA,CAtjBID,eAAe;AAAAuG,EAAA,GAAfvG,eAAe;AAwjBrB,eAAeA,eAAe;AAAC,IAAAuG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}