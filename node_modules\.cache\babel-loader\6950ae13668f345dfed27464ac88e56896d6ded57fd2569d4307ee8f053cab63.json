{"ast": null, "code": "// مولد QR Code متوافق مع المرحلة الثانية من الفوترة الإلكترونية السعودية\n// وفقاً لمعايير هيئة الزكاة والضريبة والجمارك\n\n/**\n * تشفير النص إلى Base64\n * @param {string} text - النص المراد تشفيره\n * @returns {string} النص مشفر بـ Base64\n */\nconst encodeToBase64 = text => {\n  return btoa(unescape(encodeURIComponent(text)));\n};\n\n/**\n * تحويل الرقم إلى TLV (Tag-Length-Value) format\n * @param {number} tag - رقم العلامة\n * @param {string} value - القيمة\n * @returns {string} القيمة بصيغة TLV\n */\nconst toTLV = (tag, value) => {\n  const tagHex = tag.toString(16).padStart(2, '0');\n  const lengthHex = value.length.toString(16).padStart(2, '0');\n  const valueHex = Array.from(value).map(char => char.charCodeAt(0).toString(16).padStart(2, '0')).join('');\n  return tagHex + lengthHex + valueHex;\n};\n\n/**\n * إنشاء QR Code متوافق مع المرحلة الثانية\n * @param {Object} invoiceData - بيانات الفاتورة\n * @returns {string} محتوى QR Code\n */\nexport const generateQRCodeData = invoiceData => {\n  try {\n    // تحميل إعدادات الشركة من التخزين المحلي\n    const savedSettings = localStorage.getItem('einvoice_settings');\n    const defaultSettings = {\n      companyName: 'شركة إدارة الأعمال',\n      vatNumber: '300000000000003'\n    };\n    const companySettings = savedSettings ? JSON.parse(savedSettings) : defaultSettings;\n    const companyData = {\n      name: companySettings.companyName || defaultSettings.companyName,\n      vatNumber: companySettings.vatNumber || defaultSettings.vatNumber,\n      timestamp: new Date().toISOString(),\n      invoiceTotal: invoiceData.total.toFixed(2),\n      vatAmount: invoiceData.tax.toFixed(2)\n    };\n\n    // إنشاء البيانات بصيغة TLV وفقاً للمعايير السعودية\n    let tlvData = '';\n\n    // Tag 1: اسم البائع (Seller Name)\n    tlvData += toTLV(1, companyData.name);\n\n    // Tag 2: الرقم الضريبي للبائع (Seller VAT Number)\n    tlvData += toTLV(2, companyData.vatNumber);\n\n    // Tag 3: الطابع الزمني (Timestamp)\n    tlvData += toTLV(3, companyData.timestamp);\n\n    // Tag 4: إجمالي الفاتورة شامل الضريبة (Invoice Total with VAT)\n    tlvData += toTLV(4, companyData.invoiceTotal);\n\n    // Tag 5: إجمالي ضريبة القيمة المضافة (VAT Total)\n    tlvData += toTLV(5, companyData.vatAmount);\n\n    // تحويل البيانات إلى Base64\n    const base64Data = encodeToBase64(tlvData);\n    return base64Data;\n  } catch (error) {\n    console.error('خطأ في إنشاء QR Code:', error);\n    return '';\n  }\n};\n\n/**\n * إنشاء QR Code مبسط للاختبار\n * @param {Object} invoiceData - بيانات الفاتورة\n * @returns {string} محتوى QR Code مبسط\n */\nexport const generateSimpleQRCode = invoiceData => {\n  const qrData = {\n    seller: 'شركة إدارة الأعمال',\n    vatNumber: '300000000000003',\n    invoiceNumber: invoiceData.invoiceNumber,\n    date: invoiceData.date,\n    total: invoiceData.total.toFixed(2),\n    vat: invoiceData.tax.toFixed(2),\n    customer: invoiceData.customerName\n  };\n  return JSON.stringify(qrData);\n};\n\n/**\n * التحقق من صحة الرقم الضريبي السعودي\n * @param {string} vatNumber - الرقم الضريبي\n * @returns {boolean} صحة الرقم الضريبي\n */\nexport const validateSaudiVATNumber = vatNumber => {\n  // الرقم الضريبي السعودي يجب أن يكون 15 رقم\n  const vatRegex = /^[0-9]{15}$/;\n  return vatRegex.test(vatNumber);\n};\n\n/**\n * تنسيق التاريخ للفوترة الإلكترونية\n * @param {Date} date - التاريخ\n * @returns {string} التاريخ منسق\n */\nexport const formatDateForEInvoice = date => {\n  return new Date(date).toISOString();\n};\n\n/**\n * حساب hash للفاتورة (للمرحلة الثانية المتقدمة)\n * @param {Object} invoiceData - بيانات الفاتورة\n * @returns {string} hash الفاتورة\n */\nexport const generateInvoiceHash = invoiceData => {\n  const dataString = JSON.stringify({\n    invoiceNumber: invoiceData.invoiceNumber,\n    date: invoiceData.date,\n    total: invoiceData.total,\n    items: invoiceData.items\n  });\n\n  // استخدام hash بسيط (في الإنتاج يجب استخدام SHA-256)\n  let hash = 0;\n  for (let i = 0; i < dataString.length; i++) {\n    const char = dataString.charCodeAt(i);\n    hash = (hash << 5) - hash + char;\n    hash = hash & hash; // تحويل إلى 32bit integer\n  }\n  return Math.abs(hash).toString(16);\n};", "map": {"version": 3, "names": ["encodeToBase64", "text", "btoa", "unescape", "encodeURIComponent", "toTLV", "tag", "value", "tagHex", "toString", "padStart", "lengthHex", "length", "valueHex", "Array", "from", "map", "char", "charCodeAt", "join", "generateQRCodeData", "invoiceData", "savedSettings", "localStorage", "getItem", "defaultSettings", "companyName", "vatNumber", "companySettings", "JSON", "parse", "companyData", "name", "timestamp", "Date", "toISOString", "invoiceTotal", "total", "toFixed", "vatAmount", "tax", "tlvData", "base64Data", "error", "console", "generateSimpleQRCode", "qrData", "seller", "invoiceNumber", "date", "vat", "customer", "customerName", "stringify", "validateSaudiVATNumber", "vatRegex", "test", "formatDateForEInvoice", "generateInvoiceHash", "dataString", "items", "hash", "i", "Math", "abs"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/src/utils/qrCodeGenerator.js"], "sourcesContent": ["// مولد QR Code متوافق مع المرحلة الثانية من الفوترة الإلكترونية السعودية\n// وفقاً لمعايير هيئة الزكاة والضريبة والجمارك\n\n/**\n * تشفير النص إلى Base64\n * @param {string} text - النص المراد تشفيره\n * @returns {string} النص مشفر بـ Base64\n */\nconst encodeToBase64 = (text) => {\n  return btoa(unescape(encodeURIComponent(text)));\n};\n\n/**\n * تحويل الرقم إلى TLV (Tag-Length-Value) format\n * @param {number} tag - رقم العلامة\n * @param {string} value - القيمة\n * @returns {string} القيمة بصيغة TLV\n */\nconst toTLV = (tag, value) => {\n  const tagHex = tag.toString(16).padStart(2, '0');\n  const lengthHex = value.length.toString(16).padStart(2, '0');\n  const valueHex = Array.from(value)\n    .map(char => char.charCodeAt(0).toString(16).padStart(2, '0'))\n    .join('');\n\n  return tagHex + lengthHex + valueHex;\n};\n\n/**\n * إنشاء QR Code متوافق مع المرحلة الثانية\n * @param {Object} invoiceData - بيانات الفاتورة\n * @returns {string} محتوى QR Code\n */\nexport const generateQRCodeData = (invoiceData) => {\n  try {\n    // تحميل إعدادات الشركة من التخزين المحلي\n    const savedSettings = localStorage.getItem('einvoice_settings');\n    const defaultSettings = {\n      companyName: 'شركة إدارة الأعمال',\n      vatNumber: '300000000000003'\n    };\n\n    const companySettings = savedSettings ? JSON.parse(savedSettings) : defaultSettings;\n\n    const companyData = {\n      name: companySettings.companyName || defaultSettings.companyName,\n      vatNumber: companySettings.vatNumber || defaultSettings.vatNumber,\n      timestamp: new Date().toISOString(),\n      invoiceTotal: invoiceData.total.toFixed(2),\n      vatAmount: invoiceData.tax.toFixed(2)\n    };\n\n    // إنشاء البيانات بصيغة TLV وفقاً للمعايير السعودية\n    let tlvData = '';\n\n    // Tag 1: اسم البائع (Seller Name)\n    tlvData += toTLV(1, companyData.name);\n\n    // Tag 2: الرقم الضريبي للبائع (Seller VAT Number)\n    tlvData += toTLV(2, companyData.vatNumber);\n\n    // Tag 3: الطابع الزمني (Timestamp)\n    tlvData += toTLV(3, companyData.timestamp);\n\n    // Tag 4: إجمالي الفاتورة شامل الضريبة (Invoice Total with VAT)\n    tlvData += toTLV(4, companyData.invoiceTotal);\n\n    // Tag 5: إجمالي ضريبة القيمة المضافة (VAT Total)\n    tlvData += toTLV(5, companyData.vatAmount);\n\n    // تحويل البيانات إلى Base64\n    const base64Data = encodeToBase64(tlvData);\n\n    return base64Data;\n\n  } catch (error) {\n    console.error('خطأ في إنشاء QR Code:', error);\n    return '';\n  }\n};\n\n/**\n * إنشاء QR Code مبسط للاختبار\n * @param {Object} invoiceData - بيانات الفاتورة\n * @returns {string} محتوى QR Code مبسط\n */\nexport const generateSimpleQRCode = (invoiceData) => {\n  const qrData = {\n    seller: 'شركة إدارة الأعمال',\n    vatNumber: '300000000000003',\n    invoiceNumber: invoiceData.invoiceNumber,\n    date: invoiceData.date,\n    total: invoiceData.total.toFixed(2),\n    vat: invoiceData.tax.toFixed(2),\n    customer: invoiceData.customerName\n  };\n\n  return JSON.stringify(qrData);\n};\n\n/**\n * التحقق من صحة الرقم الضريبي السعودي\n * @param {string} vatNumber - الرقم الضريبي\n * @returns {boolean} صحة الرقم الضريبي\n */\nexport const validateSaudiVATNumber = (vatNumber) => {\n  // الرقم الضريبي السعودي يجب أن يكون 15 رقم\n  const vatRegex = /^[0-9]{15}$/;\n  return vatRegex.test(vatNumber);\n};\n\n/**\n * تنسيق التاريخ للفوترة الإلكترونية\n * @param {Date} date - التاريخ\n * @returns {string} التاريخ منسق\n */\nexport const formatDateForEInvoice = (date) => {\n  return new Date(date).toISOString();\n};\n\n/**\n * حساب hash للفاتورة (للمرحلة الثانية المتقدمة)\n * @param {Object} invoiceData - بيانات الفاتورة\n * @returns {string} hash الفاتورة\n */\nexport const generateInvoiceHash = (invoiceData) => {\n  const dataString = JSON.stringify({\n    invoiceNumber: invoiceData.invoiceNumber,\n    date: invoiceData.date,\n    total: invoiceData.total,\n    items: invoiceData.items\n  });\n\n  // استخدام hash بسيط (في الإنتاج يجب استخدام SHA-256)\n  let hash = 0;\n  for (let i = 0; i < dataString.length; i++) {\n    const char = dataString.charCodeAt(i);\n    hash = ((hash << 5) - hash) + char;\n    hash = hash & hash; // تحويل إلى 32bit integer\n  }\n\n  return Math.abs(hash).toString(16);\n};\n"], "mappings": "AAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMA,cAAc,GAAIC,IAAI,IAAK;EAC/B,OAAOC,IAAI,CAACC,QAAQ,CAACC,kBAAkB,CAACH,IAAI,CAAC,CAAC,CAAC;AACjD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,KAAK,GAAGA,CAACC,GAAG,EAAEC,KAAK,KAAK;EAC5B,MAAMC,MAAM,GAAGF,GAAG,CAACG,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EAChD,MAAMC,SAAS,GAAGJ,KAAK,CAACK,MAAM,CAACH,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EAC5D,MAAMG,QAAQ,GAAGC,KAAK,CAACC,IAAI,CAACR,KAAK,CAAC,CAC/BS,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC,CAACT,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAC7DS,IAAI,CAAC,EAAE,CAAC;EAEX,OAAOX,MAAM,GAAGG,SAAS,GAAGE,QAAQ;AACtC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMO,kBAAkB,GAAIC,WAAW,IAAK;EACjD,IAAI;IACF;IACA,MAAMC,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,mBAAmB,CAAC;IAC/D,MAAMC,eAAe,GAAG;MACtBC,WAAW,EAAE,oBAAoB;MACjCC,SAAS,EAAE;IACb,CAAC;IAED,MAAMC,eAAe,GAAGN,aAAa,GAAGO,IAAI,CAACC,KAAK,CAACR,aAAa,CAAC,GAAGG,eAAe;IAEnF,MAAMM,WAAW,GAAG;MAClBC,IAAI,EAAEJ,eAAe,CAACF,WAAW,IAAID,eAAe,CAACC,WAAW;MAChEC,SAAS,EAAEC,eAAe,CAACD,SAAS,IAAIF,eAAe,CAACE,SAAS;MACjEM,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnCC,YAAY,EAAEf,WAAW,CAACgB,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC;MAC1CC,SAAS,EAAElB,WAAW,CAACmB,GAAG,CAACF,OAAO,CAAC,CAAC;IACtC,CAAC;;IAED;IACA,IAAIG,OAAO,GAAG,EAAE;;IAEhB;IACAA,OAAO,IAAIpC,KAAK,CAAC,CAAC,EAAE0B,WAAW,CAACC,IAAI,CAAC;;IAErC;IACAS,OAAO,IAAIpC,KAAK,CAAC,CAAC,EAAE0B,WAAW,CAACJ,SAAS,CAAC;;IAE1C;IACAc,OAAO,IAAIpC,KAAK,CAAC,CAAC,EAAE0B,WAAW,CAACE,SAAS,CAAC;;IAE1C;IACAQ,OAAO,IAAIpC,KAAK,CAAC,CAAC,EAAE0B,WAAW,CAACK,YAAY,CAAC;;IAE7C;IACAK,OAAO,IAAIpC,KAAK,CAAC,CAAC,EAAE0B,WAAW,CAACQ,SAAS,CAAC;;IAE1C;IACA,MAAMG,UAAU,GAAG1C,cAAc,CAACyC,OAAO,CAAC;IAE1C,OAAOC,UAAU;EAEnB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC7C,OAAO,EAAE;EACX;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,oBAAoB,GAAIxB,WAAW,IAAK;EACnD,MAAMyB,MAAM,GAAG;IACbC,MAAM,EAAE,oBAAoB;IAC5BpB,SAAS,EAAE,iBAAiB;IAC5BqB,aAAa,EAAE3B,WAAW,CAAC2B,aAAa;IACxCC,IAAI,EAAE5B,WAAW,CAAC4B,IAAI;IACtBZ,KAAK,EAAEhB,WAAW,CAACgB,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC;IACnCY,GAAG,EAAE7B,WAAW,CAACmB,GAAG,CAACF,OAAO,CAAC,CAAC,CAAC;IAC/Ba,QAAQ,EAAE9B,WAAW,CAAC+B;EACxB,CAAC;EAED,OAAOvB,IAAI,CAACwB,SAAS,CAACP,MAAM,CAAC;AAC/B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMQ,sBAAsB,GAAI3B,SAAS,IAAK;EACnD;EACA,MAAM4B,QAAQ,GAAG,aAAa;EAC9B,OAAOA,QAAQ,CAACC,IAAI,CAAC7B,SAAS,CAAC;AACjC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM8B,qBAAqB,GAAIR,IAAI,IAAK;EAC7C,OAAO,IAAIf,IAAI,CAACe,IAAI,CAAC,CAACd,WAAW,CAAC,CAAC;AACrC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMuB,mBAAmB,GAAIrC,WAAW,IAAK;EAClD,MAAMsC,UAAU,GAAG9B,IAAI,CAACwB,SAAS,CAAC;IAChCL,aAAa,EAAE3B,WAAW,CAAC2B,aAAa;IACxCC,IAAI,EAAE5B,WAAW,CAAC4B,IAAI;IACtBZ,KAAK,EAAEhB,WAAW,CAACgB,KAAK;IACxBuB,KAAK,EAAEvC,WAAW,CAACuC;EACrB,CAAC,CAAC;;EAEF;EACA,IAAIC,IAAI,GAAG,CAAC;EACZ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,UAAU,CAAC/C,MAAM,EAAEkD,CAAC,EAAE,EAAE;IAC1C,MAAM7C,IAAI,GAAG0C,UAAU,CAACzC,UAAU,CAAC4C,CAAC,CAAC;IACrCD,IAAI,GAAI,CAACA,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAI5C,IAAI;IAClC4C,IAAI,GAAGA,IAAI,GAAGA,IAAI,CAAC,CAAC;EACtB;EAEA,OAAOE,IAAI,CAACC,GAAG,CAACH,IAAI,CAAC,CAACpD,QAAQ,CAAC,EAAE,CAAC;AACpC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}