{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u0633\\u0637\\u062D \\u0627\\u0644\\u0645\\u0643\\u062A\\u0628\\\\333\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\nimport { Toaster } from 'react-hot-toast';\nimport Layout from './components/Layout/Layout';\nimport Dashboard from './pages/Dashboard';\nimport Companies from './pages/Companies';\nimport Sales from './pages/Sales';\nimport Purchases from './pages/Purchases';\nimport Inventory from './pages/Inventory';\nimport Customers from './pages/Customers';\nimport Expenses from './pages/Expenses';\nimport Reports from './pages/Reports';\nimport Settings from './pages/Settings';\nimport QRCodeTest from './pages/QRCodeTest';\nimport Login from './pages/Login';\nimport { AuthProvider } from './contexts/AuthContext';\nimport ProtectedRoute from './components/Auth/ProtectedRoute';\nimport './App.css';\n\n// إنشاء عميل React Query\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      retry: 1,\n      refetchOnWindowFocus: false,\n      staleTime: 5 * 60 * 1000 // 5 دقائق\n    }\n  }\n});\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(QueryClientProvider, {\n    client: queryClient,\n    children: /*#__PURE__*/_jsxDEV(AuthProvider, {\n      children: /*#__PURE__*/_jsxDEV(Router, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"app-container\",\n          children: [/*#__PURE__*/_jsxDEV(Toaster, {\n            position: \"top-right\",\n            toastOptions: {\n              duration: 4000,\n              style: {\n                background: '#363636',\n                color: '#fff'\n              },\n              success: {\n                duration: 3000,\n                theme: {\n                  primary: 'green',\n                  secondary: 'black'\n                }\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/login\",\n              element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Layout, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 63,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 17\n              }, this),\n              children: [/*#__PURE__*/_jsxDEV(Route, {\n                index: true,\n                element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 66,\n                  columnNumber: 39\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"companies\",\n                element: /*#__PURE__*/_jsxDEV(Companies, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 67,\n                  columnNumber: 50\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"sales\",\n                element: /*#__PURE__*/_jsxDEV(Sales, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 68,\n                  columnNumber: 46\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"purchases\",\n                element: /*#__PURE__*/_jsxDEV(Purchases, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 69,\n                  columnNumber: 50\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"inventory\",\n                element: /*#__PURE__*/_jsxDEV(Inventory, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 70,\n                  columnNumber: 50\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"customers\",\n                element: /*#__PURE__*/_jsxDEV(Customers, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 71,\n                  columnNumber: 50\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"expenses\",\n                element: /*#__PURE__*/_jsxDEV(Expenses, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 72,\n                  columnNumber: 49\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"reports\",\n                element: /*#__PURE__*/_jsxDEV(Reports, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 73,\n                  columnNumber: 48\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"settings\",\n                element: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 74,\n                  columnNumber: 49\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 17\n              }, this), process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(Route, {\n                path: \"qr-test\",\n                element: /*#__PURE__*/_jsxDEV(QRCodeTest, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 77,\n                  columnNumber: 50\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "QueryClient", "QueryClientProvider", "Toaster", "Layout", "Dashboard", "Companies", "Sales", "Purchases", "Inventory", "Customers", "Expenses", "Reports", "Settings", "QRCodeTest", "<PERSON><PERSON>", "<PERSON>th<PERSON><PERSON><PERSON>", "ProtectedRoute", "jsxDEV", "_jsxDEV", "queryClient", "defaultOptions", "queries", "retry", "refetchOnWindowFocus", "staleTime", "App", "client", "children", "className", "position", "toastOptions", "duration", "style", "background", "color", "success", "theme", "primary", "secondary", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "index", "process", "env", "NODE_ENV", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/src/App.js"], "sourcesContent": ["import React from 'react';\r\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\r\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\r\nimport { Toaster } from 'react-hot-toast';\r\nimport Layout from './components/Layout/Layout';\r\nimport Dashboard from './pages/Dashboard';\r\nimport Companies from './pages/Companies';\r\nimport Sales from './pages/Sales';\r\nimport Purchases from './pages/Purchases';\r\nimport Inventory from './pages/Inventory';\r\nimport Customers from './pages/Customers';\r\nimport Expenses from './pages/Expenses';\r\nimport Reports from './pages/Reports';\r\nimport Settings from './pages/Settings';\r\nimport QRCodeTest from './pages/QRCodeTest';\r\nimport Login from './pages/Login';\r\nimport { AuthProvider } from './contexts/AuthContext';\r\nimport ProtectedRoute from './components/Auth/ProtectedRoute';\r\nimport './App.css';\r\n\r\n// إنشاء عميل React Query\r\nconst queryClient = new QueryClient({\r\n  defaultOptions: {\r\n    queries: {\r\n      retry: 1,\r\n      refetchOnWindowFocus: false,\r\n      staleTime: 5 * 60 * 1000, // 5 دقائق\r\n    },\r\n  },\r\n});\r\n\r\nfunction App() {\r\n  return (\r\n    <QueryClientProvider client={queryClient}>\r\n      <AuthProvider>\r\n        <Router>\r\n          <div className=\"app-container\">\r\n            <Toaster\r\n              position=\"top-right\"\r\n              toastOptions={{\r\n                duration: 4000,\r\n                style: {\r\n                  background: '#363636',\r\n                  color: '#fff',\r\n                },\r\n                success: {\r\n                  duration: 3000,\r\n                  theme: {\r\n                    primary: 'green',\r\n                    secondary: 'black',\r\n                  },\r\n                },\r\n              }}\r\n            />\r\n\r\n            <Routes>\r\n              {/* صفحة تسجيل الدخول */}\r\n              <Route path=\"/login\" element={<Login />} />\r\n\r\n              {/* الصفحات المحمية */}\r\n              <Route path=\"/\" element={\r\n                <ProtectedRoute>\r\n                  <Layout />\r\n                </ProtectedRoute>\r\n              }>\r\n                <Route index element={<Dashboard />} />\r\n                <Route path=\"companies\" element={<Companies />} />\r\n                <Route path=\"sales\" element={<Sales />} />\r\n                <Route path=\"purchases\" element={<Purchases />} />\r\n                <Route path=\"inventory\" element={<Inventory />} />\r\n                <Route path=\"customers\" element={<Customers />} />\r\n                <Route path=\"expenses\" element={<Expenses />} />\r\n                <Route path=\"reports\" element={<Reports />} />\r\n                <Route path=\"settings\" element={<Settings />} />\r\n                {/* مسار اختبار QR Code للمطورين فقط */}\r\n                {process.env.NODE_ENV === 'development' && (\r\n                  <Route path=\"qr-test\" element={<QRCodeTest />} />\r\n                )}\r\n              </Route>\r\n            </Routes>\r\n          </div>\r\n        </Router>\r\n      </AuthProvider>\r\n    </QueryClientProvider>\r\n  );\r\n}\r\n\r\nexport default App;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,uBAAuB;AACxE,SAASC,OAAO,QAAQ,iBAAiB;AACzC,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,KAAK,MAAM,eAAe;AACjC,SAASC,YAAY,QAAQ,wBAAwB;AACrD,OAAOC,cAAc,MAAM,kCAAkC;AAC7D,OAAO,WAAW;;AAElB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,GAAG,IAAInB,WAAW,CAAC;EAClCoB,cAAc,EAAE;IACdC,OAAO,EAAE;MACPC,KAAK,EAAE,CAAC;MACRC,oBAAoB,EAAE,KAAK;MAC3BC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAE;IAC5B;EACF;AACF,CAAC,CAAC;AAEF,SAASC,GAAGA,CAAA,EAAG;EACb,oBACEP,OAAA,CAACjB,mBAAmB;IAACyB,MAAM,EAAEP,WAAY;IAAAQ,QAAA,eACvCT,OAAA,CAACH,YAAY;MAAAY,QAAA,eACXT,OAAA,CAACrB,MAAM;QAAA8B,QAAA,eACLT,OAAA;UAAKU,SAAS,EAAC,eAAe;UAAAD,QAAA,gBAC5BT,OAAA,CAAChB,OAAO;YACN2B,QAAQ,EAAC,WAAW;YACpBC,YAAY,EAAE;cACZC,QAAQ,EAAE,IAAI;cACdC,KAAK,EAAE;gBACLC,UAAU,EAAE,SAAS;gBACrBC,KAAK,EAAE;cACT,CAAC;cACDC,OAAO,EAAE;gBACPJ,QAAQ,EAAE,IAAI;gBACdK,KAAK,EAAE;kBACLC,OAAO,EAAE,OAAO;kBAChBC,SAAS,EAAE;gBACb;cACF;YACF;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEFxB,OAAA,CAACpB,MAAM;YAAA6B,QAAA,gBAELT,OAAA,CAACnB,KAAK;cAAC4C,IAAI,EAAC,QAAQ;cAACC,OAAO,eAAE1B,OAAA,CAACJ,KAAK;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAG3CxB,OAAA,CAACnB,KAAK;cAAC4C,IAAI,EAAC,GAAG;cAACC,OAAO,eACrB1B,OAAA,CAACF,cAAc;gBAAAW,QAAA,eACbT,OAAA,CAACf,MAAM;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CACjB;cAAAf,QAAA,gBACCT,OAAA,CAACnB,KAAK;gBAAC8C,KAAK;gBAACD,OAAO,eAAE1B,OAAA,CAACd,SAAS;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCxB,OAAA,CAACnB,KAAK;gBAAC4C,IAAI,EAAC,WAAW;gBAACC,OAAO,eAAE1B,OAAA,CAACb,SAAS;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClDxB,OAAA,CAACnB,KAAK;gBAAC4C,IAAI,EAAC,OAAO;gBAACC,OAAO,eAAE1B,OAAA,CAACZ,KAAK;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1CxB,OAAA,CAACnB,KAAK;gBAAC4C,IAAI,EAAC,WAAW;gBAACC,OAAO,eAAE1B,OAAA,CAACX,SAAS;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClDxB,OAAA,CAACnB,KAAK;gBAAC4C,IAAI,EAAC,WAAW;gBAACC,OAAO,eAAE1B,OAAA,CAACV,SAAS;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClDxB,OAAA,CAACnB,KAAK;gBAAC4C,IAAI,EAAC,WAAW;gBAACC,OAAO,eAAE1B,OAAA,CAACT,SAAS;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClDxB,OAAA,CAACnB,KAAK;gBAAC4C,IAAI,EAAC,UAAU;gBAACC,OAAO,eAAE1B,OAAA,CAACR,QAAQ;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChDxB,OAAA,CAACnB,KAAK;gBAAC4C,IAAI,EAAC,SAAS;gBAACC,OAAO,eAAE1B,OAAA,CAACP,OAAO;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9CxB,OAAA,CAACnB,KAAK;gBAAC4C,IAAI,EAAC,UAAU;gBAACC,OAAO,eAAE1B,OAAA,CAACN,QAAQ;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAE/CI,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACrC9B,OAAA,CAACnB,KAAK;gBAAC4C,IAAI,EAAC,SAAS;gBAACC,OAAO,eAAE1B,OAAA,CAACL,UAAU;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CACjD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAE1B;AAACO,EAAA,GAtDQxB,GAAG;AAwDZ,eAAeA,GAAG;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}