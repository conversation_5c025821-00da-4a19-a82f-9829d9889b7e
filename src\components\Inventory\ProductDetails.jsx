import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { Button } from '../ui/button';

const ProductDetails = ({ product, onClose }) => {
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const getStockStatus = () => {
    if (product.currentStock === 0) {
      return { text: 'نفد المخزون', color: 'text-red-600 bg-red-100' };
    } else if (product.currentStock <= product.minStock) {
      return { text: 'مخزون منخفض', color: 'text-yellow-600 bg-yellow-100' };
    } else {
      return { text: 'متوفر', color: 'text-green-600 bg-green-100' };
    }
  };

  const stockStatus = getStockStatus();

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="bg-white rounded-2xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
        >
          {/* الرأس */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">تفاصيل المنتج</h2>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>

          {/* المحتوى */}
          <div className="p-6 space-y-6">
            {/* المعلومات الأساسية */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">المعلومات الأساسية</h3>
                <div className="space-y-3">
                  <div>
                    <span className="text-sm font-medium text-gray-600">كود المنتج:</span>
                    <p className="text-gray-900">{product.code}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-600">اسم المنتج:</span>
                    <p className="text-gray-900">{product.name}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-600">الفئة:</span>
                    <p className="text-gray-900">{product.category}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-600">العلامة التجارية:</span>
                    <p className="text-gray-900">{product.brand || '-'}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-600">الوحدة:</span>
                    <p className="text-gray-900">{product.unit}</p>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">حالة المخزون</h3>
                <div className="space-y-3">
                  <div>
                    <span className="text-sm font-medium text-gray-600">المخزون الحالي:</span>
                    <p className="text-2xl font-bold text-gray-900">
                      {product.currentStock} {product.unit}
                    </p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-600">الحالة:</span>
                    <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${stockStatus.color}`}>
                      {stockStatus.text}
                    </span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-600">الحد الأدنى:</span>
                    <p className="text-gray-900">{product.minStock} {product.unit}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-600">الحد الأقصى:</span>
                    <p className="text-gray-900">{product.maxStock} {product.unit}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* الأسعار */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">الأسعار</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <span className="text-sm font-medium text-blue-600">سعر التكلفة</span>
                  <p className="text-xl font-bold text-blue-900">{formatCurrency(product.costPrice)}</p>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <span className="text-sm font-medium text-green-600">سعر البيع</span>
                  <p className="text-xl font-bold text-green-900">{formatCurrency(product.sellingPrice)}</p>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg">
                  <span className="text-sm font-medium text-purple-600">هامش الربح</span>
                  <p className="text-xl font-bold text-purple-900">
                    {((product.sellingPrice - product.costPrice) / product.costPrice * 100).toFixed(2)}%
                  </p>
                </div>
              </div>
            </div>

            {/* قيمة المخزون */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">قيمة المخزون</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <span className="text-sm font-medium text-gray-600">قيمة المخزون (بسعر التكلفة)</span>
                  <p className="text-xl font-bold text-gray-900">
                    {formatCurrency(product.currentStock * product.costPrice)}
                  </p>
                </div>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <span className="text-sm font-medium text-gray-600">قيمة المخزون (بسعر البيع)</span>
                  <p className="text-xl font-bold text-gray-900">
                    {formatCurrency(product.currentStock * product.sellingPrice)}
                  </p>
                </div>
              </div>
            </div>

            {/* معلومات إضافية */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">معلومات إضافية</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <span className="text-sm font-medium text-gray-600">موقع التخزين:</span>
                  <p className="text-gray-900">{product.location || '-'}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-gray-600">المورد:</span>
                  <p className="text-gray-900">{product.supplier || '-'}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-gray-600">الباركود:</span>
                  <p className="text-gray-900 font-mono">{product.barcode || '-'}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-gray-600">آخر تحديث:</span>
                  <p className="text-gray-900">{new Date(product.lastUpdated).toLocaleDateString('ar-SA')}</p>
                </div>
              </div>
            </div>

            {/* الوصف */}
            {product.description && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">الوصف</h3>
                <p className="text-gray-700 bg-gray-50 p-4 rounded-lg">{product.description}</p>
              </div>
            )}

            {/* حركات المخزون */}
            {product.movements && product.movements.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">آخر حركات المخزون</h3>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse border border-gray-300">
                    <thead>
                      <tr className="bg-gray-50">
                        <th className="border border-gray-300 px-4 py-2 text-right">التاريخ</th>
                        <th className="border border-gray-300 px-4 py-2 text-center">النوع</th>
                        <th className="border border-gray-300 px-4 py-2 text-center">الكمية</th>
                        <th className="border border-gray-300 px-4 py-2 text-center">المرجع</th>
                      </tr>
                    </thead>
                    <tbody>
                      {product.movements.slice(-5).reverse().map((movement, index) => (
                        <tr key={index}>
                          <td className="border border-gray-300 px-4 py-2">
                            {new Date(movement.date).toLocaleDateString('ar-SA')}
                          </td>
                          <td className="border border-gray-300 px-4 py-2 text-center">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              movement.type === 'شراء' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                            }`}>
                              {movement.type}
                            </span>
                          </td>
                          <td className="border border-gray-300 px-4 py-2 text-center">
                            <span className={movement.quantity > 0 ? 'text-green-600' : 'text-red-600'}>
                              {movement.quantity > 0 ? '+' : ''}{movement.quantity}
                            </span>
                          </td>
                          <td className="border border-gray-300 px-4 py-2 text-center">
                            {movement.reference}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* زر الإغلاق */}
            <div className="flex justify-end pt-6 border-t border-gray-200">
              <Button onClick={onClose} variant="outline">
                إغلاق
              </Button>
            </div>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default ProductDetails;
