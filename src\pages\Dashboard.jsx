import React from 'react';
import { motion } from 'framer-motion';
import {
  BuildingOfficeIcon,
  UsersIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import StatsCard from '../components/Dashboard/StatsCard';
import RecentActivities from '../components/Dashboard/RecentActivities';
import CompanyChart from '../components/Dashboard/CompanyChart';
import QuickActions from '../components/Dashboard/QuickActions';

const Dashboard = () => {
  const stats = [
    {
      title: 'إجمالي الشركات',
      value: '156',
      change: '+12%',
      changeType: 'increase',
      icon: BuildingOfficeIcon,
      color: 'blue'
    },
    {
      title: 'الشركات النشطة',
      value: '142',
      change: '+8%',
      changeType: 'increase',
      icon: UsersIcon,
      color: 'green'
    },
    {
      title: 'التقارير الشهرية',
      value: '24',
      change: '+15%',
      changeType: 'increase',
      icon: ChartBarIcon,
      color: 'purple'
    },
    {
      title: 'القيمة الإجمالية',
      value: '2.4M ر.س',
      change: '-3%',
      changeType: 'decrease',
      icon: CurrencyDollarIcon,
      color: 'orange'
    }
  ];

  return (
    <div className="space-y-6">
      {/* العنوان الرئيسي */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-gray-900">لوحة التحكم</h1>
          <p className="text-gray-600 mt-2">مرحباً بك، إليك نظرة عامة على نشاط النظام</p>
        </div>
        <div className="text-sm text-gray-500">
          آخر تحديث: {new Date().toLocaleDateString('ar-SA')}
        </div>
      </motion.div>

      {/* بطاقات الإحصائيات */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <StatsCard {...stat} />
          </motion.div>
        ))}
      </div>

      {/* الصف الثاني - الرسوم البيانية والأنشطة */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* الرسم البياني */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4 }}
          className="lg:col-span-2"
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <ChartBarIcon className="w-5 h-5 ml-2 text-blue-600" />
                نمو الشركات خلال العام
              </CardTitle>
            </CardHeader>
            <CardContent>
              <CompanyChart />
            </CardContent>
          </Card>
        </motion.div>

        {/* الأنشطة الأخيرة */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.5 }}
        >
          <RecentActivities />
        </motion.div>
      </div>

      {/* الصف الثالث - الإجراءات السريعة */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
      >
        <QuickActions />
      </motion.div>
    </div>
  );
};

export default Dashboard;
