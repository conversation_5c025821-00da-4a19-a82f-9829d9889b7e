{"version": 3, "file": "workbox-navigation-preload.prod.js", "sources": ["../_version.js", "../isSupported.js", "../disable.js", "../enable.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:navigation-preload:6.5.4'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport './_version.js';\n/**\n * @return {boolean} Whether or not the current browser supports enabling\n * navigation preload.\n *\n * @memberof workbox-navigation-preload\n */\nfunction isSupported() {\n    return Boolean(self.registration && self.registration.navigationPreload);\n}\nexport { isSupported };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { isSupported } from './isSupported.js';\nimport './_version.js';\n/**\n * If the browser supports Navigation Preload, then this will disable it.\n *\n * @memberof workbox-navigation-preload\n */\nfunction disable() {\n    if (isSupported()) {\n        self.addEventListener('activate', (event) => {\n            event.waitUntil(self.registration.navigationPreload.disable().then(() => {\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log(`Navigation preload is disabled.`);\n                }\n            }));\n        });\n    }\n    else {\n        if (process.env.NODE_ENV !== 'production') {\n            logger.log(`Navigation preload is not supported in this browser.`);\n        }\n    }\n}\nexport { disable };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { isSupported } from './isSupported.js';\nimport './_version.js';\n/**\n * If the browser supports Navigation Preload, then this will enable it.\n *\n * @param {string} [headerValue] Optionally, allows developers to\n * [override](https://developers.google.com/web/updates/2017/02/navigation-preload#changing_the_header)\n * the value of the `Service-Worker-Navigation-Preload` header which will be\n * sent to the server when making the navigation request.\n *\n * @memberof workbox-navigation-preload\n */\nfunction enable(headerValue) {\n    if (isSupported()) {\n        self.addEventListener('activate', (event) => {\n            event.waitUntil(self.registration.navigationPreload.enable().then(() => {\n                // Defaults to Service-Worker-Navigation-Preload: true if not set.\n                if (headerValue) {\n                    void self.registration.navigationPreload.setHeaderValue(headerValue);\n                }\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log(`Navigation preload is enabled.`);\n                }\n            }));\n        });\n    }\n    else {\n        if (process.env.NODE_ENV !== 'production') {\n            logger.log(`Navigation preload is not supported in this browser.`);\n        }\n    }\n}\nexport { enable };\n"], "names": ["self", "_", "e", "isSupported", "Boolean", "registration", "navigationPreload", "addEventListener", "event", "waitUntil", "disable", "then", "headerValue", "enable", "setHeaderValue"], "mappings": "sFAEA,IACIA,KAAK,qCAAuCC,GAC/C,CACD,MAAOC,ICSP,SAASC,WACEC,QAAQJ,KAAKK,cAAgBL,KAAKK,aAAaC,kBACzD,kBCDD,WACQH,KACAH,KAAKO,iBAAiB,YAAaC,IAC/BA,EAAMC,UAAUT,KAAKK,aAAaC,kBAAkBI,UAAUC,MAAK,YAY9E,WCVD,SAAgBC,GACRT,KACAH,KAAKO,iBAAiB,YAAaC,IAC/BA,EAAMC,UAAUT,KAAKK,aAAaC,kBAAkBO,SAASF,MAAK,KAE1DC,GACKZ,KAAKK,aAAaC,kBAAkBQ,eAAeF,SAa3E"}