{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u0633\\u0637\\u062D \\u0627\\u0644\\u0645\\u0643\\u062A\\u0628\\\\333\\\\src\\\\components\\\\QRCode\\\\InvoiceQRCode.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport QRCode from 'react-qr-code';\nimport { generateQRCodeData, generateSimpleQRCode, generateInvoiceHash, testQRCodeReading, decodeQRCodeData } from '../../utils/qrCodeGenerator';\nimport { Button } from '../ui/button';\nimport { QrCodeIcon, DocumentDuplicateIcon, CheckIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst InvoiceQRCode = ({\n  invoiceData,\n  size = 128,\n  showDetails = true\n}) => {\n  _s();\n  const [qrData, setQrData] = useState('');\n  const [qrType, setQrType] = useState('phase2'); // phase2 أو simple\n  const [copied, setCopied] = useState(false);\n  const [invoiceHash, setInvoiceHash] = useState('');\n  useEffect(() => {\n    if (invoiceData) {\n      // إنشاء QR Code حسب النوع المحدد\n      let data = '';\n      if (qrType === 'phase2') {\n        data = generateQRCodeData(invoiceData);\n      } else {\n        data = generateSimpleQRCode(invoiceData);\n      }\n      setQrData(data);\n\n      // إنشاء hash للفاتورة\n      const hash = generateInvoiceHash(invoiceData);\n      setInvoiceHash(hash);\n    }\n  }, [invoiceData, qrType]);\n  const handleCopyQRData = async () => {\n    try {\n      await navigator.clipboard.writeText(qrData);\n      setCopied(true);\n      setTimeout(() => setCopied(false), 2000);\n    } catch (error) {\n      console.error('فشل في نسخ البيانات:', error);\n    }\n  };\n  const downloadQRCode = () => {\n    const svg = document.getElementById(`qr-code-${invoiceData.id}`);\n    const svgData = new XMLSerializer().serializeToString(svg);\n    const canvas = document.createElement('canvas');\n    const ctx = canvas.getContext('2d');\n    const img = new Image();\n    img.onload = () => {\n      canvas.width = img.width;\n      canvas.height = img.height;\n      ctx.drawImage(img, 0, 0);\n      const pngFile = canvas.toDataURL('image/png');\n      const downloadLink = document.createElement('a');\n      downloadLink.download = `qr-code-${invoiceData.invoiceNumber}.png`;\n      downloadLink.href = pngFile;\n      downloadLink.click();\n    };\n    img.src = 'data:image/svg+xml;base64,' + btoa(svgData);\n  };\n  if (!invoiceData || !qrData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center p-4 bg-gray-100 rounded-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500\",\n        children: \"\\u062C\\u0627\\u0631\\u064A \\u062A\\u062D\\u0645\\u064A\\u0644 QR Code...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white p-4 rounded-lg border border-gray-200\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(QrCodeIcon, {\n          className: \"w-5 h-5 text-blue-600 ml-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900\",\n          children: \"\\u0631\\u0645\\u0632 \\u0627\\u0644\\u0627\\u0633\\u062A\\u062C\\u0627\\u0628\\u0629 \\u0627\\u0644\\u0633\\u0631\\u064A\\u0639\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2 space-x-reverse\",\n        children: /*#__PURE__*/_jsxDEV(\"select\", {\n          value: qrType,\n          onChange: e => setQrType(e.target.value),\n          className: \"text-sm border border-gray-300 rounded px-2 py-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"phase2\",\n            children: \"\\u0627\\u0644\\u0645\\u0631\\u062D\\u0644\\u0629 \\u0627\\u0644\\u062B\\u0627\\u0646\\u064A\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"simple\",\n            children: \"\\u0645\\u0628\\u0633\\u0637\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col items-center space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 bg-white border-2 border-gray-300 rounded-lg\",\n        children: /*#__PURE__*/_jsxDEV(QRCode, {\n          id: `qr-code-${invoiceData.id}`,\n          value: qrData,\n          size: size,\n          level: \"M\",\n          includeMargin: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this), showDetails && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full space-y-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-blue-50 p-3 rounded-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-blue-800\",\n              children: qrType === 'phase2' ? 'متوافق مع المرحلة الثانية' : 'QR Code مبسط'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded\",\n              children: qrType === 'phase2' ? 'ZATCA Phase 2' : 'Simple'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 gap-3 text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-600\",\n              children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"font-medium\",\n              children: invoiceData.invoiceNumber\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-600\",\n              children: \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"font-medium\",\n              children: new Date(invoiceData.date).toLocaleDateString('ar-SA')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-600\",\n              children: \"\\u0627\\u0644\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"font-medium\",\n              children: [invoiceData.total.toFixed(2), \" \\u0631.\\u0633\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-600\",\n              children: \"\\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u0629:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"font-medium\",\n              children: [invoiceData.tax.toFixed(2), \" \\u0631.\\u0633\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-50 p-3 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-600\",\n            children: \"Hash \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"font-mono text-xs text-gray-800 break-all\",\n            children: invoiceHash\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 13\n        }, this), qrType === 'phase2' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-green-50 p-3 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-sm font-medium text-green-800 mb-2\",\n            children: \"\\u0645\\u062A\\u0637\\u0644\\u0628\\u0627\\u062A \\u0627\\u0644\\u0645\\u0631\\u062D\\u0644\\u0629 \\u0627\\u0644\\u062B\\u0627\\u0646\\u064A\\u0629:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"text-xs text-green-700 space-y-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u2713 \\u0627\\u0633\\u0645 \\u0627\\u0644\\u0628\\u0627\\u0626\\u0639\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u2713 \\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u064A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u2713 \\u0627\\u0644\\u0637\\u0627\\u0628\\u0639 \\u0627\\u0644\\u0632\\u0645\\u0646\\u064A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u2713 \\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u2713 \\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u2713 \\u062A\\u0634\\u0641\\u064A\\u0631 Base64\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-3 space-x-reverse\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCopyQRData,\n          variant: \"outline\",\n          size: \"sm\",\n          className: \"flex items-center\",\n          children: [copied ? /*#__PURE__*/_jsxDEV(CheckIcon, {\n            className: \"w-4 h-4 ml-2 text-green-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(DocumentDuplicateIcon, {\n            className: \"w-4 h-4 ml-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 15\n          }, this), copied ? 'تم النسخ' : 'نسخ البيانات']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: downloadQRCode,\n          variant: \"outline\",\n          size: \"sm\",\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(QrCodeIcon, {\n            className: \"w-4 h-4 ml-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), \"\\u062A\\u062D\\u0645\\u064A\\u0644 QR\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this), process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(\"details\", {\n        className: \"w-full\",\n        children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n          className: \"text-sm text-gray-600 cursor-pointer hover:text-gray-800\",\n          children: \"\\u0639\\u0631\\u0636 \\u0628\\u064A\\u0627\\u0646\\u0627\\u062A QR Code (\\u0644\\u0644\\u0645\\u0637\\u0648\\u0631\\u064A\\u0646)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-2 p-3 bg-gray-100 rounded text-xs font-mono break-all\",\n          children: qrData\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 78,\n    columnNumber: 5\n  }, this);\n};\n_s(InvoiceQRCode, \"WaHPFzkvfCba0Jj8c7om4Qnj9UE=\");\n_c = InvoiceQRCode;\nexport default InvoiceQRCode;\nvar _c;\n$RefreshReg$(_c, \"InvoiceQRCode\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "QRCode", "generateQRCodeData", "generateSimpleQRCode", "generateInvoiceHash", "testQRCodeReading", "decodeQRCodeData", "<PERSON><PERSON>", "QrCodeIcon", "DocumentDuplicateIcon", "CheckIcon", "ExclamationTriangleIcon", "jsxDEV", "_jsxDEV", "InvoiceQRCode", "invoiceData", "size", "showDetails", "_s", "qrData", "setQrData", "qrType", "setQrType", "copied", "setCopied", "invoiceHash", "setInvoiceHash", "data", "hash", "handleCopyQRData", "navigator", "clipboard", "writeText", "setTimeout", "error", "console", "downloadQRCode", "svg", "document", "getElementById", "id", "svgData", "XMLSerializer", "serializeToString", "canvas", "createElement", "ctx", "getContext", "img", "Image", "onload", "width", "height", "drawImage", "pngFile", "toDataURL", "downloadLink", "download", "invoiceNumber", "href", "click", "src", "btoa", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "onChange", "e", "target", "level", "<PERSON><PERSON><PERSON><PERSON>", "Date", "date", "toLocaleDateString", "total", "toFixed", "tax", "onClick", "variant", "process", "env", "NODE_ENV", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/src/components/QRCode/InvoiceQRCode.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport QRCode from 'react-qr-code';\nimport {\n  generateQRCodeData,\n  generateSimpleQRCode,\n  generateInvoiceHash,\n  testQRCodeReading,\n  decodeQRCodeData\n} from '../../utils/qrCodeGenerator';\nimport { Button } from '../ui/button';\nimport { QrCodeIcon, DocumentDuplicateIcon, CheckIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';\n\nconst InvoiceQRCode = ({ invoiceData, size = 128, showDetails = true }) => {\n  const [qrData, setQrData] = useState('');\n  const [qrType, setQrType] = useState('phase2'); // phase2 أو simple\n  const [copied, setCopied] = useState(false);\n  const [invoiceHash, setInvoiceHash] = useState('');\n\n  useEffect(() => {\n    if (invoiceData) {\n      // إنشاء QR Code حسب النوع المحدد\n      let data = '';\n      if (qrType === 'phase2') {\n        data = generateQRCodeData(invoiceData);\n      } else {\n        data = generateSimpleQRCode(invoiceData);\n      }\n\n      setQrData(data);\n\n      // إنشاء hash للفاتورة\n      const hash = generateInvoiceHash(invoiceData);\n      setInvoiceHash(hash);\n    }\n  }, [invoiceData, qrType]);\n\n  const handleCopyQRData = async () => {\n    try {\n      await navigator.clipboard.writeText(qrData);\n      setCopied(true);\n      setTimeout(() => setCopied(false), 2000);\n    } catch (error) {\n      console.error('فشل في نسخ البيانات:', error);\n    }\n  };\n\n  const downloadQRCode = () => {\n    const svg = document.getElementById(`qr-code-${invoiceData.id}`);\n    const svgData = new XMLSerializer().serializeToString(svg);\n    const canvas = document.createElement('canvas');\n    const ctx = canvas.getContext('2d');\n    const img = new Image();\n\n    img.onload = () => {\n      canvas.width = img.width;\n      canvas.height = img.height;\n      ctx.drawImage(img, 0, 0);\n\n      const pngFile = canvas.toDataURL('image/png');\n      const downloadLink = document.createElement('a');\n      downloadLink.download = `qr-code-${invoiceData.invoiceNumber}.png`;\n      downloadLink.href = pngFile;\n      downloadLink.click();\n    };\n\n    img.src = 'data:image/svg+xml;base64,' + btoa(svgData);\n  };\n\n  if (!invoiceData || !qrData) {\n    return (\n      <div className=\"flex items-center justify-center p-4 bg-gray-100 rounded-lg\">\n        <p className=\"text-gray-500\">جاري تحميل QR Code...</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white p-4 rounded-lg border border-gray-200\">\n      {/* عنوان QR Code */}\n      <div className=\"flex items-center justify-between mb-4\">\n        <div className=\"flex items-center\">\n          <QrCodeIcon className=\"w-5 h-5 text-blue-600 ml-2\" />\n          <h3 className=\"text-lg font-semibold text-gray-900\">\n            رمز الاستجابة السريعة\n          </h3>\n        </div>\n\n        {/* تبديل نوع QR Code */}\n        <div className=\"flex items-center space-x-2 space-x-reverse\">\n          <select\n            value={qrType}\n            onChange={(e) => setQrType(e.target.value)}\n            className=\"text-sm border border-gray-300 rounded px-2 py-1\"\n          >\n            <option value=\"phase2\">المرحلة الثانية</option>\n            <option value=\"simple\">مبسط</option>\n          </select>\n        </div>\n      </div>\n\n      {/* QR Code */}\n      <div className=\"flex flex-col items-center space-y-4\">\n        <div className=\"p-4 bg-white border-2 border-gray-300 rounded-lg\">\n          <QRCode\n            id={`qr-code-${invoiceData.id}`}\n            value={qrData}\n            size={size}\n            level=\"M\"\n            includeMargin={true}\n          />\n        </div>\n\n        {/* معلومات QR Code */}\n        {showDetails && (\n          <div className=\"w-full space-y-3\">\n            {/* نوع QR Code */}\n            <div className=\"bg-blue-50 p-3 rounded-lg\">\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm font-medium text-blue-800\">\n                  {qrType === 'phase2' ? 'متوافق مع المرحلة الثانية' : 'QR Code مبسط'}\n                </span>\n                <span className=\"text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded\">\n                  {qrType === 'phase2' ? 'ZATCA Phase 2' : 'Simple'}\n                </span>\n              </div>\n            </div>\n\n            {/* معلومات الفاتورة */}\n            <div className=\"grid grid-cols-2 gap-3 text-sm\">\n              <div>\n                <span className=\"text-gray-600\">رقم الفاتورة:</span>\n                <p className=\"font-medium\">{invoiceData.invoiceNumber}</p>\n              </div>\n              <div>\n                <span className=\"text-gray-600\">التاريخ:</span>\n                <p className=\"font-medium\">{new Date(invoiceData.date).toLocaleDateString('ar-SA')}</p>\n              </div>\n              <div>\n                <span className=\"text-gray-600\">الإجمالي:</span>\n                <p className=\"font-medium\">{invoiceData.total.toFixed(2)} ر.س</p>\n              </div>\n              <div>\n                <span className=\"text-gray-600\">الضريبة:</span>\n                <p className=\"font-medium\">{invoiceData.tax.toFixed(2)} ر.س</p>\n              </div>\n            </div>\n\n            {/* hash الفاتورة */}\n            <div className=\"bg-gray-50 p-3 rounded-lg\">\n              <span className=\"text-sm text-gray-600\">Hash الفاتورة:</span>\n              <p className=\"font-mono text-xs text-gray-800 break-all\">{invoiceHash}</p>\n            </div>\n\n            {/* معلومات المرحلة الثانية */}\n            {qrType === 'phase2' && (\n              <div className=\"bg-green-50 p-3 rounded-lg\">\n                <h4 className=\"text-sm font-medium text-green-800 mb-2\">\n                  متطلبات المرحلة الثانية:\n                </h4>\n                <ul className=\"text-xs text-green-700 space-y-1\">\n                  <li>✓ اسم البائع</li>\n                  <li>✓ الرقم الضريبي</li>\n                  <li>✓ الطابع الزمني</li>\n                  <li>✓ إجمالي الفاتورة</li>\n                  <li>✓ إجمالي الضريبة</li>\n                  <li>✓ تشفير Base64</li>\n                </ul>\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* أزرار العمليات */}\n        <div className=\"flex items-center space-x-3 space-x-reverse\">\n          <Button\n            onClick={handleCopyQRData}\n            variant=\"outline\"\n            size=\"sm\"\n            className=\"flex items-center\"\n          >\n            {copied ? (\n              <CheckIcon className=\"w-4 h-4 ml-2 text-green-600\" />\n            ) : (\n              <DocumentDuplicateIcon className=\"w-4 h-4 ml-2\" />\n            )}\n            {copied ? 'تم النسخ' : 'نسخ البيانات'}\n          </Button>\n\n          <Button\n            onClick={downloadQRCode}\n            variant=\"outline\"\n            size=\"sm\"\n            className=\"flex items-center\"\n          >\n            <QrCodeIcon className=\"w-4 h-4 ml-2\" />\n            تحميل QR\n          </Button>\n        </div>\n\n        {/* معلومات إضافية للمطورين */}\n        {process.env.NODE_ENV === 'development' && (\n          <details className=\"w-full\">\n            <summary className=\"text-sm text-gray-600 cursor-pointer hover:text-gray-800\">\n              عرض بيانات QR Code (للمطورين)\n            </summary>\n            <div className=\"mt-2 p-3 bg-gray-100 rounded text-xs font-mono break-all\">\n              {qrData}\n            </div>\n          </details>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default InvoiceQRCode;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,eAAe;AAClC,SACEC,kBAAkB,EAClBC,oBAAoB,EACpBC,mBAAmB,EACnBC,iBAAiB,EACjBC,gBAAgB,QACX,6BAA6B;AACpC,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,UAAU,EAAEC,qBAAqB,EAAEC,SAAS,EAAEC,uBAAuB,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpH,MAAMC,aAAa,GAAGA,CAAC;EAAEC,WAAW;EAAEC,IAAI,GAAG,GAAG;EAAEC,WAAW,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EACzE,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACsB,MAAM,EAAEC,SAAS,CAAC,GAAGvB,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;EAChD,MAAM,CAACwB,MAAM,EAAEC,SAAS,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC0B,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAElDC,SAAS,CAAC,MAAM;IACd,IAAIe,WAAW,EAAE;MACf;MACA,IAAIY,IAAI,GAAG,EAAE;MACb,IAAIN,MAAM,KAAK,QAAQ,EAAE;QACvBM,IAAI,GAAGzB,kBAAkB,CAACa,WAAW,CAAC;MACxC,CAAC,MAAM;QACLY,IAAI,GAAGxB,oBAAoB,CAACY,WAAW,CAAC;MAC1C;MAEAK,SAAS,CAACO,IAAI,CAAC;;MAEf;MACA,MAAMC,IAAI,GAAGxB,mBAAmB,CAACW,WAAW,CAAC;MAC7CW,cAAc,CAACE,IAAI,CAAC;IACtB;EACF,CAAC,EAAE,CAACb,WAAW,EAAEM,MAAM,CAAC,CAAC;EAEzB,MAAMQ,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACb,MAAM,CAAC;MAC3CK,SAAS,CAAC,IAAI,CAAC;MACfS,UAAU,CAAC,MAAMT,SAAS,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IAC1C,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C;EACF,CAAC;EAED,MAAME,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,GAAG,GAAGC,QAAQ,CAACC,cAAc,CAAC,WAAWxB,WAAW,CAACyB,EAAE,EAAE,CAAC;IAChE,MAAMC,OAAO,GAAG,IAAIC,aAAa,CAAC,CAAC,CAACC,iBAAiB,CAACN,GAAG,CAAC;IAC1D,MAAMO,MAAM,GAAGN,QAAQ,CAACO,aAAa,CAAC,QAAQ,CAAC;IAC/C,MAAMC,GAAG,GAAGF,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;IACnC,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;IAEvBD,GAAG,CAACE,MAAM,GAAG,MAAM;MACjBN,MAAM,CAACO,KAAK,GAAGH,GAAG,CAACG,KAAK;MACxBP,MAAM,CAACQ,MAAM,GAAGJ,GAAG,CAACI,MAAM;MAC1BN,GAAG,CAACO,SAAS,CAACL,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;MAExB,MAAMM,OAAO,GAAGV,MAAM,CAACW,SAAS,CAAC,WAAW,CAAC;MAC7C,MAAMC,YAAY,GAAGlB,QAAQ,CAACO,aAAa,CAAC,GAAG,CAAC;MAChDW,YAAY,CAACC,QAAQ,GAAG,WAAW1C,WAAW,CAAC2C,aAAa,MAAM;MAClEF,YAAY,CAACG,IAAI,GAAGL,OAAO;MAC3BE,YAAY,CAACI,KAAK,CAAC,CAAC;IACtB,CAAC;IAEDZ,GAAG,CAACa,GAAG,GAAG,4BAA4B,GAAGC,IAAI,CAACrB,OAAO,CAAC;EACxD,CAAC;EAED,IAAI,CAAC1B,WAAW,IAAI,CAACI,MAAM,EAAE;IAC3B,oBACEN,OAAA;MAAKkD,SAAS,EAAC,6DAA6D;MAAAC,QAAA,eAC1EnD,OAAA;QAAGkD,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CAAC;EAEV;EAEA,oBACEvD,OAAA;IAAKkD,SAAS,EAAC,gDAAgD;IAAAC,QAAA,gBAE7DnD,OAAA;MAAKkD,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrDnD,OAAA;QAAKkD,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCnD,OAAA,CAACL,UAAU;UAACuD,SAAS,EAAC;QAA4B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrDvD,OAAA;UAAIkD,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAEpD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGNvD,OAAA;QAAKkD,SAAS,EAAC,6CAA6C;QAAAC,QAAA,eAC1DnD,OAAA;UACEwD,KAAK,EAAEhD,MAAO;UACdiD,QAAQ,EAAGC,CAAC,IAAKjD,SAAS,CAACiD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAC3CN,SAAS,EAAC,kDAAkD;UAAAC,QAAA,gBAE5DnD,OAAA;YAAQwD,KAAK,EAAC,QAAQ;YAAAL,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC/CvD,OAAA;YAAQwD,KAAK,EAAC,QAAQ;YAAAL,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvD,OAAA;MAAKkD,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnDnD,OAAA;QAAKkD,SAAS,EAAC,kDAAkD;QAAAC,QAAA,eAC/DnD,OAAA,CAACZ,MAAM;UACLuC,EAAE,EAAE,WAAWzB,WAAW,CAACyB,EAAE,EAAG;UAChC6B,KAAK,EAAElD,MAAO;UACdH,IAAI,EAAEA,IAAK;UACXyD,KAAK,EAAC,GAAG;UACTC,aAAa,EAAE;QAAK;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAGLnD,WAAW,iBACVJ,OAAA;QAAKkD,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAE/BnD,OAAA;UAAKkD,SAAS,EAAC,2BAA2B;UAAAC,QAAA,eACxCnD,OAAA;YAAKkD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDnD,OAAA;cAAMkD,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAChD3C,MAAM,KAAK,QAAQ,GAAG,2BAA2B,GAAG;YAAc;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC,eACPvD,OAAA;cAAMkD,SAAS,EAAC,qDAAqD;cAAAC,QAAA,EAClE3C,MAAM,KAAK,QAAQ,GAAG,eAAe,GAAG;YAAQ;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNvD,OAAA;UAAKkD,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBAC7CnD,OAAA;YAAAmD,QAAA,gBACEnD,OAAA;cAAMkD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpDvD,OAAA;cAAGkD,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEjD,WAAW,CAAC2C;YAAa;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,eACNvD,OAAA;YAAAmD,QAAA,gBACEnD,OAAA;cAAMkD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/CvD,OAAA;cAAGkD,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAE,IAAIW,IAAI,CAAC5D,WAAW,CAAC6D,IAAI,CAAC,CAACC,kBAAkB,CAAC,OAAO;YAAC;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC,eACNvD,OAAA;YAAAmD,QAAA,gBACEnD,OAAA;cAAMkD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChDvD,OAAA;cAAGkD,SAAS,EAAC,aAAa;cAAAC,QAAA,GAAEjD,WAAW,CAAC+D,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,gBAAI;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eACNvD,OAAA;YAAAmD,QAAA,gBACEnD,OAAA;cAAMkD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/CvD,OAAA;cAAGkD,SAAS,EAAC,aAAa;cAAAC,QAAA,GAAEjD,WAAW,CAACiE,GAAG,CAACD,OAAO,CAAC,CAAC,CAAC,EAAC,gBAAI;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNvD,OAAA;UAAKkD,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxCnD,OAAA;YAAMkD,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7DvD,OAAA;YAAGkD,SAAS,EAAC,2CAA2C;YAAAC,QAAA,EAAEvC;UAAW;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC,EAGL/C,MAAM,KAAK,QAAQ,iBAClBR,OAAA;UAAKkD,SAAS,EAAC,4BAA4B;UAAAC,QAAA,gBACzCnD,OAAA;YAAIkD,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAExD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLvD,OAAA;YAAIkD,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC9CnD,OAAA;cAAAmD,QAAA,EAAI;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrBvD,OAAA;cAAAmD,QAAA,EAAI;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxBvD,OAAA;cAAAmD,QAAA,EAAI;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxBvD,OAAA;cAAAmD,QAAA,EAAI;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1BvD,OAAA;cAAAmD,QAAA,EAAI;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzBvD,OAAA;cAAAmD,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,eAGDvD,OAAA;QAAKkD,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAC1DnD,OAAA,CAACN,MAAM;UACL0E,OAAO,EAAEpD,gBAAiB;UAC1BqD,OAAO,EAAC,SAAS;UACjBlE,IAAI,EAAC,IAAI;UACT+C,SAAS,EAAC,mBAAmB;UAAAC,QAAA,GAE5BzC,MAAM,gBACLV,OAAA,CAACH,SAAS;YAACqD,SAAS,EAAC;UAA6B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAErDvD,OAAA,CAACJ,qBAAqB;YAACsD,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAClD,EACA7C,MAAM,GAAG,UAAU,GAAG,cAAc;QAAA;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eAETvD,OAAA,CAACN,MAAM;UACL0E,OAAO,EAAE7C,cAAe;UACxB8C,OAAO,EAAC,SAAS;UACjBlE,IAAI,EAAC,IAAI;UACT+C,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAE7BnD,OAAA,CAACL,UAAU;YAACuD,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,qCAEzC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLe,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACrCxE,OAAA;QAASkD,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACzBnD,OAAA;UAASkD,SAAS,EAAC,0DAA0D;UAAAC,QAAA,EAAC;QAE9E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,eACVvD,OAAA;UAAKkD,SAAS,EAAC,0DAA0D;UAAAC,QAAA,EACtE7C;QAAM;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACV;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClD,EAAA,CAzMIJ,aAAa;AAAAwE,EAAA,GAAbxE,aAAa;AA2MnB,eAAeA,aAAa;AAAC,IAAAwE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}