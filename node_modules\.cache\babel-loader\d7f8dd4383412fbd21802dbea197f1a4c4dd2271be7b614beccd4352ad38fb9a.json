{"ast": null, "code": "// API لإدارة المشتريات\nclass PurchasesAPI {\n  constructor() {\n    this.storageKey = 'purchases_data';\n    this.initializeData();\n  }\n  initializeData() {\n    const existingData = localStorage.getItem(this.storageKey);\n    if (!existingData) {\n      const initialData = {\n        purchases: [{\n          id: 1,\n          invoiceNumber: 'PUR-2024-001',\n          supplierId: 1,\n          supplierName: 'شركة التقنية المتقدمة',\n          date: '2024-01-10',\n          items: [{\n            productId: 1,\n            productName: 'لابتوب Dell',\n            quantity: 10,\n            price: 3000,\n            total: 30000\n          }, {\n            productId: 2,\n            productName: 'ماوس لاسلكي',\n            quantity: 50,\n            price: 100,\n            total: 5000\n          }],\n          subtotal: 35000,\n          tax: 5250,\n          discount: 500,\n          total: 39750,\n          status: 'مكتملة',\n          paymentMethod: 'تحويل بنكي',\n          dueDate: '2024-02-10',\n          notes: 'شحنة شهرية منتظمة'\n        }, {\n          id: 2,\n          invoiceNumber: 'PUR-2024-002',\n          supplierId: 2,\n          supplierName: 'مؤسسة الإلكترونيات',\n          date: '2024-01-12',\n          items: [{\n            productId: 3,\n            productName: 'طابعة HP',\n            quantity: 5,\n            price: 600,\n            total: 3000\n          }],\n          subtotal: 3000,\n          tax: 450,\n          discount: 0,\n          total: 3450,\n          status: 'معلقة',\n          paymentMethod: 'آجل',\n          dueDate: '2024-02-12',\n          notes: 'في انتظار التسليم'\n        }],\n        lastId: 2\n      };\n      localStorage.setItem(this.storageKey, JSON.stringify(initialData));\n    }\n  }\n  getData() {\n    return JSON.parse(localStorage.getItem(this.storageKey));\n  }\n  saveData(data) {\n    localStorage.setItem(this.storageKey, JSON.stringify(data));\n  }\n\n  // الحصول على جميع المشتريات\n  getAllPurchases() {\n    return this.getData().purchases;\n  }\n\n  // إضافة فاتورة مشتريات جديدة\n  addPurchase(purchaseData) {\n    const data = this.getData();\n    const newPurchase = {\n      id: data.lastId + 1,\n      invoiceNumber: `PUR-${new Date().getFullYear()}-${String(data.lastId + 1).padStart(3, '0')}`,\n      date: new Date().toISOString().split('T')[0],\n      status: 'معلقة',\n      ...purchaseData\n    };\n    data.purchases.push(newPurchase);\n    data.lastId += 1;\n    this.saveData(data);\n    return newPurchase;\n  }\n\n  // تحديث فاتورة مشتريات\n  updatePurchase(id, purchaseData) {\n    const data = this.getData();\n    const index = data.purchases.findIndex(purchase => purchase.id === id);\n    if (index !== -1) {\n      data.purchases[index] = {\n        ...data.purchases[index],\n        ...purchaseData\n      };\n      this.saveData(data);\n      return data.purchases[index];\n    }\n    return null;\n  }\n\n  // حذف فاتورة مشتريات\n  deletePurchase(id) {\n    const data = this.getData();\n    data.purchases = data.purchases.filter(purchase => purchase.id !== id);\n    this.saveData(data);\n    return true;\n  }\n\n  // الحصول على فاتورة بالمعرف\n  getPurchaseById(id) {\n    const data = this.getData();\n    return data.purchases.find(purchase => purchase.id === id);\n  }\n\n  // إحصائيات المشتريات\n  getPurchasesStats() {\n    const purchases = this.getAllPurchases();\n    const today = new Date().toISOString().split('T')[0];\n    const thisMonth = new Date().toISOString().slice(0, 7);\n    return {\n      totalPurchases: purchases.reduce((sum, purchase) => sum + purchase.total, 0),\n      todayPurchases: purchases.filter(purchase => purchase.date === today).reduce((sum, purchase) => sum + purchase.total, 0),\n      monthPurchases: purchases.filter(purchase => purchase.date.startsWith(thisMonth)).reduce((sum, purchase) => sum + purchase.total, 0),\n      totalInvoices: purchases.length,\n      completedPurchases: purchases.filter(purchase => purchase.status === 'مكتملة').length,\n      pendingPurchases: purchases.filter(purchase => purchase.status === 'معلقة').length\n    };\n  }\n\n  // البحث في المشتريات\n  searchPurchases(query) {\n    const purchases = this.getAllPurchases();\n    return purchases.filter(purchase => purchase.invoiceNumber.toLowerCase().includes(query.toLowerCase()) || purchase.supplierName.toLowerCase().includes(query.toLowerCase()) || purchase.status.toLowerCase().includes(query.toLowerCase()));\n  }\n}\nexport default new PurchasesAPI();", "map": {"version": 3, "names": ["PurchasesAPI", "constructor", "storageKey", "initializeData", "existingData", "localStorage", "getItem", "initialData", "purchases", "id", "invoiceNumber", "supplierId", "supplierName", "date", "items", "productId", "productName", "quantity", "price", "total", "subtotal", "tax", "discount", "status", "paymentMethod", "dueDate", "notes", "lastId", "setItem", "JSON", "stringify", "getData", "parse", "saveData", "data", "getAllPurchases", "addPurchase", "purchaseData", "newPurchase", "Date", "getFullYear", "String", "padStart", "toISOString", "split", "push", "updatePurchase", "index", "findIndex", "purchase", "deletePurchase", "filter", "getPurchaseById", "find", "getPurchasesStats", "today", "thisMonth", "slice", "totalPurchases", "reduce", "sum", "todayPurchases", "monthPurchases", "startsWith", "totalInvoices", "length", "completedPurchases", "pendingPurchases", "searchPurchases", "query", "toLowerCase", "includes"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/src/api/purchasesAPI.js"], "sourcesContent": ["// API لإدارة المشتريات\nclass PurchasesAPI {\n  constructor() {\n    this.storageKey = 'purchases_data';\n    this.initializeData();\n  }\n\n  initializeData() {\n    const existingData = localStorage.getItem(this.storageKey);\n    if (!existingData) {\n      const initialData = {\n        purchases: [\n          {\n            id: 1,\n            invoiceNumber: 'PUR-2024-001',\n            supplierId: 1,\n            supplierName: 'شركة التقنية المتقدمة',\n            date: '2024-01-10',\n            items: [\n              { productId: 1, productName: 'لابتوب Dell', quantity: 10, price: 3000, total: 30000 },\n              { productId: 2, productName: 'ماوس لاسلكي', quantity: 50, price: 100, total: 5000 }\n            ],\n            subtotal: 35000,\n            tax: 5250,\n            discount: 500,\n            total: 39750,\n            status: 'مكتملة',\n            paymentMethod: 'تحويل بنكي',\n            dueDate: '2024-02-10',\n            notes: 'شحنة شهرية منتظمة'\n          },\n          {\n            id: 2,\n            invoiceNumber: 'PUR-2024-002',\n            supplierId: 2,\n            supplierName: 'مؤسسة الإلكترونيات',\n            date: '2024-01-12',\n            items: [\n              { productId: 3, productName: 'طابعة HP', quantity: 5, price: 600, total: 3000 }\n            ],\n            subtotal: 3000,\n            tax: 450,\n            discount: 0,\n            total: 3450,\n            status: 'معلقة',\n            paymentMethod: 'آجل',\n            dueDate: '2024-02-12',\n            notes: 'في انتظار التسليم'\n          }\n        ],\n        lastId: 2\n      };\n      localStorage.setItem(this.storageKey, JSON.stringify(initialData));\n    }\n  }\n\n  getData() {\n    return JSON.parse(localStorage.getItem(this.storageKey));\n  }\n\n  saveData(data) {\n    localStorage.setItem(this.storageKey, JSON.stringify(data));\n  }\n\n  // الحصول على جميع المشتريات\n  getAllPurchases() {\n    return this.getData().purchases;\n  }\n\n  // إضافة فاتورة مشتريات جديدة\n  addPurchase(purchaseData) {\n    const data = this.getData();\n    const newPurchase = {\n      id: data.lastId + 1,\n      invoiceNumber: `PUR-${new Date().getFullYear()}-${String(data.lastId + 1).padStart(3, '0')}`,\n      date: new Date().toISOString().split('T')[0],\n      status: 'معلقة',\n      ...purchaseData\n    };\n    \n    data.purchases.push(newPurchase);\n    data.lastId += 1;\n    this.saveData(data);\n    return newPurchase;\n  }\n\n  // تحديث فاتورة مشتريات\n  updatePurchase(id, purchaseData) {\n    const data = this.getData();\n    const index = data.purchases.findIndex(purchase => purchase.id === id);\n    if (index !== -1) {\n      data.purchases[index] = { ...data.purchases[index], ...purchaseData };\n      this.saveData(data);\n      return data.purchases[index];\n    }\n    return null;\n  }\n\n  // حذف فاتورة مشتريات\n  deletePurchase(id) {\n    const data = this.getData();\n    data.purchases = data.purchases.filter(purchase => purchase.id !== id);\n    this.saveData(data);\n    return true;\n  }\n\n  // الحصول على فاتورة بالمعرف\n  getPurchaseById(id) {\n    const data = this.getData();\n    return data.purchases.find(purchase => purchase.id === id);\n  }\n\n  // إحصائيات المشتريات\n  getPurchasesStats() {\n    const purchases = this.getAllPurchases();\n    const today = new Date().toISOString().split('T')[0];\n    const thisMonth = new Date().toISOString().slice(0, 7);\n    \n    return {\n      totalPurchases: purchases.reduce((sum, purchase) => sum + purchase.total, 0),\n      todayPurchases: purchases.filter(purchase => purchase.date === today).reduce((sum, purchase) => sum + purchase.total, 0),\n      monthPurchases: purchases.filter(purchase => purchase.date.startsWith(thisMonth)).reduce((sum, purchase) => sum + purchase.total, 0),\n      totalInvoices: purchases.length,\n      completedPurchases: purchases.filter(purchase => purchase.status === 'مكتملة').length,\n      pendingPurchases: purchases.filter(purchase => purchase.status === 'معلقة').length\n    };\n  }\n\n  // البحث في المشتريات\n  searchPurchases(query) {\n    const purchases = this.getAllPurchases();\n    return purchases.filter(purchase => \n      purchase.invoiceNumber.toLowerCase().includes(query.toLowerCase()) ||\n      purchase.supplierName.toLowerCase().includes(query.toLowerCase()) ||\n      purchase.status.toLowerCase().includes(query.toLowerCase())\n    );\n  }\n}\n\nexport default new PurchasesAPI();\n"], "mappings": "AAAA;AACA,MAAMA,YAAY,CAAC;EACjBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,UAAU,GAAG,gBAAgB;IAClC,IAAI,CAACC,cAAc,CAAC,CAAC;EACvB;EAEAA,cAAcA,CAAA,EAAG;IACf,MAAMC,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,IAAI,CAACJ,UAAU,CAAC;IAC1D,IAAI,CAACE,YAAY,EAAE;MACjB,MAAMG,WAAW,GAAG;QAClBC,SAAS,EAAE,CACT;UACEC,EAAE,EAAE,CAAC;UACLC,aAAa,EAAE,cAAc;UAC7BC,UAAU,EAAE,CAAC;UACbC,YAAY,EAAE,uBAAuB;UACrCC,IAAI,EAAE,YAAY;UAClBC,KAAK,EAAE,CACL;YAAEC,SAAS,EAAE,CAAC;YAAEC,WAAW,EAAE,aAAa;YAAEC,QAAQ,EAAE,EAAE;YAAEC,KAAK,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAM,CAAC,EACrF;YAAEJ,SAAS,EAAE,CAAC;YAAEC,WAAW,EAAE,aAAa;YAAEC,QAAQ,EAAE,EAAE;YAAEC,KAAK,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAK,CAAC,CACpF;UACDC,QAAQ,EAAE,KAAK;UACfC,GAAG,EAAE,IAAI;UACTC,QAAQ,EAAE,GAAG;UACbH,KAAK,EAAE,KAAK;UACZI,MAAM,EAAE,QAAQ;UAChBC,aAAa,EAAE,YAAY;UAC3BC,OAAO,EAAE,YAAY;UACrBC,KAAK,EAAE;QACT,CAAC,EACD;UACEjB,EAAE,EAAE,CAAC;UACLC,aAAa,EAAE,cAAc;UAC7BC,UAAU,EAAE,CAAC;UACbC,YAAY,EAAE,oBAAoB;UAClCC,IAAI,EAAE,YAAY;UAClBC,KAAK,EAAE,CACL;YAAEC,SAAS,EAAE,CAAC;YAAEC,WAAW,EAAE,UAAU;YAAEC,QAAQ,EAAE,CAAC;YAAEC,KAAK,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAK,CAAC,CAChF;UACDC,QAAQ,EAAE,IAAI;UACdC,GAAG,EAAE,GAAG;UACRC,QAAQ,EAAE,CAAC;UACXH,KAAK,EAAE,IAAI;UACXI,MAAM,EAAE,OAAO;UACfC,aAAa,EAAE,KAAK;UACpBC,OAAO,EAAE,YAAY;UACrBC,KAAK,EAAE;QACT,CAAC,CACF;QACDC,MAAM,EAAE;MACV,CAAC;MACDtB,YAAY,CAACuB,OAAO,CAAC,IAAI,CAAC1B,UAAU,EAAE2B,IAAI,CAACC,SAAS,CAACvB,WAAW,CAAC,CAAC;IACpE;EACF;EAEAwB,OAAOA,CAAA,EAAG;IACR,OAAOF,IAAI,CAACG,KAAK,CAAC3B,YAAY,CAACC,OAAO,CAAC,IAAI,CAACJ,UAAU,CAAC,CAAC;EAC1D;EAEA+B,QAAQA,CAACC,IAAI,EAAE;IACb7B,YAAY,CAACuB,OAAO,CAAC,IAAI,CAAC1B,UAAU,EAAE2B,IAAI,CAACC,SAAS,CAACI,IAAI,CAAC,CAAC;EAC7D;;EAEA;EACAC,eAAeA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACJ,OAAO,CAAC,CAAC,CAACvB,SAAS;EACjC;;EAEA;EACA4B,WAAWA,CAACC,YAAY,EAAE;IACxB,MAAMH,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC,CAAC;IAC3B,MAAMO,WAAW,GAAG;MAClB7B,EAAE,EAAEyB,IAAI,CAACP,MAAM,GAAG,CAAC;MACnBjB,aAAa,EAAE,OAAO,IAAI6B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,IAAIC,MAAM,CAACP,IAAI,CAACP,MAAM,GAAG,CAAC,CAAC,CAACe,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;MAC5F7B,IAAI,EAAE,IAAI0B,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC5CrB,MAAM,EAAE,OAAO;MACf,GAAGc;IACL,CAAC;IAEDH,IAAI,CAAC1B,SAAS,CAACqC,IAAI,CAACP,WAAW,CAAC;IAChCJ,IAAI,CAACP,MAAM,IAAI,CAAC;IAChB,IAAI,CAACM,QAAQ,CAACC,IAAI,CAAC;IACnB,OAAOI,WAAW;EACpB;;EAEA;EACAQ,cAAcA,CAACrC,EAAE,EAAE4B,YAAY,EAAE;IAC/B,MAAMH,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC,CAAC;IAC3B,MAAMgB,KAAK,GAAGb,IAAI,CAAC1B,SAAS,CAACwC,SAAS,CAACC,QAAQ,IAAIA,QAAQ,CAACxC,EAAE,KAAKA,EAAE,CAAC;IACtE,IAAIsC,KAAK,KAAK,CAAC,CAAC,EAAE;MAChBb,IAAI,CAAC1B,SAAS,CAACuC,KAAK,CAAC,GAAG;QAAE,GAAGb,IAAI,CAAC1B,SAAS,CAACuC,KAAK,CAAC;QAAE,GAAGV;MAAa,CAAC;MACrE,IAAI,CAACJ,QAAQ,CAACC,IAAI,CAAC;MACnB,OAAOA,IAAI,CAAC1B,SAAS,CAACuC,KAAK,CAAC;IAC9B;IACA,OAAO,IAAI;EACb;;EAEA;EACAG,cAAcA,CAACzC,EAAE,EAAE;IACjB,MAAMyB,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC,CAAC;IAC3BG,IAAI,CAAC1B,SAAS,GAAG0B,IAAI,CAAC1B,SAAS,CAAC2C,MAAM,CAACF,QAAQ,IAAIA,QAAQ,CAACxC,EAAE,KAAKA,EAAE,CAAC;IACtE,IAAI,CAACwB,QAAQ,CAACC,IAAI,CAAC;IACnB,OAAO,IAAI;EACb;;EAEA;EACAkB,eAAeA,CAAC3C,EAAE,EAAE;IAClB,MAAMyB,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC,CAAC;IAC3B,OAAOG,IAAI,CAAC1B,SAAS,CAAC6C,IAAI,CAACJ,QAAQ,IAAIA,QAAQ,CAACxC,EAAE,KAAKA,EAAE,CAAC;EAC5D;;EAEA;EACA6C,iBAAiBA,CAAA,EAAG;IAClB,MAAM9C,SAAS,GAAG,IAAI,CAAC2B,eAAe,CAAC,CAAC;IACxC,MAAMoB,KAAK,GAAG,IAAIhB,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACpD,MAAMY,SAAS,GAAG,IAAIjB,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,CAACc,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IAEtD,OAAO;MACLC,cAAc,EAAElD,SAAS,CAACmD,MAAM,CAAC,CAACC,GAAG,EAAEX,QAAQ,KAAKW,GAAG,GAAGX,QAAQ,CAAC9B,KAAK,EAAE,CAAC,CAAC;MAC5E0C,cAAc,EAAErD,SAAS,CAAC2C,MAAM,CAACF,QAAQ,IAAIA,QAAQ,CAACpC,IAAI,KAAK0C,KAAK,CAAC,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEX,QAAQ,KAAKW,GAAG,GAAGX,QAAQ,CAAC9B,KAAK,EAAE,CAAC,CAAC;MACxH2C,cAAc,EAAEtD,SAAS,CAAC2C,MAAM,CAACF,QAAQ,IAAIA,QAAQ,CAACpC,IAAI,CAACkD,UAAU,CAACP,SAAS,CAAC,CAAC,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEX,QAAQ,KAAKW,GAAG,GAAGX,QAAQ,CAAC9B,KAAK,EAAE,CAAC,CAAC;MACpI6C,aAAa,EAAExD,SAAS,CAACyD,MAAM;MAC/BC,kBAAkB,EAAE1D,SAAS,CAAC2C,MAAM,CAACF,QAAQ,IAAIA,QAAQ,CAAC1B,MAAM,KAAK,QAAQ,CAAC,CAAC0C,MAAM;MACrFE,gBAAgB,EAAE3D,SAAS,CAAC2C,MAAM,CAACF,QAAQ,IAAIA,QAAQ,CAAC1B,MAAM,KAAK,OAAO,CAAC,CAAC0C;IAC9E,CAAC;EACH;;EAEA;EACAG,eAAeA,CAACC,KAAK,EAAE;IACrB,MAAM7D,SAAS,GAAG,IAAI,CAAC2B,eAAe,CAAC,CAAC;IACxC,OAAO3B,SAAS,CAAC2C,MAAM,CAACF,QAAQ,IAC9BA,QAAQ,CAACvC,aAAa,CAAC4D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC,IAClErB,QAAQ,CAACrC,YAAY,CAAC0D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC,IACjErB,QAAQ,CAAC1B,MAAM,CAAC+C,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,KAAK,CAACC,WAAW,CAAC,CAAC,CAC5D,CAAC;EACH;AACF;AAEA,eAAe,IAAItE,YAAY,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}