import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import CompanyManagement from './CompanyManagement';

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

describe('CompanyManagement', () => {
  beforeEach(() => {
    localStorageMock.getItem.mockReturnValue(JSON.stringify({
      companies: [],
      lastId: 0
    }));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('renders company management interface', () => {
    render(<CompanyManagement />);
    
    // Check if main title is rendered
    expect(screen.getByText('إدارة الشركات')).toBeInTheDocument();
    
    // Check if add company button is rendered
    expect(screen.getByText('إضافة شركة جديدة')).toBeInTheDocument();
    
    // Check if search input is rendered
    expect(screen.getByPlaceholderText('ابحث عن شركة...')).toBeInTheDocument();
  });

  test('opens add company modal when button is clicked', async () => {
    render(<CompanyManagement />);
    
    const addButton = screen.getByText('إضافة شركة جديدة');
    fireEvent.click(addButton);
    
    await waitFor(() => {
      expect(screen.getByText('إضافة شركة جديدة')).toBeInTheDocument();
    });
  });

  test('validates required fields', async () => {
    render(<CompanyManagement />);
    
    const addButton = screen.getByText('إضافة شركة جديدة');
    fireEvent.click(addButton);
    
    await waitFor(() => {
      const submitButton = screen.getByText('إضافة شركة');
      fireEvent.click(submitButton);
    });
    
    // Should show validation errors for required fields
    await waitFor(() => {
      expect(screen.getByText('اسم الشركة مطلوب')).toBeInTheDocument();
      expect(screen.getByText('الرقم الضريبي مطلوب')).toBeInTheDocument();
    });
  });

  test('adds a new company successfully', async () => {
    render(<CompanyManagement />);
    
    const addButton = screen.getByText('إضافة شركة جديدة');
    fireEvent.click(addButton);
    
    await waitFor(() => {
      // Fill in required fields
      const nameInput = screen.getByLabelText('اسم الشركة *');
      const taxIdInput = screen.getByLabelText('الرقم الضريبي *');
      
      fireEvent.change(nameInput, { target: { value: 'شركة تجريبية' } });
      fireEvent.change(taxIdInput, { target: { value: '*********' } });
      
      const submitButton = screen.getByText('إضافة شركة');
      fireEvent.click(submitButton);
    });
    
    // Check if localStorage was called to save the company
    await waitFor(() => {
      expect(localStorageMock.setItem).toHaveBeenCalled();
    });
  });

  test('filters companies based on search term', async () => {
    // Mock localStorage with some test data
    localStorageMock.getItem.mockReturnValue(JSON.stringify({
      companies: [
        { company_id: 1, name: 'شركة الاختبار', tax_id: '123', address: '', contact_person: '', email: '', phone: '' },
        { company_id: 2, name: 'شركة أخرى', tax_id: '456', address: '', contact_person: '', email: '', phone: '' }
      ],
      lastId: 2
    }));
    
    render(<CompanyManagement />);
    
    await waitFor(() => {
      expect(screen.getByText('شركة الاختبار')).toBeInTheDocument();
      expect(screen.getByText('شركة أخرى')).toBeInTheDocument();
    });
    
    const searchInput = screen.getByPlaceholderText('ابحث عن شركة...');
    fireEvent.change(searchInput, { target: { value: 'الاختبار' } });
    
    await waitFor(() => {
      expect(screen.getByText('شركة الاختبار')).toBeInTheDocument();
      expect(screen.queryByText('شركة أخرى')).not.toBeInTheDocument();
    });
  });
});
