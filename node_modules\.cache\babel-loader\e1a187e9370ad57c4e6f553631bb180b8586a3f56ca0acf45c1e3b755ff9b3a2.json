{"ast": null, "code": "import ascending from \"./ascending.js\";\nexport default function greatest(values, compare = ascending) {\n  let max;\n  let defined = false;\n  if (compare.length === 1) {\n    let maxValue;\n    for (const element of values) {\n      const value = compare(element);\n      if (defined ? ascending(value, maxValue) > 0 : ascending(value, value) === 0) {\n        max = element;\n        maxValue = value;\n        defined = true;\n      }\n    }\n  } else {\n    for (const value of values) {\n      if (defined ? compare(value, max) > 0 : compare(value, value) === 0) {\n        max = value;\n        defined = true;\n      }\n    }\n  }\n  return max;\n}", "map": {"version": 3, "names": ["ascending", "greatest", "values", "compare", "max", "defined", "length", "maxValue", "element", "value"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/node_modules/d3-array/src/greatest.js"], "sourcesContent": ["import ascending from \"./ascending.js\";\n\nexport default function greatest(values, compare = ascending) {\n  let max;\n  let defined = false;\n  if (compare.length === 1) {\n    let maxValue;\n    for (const element of values) {\n      const value = compare(element);\n      if (defined\n          ? ascending(value, maxValue) > 0\n          : ascending(value, value) === 0) {\n        max = element;\n        maxValue = value;\n        defined = true;\n      }\n    }\n  } else {\n    for (const value of values) {\n      if (defined\n          ? compare(value, max) > 0\n          : compare(value, value) === 0) {\n        max = value;\n        defined = true;\n      }\n    }\n  }\n  return max;\n}\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;AAEtC,eAAe,SAASC,QAAQA,CAACC,MAAM,EAAEC,OAAO,GAAGH,SAAS,EAAE;EAC5D,IAAII,GAAG;EACP,IAAIC,OAAO,GAAG,KAAK;EACnB,IAAIF,OAAO,CAACG,MAAM,KAAK,CAAC,EAAE;IACxB,IAAIC,QAAQ;IACZ,KAAK,MAAMC,OAAO,IAAIN,MAAM,EAAE;MAC5B,MAAMO,KAAK,GAAGN,OAAO,CAACK,OAAO,CAAC;MAC9B,IAAIH,OAAO,GACLL,SAAS,CAACS,KAAK,EAAEF,QAAQ,CAAC,GAAG,CAAC,GAC9BP,SAAS,CAACS,KAAK,EAAEA,KAAK,CAAC,KAAK,CAAC,EAAE;QACnCL,GAAG,GAAGI,OAAO;QACbD,QAAQ,GAAGE,KAAK;QAChBJ,OAAO,GAAG,IAAI;MAChB;IACF;EACF,CAAC,MAAM;IACL,KAAK,MAAMI,KAAK,IAAIP,MAAM,EAAE;MAC1B,IAAIG,OAAO,GACLF,OAAO,CAACM,KAAK,EAAEL,GAAG,CAAC,GAAG,CAAC,GACvBD,OAAO,CAACM,KAAK,EAAEA,KAAK,CAAC,KAAK,CAAC,EAAE;QACjCL,GAAG,GAAGK,KAAK;QACXJ,OAAO,GAAG,IAAI;MAChB;IACF;EACF;EACA,OAAOD,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}