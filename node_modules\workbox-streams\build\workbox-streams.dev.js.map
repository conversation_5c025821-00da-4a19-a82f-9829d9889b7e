{"version": 3, "file": "workbox-streams.dev.js", "sources": ["../_version.js", "../concatenate.js", "../utils/createHeaders.js", "../concatenateToResponse.js", "../isSupported.js", "../strategy.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:streams:6.5.4'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { Deferred } from 'workbox-core/_private/Deferred.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport './_version.js';\n/**\n * Takes either a Response, a ReadableStream, or a\n * [BodyInit](https://fetch.spec.whatwg.org/#bodyinit) and returns the\n * ReadableStreamReader object associated with it.\n *\n * @param {workbox-streams.StreamSource} source\n * @return {ReadableStreamReader}\n * @private\n */\nfunction _getReaderFromSource(source) {\n    if (source instanceof Response) {\n        // See https://github.com/GoogleChrome/workbox/issues/2998\n        if (source.body) {\n            return source.body.getReader();\n        }\n        throw new WorkboxError('opaque-streams-source', { type: source.type });\n    }\n    if (source instanceof ReadableStream) {\n        return source.getReader();\n    }\n    return new Response(source).body.getReader();\n}\n/**\n * Takes multiple source Promises, each of which could resolve to a Response, a\n * ReadableStream, or a [BodyInit](https://fetch.spec.whatwg.org/#bodyinit).\n *\n * Returns an object exposing a ReadableStream with each individual stream's\n * data returned in sequence, along with a Promise which signals when the\n * stream is finished (useful for passing to a FetchEvent's waitUntil()).\n *\n * @param {Array<Promise<workbox-streams.StreamSource>>} sourcePromises\n * @return {Object<{done: Promise, stream: ReadableStream}>}\n *\n * @memberof workbox-streams\n */\nfunction concatenate(sourcePromises) {\n    if (process.env.NODE_ENV !== 'production') {\n        assert.isArray(sourcePromises, {\n            moduleName: 'workbox-streams',\n            funcName: 'concatenate',\n            paramName: 'sourcePromises',\n        });\n    }\n    const readerPromises = sourcePromises.map((sourcePromise) => {\n        return Promise.resolve(sourcePromise).then((source) => {\n            return _getReaderFromSource(source);\n        });\n    });\n    const streamDeferred = new Deferred();\n    let i = 0;\n    const logMessages = [];\n    const stream = new ReadableStream({\n        pull(controller) {\n            return readerPromises[i]\n                .then((reader) => {\n                if (reader instanceof ReadableStreamDefaultReader) {\n                    return reader.read();\n                }\n                else {\n                    return;\n                }\n            })\n                .then((result) => {\n                if (result === null || result === void 0 ? void 0 : result.done) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        logMessages.push([\n                            'Reached the end of source:',\n                            sourcePromises[i],\n                        ]);\n                    }\n                    i++;\n                    if (i >= readerPromises.length) {\n                        // Log all the messages in the group at once in a single group.\n                        if (process.env.NODE_ENV !== 'production') {\n                            logger.groupCollapsed(`Concatenating ${readerPromises.length} sources.`);\n                            for (const message of logMessages) {\n                                if (Array.isArray(message)) {\n                                    logger.log(...message);\n                                }\n                                else {\n                                    logger.log(message);\n                                }\n                            }\n                            logger.log('Finished reading all sources.');\n                            logger.groupEnd();\n                        }\n                        controller.close();\n                        streamDeferred.resolve();\n                        return;\n                    }\n                    // The `pull` method is defined because we're inside it.\n                    return this.pull(controller);\n                }\n                else {\n                    controller.enqueue(result === null || result === void 0 ? void 0 : result.value);\n                }\n            })\n                .catch((error) => {\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.error('An error occurred:', error);\n                }\n                streamDeferred.reject(error);\n                throw error;\n            });\n        },\n        cancel() {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.warn('The ReadableStream was cancelled.');\n            }\n            streamDeferred.resolve();\n        },\n    });\n    return { done: streamDeferred.promise, stream };\n}\nexport { concatenate };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * This is a utility method that determines whether the current browser supports\n * the features required to create streamed responses. Currently, it checks if\n * [`ReadableStream`](https://developer.mozilla.org/en-US/docs/Web/API/ReadableStream/ReadableStream)\n * is available.\n *\n * @private\n * @param {HeadersInit} [headersInit] If there's no `Content-Type` specified,\n * `'text/html'` will be used by default.\n * @return {boolean} `true`, if the current browser meets the requirements for\n * streaming responses, and `false` otherwise.\n *\n * @memberof workbox-streams\n */\nfunction createHeaders(headersInit = {}) {\n    // See https://github.com/GoogleChrome/workbox/issues/1461\n    const headers = new Headers(headersInit);\n    if (!headers.has('content-type')) {\n        headers.set('content-type', 'text/html');\n    }\n    return headers;\n}\nexport { createHeaders };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { createHeaders } from './utils/createHeaders.js';\nimport { concatenate } from './concatenate.js';\nimport './_version.js';\n/**\n * Takes multiple source Promises, each of which could resolve to a Response, a\n * ReadableStream, or a [BodyInit](https://fetch.spec.whatwg.org/#bodyinit),\n * along with a\n * [HeadersInit](https://fetch.spec.whatwg.org/#typedefdef-headersinit).\n *\n * Returns an object exposing a Response whose body consists of each individual\n * stream's data returned in sequence, along with a Promise which signals when\n * the stream is finished (useful for passing to a FetchEvent's waitUntil()).\n *\n * @param {Array<Promise<workbox-streams.StreamSource>>} sourcePromises\n * @param {HeadersInit} [headersInit] If there's no `Content-Type` specified,\n * `'text/html'` will be used by default.\n * @return {Object<{done: Promise, response: Response}>}\n *\n * @memberof workbox-streams\n */\nfunction concatenateToResponse(sourcePromises, headersInit) {\n    const { done, stream } = concatenate(sourcePromises);\n    const headers = createHeaders(headersInit);\n    const response = new Response(stream, { headers });\n    return { done, response };\n}\nexport { concatenateToResponse };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { canConstructReadableStream } from 'workbox-core/_private/canConstructReadableStream.js';\nimport './_version.js';\n/**\n * This is a utility method that determines whether the current browser supports\n * the features required to create streamed responses. Currently, it checks if\n * [`ReadableStream`](https://developer.mozilla.org/en-US/docs/Web/API/ReadableStream/ReadableStream)\n * can be created.\n *\n * @return {boolean} `true`, if the current browser meets the requirements for\n * streaming responses, and `false` otherwise.\n *\n * @memberof workbox-streams\n */\nfunction isSupported() {\n    return canConstructReadableStream();\n}\nexport { isSupported };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { createHeaders } from './utils/createHeaders.js';\nimport { concatenateToResponse } from './concatenateToResponse.js';\nimport { isSupported } from './isSupported.js';\nimport './_version.js';\n/**\n * A shortcut to create a strategy that could be dropped-in to Workbox's router.\n *\n * On browsers that do not support constructing new `ReadableStream`s, this\n * strategy will automatically wait for all the `sourceFunctions` to complete,\n * and create a final response that concatenates their values together.\n *\n * @param {Array<function({event, request, url, params})>} sourceFunctions\n * An array of functions similar to {@link workbox-routing~handlerCallback}\n * but that instead return a {@link workbox-streams.StreamSource} (or a\n * Promise which resolves to one).\n * @param {HeadersInit} [headersInit] If there's no `Content-Type` specified,\n * `'text/html'` will be used by default.\n * @return {workbox-routing~handlerCallback}\n * @memberof workbox-streams\n */\nfunction strategy(sourceFunctions, headersInit) {\n    return async ({ event, request, url, params }) => {\n        const sourcePromises = sourceFunctions.map((fn) => {\n            // Ensure the return value of the function is always a promise.\n            return Promise.resolve(fn({ event, request, url, params }));\n        });\n        if (isSupported()) {\n            const { done, response } = concatenateToResponse(sourcePromises, headersInit);\n            if (event) {\n                event.waitUntil(done);\n            }\n            return response;\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.log(`The current browser doesn't support creating response ` +\n                `streams. Falling back to non-streaming response instead.`);\n        }\n        // Fallback to waiting for everything to finish, and concatenating the\n        // responses.\n        const blobPartsPromises = sourcePromises.map(async (sourcePromise) => {\n            const source = await sourcePromise;\n            if (source instanceof Response) {\n                return source.blob();\n            }\n            else {\n                // Technically, a `StreamSource` object can include any valid\n                // `BodyInit` type, including `FormData` and `URLSearchParams`, which\n                // cannot be passed to the Blob constructor directly, so we have to\n                // convert them to actual Blobs first.\n                return new Response(source).blob();\n            }\n        });\n        const blobParts = await Promise.all(blobPartsPromises);\n        const headers = createHeaders(headersInit);\n        // Constructing a new Response from a Blob source is well-supported.\n        // So is constructing a new Blob from multiple source Blobs or strings.\n        return new Response(new Blob(blobParts), { headers });\n    };\n}\nexport { strategy };\n"], "names": ["self", "_", "e", "_getReaderFromSource", "source", "Response", "body", "<PERSON><PERSON><PERSON><PERSON>", "WorkboxError", "type", "ReadableStream", "concatenate", "sourcePromises", "assert", "isArray", "moduleName", "funcName", "paramName", "readerPromises", "map", "sourcePromise", "Promise", "resolve", "then", "streamDef<PERSON>red", "Deferred", "i", "logMessages", "stream", "pull", "controller", "reader", "ReadableStreamDefaultReader", "read", "result", "done", "push", "length", "logger", "groupCollapsed", "message", "Array", "log", "groupEnd", "close", "enqueue", "value", "catch", "error", "reject", "cancel", "warn", "promise", "createHeaders", "headersInit", "headers", "Headers", "has", "set", "concatenateToResponse", "response", "isSupported", "canConstructReadableStream", "strategy", "sourceFunctions", "event", "request", "url", "params", "fn", "waitUntil", "blobPartsPromises", "blob", "blobParts", "all", "Blob"], "mappings": ";;;;IAEA,IAAI;IACAA,EAAAA,IAAI,CAAC,uBAAD,CAAJ,IAAiCC,CAAC,EAAlC;IACH,CAFD,CAGA,OAAOC,CAAP,EAAU;;ICLV;IACA;AACA;IACA;IACA;IACA;IACA;IAMA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,SAASC,oBAAT,CAA8BC,MAA9B,EAAsC;IAClC,MAAIA,MAAM,YAAYC,QAAtB,EAAgC;IAC5B;IACA,QAAID,MAAM,CAACE,IAAX,EAAiB;IACb,aAAOF,MAAM,CAACE,IAAP,CAAYC,SAAZ,EAAP;IACH;;IACD,UAAM,IAAIC,4BAAJ,CAAiB,uBAAjB,EAA0C;IAAEC,MAAAA,IAAI,EAAEL,MAAM,CAACK;IAAf,KAA1C,CAAN;IACH;;IACD,MAAIL,MAAM,YAAYM,cAAtB,EAAsC;IAClC,WAAON,MAAM,CAACG,SAAP,EAAP;IACH;;IACD,SAAO,IAAIF,QAAJ,CAAaD,MAAb,EAAqBE,IAArB,CAA0BC,SAA1B,EAAP;IACH;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACA,SAASI,WAAT,CAAqBC,cAArB,EAAqC;IACjC,EAA2C;IACvCC,IAAAA,gBAAM,CAACC,OAAP,CAAeF,cAAf,EAA+B;IAC3BG,MAAAA,UAAU,EAAE,iBADe;IAE3BC,MAAAA,QAAQ,EAAE,aAFiB;IAG3BC,MAAAA,SAAS,EAAE;IAHgB,KAA/B;IAKH;;IACD,QAAMC,cAAc,GAAGN,cAAc,CAACO,GAAf,CAAoBC,aAAD,IAAmB;IACzD,WAAOC,OAAO,CAACC,OAAR,CAAgBF,aAAhB,EAA+BG,IAA/B,CAAqCnB,MAAD,IAAY;IACnD,aAAOD,oBAAoB,CAACC,MAAD,CAA3B;IACH,KAFM,CAAP;IAGH,GAJsB,CAAvB;IAKA,QAAMoB,cAAc,GAAG,IAAIC,oBAAJ,EAAvB;IACA,MAAIC,CAAC,GAAG,CAAR;IACA,QAAMC,WAAW,GAAG,EAApB;IACA,QAAMC,MAAM,GAAG,IAAIlB,cAAJ,CAAmB;IAC9BmB,IAAAA,IAAI,CAACC,UAAD,EAAa;IACb,aAAOZ,cAAc,CAACQ,CAAD,CAAd,CACFH,IADE,CACIQ,MAAD,IAAY;IAClB,YAAIA,MAAM,YAAYC,2BAAtB,EAAmD;IAC/C,iBAAOD,MAAM,CAACE,IAAP,EAAP;IACH,SAFD,MAGK;IACD;IACH;IACJ,OARM,EASFV,IATE,CASIW,MAAD,IAAY;IAClB,YAAIA,MAAM,KAAK,IAAX,IAAmBA,MAAM,KAAK,KAAK,CAAnC,GAAuC,KAAK,CAA5C,GAAgDA,MAAM,CAACC,IAA3D,EAAiE;IAC7D,UAA2C;IACvCR,YAAAA,WAAW,CAACS,IAAZ,CAAiB,CACb,4BADa,EAEbxB,cAAc,CAACc,CAAD,CAFD,CAAjB;IAIH;;IACDA,UAAAA,CAAC;;IACD,cAAIA,CAAC,IAAIR,cAAc,CAACmB,MAAxB,EAAgC;IAC5B;IACA,YAA2C;IACvCC,cAAAA,gBAAM,CAACC,cAAP,CAAuB,iBAAgBrB,cAAc,CAACmB,MAAO,WAA7D;;IACA,mBAAK,MAAMG,OAAX,IAAsBb,WAAtB,EAAmC;IAC/B,oBAAIc,KAAK,CAAC3B,OAAN,CAAc0B,OAAd,CAAJ,EAA4B;IACxBF,kBAAAA,gBAAM,CAACI,GAAP,CAAW,GAAGF,OAAd;IACH,iBAFD,MAGK;IACDF,kBAAAA,gBAAM,CAACI,GAAP,CAAWF,OAAX;IACH;IACJ;;IACDF,cAAAA,gBAAM,CAACI,GAAP,CAAW,+BAAX;IACAJ,cAAAA,gBAAM,CAACK,QAAP;IACH;;IACDb,YAAAA,UAAU,CAACc,KAAX;IACApB,YAAAA,cAAc,CAACF,OAAf;IACA;IACH,WA1B4D;;;IA4B7D,iBAAO,KAAKO,IAAL,CAAUC,UAAV,CAAP;IACH,SA7BD,MA8BK;IACDA,UAAAA,UAAU,CAACe,OAAX,CAAmBX,MAAM,KAAK,IAAX,IAAmBA,MAAM,KAAK,KAAK,CAAnC,GAAuC,KAAK,CAA5C,GAAgDA,MAAM,CAACY,KAA1E;IACH;IACJ,OA3CM,EA4CFC,KA5CE,CA4CKC,KAAD,IAAW;IAClB,QAA2C;IACvCV,UAAAA,gBAAM,CAACU,KAAP,CAAa,oBAAb,EAAmCA,KAAnC;IACH;;IACDxB,QAAAA,cAAc,CAACyB,MAAf,CAAsBD,KAAtB;IACA,cAAMA,KAAN;IACH,OAlDM,CAAP;IAmDH,KArD6B;;IAsD9BE,IAAAA,MAAM,GAAG;IACL,MAA2C;IACvCZ,QAAAA,gBAAM,CAACa,IAAP,CAAY,mCAAZ;IACH;;IACD3B,MAAAA,cAAc,CAACF,OAAf;IACH;;IA3D6B,GAAnB,CAAf;IA6DA,SAAO;IAAEa,IAAAA,IAAI,EAAEX,cAAc,CAAC4B,OAAvB;IAAgCxB,IAAAA;IAAhC,GAAP;IACH;;IC7HD;IACA;AACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,SAASyB,aAAT,CAAuBC,WAAW,GAAG,EAArC,EAAyC;IACrC;IACA,QAAMC,OAAO,GAAG,IAAIC,OAAJ,CAAYF,WAAZ,CAAhB;;IACA,MAAI,CAACC,OAAO,CAACE,GAAR,CAAY,cAAZ,CAAL,EAAkC;IAC9BF,IAAAA,OAAO,CAACG,GAAR,CAAY,cAAZ,EAA4B,WAA5B;IACH;;IACD,SAAOH,OAAP;IACH;;IC7BD;IACA;AACA;IACA;IACA;IACA;IACA;IAIA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,SAASI,qBAAT,CAA+B/C,cAA/B,EAA+C0C,WAA/C,EAA4D;IACxD,QAAM;IAAEnB,IAAAA,IAAF;IAAQP,IAAAA;IAAR,MAAmBjB,WAAW,CAACC,cAAD,CAApC;IACA,QAAM2C,OAAO,GAAGF,aAAa,CAACC,WAAD,CAA7B;IACA,QAAMM,QAAQ,GAAG,IAAIvD,QAAJ,CAAauB,MAAb,EAAqB;IAAE2B,IAAAA;IAAF,GAArB,CAAjB;IACA,SAAO;IAAEpB,IAAAA,IAAF;IAAQyB,IAAAA;IAAR,GAAP;IACH;;IChCD;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,SAASC,WAAT,GAAuB;IACnB,SAAOC,wDAA0B,EAAjC;IACH;;ICtBD;IACA;AACA;IACA;IACA;IACA;IACA;IAMA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,SAASC,QAAT,CAAkBC,eAAlB,EAAmCV,WAAnC,EAAgD;IAC5C,SAAO,OAAO;IAAEW,IAAAA,KAAF;IAASC,IAAAA,OAAT;IAAkBC,IAAAA,GAAlB;IAAuBC,IAAAA;IAAvB,GAAP,KAA2C;IAC9C,UAAMxD,cAAc,GAAGoD,eAAe,CAAC7C,GAAhB,CAAqBkD,EAAD,IAAQ;IAC/C;IACA,aAAOhD,OAAO,CAACC,OAAR,CAAgB+C,EAAE,CAAC;IAAEJ,QAAAA,KAAF;IAASC,QAAAA,OAAT;IAAkBC,QAAAA,GAAlB;IAAuBC,QAAAA;IAAvB,OAAD,CAAlB,CAAP;IACH,KAHsB,CAAvB;;IAIA,QAAIP,WAAW,EAAf,EAAmB;IACf,YAAM;IAAE1B,QAAAA,IAAF;IAAQyB,QAAAA;IAAR,UAAqBD,qBAAqB,CAAC/C,cAAD,EAAiB0C,WAAjB,CAAhD;;IACA,UAAIW,KAAJ,EAAW;IACPA,QAAAA,KAAK,CAACK,SAAN,CAAgBnC,IAAhB;IACH;;IACD,aAAOyB,QAAP;IACH;;IACD,IAA2C;IACvCtB,MAAAA,gBAAM,CAACI,GAAP,CAAY,wDAAD,GACN,0DADL;IAEH,KAf6C;IAiB9C;;;IACA,UAAM6B,iBAAiB,GAAG3D,cAAc,CAACO,GAAf,CAAmB,MAAOC,aAAP,IAAyB;IAClE,YAAMhB,MAAM,GAAG,MAAMgB,aAArB;;IACA,UAAIhB,MAAM,YAAYC,QAAtB,EAAgC;IAC5B,eAAOD,MAAM,CAACoE,IAAP,EAAP;IACH,OAFD,MAGK;IACD;IACA;IACA;IACA;IACA,eAAO,IAAInE,QAAJ,CAAaD,MAAb,EAAqBoE,IAArB,EAAP;IACH;IACJ,KAZyB,CAA1B;IAaA,UAAMC,SAAS,GAAG,MAAMpD,OAAO,CAACqD,GAAR,CAAYH,iBAAZ,CAAxB;IACA,UAAMhB,OAAO,GAAGF,aAAa,CAACC,WAAD,CAA7B,CAhC8C;IAkC9C;;IACA,WAAO,IAAIjD,QAAJ,CAAa,IAAIsE,IAAJ,CAASF,SAAT,CAAb,EAAkC;IAAElB,MAAAA;IAAF,KAAlC,CAAP;IACH,GApCD;IAqCH;;;;;;;;;;;;;"}