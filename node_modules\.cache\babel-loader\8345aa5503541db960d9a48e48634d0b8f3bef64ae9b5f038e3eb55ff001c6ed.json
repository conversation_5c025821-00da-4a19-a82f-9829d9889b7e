{"ast": null, "code": "function r(n) {\n  let e = n.parentElement,\n    l = null;\n  for (; e && !(e instanceof HTMLFieldSetElement);) e instanceof HTMLLegendElement && (l = e), e = e.parentElement;\n  let t = (e == null ? void 0 : e.getAttribute(\"disabled\")) === \"\";\n  return t && i(l) ? !1 : t;\n}\nfunction i(n) {\n  if (!n) return !1;\n  let e = n.previousElementSibling;\n  for (; e !== null;) {\n    if (e instanceof HTMLLegendElement) return !1;\n    e = e.previousElementSibling;\n  }\n  return !0;\n}\nexport { r as isDisabledReactIssue7711 };", "map": {"version": 3, "names": ["r", "n", "e", "parentElement", "l", "HTMLFieldSetElement", "HTMLLegendElement", "t", "getAttribute", "i", "previousElementSibling", "isDisabledReactIssue7711"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/node_modules/@headlessui/react/dist/utils/bugs.js"], "sourcesContent": ["function r(n){let e=n.parentElement,l=null;for(;e&&!(e instanceof HTMLFieldSetElement);)e instanceof HTMLLegendElement&&(l=e),e=e.parentElement;let t=(e==null?void 0:e.getAttribute(\"disabled\"))===\"\";return t&&i(l)?!1:t}function i(n){if(!n)return!1;let e=n.previousElementSibling;for(;e!==null;){if(e instanceof HTMLLegendElement)return!1;e=e.previousElementSibling}return!0}export{r as isDisabledReactIssue7711};\n"], "mappings": "AAAA,SAASA,CAACA,CAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACD,CAAC,CAACE,aAAa;IAACC,CAAC,GAAC,IAAI;EAAC,OAAKF,CAAC,IAAE,EAAEA,CAAC,YAAYG,mBAAmB,CAAC,GAAEH,CAAC,YAAYI,iBAAiB,KAAGF,CAAC,GAACF,CAAC,CAAC,EAACA,CAAC,GAACA,CAAC,CAACC,aAAa;EAAC,IAAII,CAAC,GAAC,CAACL,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACM,YAAY,CAAC,UAAU,CAAC,MAAI,EAAE;EAAC,OAAOD,CAAC,IAAEE,CAAC,CAACL,CAAC,CAAC,GAAC,CAAC,CAAC,GAACG,CAAC;AAAA;AAAC,SAASE,CAACA,CAACR,CAAC,EAAC;EAAC,IAAG,CAACA,CAAC,EAAC,OAAM,CAAC,CAAC;EAAC,IAAIC,CAAC,GAACD,CAAC,CAACS,sBAAsB;EAAC,OAAKR,CAAC,KAAG,IAAI,GAAE;IAAC,IAAGA,CAAC,YAAYI,iBAAiB,EAAC,OAAM,CAAC,CAAC;IAACJ,CAAC,GAACA,CAAC,CAACQ,sBAAsB;EAAA;EAAC,OAAM,CAAC,CAAC;AAAA;AAAC,SAAOV,CAAC,IAAIW,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}