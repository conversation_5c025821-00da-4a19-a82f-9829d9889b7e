{"ast": null, "code": "// API لإدارة المبيعات\nclass SalesAPI {\n  constructor() {\n    this.storageKey = 'sales_data';\n    this.initializeData();\n  }\n  initializeData() {\n    const existingData = localStorage.getItem(this.storageKey);\n    if (!existingData) {\n      const initialData = {\n        sales: [{\n          id: 1,\n          invoiceNumber: 'INV-2024-001',\n          customerId: 1,\n          customerName: 'أحمد محمد',\n          date: '2024-01-15',\n          items: [{\n            productId: 1,\n            productName: 'لابتوب Dell',\n            quantity: 2,\n            price: 3500,\n            total: 7000\n          }, {\n            productId: 2,\n            productName: 'ماوس لاسلكي',\n            quantity: 5,\n            price: 150,\n            total: 750\n          }],\n          subtotal: 7750,\n          tax: 1162.5,\n          discount: 200,\n          total: 8712.5,\n          status: 'مكتملة',\n          paymentMethod: 'نقدي',\n          notes: 'عميل مميز - خصم خاص'\n        }, {\n          id: 2,\n          invoiceNumber: 'INV-2024-002',\n          customerId: 2,\n          customerName: 'فاطمة أحمد',\n          date: '2024-01-16',\n          items: [{\n            productId: 3,\n            productName: 'طابعة HP',\n            quantity: 1,\n            price: 800,\n            total: 800\n          }],\n          subtotal: 800,\n          tax: 120,\n          discount: 0,\n          total: 920,\n          status: 'معلقة',\n          paymentMethod: 'آجل',\n          notes: ''\n        }],\n        lastId: 2\n      };\n      localStorage.setItem(this.storageKey, JSON.stringify(initialData));\n    }\n  }\n  getData() {\n    return JSON.parse(localStorage.getItem(this.storageKey));\n  }\n  saveData(data) {\n    localStorage.setItem(this.storageKey, JSON.stringify(data));\n  }\n\n  // الحصول على جميع المبيعات\n  getAllSales() {\n    return this.getData().sales;\n  }\n\n  // إضافة فاتورة مبيعات جديدة\n  addSale(saleData) {\n    const data = this.getData();\n    const newSale = {\n      id: data.lastId + 1,\n      invoiceNumber: `INV-${new Date().getFullYear()}-${String(data.lastId + 1).padStart(3, '0')}`,\n      date: new Date().toISOString().split('T')[0],\n      status: 'مكتملة',\n      ...saleData\n    };\n    data.sales.push(newSale);\n    data.lastId += 1;\n    this.saveData(data);\n    return newSale;\n  }\n\n  // تحديث فاتورة مبيعات\n  updateSale(id, saleData) {\n    const data = this.getData();\n    const index = data.sales.findIndex(sale => sale.id === id);\n    if (index !== -1) {\n      data.sales[index] = {\n        ...data.sales[index],\n        ...saleData\n      };\n      this.saveData(data);\n      return data.sales[index];\n    }\n    return null;\n  }\n\n  // حذف فاتورة مبيعات\n  deleteSale(id) {\n    const data = this.getData();\n    data.sales = data.sales.filter(sale => sale.id !== id);\n    this.saveData(data);\n    return true;\n  }\n\n  // الحصول على فاتورة بالمعرف\n  getSaleById(id) {\n    const data = this.getData();\n    return data.sales.find(sale => sale.id === id);\n  }\n\n  // إحصائيات المبيعات\n  getSalesStats() {\n    const sales = this.getAllSales();\n    const today = new Date().toISOString().split('T')[0];\n    const thisMonth = new Date().toISOString().slice(0, 7);\n    return {\n      totalSales: sales.reduce((sum, sale) => sum + sale.total, 0),\n      todaySales: sales.filter(sale => sale.date === today).reduce((sum, sale) => sum + sale.total, 0),\n      monthSales: sales.filter(sale => sale.date.startsWith(thisMonth)).reduce((sum, sale) => sum + sale.total, 0),\n      totalInvoices: sales.length,\n      completedSales: sales.filter(sale => sale.status === 'مكتملة').length,\n      pendingSales: sales.filter(sale => sale.status === 'معلقة').length\n    };\n  }\n\n  // البحث في المبيعات\n  searchSales(query) {\n    const sales = this.getAllSales();\n    return sales.filter(sale => sale.invoiceNumber.toLowerCase().includes(query.toLowerCase()) || sale.customerName.toLowerCase().includes(query.toLowerCase()) || sale.status.toLowerCase().includes(query.toLowerCase()));\n  }\n}\nexport default new SalesAPI();", "map": {"version": 3, "names": ["SalesAPI", "constructor", "storageKey", "initializeData", "existingData", "localStorage", "getItem", "initialData", "sales", "id", "invoiceNumber", "customerId", "customerName", "date", "items", "productId", "productName", "quantity", "price", "total", "subtotal", "tax", "discount", "status", "paymentMethod", "notes", "lastId", "setItem", "JSON", "stringify", "getData", "parse", "saveData", "data", "getAllSales", "addSale", "saleData", "newSale", "Date", "getFullYear", "String", "padStart", "toISOString", "split", "push", "updateSale", "index", "findIndex", "sale", "deleteSale", "filter", "getSaleById", "find", "getSalesStats", "today", "thisMonth", "slice", "totalSales", "reduce", "sum", "todaySales", "monthSales", "startsWith", "totalInvoices", "length", "completedSales", "pendingSales", "searchSales", "query", "toLowerCase", "includes"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/src/api/salesAPI.js"], "sourcesContent": ["// API لإدارة المبيعات\nclass SalesAPI {\n  constructor() {\n    this.storageKey = 'sales_data';\n    this.initializeData();\n  }\n\n  initializeData() {\n    const existingData = localStorage.getItem(this.storageKey);\n    if (!existingData) {\n      const initialData = {\n        sales: [\n          {\n            id: 1,\n            invoiceNumber: 'INV-2024-001',\n            customerId: 1,\n            customerName: 'أحمد محمد',\n            date: '2024-01-15',\n            items: [\n              { productId: 1, productName: 'لابتوب Dell', quantity: 2, price: 3500, total: 7000 },\n              { productId: 2, productName: 'ماوس لاسلكي', quantity: 5, price: 150, total: 750 }\n            ],\n            subtotal: 7750,\n            tax: 1162.5,\n            discount: 200,\n            total: 8712.5,\n            status: 'مكتملة',\n            paymentMethod: 'نقدي',\n            notes: 'عميل مميز - خصم خاص'\n          },\n          {\n            id: 2,\n            invoiceNumber: 'INV-2024-002',\n            customerId: 2,\n            customerName: 'فاطمة أحمد',\n            date: '2024-01-16',\n            items: [\n              { productId: 3, productName: 'طابعة HP', quantity: 1, price: 800, total: 800 }\n            ],\n            subtotal: 800,\n            tax: 120,\n            discount: 0,\n            total: 920,\n            status: 'معلقة',\n            paymentMethod: 'آجل',\n            notes: ''\n          }\n        ],\n        lastId: 2\n      };\n      localStorage.setItem(this.storageKey, JSON.stringify(initialData));\n    }\n  }\n\n  getData() {\n    return JSON.parse(localStorage.getItem(this.storageKey));\n  }\n\n  saveData(data) {\n    localStorage.setItem(this.storageKey, JSON.stringify(data));\n  }\n\n  // الحصول على جميع المبيعات\n  getAllSales() {\n    return this.getData().sales;\n  }\n\n  // إضافة فاتورة مبيعات جديدة\n  addSale(saleData) {\n    const data = this.getData();\n    const newSale = {\n      id: data.lastId + 1,\n      invoiceNumber: `INV-${new Date().getFullYear()}-${String(data.lastId + 1).padStart(3, '0')}`,\n      date: new Date().toISOString().split('T')[0],\n      status: 'مكتملة',\n      ...saleData\n    };\n    \n    data.sales.push(newSale);\n    data.lastId += 1;\n    this.saveData(data);\n    return newSale;\n  }\n\n  // تحديث فاتورة مبيعات\n  updateSale(id, saleData) {\n    const data = this.getData();\n    const index = data.sales.findIndex(sale => sale.id === id);\n    if (index !== -1) {\n      data.sales[index] = { ...data.sales[index], ...saleData };\n      this.saveData(data);\n      return data.sales[index];\n    }\n    return null;\n  }\n\n  // حذف فاتورة مبيعات\n  deleteSale(id) {\n    const data = this.getData();\n    data.sales = data.sales.filter(sale => sale.id !== id);\n    this.saveData(data);\n    return true;\n  }\n\n  // الحصول على فاتورة بالمعرف\n  getSaleById(id) {\n    const data = this.getData();\n    return data.sales.find(sale => sale.id === id);\n  }\n\n  // إحصائيات المبيعات\n  getSalesStats() {\n    const sales = this.getAllSales();\n    const today = new Date().toISOString().split('T')[0];\n    const thisMonth = new Date().toISOString().slice(0, 7);\n    \n    return {\n      totalSales: sales.reduce((sum, sale) => sum + sale.total, 0),\n      todaySales: sales.filter(sale => sale.date === today).reduce((sum, sale) => sum + sale.total, 0),\n      monthSales: sales.filter(sale => sale.date.startsWith(thisMonth)).reduce((sum, sale) => sum + sale.total, 0),\n      totalInvoices: sales.length,\n      completedSales: sales.filter(sale => sale.status === 'مكتملة').length,\n      pendingSales: sales.filter(sale => sale.status === 'معلقة').length\n    };\n  }\n\n  // البحث في المبيعات\n  searchSales(query) {\n    const sales = this.getAllSales();\n    return sales.filter(sale => \n      sale.invoiceNumber.toLowerCase().includes(query.toLowerCase()) ||\n      sale.customerName.toLowerCase().includes(query.toLowerCase()) ||\n      sale.status.toLowerCase().includes(query.toLowerCase())\n    );\n  }\n}\n\nexport default new SalesAPI();\n"], "mappings": "AAAA;AACA,MAAMA,QAAQ,CAAC;EACbC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,UAAU,GAAG,YAAY;IAC9B,IAAI,CAACC,cAAc,CAAC,CAAC;EACvB;EAEAA,cAAcA,CAAA,EAAG;IACf,MAAMC,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,IAAI,CAACJ,UAAU,CAAC;IAC1D,IAAI,CAACE,YAAY,EAAE;MACjB,MAAMG,WAAW,GAAG;QAClBC,KAAK,EAAE,CACL;UACEC,EAAE,EAAE,CAAC;UACLC,aAAa,EAAE,cAAc;UAC7BC,UAAU,EAAE,CAAC;UACbC,YAAY,EAAE,WAAW;UACzBC,IAAI,EAAE,YAAY;UAClBC,KAAK,EAAE,CACL;YAAEC,SAAS,EAAE,CAAC;YAAEC,WAAW,EAAE,aAAa;YAAEC,QAAQ,EAAE,CAAC;YAAEC,KAAK,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAK,CAAC,EACnF;YAAEJ,SAAS,EAAE,CAAC;YAAEC,WAAW,EAAE,aAAa;YAAEC,QAAQ,EAAE,CAAC;YAAEC,KAAK,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAI,CAAC,CAClF;UACDC,QAAQ,EAAE,IAAI;UACdC,GAAG,EAAE,MAAM;UACXC,QAAQ,EAAE,GAAG;UACbH,KAAK,EAAE,MAAM;UACbI,MAAM,EAAE,QAAQ;UAChBC,aAAa,EAAE,MAAM;UACrBC,KAAK,EAAE;QACT,CAAC,EACD;UACEhB,EAAE,EAAE,CAAC;UACLC,aAAa,EAAE,cAAc;UAC7BC,UAAU,EAAE,CAAC;UACbC,YAAY,EAAE,YAAY;UAC1BC,IAAI,EAAE,YAAY;UAClBC,KAAK,EAAE,CACL;YAAEC,SAAS,EAAE,CAAC;YAAEC,WAAW,EAAE,UAAU;YAAEC,QAAQ,EAAE,CAAC;YAAEC,KAAK,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAI,CAAC,CAC/E;UACDC,QAAQ,EAAE,GAAG;UACbC,GAAG,EAAE,GAAG;UACRC,QAAQ,EAAE,CAAC;UACXH,KAAK,EAAE,GAAG;UACVI,MAAM,EAAE,OAAO;UACfC,aAAa,EAAE,KAAK;UACpBC,KAAK,EAAE;QACT,CAAC,CACF;QACDC,MAAM,EAAE;MACV,CAAC;MACDrB,YAAY,CAACsB,OAAO,CAAC,IAAI,CAACzB,UAAU,EAAE0B,IAAI,CAACC,SAAS,CAACtB,WAAW,CAAC,CAAC;IACpE;EACF;EAEAuB,OAAOA,CAAA,EAAG;IACR,OAAOF,IAAI,CAACG,KAAK,CAAC1B,YAAY,CAACC,OAAO,CAAC,IAAI,CAACJ,UAAU,CAAC,CAAC;EAC1D;EAEA8B,QAAQA,CAACC,IAAI,EAAE;IACb5B,YAAY,CAACsB,OAAO,CAAC,IAAI,CAACzB,UAAU,EAAE0B,IAAI,CAACC,SAAS,CAACI,IAAI,CAAC,CAAC;EAC7D;;EAEA;EACAC,WAAWA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACJ,OAAO,CAAC,CAAC,CAACtB,KAAK;EAC7B;;EAEA;EACA2B,OAAOA,CAACC,QAAQ,EAAE;IAChB,MAAMH,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC,CAAC;IAC3B,MAAMO,OAAO,GAAG;MACd5B,EAAE,EAAEwB,IAAI,CAACP,MAAM,GAAG,CAAC;MACnBhB,aAAa,EAAE,OAAO,IAAI4B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,IAAIC,MAAM,CAACP,IAAI,CAACP,MAAM,GAAG,CAAC,CAAC,CAACe,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;MAC5F5B,IAAI,EAAE,IAAIyB,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC5CpB,MAAM,EAAE,QAAQ;MAChB,GAAGa;IACL,CAAC;IAEDH,IAAI,CAACzB,KAAK,CAACoC,IAAI,CAACP,OAAO,CAAC;IACxBJ,IAAI,CAACP,MAAM,IAAI,CAAC;IAChB,IAAI,CAACM,QAAQ,CAACC,IAAI,CAAC;IACnB,OAAOI,OAAO;EAChB;;EAEA;EACAQ,UAAUA,CAACpC,EAAE,EAAE2B,QAAQ,EAAE;IACvB,MAAMH,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC,CAAC;IAC3B,MAAMgB,KAAK,GAAGb,IAAI,CAACzB,KAAK,CAACuC,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACvC,EAAE,KAAKA,EAAE,CAAC;IAC1D,IAAIqC,KAAK,KAAK,CAAC,CAAC,EAAE;MAChBb,IAAI,CAACzB,KAAK,CAACsC,KAAK,CAAC,GAAG;QAAE,GAAGb,IAAI,CAACzB,KAAK,CAACsC,KAAK,CAAC;QAAE,GAAGV;MAAS,CAAC;MACzD,IAAI,CAACJ,QAAQ,CAACC,IAAI,CAAC;MACnB,OAAOA,IAAI,CAACzB,KAAK,CAACsC,KAAK,CAAC;IAC1B;IACA,OAAO,IAAI;EACb;;EAEA;EACAG,UAAUA,CAACxC,EAAE,EAAE;IACb,MAAMwB,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC,CAAC;IAC3BG,IAAI,CAACzB,KAAK,GAAGyB,IAAI,CAACzB,KAAK,CAAC0C,MAAM,CAACF,IAAI,IAAIA,IAAI,CAACvC,EAAE,KAAKA,EAAE,CAAC;IACtD,IAAI,CAACuB,QAAQ,CAACC,IAAI,CAAC;IACnB,OAAO,IAAI;EACb;;EAEA;EACAkB,WAAWA,CAAC1C,EAAE,EAAE;IACd,MAAMwB,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC,CAAC;IAC3B,OAAOG,IAAI,CAACzB,KAAK,CAAC4C,IAAI,CAACJ,IAAI,IAAIA,IAAI,CAACvC,EAAE,KAAKA,EAAE,CAAC;EAChD;;EAEA;EACA4C,aAAaA,CAAA,EAAG;IACd,MAAM7C,KAAK,GAAG,IAAI,CAAC0B,WAAW,CAAC,CAAC;IAChC,MAAMoB,KAAK,GAAG,IAAIhB,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACpD,MAAMY,SAAS,GAAG,IAAIjB,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,CAACc,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IAEtD,OAAO;MACLC,UAAU,EAAEjD,KAAK,CAACkD,MAAM,CAAC,CAACC,GAAG,EAAEX,IAAI,KAAKW,GAAG,GAAGX,IAAI,CAAC7B,KAAK,EAAE,CAAC,CAAC;MAC5DyC,UAAU,EAAEpD,KAAK,CAAC0C,MAAM,CAACF,IAAI,IAAIA,IAAI,CAACnC,IAAI,KAAKyC,KAAK,CAAC,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEX,IAAI,KAAKW,GAAG,GAAGX,IAAI,CAAC7B,KAAK,EAAE,CAAC,CAAC;MAChG0C,UAAU,EAAErD,KAAK,CAAC0C,MAAM,CAACF,IAAI,IAAIA,IAAI,CAACnC,IAAI,CAACiD,UAAU,CAACP,SAAS,CAAC,CAAC,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEX,IAAI,KAAKW,GAAG,GAAGX,IAAI,CAAC7B,KAAK,EAAE,CAAC,CAAC;MAC5G4C,aAAa,EAAEvD,KAAK,CAACwD,MAAM;MAC3BC,cAAc,EAAEzD,KAAK,CAAC0C,MAAM,CAACF,IAAI,IAAIA,IAAI,CAACzB,MAAM,KAAK,QAAQ,CAAC,CAACyC,MAAM;MACrEE,YAAY,EAAE1D,KAAK,CAAC0C,MAAM,CAACF,IAAI,IAAIA,IAAI,CAACzB,MAAM,KAAK,OAAO,CAAC,CAACyC;IAC9D,CAAC;EACH;;EAEA;EACAG,WAAWA,CAACC,KAAK,EAAE;IACjB,MAAM5D,KAAK,GAAG,IAAI,CAAC0B,WAAW,CAAC,CAAC;IAChC,OAAO1B,KAAK,CAAC0C,MAAM,CAACF,IAAI,IACtBA,IAAI,CAACtC,aAAa,CAAC2D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC,IAC9DrB,IAAI,CAACpC,YAAY,CAACyD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC,IAC7DrB,IAAI,CAACzB,MAAM,CAAC8C,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,KAAK,CAACC,WAAW,CAAC,CAAC,CACxD,CAAC;EACH;AACF;AAEA,eAAe,IAAIrE,QAAQ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}