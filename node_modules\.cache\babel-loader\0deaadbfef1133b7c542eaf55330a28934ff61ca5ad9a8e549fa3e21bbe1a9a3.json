{"ast": null, "code": "// إعدادات الشركة الافتراضية - يجب تحديثها حسب بيانات شركتك\nexport const DEFAULT_COMPANY_CONFIG = {\n  // معلومات الشركة الأساسية\n  companyInfo: {\n    name: 'اسم شركتك هنا',\n    nameEn: 'Your Company Name Here',\n    logo: null,\n    // سيتم إضافة الشعار لاحقاً\n\n    // معلومات قانونية\n    vatNumber: '',\n    // الرقم الضريبي (15 رقم)\n    crNumber: '',\n    // رقم السجل التجاري\n    licenseNumber: '',\n    // رقم الرخصة التجارية\n\n    // معلومات الاتصال\n    address: {\n      street: '',\n      city: '',\n      region: '',\n      postalCode: '',\n      country: 'المملكة العربية السعودية'\n    },\n    contact: {\n      phone: '',\n      mobile: '',\n      email: '',\n      website: '',\n      fax: ''\n    },\n    // إعدادات البنك\n    bankInfo: {\n      bankName: '',\n      accountNumber: '',\n      iban: '',\n      swiftCode: ''\n    }\n  },\n  // إعدادات النظام\n  systemSettings: {\n    currency: 'ر.س',\n    currencyCode: 'SAR',\n    language: 'ar',\n    timezone: 'Asia/Riyadh',\n    dateFormat: 'DD/MM/YYYY',\n    // إعدادات الضرائب\n    defaultVatRate: 15,\n    // 15% ضريبة القيمة المضافة\n    vatEnabled: true,\n    // إعدادات الفوترة\n    invoicePrefix: 'INV',\n    purchasePrefix: 'PUR',\n    expensePrefix: 'EXP',\n    // إعدادات التنبيهات\n    lowStockThreshold: 10,\n    creditLimitWarning: true,\n    // إعدادات النسخ الاحتياطي\n    autoBackup: true,\n    backupFrequency: 'daily' // daily, weekly, monthly\n  },\n  // إعدادات الفوترة الإلكترونية\n  eInvoiceSettings: {\n    enabled: true,\n    phase2Enabled: true,\n    qrCodeEnabled: true,\n    autoGenerateQR: true,\n    // إعدادات ZATCA\n    zatcaEnvironment: 'sandbox',\n    // sandbox أو production\n    certificatePath: '',\n    privateKeyPath: ''\n  },\n  // إعدادات الطباعة\n  printSettings: {\n    paperSize: 'A4',\n    orientation: 'portrait',\n    margins: {\n      top: 20,\n      bottom: 20,\n      left: 20,\n      right: 20\n    },\n    // إعدادات الفاتورة\n    showLogo: true,\n    showQRCode: true,\n    showBankInfo: true,\n    showTermsAndConditions: true,\n    // نص الشروط والأحكام\n    termsAndConditions: `\nالشروط والأحكام:\n1. جميع الأسعار شاملة ضريبة القيمة المضافة\n2. الدفع مستحق خلال 30 يوم من تاريخ الفاتورة\n3. يحق للشركة تحصيل فوائد تأخير 2% شهرياً\n4. البضاعة المباعة لا ترد ولا تستبدل إلا بعذر مقبول\n5. أي نزاع يحل وفقاً لأنظمة المملكة العربية السعودية\n    `.trim()\n  }\n};\n\n// دالة للحصول على إعدادات الشركة\nexport const getCompanyConfig = () => {\n  const savedConfig = localStorage.getItem('company_config');\n  if (savedConfig) {\n    try {\n      const parsed = JSON.parse(savedConfig);\n      // دمج الإعدادات المحفوظة مع الافتراضية\n      return {\n        ...DEFAULT_COMPANY_CONFIG,\n        ...parsed,\n        companyInfo: {\n          ...DEFAULT_COMPANY_CONFIG.companyInfo,\n          ...parsed.companyInfo\n        },\n        systemSettings: {\n          ...DEFAULT_COMPANY_CONFIG.systemSettings,\n          ...parsed.systemSettings\n        }\n      };\n    } catch (error) {\n      console.error('خطأ في قراءة إعدادات الشركة:', error);\n      return DEFAULT_COMPANY_CONFIG;\n    }\n  }\n  return DEFAULT_COMPANY_CONFIG;\n};\n\n// دالة لحفظ إعدادات الشركة\nexport const saveCompanyConfig = config => {\n  try {\n    localStorage.setItem('company_config', JSON.stringify(config));\n    return true;\n  } catch (error) {\n    console.error('خطأ في حفظ إعدادات الشركة:', error);\n    return false;\n  }\n};\n\n// دالة للتحقق من اكتمال إعدادات الشركة\nexport const validateCompanyConfig = config => {\n  const errors = [];\n  if (!config.companyInfo.name.trim()) {\n    errors.push('اسم الشركة مطلوب');\n  }\n  if (!config.companyInfo.vatNumber || config.companyInfo.vatNumber.length !== 15) {\n    errors.push('الرقم الضريبي يجب أن يكون 15 رقم');\n  }\n  if (!config.companyInfo.contact.phone.trim()) {\n    errors.push('رقم الهاتف مطلوب');\n  }\n  if (!config.companyInfo.contact.email.trim()) {\n    errors.push('البريد الإلكتروني مطلوب');\n  }\n  if (!config.companyInfo.address.city.trim()) {\n    errors.push('المدينة مطلوبة');\n  }\n  return {\n    isValid: errors.length === 0,\n    errors\n  };\n};\n\n// دالة لإنشاء إعدادات شركة جديدة\nexport const createNewCompanyConfig = companyData => {\n  return {\n    ...DEFAULT_COMPANY_CONFIG,\n    companyInfo: {\n      ...DEFAULT_COMPANY_CONFIG.companyInfo,\n      ...companyData\n    }\n  };\n};", "map": {"version": 3, "names": ["DEFAULT_COMPANY_CONFIG", "companyInfo", "name", "nameEn", "logo", "vatNumber", "crNumber", "licenseNumber", "address", "street", "city", "region", "postalCode", "country", "contact", "phone", "mobile", "email", "website", "fax", "bankInfo", "bankName", "accountNumber", "iban", "swiftCode", "systemSettings", "currency", "currencyCode", "language", "timezone", "dateFormat", "defaultVatRate", "vatEnabled", "invoicePrefix", "purchasePrefix", "expensePrefix", "lowStockThreshold", "creditLimitWarning", "autoBackup", "backupFrequency", "eInvoiceSettings", "enabled", "phase2Enabled", "qrCodeEnabled", "autoGenerateQR", "zatcaEnvironment", "certificatePath", "privateKeyPath", "printSettings", "paperSize", "orientation", "margins", "top", "bottom", "left", "right", "showLogo", "showQRCode", "showBankInfo", "showTermsAndConditions", "termsAndConditions", "trim", "getCompanyConfig", "savedConfig", "localStorage", "getItem", "parsed", "JSON", "parse", "error", "console", "saveCompanyConfig", "config", "setItem", "stringify", "validateCompanyConfig", "errors", "push", "length", "<PERSON><PERSON><PERSON><PERSON>", "createNewCompanyConfig", "companyData"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/src/config/companyConfig.js"], "sourcesContent": ["// إعدادات الشركة الافتراضية - يجب تحديثها حسب بيانات شركتك\nexport const DEFAULT_COMPANY_CONFIG = {\n  // معلومات الشركة الأساسية\n  companyInfo: {\n    name: 'اسم شركتك هنا',\n    nameEn: 'Your Company Name Here',\n    logo: null, // سيتم إضافة الشعار لاحقاً\n    \n    // معلومات قانونية\n    vatNumber: '', // الرقم الضريبي (15 رقم)\n    crNumber: '', // رقم السجل التجاري\n    licenseNumber: '', // رقم الرخصة التجارية\n    \n    // معلومات الاتصال\n    address: {\n      street: '',\n      city: '',\n      region: '',\n      postalCode: '',\n      country: 'المملكة العربية السعودية'\n    },\n    \n    contact: {\n      phone: '',\n      mobile: '',\n      email: '',\n      website: '',\n      fax: ''\n    },\n    \n    // إعدادات البنك\n    bankInfo: {\n      bankName: '',\n      accountNumber: '',\n      iban: '',\n      swiftCode: ''\n    }\n  },\n  \n  // إعدادات النظام\n  systemSettings: {\n    currency: 'ر.س',\n    currencyCode: 'SAR',\n    language: 'ar',\n    timezone: 'Asia/Riyadh',\n    dateFormat: 'DD/MM/YYYY',\n    \n    // إعدادات الضرائب\n    defaultVatRate: 15, // 15% ضريبة القيمة المضافة\n    vatEnabled: true,\n    \n    // إعدادات الفوترة\n    invoicePrefix: 'INV',\n    purchasePrefix: 'PUR',\n    expensePrefix: 'EXP',\n    \n    // إعدادات التنبيهات\n    lowStockThreshold: 10,\n    creditLimitWarning: true,\n    \n    // إعدادات النسخ الاحتياطي\n    autoBackup: true,\n    backupFrequency: 'daily' // daily, weekly, monthly\n  },\n  \n  // إعدادات الفوترة الإلكترونية\n  eInvoiceSettings: {\n    enabled: true,\n    phase2Enabled: true,\n    qrCodeEnabled: true,\n    autoGenerateQR: true,\n    \n    // إعدادات ZATCA\n    zatcaEnvironment: 'sandbox', // sandbox أو production\n    certificatePath: '',\n    privateKeyPath: ''\n  },\n  \n  // إعدادات الطباعة\n  printSettings: {\n    paperSize: 'A4',\n    orientation: 'portrait',\n    margins: {\n      top: 20,\n      bottom: 20,\n      left: 20,\n      right: 20\n    },\n    \n    // إعدادات الفاتورة\n    showLogo: true,\n    showQRCode: true,\n    showBankInfo: true,\n    showTermsAndConditions: true,\n    \n    // نص الشروط والأحكام\n    termsAndConditions: `\nالشروط والأحكام:\n1. جميع الأسعار شاملة ضريبة القيمة المضافة\n2. الدفع مستحق خلال 30 يوم من تاريخ الفاتورة\n3. يحق للشركة تحصيل فوائد تأخير 2% شهرياً\n4. البضاعة المباعة لا ترد ولا تستبدل إلا بعذر مقبول\n5. أي نزاع يحل وفقاً لأنظمة المملكة العربية السعودية\n    `.trim()\n  }\n};\n\n// دالة للحصول على إعدادات الشركة\nexport const getCompanyConfig = () => {\n  const savedConfig = localStorage.getItem('company_config');\n  if (savedConfig) {\n    try {\n      const parsed = JSON.parse(savedConfig);\n      // دمج الإعدادات المحفوظة مع الافتراضية\n      return {\n        ...DEFAULT_COMPANY_CONFIG,\n        ...parsed,\n        companyInfo: {\n          ...DEFAULT_COMPANY_CONFIG.companyInfo,\n          ...parsed.companyInfo\n        },\n        systemSettings: {\n          ...DEFAULT_COMPANY_CONFIG.systemSettings,\n          ...parsed.systemSettings\n        }\n      };\n    } catch (error) {\n      console.error('خطأ في قراءة إعدادات الشركة:', error);\n      return DEFAULT_COMPANY_CONFIG;\n    }\n  }\n  return DEFAULT_COMPANY_CONFIG;\n};\n\n// دالة لحفظ إعدادات الشركة\nexport const saveCompanyConfig = (config) => {\n  try {\n    localStorage.setItem('company_config', JSON.stringify(config));\n    return true;\n  } catch (error) {\n    console.error('خطأ في حفظ إعدادات الشركة:', error);\n    return false;\n  }\n};\n\n// دالة للتحقق من اكتمال إعدادات الشركة\nexport const validateCompanyConfig = (config) => {\n  const errors = [];\n  \n  if (!config.companyInfo.name.trim()) {\n    errors.push('اسم الشركة مطلوب');\n  }\n  \n  if (!config.companyInfo.vatNumber || config.companyInfo.vatNumber.length !== 15) {\n    errors.push('الرقم الضريبي يجب أن يكون 15 رقم');\n  }\n  \n  if (!config.companyInfo.contact.phone.trim()) {\n    errors.push('رقم الهاتف مطلوب');\n  }\n  \n  if (!config.companyInfo.contact.email.trim()) {\n    errors.push('البريد الإلكتروني مطلوب');\n  }\n  \n  if (!config.companyInfo.address.city.trim()) {\n    errors.push('المدينة مطلوبة');\n  }\n  \n  return {\n    isValid: errors.length === 0,\n    errors\n  };\n};\n\n// دالة لإنشاء إعدادات شركة جديدة\nexport const createNewCompanyConfig = (companyData) => {\n  return {\n    ...DEFAULT_COMPANY_CONFIG,\n    companyInfo: {\n      ...DEFAULT_COMPANY_CONFIG.companyInfo,\n      ...companyData\n    }\n  };\n};\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,sBAAsB,GAAG;EACpC;EACAC,WAAW,EAAE;IACXC,IAAI,EAAE,eAAe;IACrBC,MAAM,EAAE,wBAAwB;IAChCC,IAAI,EAAE,IAAI;IAAE;;IAEZ;IACAC,SAAS,EAAE,EAAE;IAAE;IACfC,QAAQ,EAAE,EAAE;IAAE;IACdC,aAAa,EAAE,EAAE;IAAE;;IAEnB;IACAC,OAAO,EAAE;MACPC,MAAM,EAAE,EAAE;MACVC,IAAI,EAAE,EAAE;MACRC,MAAM,EAAE,EAAE;MACVC,UAAU,EAAE,EAAE;MACdC,OAAO,EAAE;IACX,CAAC;IAEDC,OAAO,EAAE;MACPC,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,EAAE;MACVC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,GAAG,EAAE;IACP,CAAC;IAED;IACAC,QAAQ,EAAE;MACRC,QAAQ,EAAE,EAAE;MACZC,aAAa,EAAE,EAAE;MACjBC,IAAI,EAAE,EAAE;MACRC,SAAS,EAAE;IACb;EACF,CAAC;EAED;EACAC,cAAc,EAAE;IACdC,QAAQ,EAAE,KAAK;IACfC,YAAY,EAAE,KAAK;IACnBC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,aAAa;IACvBC,UAAU,EAAE,YAAY;IAExB;IACAC,cAAc,EAAE,EAAE;IAAE;IACpBC,UAAU,EAAE,IAAI;IAEhB;IACAC,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,KAAK;IACrBC,aAAa,EAAE,KAAK;IAEpB;IACAC,iBAAiB,EAAE,EAAE;IACrBC,kBAAkB,EAAE,IAAI;IAExB;IACAC,UAAU,EAAE,IAAI;IAChBC,eAAe,EAAE,OAAO,CAAC;EAC3B,CAAC;EAED;EACAC,gBAAgB,EAAE;IAChBC,OAAO,EAAE,IAAI;IACbC,aAAa,EAAE,IAAI;IACnBC,aAAa,EAAE,IAAI;IACnBC,cAAc,EAAE,IAAI;IAEpB;IACAC,gBAAgB,EAAE,SAAS;IAAE;IAC7BC,eAAe,EAAE,EAAE;IACnBC,cAAc,EAAE;EAClB,CAAC;EAED;EACAC,aAAa,EAAE;IACbC,SAAS,EAAE,IAAI;IACfC,WAAW,EAAE,UAAU;IACvBC,OAAO,EAAE;MACPC,GAAG,EAAE,EAAE;MACPC,MAAM,EAAE,EAAE;MACVC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE;IACT,CAAC;IAED;IACAC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,IAAI;IAChBC,YAAY,EAAE,IAAI;IAClBC,sBAAsB,EAAE,IAAI;IAE5B;IACAC,kBAAkB,EAAE;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,CAACC,IAAI,CAAC;EACT;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EACpC,MAAMC,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;EAC1D,IAAIF,WAAW,EAAE;IACf,IAAI;MACF,MAAMG,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACL,WAAW,CAAC;MACtC;MACA,OAAO;QACL,GAAG/D,sBAAsB;QACzB,GAAGkE,MAAM;QACTjE,WAAW,EAAE;UACX,GAAGD,sBAAsB,CAACC,WAAW;UACrC,GAAGiE,MAAM,CAACjE;QACZ,CAAC;QACDwB,cAAc,EAAE;UACd,GAAGzB,sBAAsB,CAACyB,cAAc;UACxC,GAAGyC,MAAM,CAACzC;QACZ;MACF,CAAC;IACH,CAAC,CAAC,OAAO4C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAOrE,sBAAsB;IAC/B;EACF;EACA,OAAOA,sBAAsB;AAC/B,CAAC;;AAED;AACA,OAAO,MAAMuE,iBAAiB,GAAIC,MAAM,IAAK;EAC3C,IAAI;IACFR,YAAY,CAACS,OAAO,CAAC,gBAAgB,EAAEN,IAAI,CAACO,SAAS,CAACF,MAAM,CAAC,CAAC;IAC9D,OAAO,IAAI;EACb,CAAC,CAAC,OAAOH,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IAClD,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA,OAAO,MAAMM,qBAAqB,GAAIH,MAAM,IAAK;EAC/C,MAAMI,MAAM,GAAG,EAAE;EAEjB,IAAI,CAACJ,MAAM,CAACvE,WAAW,CAACC,IAAI,CAAC2D,IAAI,CAAC,CAAC,EAAE;IACnCe,MAAM,CAACC,IAAI,CAAC,kBAAkB,CAAC;EACjC;EAEA,IAAI,CAACL,MAAM,CAACvE,WAAW,CAACI,SAAS,IAAImE,MAAM,CAACvE,WAAW,CAACI,SAAS,CAACyE,MAAM,KAAK,EAAE,EAAE;IAC/EF,MAAM,CAACC,IAAI,CAAC,kCAAkC,CAAC;EACjD;EAEA,IAAI,CAACL,MAAM,CAACvE,WAAW,CAACa,OAAO,CAACC,KAAK,CAAC8C,IAAI,CAAC,CAAC,EAAE;IAC5Ce,MAAM,CAACC,IAAI,CAAC,kBAAkB,CAAC;EACjC;EAEA,IAAI,CAACL,MAAM,CAACvE,WAAW,CAACa,OAAO,CAACG,KAAK,CAAC4C,IAAI,CAAC,CAAC,EAAE;IAC5Ce,MAAM,CAACC,IAAI,CAAC,yBAAyB,CAAC;EACxC;EAEA,IAAI,CAACL,MAAM,CAACvE,WAAW,CAACO,OAAO,CAACE,IAAI,CAACmD,IAAI,CAAC,CAAC,EAAE;IAC3Ce,MAAM,CAACC,IAAI,CAAC,gBAAgB,CAAC;EAC/B;EAEA,OAAO;IACLE,OAAO,EAAEH,MAAM,CAACE,MAAM,KAAK,CAAC;IAC5BF;EACF,CAAC;AACH,CAAC;;AAED;AACA,OAAO,MAAMI,sBAAsB,GAAIC,WAAW,IAAK;EACrD,OAAO;IACL,GAAGjF,sBAAsB;IACzBC,WAAW,EAAE;MACX,GAAGD,sBAAsB,CAACC,WAAW;MACrC,GAAGgF;IACL;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}