import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FunnelIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';

const ReportFilters = () => {
  const [filters, setFilters] = useState({
    dateFrom: '',
    dateTo: '',
    companyType: '',
    status: '',
    searchTerm: ''
  });

  const [showFilters, setShowFilters] = useState(false);

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const clearFilters = () => {
    setFilters({
      dateFrom: '',
      dateTo: '',
      companyType: '',
      status: '',
      searchTerm: ''
    });
  };

  const applyFilters = () => {
    console.log('تطبيق الفلاتر:', filters);
    // هنا يمكن إضافة منطق تطبيق الفلاتر
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            <FunnelIcon className="w-5 h-5 ml-2 text-gray-600" />
            فلاتر التقارير
          </CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
          >
            {showFilters ? 'إخفاء' : 'إظهار'} الفلاتر
          </Button>
        </div>
      </CardHeader>
      
      {showFilters && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
        >
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* البحث */}
              <div>
                <Label htmlFor="searchTerm" className="block text-sm font-medium text-gray-700 mb-2">
                  البحث
                </Label>
                <Input
                  id="searchTerm"
                  type="text"
                  placeholder="ابحث في الشركات..."
                  value={filters.searchTerm}
                  onChange={(e) => handleFilterChange('searchTerm', e.target.value)}
                />
              </div>

              {/* تاريخ البداية */}
              <div>
                <Label htmlFor="dateFrom" className="block text-sm font-medium text-gray-700 mb-2">
                  من تاريخ
                </Label>
                <Input
                  id="dateFrom"
                  type="date"
                  value={filters.dateFrom}
                  onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                />
              </div>

              {/* تاريخ النهاية */}
              <div>
                <Label htmlFor="dateTo" className="block text-sm font-medium text-gray-700 mb-2">
                  إلى تاريخ
                </Label>
                <Input
                  id="dateTo"
                  type="date"
                  value={filters.dateTo}
                  onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                />
              </div>

              {/* نوع الشركة */}
              <div>
                <Label htmlFor="companyType" className="block text-sm font-medium text-gray-700 mb-2">
                  نوع الشركة
                </Label>
                <select
                  id="companyType"
                  value={filters.companyType}
                  onChange={(e) => handleFilterChange('companyType', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">جميع الأنواع</option>
                  <option value="tech">شركات تقنية</option>
                  <option value="commercial">شركات تجارية</option>
                  <option value="industrial">شركات صناعية</option>
                  <option value="service">شركات خدمية</option>
                </select>
              </div>

              {/* الحالة */}
              <div>
                <Label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-2">
                  الحالة
                </Label>
                <select
                  id="status"
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">جميع الحالات</option>
                  <option value="active">نشطة</option>
                  <option value="inactive">غير نشطة</option>
                  <option value="pending">قيد المراجعة</option>
                </select>
              </div>
            </div>

            {/* أزرار الإجراءات */}
            <div className="flex items-center justify-end space-x-4 space-x-reverse mt-6 pt-4 border-t border-gray-200">
              <Button
                variant="outline"
                onClick={clearFilters}
                className="flex items-center"
              >
                <XMarkIcon className="w-4 h-4 ml-2" />
                مسح الفلاتر
              </Button>
              <Button
                onClick={applyFilters}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                تطبيق الفلاتر
              </Button>
            </div>
          </CardContent>
        </motion.div>
      )}
    </Card>
  );
};

export default ReportFilters;
