{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u0633\\u0637\\u062D \\u0627\\u0644\\u0645\\u0643\\u062A\\u0628\\\\333\\\\src\\\\pages\\\\QRCodeTest.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { QrCodeIcon, DocumentTextIcon, CheckCircleIcon } from '@heroicons/react/24/outline';\nimport { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';\nimport { Button } from '../components/ui/button';\nimport InvoiceQRCode from '../components/QRCode/InvoiceQRCode';\nimport { generateQRCodeData, generateSimpleQRCode, validateSaudiVATNumber, testQRCodeReading, decodeQRCodeData } from '../utils/qrCodeGenerator';\nimport { comprehensiveQRTest, runMultipleQRTests, generateQRReport } from '../utils/qrCodeTester';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QRCodeTest = () => {\n  _s();\n  const [testInvoice] = useState({\n    id: 1,\n    invoiceNumber: 'INV-2024-001',\n    customerId: 1,\n    customerName: 'أحمد محمد',\n    date: '2024-01-15',\n    items: [{\n      productId: 1,\n      productName: 'لابتوب Dell',\n      quantity: 2,\n      price: 3500,\n      total: 7000\n    }, {\n      productId: 2,\n      productName: 'ماوس لاسلكي',\n      quantity: 5,\n      price: 150,\n      total: 750\n    }],\n    subtotal: 7750,\n    tax: 1162.5,\n    discount: 200,\n    total: 8712.5,\n    status: 'مكتملة',\n    paymentMethod: 'نقدي',\n    notes: 'فاتورة اختبار للتحقق من QR Code'\n  });\n  const [qrData, setQrData] = useState('');\n  const [qrType, setQrType] = useState('phase2');\n  const [readingTest, setReadingTest] = useState(null);\n  const [comprehensiveTest, setComprehensiveTest] = useState(null);\n  const [multipleTests, setMultipleTests] = useState(null);\n  const generateTestQR = () => {\n    let data = '';\n    if (qrType === 'phase2') {\n      data = generateQRCodeData(testInvoice);\n    } else {\n      data = generateSimpleQRCode(testInvoice);\n    }\n    setQrData(data);\n\n    // اختبار قراءة QR Code\n    if (qrType === 'phase2' && data) {\n      const testResult = testQRCodeReading(data);\n      setReadingTest(testResult);\n    } else {\n      setReadingTest(null);\n    }\n  };\n  const testVATNumber = vatNumber => {\n    return validateSaudiVATNumber(vatNumber);\n  };\n  const runComprehensiveTest = () => {\n    const result = comprehensiveQRTest(testInvoice);\n    setComprehensiveTest(result);\n  };\n  const runMultipleTestSuite = () => {\n    const results = runMultipleQRTests();\n    const report = generateQRReport(results);\n    setMultipleTests({\n      results,\n      report\n    });\n  };\n  const testCases = [{\n    vatNumber: '300000000000003',\n    expected: true,\n    description: 'رقم ضريبي صحيح (15 رقم)'\n  }, {\n    vatNumber: '12345678901234',\n    expected: false,\n    description: 'رقم ضريبي قصير (14 رقم)'\n  }, {\n    vatNumber: '1234567890123456',\n    expected: false,\n    description: 'رقم ضريبي طويل (16 رقم)'\n  }, {\n    vatNumber: '30000000000000A',\n    expected: false,\n    description: 'رقم ضريبي يحتوي على أحرف'\n  }, {\n    vatNumber: '',\n    expected: false,\n    description: 'رقم ضريبي فارغ'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(QrCodeIcon, {\n          className: \"w-8 h-8 text-blue-600 ml-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold text-gray-900\",\n            children: \"\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631 QR Code\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mt-2\",\n            children: \"\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631 \\u0648\\u062A\\u062C\\u0631\\u0628\\u0629 QR Code \\u0644\\u0644\\u0641\\u0648\\u062A\\u0631\\u0629 \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.1\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n            children: /*#__PURE__*/_jsxDEV(CardTitle, {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(DocumentTextIcon, {\n                className: \"w-5 h-5 ml-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 17\n              }, this), \"\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629 \\u062A\\u062C\\u0631\\u064A\\u0628\\u064A\\u0629\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gray-50 p-4 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-gray-900 mb-3\",\n                  children: \"\\u062A\\u0641\\u0627\\u0635\\u064A\\u0644 \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 121,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-2 gap-3 text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-600\",\n                      children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 124,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-medium\",\n                      children: testInvoice.invoiceNumber\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 125,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 123,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-600\",\n                      children: \"\\u0627\\u0644\\u0639\\u0645\\u064A\\u0644:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 128,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-medium\",\n                      children: testInvoice.customerName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 129,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 127,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-600\",\n                      children: \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 132,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-medium\",\n                      children: new Date(testInvoice.date).toLocaleDateString('ar-SA')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 133,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 131,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-600\",\n                      children: \"\\u0627\\u0644\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 136,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-medium\",\n                      children: [testInvoice.total.toFixed(2), \" \\u0631.\\u0633\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 137,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 135,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(InvoiceQRCode, {\n                invoiceData: testInvoice,\n                size: 200,\n                showDetails: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.2\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n            children: /*#__PURE__*/_jsxDEV(CardTitle, {\n              children: \"\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631 \\u0645\\u0648\\u0644\\u062F QR Code\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"\\u0646\\u0648\\u0639 QR Code:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: qrType,\n                onChange: e => setQrType(e.target.value),\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"phase2\",\n                  children: \"\\u0627\\u0644\\u0645\\u0631\\u062D\\u0644\\u0629 \\u0627\\u0644\\u062B\\u0627\\u0646\\u064A\\u0629 (TLV + Base64)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"simple\",\n                  children: \"\\u0645\\u0628\\u0633\\u0637 (JSON)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: generateTestQR,\n              className: \"w-full bg-blue-600 hover:bg-blue-700 text-white\",\n              children: \"\\u0625\\u0646\\u0634\\u0627\\u0621 QR Code\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this), qrData && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-gray-900\",\n                children: \"\\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0645\\u064F\\u0646\\u0634\\u0623\\u0629:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gray-100 p-3 rounded text-xs font-mono break-all max-h-40 overflow-y-auto\",\n                children: qrData\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: [\"\\u0637\\u0648\\u0644 \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A: \", qrData.length, \" \\u062D\\u0631\\u0641\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 17\n            }, this), readingTest && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `p-4 rounded-lg border ${readingTest.isValid && readingTest.hasAllRequiredFields ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: `font-medium mb-3 ${readingTest.isValid && readingTest.hasAllRequiredFields ? 'text-green-800' : 'text-red-800'}`,\n                children: \"\\u0646\\u062A\\u064A\\u062C\\u0629 \\u0627\\u062E\\u062A\\u0628\\u0627\\u0631 \\u0627\\u0644\\u0642\\u0631\\u0627\\u0621\\u0629:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 19\n              }, this), readingTest.isValid && readingTest.hasAllRequiredFields ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center text-green-700\",\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                    className: \"w-5 h-5 ml-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium\",\n                    children: \"QR Code \\u0642\\u0627\\u0628\\u0644 \\u0644\\u0644\\u0642\\u0631\\u0627\\u0621\\u0629 \\u0628\\u0646\\u062C\\u0627\\u062D!\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 23\n                }, this), readingTest.data && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white p-3 rounded border border-green-200\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"font-medium text-green-800 mb-2\",\n                    children: \"\\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0645\\u0642\\u0631\\u0648\\u0621\\u0629:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 224,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 gap-2 text-sm\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-medium text-gray-600\",\n                        children: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0628\\u0627\\u0626\\u0639:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 227,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"mr-2\",\n                        children: readingTest.data.sellerName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 228,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 226,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-medium text-gray-600\",\n                        children: \"\\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u064A:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 231,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"mr-2\",\n                        children: readingTest.data.vatNumber\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 232,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 230,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-medium text-gray-600\",\n                        children: \"\\u0627\\u0644\\u0637\\u0627\\u0628\\u0639 \\u0627\\u0644\\u0632\\u0645\\u0646\\u064A:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 235,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"mr-2\",\n                        children: readingTest.data.timestamp\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 236,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 234,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-medium text-gray-600\",\n                        children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 239,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"mr-2\",\n                        children: [readingTest.data.invoiceTotal, \" \\u0631.\\u0633\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 240,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 238,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-medium text-gray-600\",\n                        children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u0629:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 243,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"mr-2\",\n                        children: [readingTest.data.vatAmount, \" \\u0631.\\u0633\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 244,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 242,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-red-700\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-red-500 text-xl ml-2\",\n                    children: \"\\u2717\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium\",\n                    children: \"\\u0641\\u0634\\u0644 \\u0641\\u064A \\u0642\\u0631\\u0627\\u0621\\u0629 QR Code\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 254,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm\",\n                  children: \"\\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u063A\\u064A\\u0631 \\u0645\\u0643\\u062A\\u0645\\u0644\\u0629 \\u0623\\u0648 \\u063A\\u064A\\u0631 \\u0635\\u062D\\u064A\\u062D\\u0629. \\u064A\\u0631\\u062C\\u0649 \\u0627\\u0644\\u062A\\u062D\\u0642\\u0642 \\u0645\\u0646 \\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0634\\u0631\\u0643\\u0629.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.3\n      },\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n          children: /*#__PURE__*/_jsxDEV(CardTitle, {\n            children: \"\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631 \\u0627\\u0644\\u062A\\u062D\\u0642\\u0642 \\u0645\\u0646 \\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u064A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600 mb-4\",\n              children: \"\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631 \\u062F\\u0627\\u0644\\u0629 \\u0627\\u0644\\u062A\\u062D\\u0642\\u0642 \\u0645\\u0646 \\u0635\\u062D\\u0629 \\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u064A \\u0627\\u0644\\u0633\\u0639\\u0648\\u062F\\u064A (15 \\u0631\\u0642\\u0645):\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: testCases.map((testCase, index) => {\n                const result = testVATNumber(testCase.vatNumber);\n                const isCorrect = result === testCase.expected;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `p-3 rounded-lg border ${isCorrect ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"font-mono text-sm\",\n                        children: testCase.vatNumber || '(فارغ)'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 296,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-gray-600\",\n                        children: testCase.description\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 299,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 295,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-2 space-x-reverse\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `text-xs px-2 py-1 rounded ${result ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                        children: result ? 'صحيح' : 'خطأ'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 304,\n                        columnNumber: 27\n                      }, this), isCorrect && /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                        className: \"w-4 h-4 text-green-600\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 310,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 303,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 294,\n                    columnNumber: 23\n                  }, this)\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.4\n      },\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n          children: /*#__PURE__*/_jsxDEV(CardTitle, {\n            children: \"\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u062A\\u0642\\u0646\\u064A\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-gray-900 mb-3\",\n                children: \"\\u0645\\u062A\\u0637\\u0644\\u0628\\u0627\\u062A \\u0627\\u0644\\u0645\\u0631\\u062D\\u0644\\u0629 \\u0627\\u0644\\u062B\\u0627\\u0646\\u064A\\u0629:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"space-y-2 text-sm text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                    className: \"w-4 h-4 text-green-500 ml-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 339,\n                    columnNumber: 21\n                  }, this), \"\\u062A\\u0646\\u0633\\u064A\\u0642 TLV (Tag-Length-Value)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                    className: \"w-4 h-4 text-green-500 ml-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 343,\n                    columnNumber: 21\n                  }, this), \"\\u062A\\u0634\\u0641\\u064A\\u0631 Base64\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                    className: \"w-4 h-4 text-green-500 ml-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 347,\n                    columnNumber: 21\n                  }, this), \"5 \\u062D\\u0642\\u0648\\u0644 \\u0645\\u0637\\u0644\\u0648\\u0628\\u0629 (\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0628\\u0627\\u0626\\u0639\\u060C \\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u064A\\u060C \\u0627\\u0644\\u0637\\u0627\\u0628\\u0639 \\u0627\\u0644\\u0632\\u0645\\u0646\\u064A\\u060C \\u0627\\u0644\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A\\u060C \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u0629)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                    className: \"w-4 h-4 text-green-500 ml-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 351,\n                    columnNumber: 21\n                  }, this), \"\\u0627\\u0644\\u062A\\u062D\\u0642\\u0642 \\u0645\\u0646 \\u0635\\u062D\\u0629 \\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u064A\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-gray-900 mb-3\",\n                children: \"\\u062A\\u0646\\u0633\\u064A\\u0642 TLV:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2 text-sm text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gray-50 p-2 rounded font-mono\",\n                  children: \"Tag 1: \\u0627\\u0633\\u0645 \\u0627\\u0644\\u0628\\u0627\\u0626\\u0639\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gray-50 p-2 rounded font-mono\",\n                  children: \"Tag 2: \\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u064A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 363,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gray-50 p-2 rounded font-mono\",\n                  children: \"Tag 3: \\u0627\\u0644\\u0637\\u0627\\u0628\\u0639 \\u0627\\u0644\\u0632\\u0645\\u0646\\u064A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gray-50 p-2 rounded font-mono\",\n                  children: \"Tag 4: \\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 369,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gray-50 p-2 rounded font-mono\",\n                  children: \"Tag 5: \\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 88,\n    columnNumber: 5\n  }, this);\n};\n_s(QRCodeTest, \"p+cpoe53GPfRX23HpA2eM9lKums=\");\n_c = QRCodeTest;\nexport default QRCodeTest;\nvar _c;\n$RefreshReg$(_c, \"QRCodeTest\");", "map": {"version": 3, "names": ["React", "useState", "motion", "QrCodeIcon", "DocumentTextIcon", "CheckCircleIcon", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "<PERSON><PERSON>", "InvoiceQRCode", "generateQRCodeData", "generateSimpleQRCode", "validateSaudiVATNumber", "testQRCodeReading", "decodeQRCodeData", "comprehensiveQRTest", "runMultipleQRTests", "generateQRReport", "jsxDEV", "_jsxDEV", "QRCodeTest", "_s", "testInvoice", "id", "invoiceNumber", "customerId", "customerName", "date", "items", "productId", "productName", "quantity", "price", "total", "subtotal", "tax", "discount", "status", "paymentMethod", "notes", "qrData", "setQrData", "qrType", "setQrType", "readingTest", "setReadingTest", "comprehensiveTest", "setComprehensiveTest", "multipleTests", "setMultipleTests", "generateTestQR", "data", "testResult", "testVATNumber", "vatNumber", "runComprehensiveTest", "result", "runMultipleTestSuite", "results", "report", "testCases", "expected", "description", "className", "children", "div", "initial", "opacity", "y", "animate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "transition", "delay", "Date", "toLocaleDateString", "toFixed", "invoiceData", "size", "showDetails", "value", "onChange", "e", "target", "onClick", "length", "<PERSON><PERSON><PERSON><PERSON>", "hasAllRequiredFields", "sellerName", "timestamp", "invoiceTotal", "vatAmount", "map", "testCase", "index", "isCorrect", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/src/pages/QRCodeTest.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { QrCodeIcon, DocumentTextIcon, CheckCircleIcon } from '@heroicons/react/24/outline';\nimport { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';\nimport { Button } from '../components/ui/button';\nimport InvoiceQRCode from '../components/QRCode/InvoiceQRCode';\nimport {\n  generateQRCodeData,\n  generateSimpleQRCode,\n  validateSaudiVATNumber,\n  testQRCodeReading,\n  decodeQRCodeData\n} from '../utils/qrCodeGenerator';\nimport {\n  comprehensiveQRTest,\n  runMultipleQRTests,\n  generateQRReport\n} from '../utils/qrCodeTester';\n\nconst QRCodeTest = () => {\n  const [testInvoice] = useState({\n    id: 1,\n    invoiceNumber: 'INV-2024-001',\n    customerId: 1,\n    customerName: 'أحمد محمد',\n    date: '2024-01-15',\n    items: [\n      { productId: 1, productName: 'لابتوب Dell', quantity: 2, price: 3500, total: 7000 },\n      { productId: 2, productName: 'ماوس لاسلكي', quantity: 5, price: 150, total: 750 }\n    ],\n    subtotal: 7750,\n    tax: 1162.5,\n    discount: 200,\n    total: 8712.5,\n    status: 'مكتملة',\n    paymentMethod: 'نقدي',\n    notes: 'فاتورة اختبار للتحقق من QR Code'\n  });\n\n  const [qrData, setQrData] = useState('');\n  const [qrType, setQrType] = useState('phase2');\n  const [readingTest, setReadingTest] = useState(null);\n  const [comprehensiveTest, setComprehensiveTest] = useState(null);\n  const [multipleTests, setMultipleTests] = useState(null);\n\n  const generateTestQR = () => {\n    let data = '';\n    if (qrType === 'phase2') {\n      data = generateQRCodeData(testInvoice);\n    } else {\n      data = generateSimpleQRCode(testInvoice);\n    }\n    setQrData(data);\n\n    // اختبار قراءة QR Code\n    if (qrType === 'phase2' && data) {\n      const testResult = testQRCodeReading(data);\n      setReadingTest(testResult);\n    } else {\n      setReadingTest(null);\n    }\n  };\n\n  const testVATNumber = (vatNumber) => {\n    return validateSaudiVATNumber(vatNumber);\n  };\n\n  const runComprehensiveTest = () => {\n    const result = comprehensiveQRTest(testInvoice);\n    setComprehensiveTest(result);\n  };\n\n  const runMultipleTestSuite = () => {\n    const results = runMultipleQRTests();\n    const report = generateQRReport(results);\n    setMultipleTests({ results, report });\n  };\n\n  const testCases = [\n    { vatNumber: '300000000000003', expected: true, description: 'رقم ضريبي صحيح (15 رقم)' },\n    { vatNumber: '12345678901234', expected: false, description: 'رقم ضريبي قصير (14 رقم)' },\n    { vatNumber: '1234567890123456', expected: false, description: 'رقم ضريبي طويل (16 رقم)' },\n    { vatNumber: '30000000000000A', expected: false, description: 'رقم ضريبي يحتوي على أحرف' },\n    { vatNumber: '', expected: false, description: 'رقم ضريبي فارغ' }\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* العنوان */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n      >\n        <div className=\"flex items-center mb-6\">\n          <QrCodeIcon className=\"w-8 h-8 text-blue-600 ml-3\" />\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900\">اختبار QR Code</h1>\n            <p className=\"text-gray-600 mt-2\">اختبار وتجربة QR Code للفوترة الإلكترونية</p>\n          </div>\n        </div>\n      </motion.div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* QR Code للفاتورة التجريبية */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.1 }}\n        >\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <DocumentTextIcon className=\"w-5 h-5 ml-2\" />\n                فاتورة تجريبية\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {/* معلومات الفاتورة */}\n                <div className=\"bg-gray-50 p-4 rounded-lg\">\n                  <h4 className=\"font-medium text-gray-900 mb-3\">تفاصيل الفاتورة:</h4>\n                  <div className=\"grid grid-cols-2 gap-3 text-sm\">\n                    <div>\n                      <span className=\"text-gray-600\">رقم الفاتورة:</span>\n                      <p className=\"font-medium\">{testInvoice.invoiceNumber}</p>\n                    </div>\n                    <div>\n                      <span className=\"text-gray-600\">العميل:</span>\n                      <p className=\"font-medium\">{testInvoice.customerName}</p>\n                    </div>\n                    <div>\n                      <span className=\"text-gray-600\">التاريخ:</span>\n                      <p className=\"font-medium\">{new Date(testInvoice.date).toLocaleDateString('ar-SA')}</p>\n                    </div>\n                    <div>\n                      <span className=\"text-gray-600\">الإجمالي:</span>\n                      <p className=\"font-medium\">{testInvoice.total.toFixed(2)} ر.س</p>\n                    </div>\n                  </div>\n                </div>\n\n                {/* QR Code */}\n                <InvoiceQRCode\n                  invoiceData={testInvoice}\n                  size={200}\n                  showDetails={true}\n                />\n              </div>\n            </CardContent>\n          </Card>\n        </motion.div>\n\n        {/* اختبار مولد QR Code */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.2 }}\n        >\n          <Card>\n            <CardHeader>\n              <CardTitle>اختبار مولد QR Code</CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              {/* اختيار نوع QR Code */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  نوع QR Code:\n                </label>\n                <select\n                  value={qrType}\n                  onChange={(e) => setQrType(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"phase2\">المرحلة الثانية (TLV + Base64)</option>\n                  <option value=\"simple\">مبسط (JSON)</option>\n                </select>\n              </div>\n\n              {/* زر إنشاء QR Code */}\n              <Button\n                onClick={generateTestQR}\n                className=\"w-full bg-blue-600 hover:bg-blue-700 text-white\"\n              >\n                إنشاء QR Code\n              </Button>\n\n              {/* عرض البيانات المُنشأة */}\n              {qrData && (\n                <div className=\"space-y-3\">\n                  <h4 className=\"font-medium text-gray-900\">البيانات المُنشأة:</h4>\n                  <div className=\"bg-gray-100 p-3 rounded text-xs font-mono break-all max-h-40 overflow-y-auto\">\n                    {qrData}\n                  </div>\n                  <div className=\"text-sm text-gray-600\">\n                    طول البيانات: {qrData.length} حرف\n                  </div>\n                </div>\n              )}\n\n              {/* نتيجة اختبار القراءة */}\n              {readingTest && (\n                <div className={`p-4 rounded-lg border ${\n                  readingTest.isValid && readingTest.hasAllRequiredFields\n                    ? 'bg-green-50 border-green-200'\n                    : 'bg-red-50 border-red-200'\n                }`}>\n                  <h4 className={`font-medium mb-3 ${\n                    readingTest.isValid && readingTest.hasAllRequiredFields\n                      ? 'text-green-800'\n                      : 'text-red-800'\n                  }`}>\n                    نتيجة اختبار القراءة:\n                  </h4>\n\n                  {readingTest.isValid && readingTest.hasAllRequiredFields ? (\n                    <div className=\"space-y-2\">\n                      <div className=\"flex items-center text-green-700\">\n                        <CheckCircleIcon className=\"w-5 h-5 ml-2\" />\n                        <span className=\"font-medium\">QR Code قابل للقراءة بنجاح!</span>\n                      </div>\n\n                      {readingTest.data && (\n                        <div className=\"bg-white p-3 rounded border border-green-200\">\n                          <h5 className=\"font-medium text-green-800 mb-2\">البيانات المقروءة:</h5>\n                          <div className=\"grid grid-cols-1 gap-2 text-sm\">\n                            <div>\n                              <span className=\"font-medium text-gray-600\">اسم البائع:</span>\n                              <span className=\"mr-2\">{readingTest.data.sellerName}</span>\n                            </div>\n                            <div>\n                              <span className=\"font-medium text-gray-600\">الرقم الضريبي:</span>\n                              <span className=\"mr-2\">{readingTest.data.vatNumber}</span>\n                            </div>\n                            <div>\n                              <span className=\"font-medium text-gray-600\">الطابع الزمني:</span>\n                              <span className=\"mr-2\">{readingTest.data.timestamp}</span>\n                            </div>\n                            <div>\n                              <span className=\"font-medium text-gray-600\">إجمالي الفاتورة:</span>\n                              <span className=\"mr-2\">{readingTest.data.invoiceTotal} ر.س</span>\n                            </div>\n                            <div>\n                              <span className=\"font-medium text-gray-600\">إجمالي الضريبة:</span>\n                              <span className=\"mr-2\">{readingTest.data.vatAmount} ر.س</span>\n                            </div>\n                          </div>\n                        </div>\n                      )}\n                    </div>\n                  ) : (\n                    <div className=\"text-red-700\">\n                      <div className=\"flex items-center mb-2\">\n                        <span className=\"text-red-500 text-xl ml-2\">✗</span>\n                        <span className=\"font-medium\">فشل في قراءة QR Code</span>\n                      </div>\n                      <p className=\"text-sm\">البيانات غير مكتملة أو غير صحيحة. يرجى التحقق من إعدادات الشركة.</p>\n                    </div>\n                  )}\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        </motion.div>\n      </div>\n\n      {/* اختبار التحقق من الرقم الضريبي */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.3 }}\n      >\n        <Card>\n          <CardHeader>\n            <CardTitle>اختبار التحقق من الرقم الضريبي</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              <p className=\"text-sm text-gray-600 mb-4\">\n                اختبار دالة التحقق من صحة الرقم الضريبي السعودي (15 رقم):\n              </p>\n\n              <div className=\"space-y-3\">\n                {testCases.map((testCase, index) => {\n                  const result = testVATNumber(testCase.vatNumber);\n                  const isCorrect = result === testCase.expected;\n\n                  return (\n                    <div\n                      key={index}\n                      className={`p-3 rounded-lg border ${\n                        isCorrect ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'\n                      }`}\n                    >\n                      <div className=\"flex items-center justify-between\">\n                        <div>\n                          <p className=\"font-mono text-sm\">\n                            {testCase.vatNumber || '(فارغ)'}\n                          </p>\n                          <p className=\"text-xs text-gray-600\">\n                            {testCase.description}\n                          </p>\n                        </div>\n                        <div className=\"flex items-center space-x-2 space-x-reverse\">\n                          <span className={`text-xs px-2 py-1 rounded ${\n                            result ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'\n                          }`}>\n                            {result ? 'صحيح' : 'خطأ'}\n                          </span>\n                          {isCorrect && (\n                            <CheckCircleIcon className=\"w-4 h-4 text-green-600\" />\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  );\n                })}\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </motion.div>\n\n      {/* معلومات تقنية */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.4 }}\n      >\n        <Card>\n          <CardHeader>\n            <CardTitle>معلومات تقنية</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <h4 className=\"font-medium text-gray-900 mb-3\">متطلبات المرحلة الثانية:</h4>\n                <ul className=\"space-y-2 text-sm text-gray-600\">\n                  <li className=\"flex items-center\">\n                    <CheckCircleIcon className=\"w-4 h-4 text-green-500 ml-2\" />\n                    تنسيق TLV (Tag-Length-Value)\n                  </li>\n                  <li className=\"flex items-center\">\n                    <CheckCircleIcon className=\"w-4 h-4 text-green-500 ml-2\" />\n                    تشفير Base64\n                  </li>\n                  <li className=\"flex items-center\">\n                    <CheckCircleIcon className=\"w-4 h-4 text-green-500 ml-2\" />\n                    5 حقول مطلوبة (اسم البائع، الرقم الضريبي، الطابع الزمني، الإجمالي، الضريبة)\n                  </li>\n                  <li className=\"flex items-center\">\n                    <CheckCircleIcon className=\"w-4 h-4 text-green-500 ml-2\" />\n                    التحقق من صحة الرقم الضريبي\n                  </li>\n                </ul>\n              </div>\n\n              <div>\n                <h4 className=\"font-medium text-gray-900 mb-3\">تنسيق TLV:</h4>\n                <div className=\"space-y-2 text-sm text-gray-600\">\n                  <div className=\"bg-gray-50 p-2 rounded font-mono\">\n                    Tag 1: اسم البائع\n                  </div>\n                  <div className=\"bg-gray-50 p-2 rounded font-mono\">\n                    Tag 2: الرقم الضريبي\n                  </div>\n                  <div className=\"bg-gray-50 p-2 rounded font-mono\">\n                    Tag 3: الطابع الزمني\n                  </div>\n                  <div className=\"bg-gray-50 p-2 rounded font-mono\">\n                    Tag 4: إجمالي الفاتورة\n                  </div>\n                  <div className=\"bg-gray-50 p-2 rounded font-mono\">\n                    Tag 5: إجمالي الضريبة\n                  </div>\n                </div>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default QRCodeTest;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,UAAU,EAAEC,gBAAgB,EAAEC,eAAe,QAAQ,6BAA6B;AAC3F,SAASC,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,SAAS,QAAQ,uBAAuB;AAChF,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,aAAa,MAAM,oCAAoC;AAC9D,SACEC,kBAAkB,EAClBC,oBAAoB,EACpBC,sBAAsB,EACtBC,iBAAiB,EACjBC,gBAAgB,QACX,0BAA0B;AACjC,SACEC,mBAAmB,EACnBC,kBAAkB,EAClBC,gBAAgB,QACX,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM,CAACC,WAAW,CAAC,GAAGvB,QAAQ,CAAC;IAC7BwB,EAAE,EAAE,CAAC;IACLC,aAAa,EAAE,cAAc;IAC7BC,UAAU,EAAE,CAAC;IACbC,YAAY,EAAE,WAAW;IACzBC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,CACL;MAAEC,SAAS,EAAE,CAAC;MAAEC,WAAW,EAAE,aAAa;MAAEC,QAAQ,EAAE,CAAC;MAAEC,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,EACnF;MAAEJ,SAAS,EAAE,CAAC;MAAEC,WAAW,EAAE,aAAa;MAAEC,QAAQ,EAAE,CAAC;MAAEC,KAAK,EAAE,GAAG;MAAEC,KAAK,EAAE;IAAI,CAAC,CAClF;IACDC,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAE,MAAM;IACXC,QAAQ,EAAE,GAAG;IACbH,KAAK,EAAE,MAAM;IACbI,MAAM,EAAE,QAAQ;IAChBC,aAAa,EAAE,MAAM;IACrBC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC2C,MAAM,EAAEC,SAAS,CAAC,GAAG5C,QAAQ,CAAC,QAAQ,CAAC;EAC9C,MAAM,CAAC6C,WAAW,EAAEC,cAAc,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC+C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACiD,aAAa,EAAEC,gBAAgB,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EAExD,MAAMmD,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIC,IAAI,GAAG,EAAE;IACb,IAAIT,MAAM,KAAK,QAAQ,EAAE;MACvBS,IAAI,GAAGzC,kBAAkB,CAACY,WAAW,CAAC;IACxC,CAAC,MAAM;MACL6B,IAAI,GAAGxC,oBAAoB,CAACW,WAAW,CAAC;IAC1C;IACAmB,SAAS,CAACU,IAAI,CAAC;;IAEf;IACA,IAAIT,MAAM,KAAK,QAAQ,IAAIS,IAAI,EAAE;MAC/B,MAAMC,UAAU,GAAGvC,iBAAiB,CAACsC,IAAI,CAAC;MAC1CN,cAAc,CAACO,UAAU,CAAC;IAC5B,CAAC,MAAM;MACLP,cAAc,CAAC,IAAI,CAAC;IACtB;EACF,CAAC;EAED,MAAMQ,aAAa,GAAIC,SAAS,IAAK;IACnC,OAAO1C,sBAAsB,CAAC0C,SAAS,CAAC;EAC1C,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMC,MAAM,GAAGzC,mBAAmB,CAACO,WAAW,CAAC;IAC/CyB,oBAAoB,CAACS,MAAM,CAAC;EAC9B,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMC,OAAO,GAAG1C,kBAAkB,CAAC,CAAC;IACpC,MAAM2C,MAAM,GAAG1C,gBAAgB,CAACyC,OAAO,CAAC;IACxCT,gBAAgB,CAAC;MAAES,OAAO;MAAEC;IAAO,CAAC,CAAC;EACvC,CAAC;EAED,MAAMC,SAAS,GAAG,CAChB;IAAEN,SAAS,EAAE,iBAAiB;IAAEO,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAA0B,CAAC,EACxF;IAAER,SAAS,EAAE,gBAAgB;IAAEO,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAA0B,CAAC,EACxF;IAAER,SAAS,EAAE,kBAAkB;IAAEO,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAA0B,CAAC,EAC1F;IAAER,SAAS,EAAE,iBAAiB;IAAEO,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAA2B,CAAC,EAC1F;IAAER,SAAS,EAAE,EAAE;IAAEO,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAiB,CAAC,CAClE;EAED,oBACE3C,OAAA;IAAK4C,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB7C,OAAA,CAACnB,MAAM,CAACiE,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAJ,QAAA,eAE9B7C,OAAA;QAAK4C,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrC7C,OAAA,CAAClB,UAAU;UAAC8D,SAAS,EAAC;QAA4B;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrDtD,OAAA;UAAA6C,QAAA,gBACE7C,OAAA;YAAI4C,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAc;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpEtD,OAAA;YAAG4C,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAyC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAEbtD,OAAA;MAAK4C,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpD7C,OAAA,CAACnB,MAAM,CAACiE,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BM,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAX,QAAA,eAE3B7C,OAAA,CAACf,IAAI;UAAA4D,QAAA,gBACH7C,OAAA,CAACb,UAAU;YAAA0D,QAAA,eACT7C,OAAA,CAACZ,SAAS;cAACwD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBACtC7C,OAAA,CAACjB,gBAAgB;gBAAC6D,SAAS,EAAC;cAAc;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mFAE/C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACbtD,OAAA,CAACd,WAAW;YAAA2D,QAAA,eACV7C,OAAA;cAAK4C,SAAS,EAAC,WAAW;cAAAC,QAAA,gBAExB7C,OAAA;gBAAK4C,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxC7C,OAAA;kBAAI4C,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpEtD,OAAA;kBAAK4C,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7C7C,OAAA;oBAAA6C,QAAA,gBACE7C,OAAA;sBAAM4C,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAa;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACpDtD,OAAA;sBAAG4C,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAE1C,WAAW,CAACE;oBAAa;sBAAA8C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD,CAAC,eACNtD,OAAA;oBAAA6C,QAAA,gBACE7C,OAAA;sBAAM4C,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAO;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC9CtD,OAAA;sBAAG4C,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAE1C,WAAW,CAACI;oBAAY;sBAAA4C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC,eACNtD,OAAA;oBAAA6C,QAAA,gBACE7C,OAAA;sBAAM4C,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC/CtD,OAAA;sBAAG4C,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAE,IAAIY,IAAI,CAACtD,WAAW,CAACK,IAAI,CAAC,CAACkD,kBAAkB,CAAC,OAAO;oBAAC;sBAAAP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpF,CAAC,eACNtD,OAAA;oBAAA6C,QAAA,gBACE7C,OAAA;sBAAM4C,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAS;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAChDtD,OAAA;sBAAG4C,SAAS,EAAC,aAAa;sBAAAC,QAAA,GAAE1C,WAAW,CAACW,KAAK,CAAC6C,OAAO,CAAC,CAAC,CAAC,EAAC,gBAAI;oBAAA;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNtD,OAAA,CAACV,aAAa;gBACZsE,WAAW,EAAEzD,WAAY;gBACzB0D,IAAI,EAAE,GAAI;gBACVC,WAAW,EAAE;cAAK;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAGbtD,OAAA,CAACnB,MAAM,CAACiE,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BM,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAX,QAAA,eAE3B7C,OAAA,CAACf,IAAI;UAAA4D,QAAA,gBACH7C,OAAA,CAACb,UAAU;YAAA0D,QAAA,eACT7C,OAAA,CAACZ,SAAS;cAAAyD,QAAA,EAAC;YAAmB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACbtD,OAAA,CAACd,WAAW;YAAC0D,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAEhC7C,OAAA;cAAA6C,QAAA,gBACE7C,OAAA;gBAAO4C,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRtD,OAAA;gBACE+D,KAAK,EAAExC,MAAO;gBACdyC,QAAQ,EAAGC,CAAC,IAAKzC,SAAS,CAACyC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAC3CnB,SAAS,EAAC,2GAA2G;gBAAAC,QAAA,gBAErH7C,OAAA;kBAAQ+D,KAAK,EAAC,QAAQ;kBAAAlB,QAAA,EAAC;gBAA8B;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9DtD,OAAA;kBAAQ+D,KAAK,EAAC,QAAQ;kBAAAlB,QAAA,EAAC;gBAAW;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGNtD,OAAA,CAACX,MAAM;cACL8E,OAAO,EAAEpC,cAAe;cACxBa,SAAS,EAAC,iDAAiD;cAAAC,QAAA,EAC5D;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAGRjC,MAAM,iBACLrB,OAAA;cAAK4C,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB7C,OAAA;gBAAI4C,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAkB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjEtD,OAAA;gBAAK4C,SAAS,EAAC,8EAA8E;gBAAAC,QAAA,EAC1FxB;cAAM;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNtD,OAAA;gBAAK4C,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAC,uEACvB,EAACxB,MAAM,CAAC+C,MAAM,EAAC,qBAC/B;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,EAGA7B,WAAW,iBACVzB,OAAA;cAAK4C,SAAS,EAAE,yBACdnB,WAAW,CAAC4C,OAAO,IAAI5C,WAAW,CAAC6C,oBAAoB,GACnD,8BAA8B,GAC9B,0BAA0B,EAC7B;cAAAzB,QAAA,gBACD7C,OAAA;gBAAI4C,SAAS,EAAE,oBACbnB,WAAW,CAAC4C,OAAO,IAAI5C,WAAW,CAAC6C,oBAAoB,GACnD,gBAAgB,GAChB,cAAc,EACjB;gBAAAzB,QAAA,EAAC;cAEJ;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EAEJ7B,WAAW,CAAC4C,OAAO,IAAI5C,WAAW,CAAC6C,oBAAoB,gBACtDtE,OAAA;gBAAK4C,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB7C,OAAA;kBAAK4C,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAC/C7C,OAAA,CAAChB,eAAe;oBAAC4D,SAAS,EAAC;kBAAc;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5CtD,OAAA;oBAAM4C,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAA2B;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC,EAEL7B,WAAW,CAACO,IAAI,iBACfhC,OAAA;kBAAK4C,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,gBAC3D7C,OAAA;oBAAI4C,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAC;kBAAkB;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvEtD,OAAA;oBAAK4C,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,gBAC7C7C,OAAA;sBAAA6C,QAAA,gBACE7C,OAAA;wBAAM4C,SAAS,EAAC,2BAA2B;wBAAAC,QAAA,EAAC;sBAAW;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC9DtD,OAAA;wBAAM4C,SAAS,EAAC,MAAM;wBAAAC,QAAA,EAAEpB,WAAW,CAACO,IAAI,CAACuC;sBAAU;wBAAApB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxD,CAAC,eACNtD,OAAA;sBAAA6C,QAAA,gBACE7C,OAAA;wBAAM4C,SAAS,EAAC,2BAA2B;wBAAAC,QAAA,EAAC;sBAAc;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACjEtD,OAAA;wBAAM4C,SAAS,EAAC,MAAM;wBAAAC,QAAA,EAAEpB,WAAW,CAACO,IAAI,CAACG;sBAAS;wBAAAgB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvD,CAAC,eACNtD,OAAA;sBAAA6C,QAAA,gBACE7C,OAAA;wBAAM4C,SAAS,EAAC,2BAA2B;wBAAAC,QAAA,EAAC;sBAAc;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACjEtD,OAAA;wBAAM4C,SAAS,EAAC,MAAM;wBAAAC,QAAA,EAAEpB,WAAW,CAACO,IAAI,CAACwC;sBAAS;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvD,CAAC,eACNtD,OAAA;sBAAA6C,QAAA,gBACE7C,OAAA;wBAAM4C,SAAS,EAAC,2BAA2B;wBAAAC,QAAA,EAAC;sBAAgB;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACnEtD,OAAA;wBAAM4C,SAAS,EAAC,MAAM;wBAAAC,QAAA,GAAEpB,WAAW,CAACO,IAAI,CAACyC,YAAY,EAAC,gBAAI;sBAAA;wBAAAtB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9D,CAAC,eACNtD,OAAA;sBAAA6C,QAAA,gBACE7C,OAAA;wBAAM4C,SAAS,EAAC,2BAA2B;wBAAAC,QAAA,EAAC;sBAAe;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAClEtD,OAAA;wBAAM4C,SAAS,EAAC,MAAM;wBAAAC,QAAA,GAAEpB,WAAW,CAACO,IAAI,CAAC0C,SAAS,EAAC,gBAAI;sBAAA;wBAAAvB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,gBAENtD,OAAA;gBAAK4C,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B7C,OAAA;kBAAK4C,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrC7C,OAAA;oBAAM4C,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAC;kBAAC;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpDtD,OAAA;oBAAM4C,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACNtD,OAAA;kBAAG4C,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAgE;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxF,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGNtD,OAAA,CAACnB,MAAM,CAACiE,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BM,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAAAX,QAAA,eAE3B7C,OAAA,CAACf,IAAI;QAAA4D,QAAA,gBACH7C,OAAA,CAACb,UAAU;UAAA0D,QAAA,eACT7C,OAAA,CAACZ,SAAS;YAAAyD,QAAA,EAAC;UAA8B;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eACbtD,OAAA,CAACd,WAAW;UAAA2D,QAAA,eACV7C,OAAA;YAAK4C,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB7C,OAAA;cAAG4C,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE1C;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJtD,OAAA;cAAK4C,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBJ,SAAS,CAACkC,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;gBAClC,MAAMxC,MAAM,GAAGH,aAAa,CAAC0C,QAAQ,CAACzC,SAAS,CAAC;gBAChD,MAAM2C,SAAS,GAAGzC,MAAM,KAAKuC,QAAQ,CAAClC,QAAQ;gBAE9C,oBACE1C,OAAA;kBAEE4C,SAAS,EAAE,yBACTkC,SAAS,GAAG,8BAA8B,GAAG,0BAA0B,EACtE;kBAAAjC,QAAA,eAEH7C,OAAA;oBAAK4C,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,gBAChD7C,OAAA;sBAAA6C,QAAA,gBACE7C,OAAA;wBAAG4C,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,EAC7B+B,QAAQ,CAACzC,SAAS,IAAI;sBAAQ;wBAAAgB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9B,CAAC,eACJtD,OAAA;wBAAG4C,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EACjC+B,QAAQ,CAACjC;sBAAW;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC,eACNtD,OAAA;sBAAK4C,SAAS,EAAC,6CAA6C;sBAAAC,QAAA,gBAC1D7C,OAAA;wBAAM4C,SAAS,EAAE,6BACfP,MAAM,GAAG,6BAA6B,GAAG,yBAAyB,EACjE;wBAAAQ,QAAA,EACAR,MAAM,GAAG,MAAM,GAAG;sBAAK;wBAAAc,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC,EACNwB,SAAS,iBACR9E,OAAA,CAAChB,eAAe;wBAAC4D,SAAS,EAAC;sBAAwB;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CACtD;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC,GAxBDuB,KAAK;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAyBP,CAAC;cAEV,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGbtD,OAAA,CAACnB,MAAM,CAACiE,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BM,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAAAX,QAAA,eAE3B7C,OAAA,CAACf,IAAI;QAAA4D,QAAA,gBACH7C,OAAA,CAACb,UAAU;UAAA0D,QAAA,eACT7C,OAAA,CAACZ,SAAS;YAAAyD,QAAA,EAAC;UAAa;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACbtD,OAAA,CAACd,WAAW;UAAA2D,QAAA,eACV7C,OAAA;YAAK4C,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD7C,OAAA;cAAA6C,QAAA,gBACE7C,OAAA;gBAAI4C,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAC;cAAwB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5EtD,OAAA;gBAAI4C,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAC7C7C,OAAA;kBAAI4C,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC/B7C,OAAA,CAAChB,eAAe;oBAAC4D,SAAS,EAAC;kBAA6B;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,yDAE7D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLtD,OAAA;kBAAI4C,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC/B7C,OAAA,CAAChB,eAAe;oBAAC4D,SAAS,EAAC;kBAA6B;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,yCAE7D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLtD,OAAA;kBAAI4C,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC/B7C,OAAA,CAAChB,eAAe;oBAAC4D,SAAS,EAAC;kBAA6B;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,qYAE7D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLtD,OAAA;kBAAI4C,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC/B7C,OAAA,CAAChB,eAAe;oBAAC4D,SAAS,EAAC;kBAA6B;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,kJAE7D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAENtD,OAAA;cAAA6C,QAAA,gBACE7C,OAAA;gBAAI4C,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAC;cAAU;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9DtD,OAAA;gBAAK4C,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAC9C7C,OAAA;kBAAK4C,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAElD;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNtD,OAAA;kBAAK4C,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAElD;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNtD,OAAA;kBAAK4C,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAElD;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNtD,OAAA;kBAAK4C,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAElD;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNtD,OAAA;kBAAK4C,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAElD;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAACpD,EAAA,CA3WID,UAAU;AAAA8E,EAAA,GAAV9E,UAAU;AA6WhB,eAAeA,UAAU;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}