import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { XMarkIcon, PrinterIcon, DocumentArrowDownIcon } from '@heroicons/react/24/outline';
import { Button } from '../ui/button';
import toast from 'react-hot-toast';

const ExpenseDetails = ({ expense, onClose }) => {
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const handlePrint = () => {
    window.print();
    toast.success('تم إرسال المصروف للطباعة');
  };

  const handleDownload = () => {
    toast.success('تم تحميل تفاصيل المصروف');
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'مدفوع':
        return 'bg-green-100 text-green-800';
      case 'معلق':
        return 'bg-yellow-100 text-yellow-800';
      case 'ملغي':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryColor = (category) => {
    const colors = {
      'إيجار': 'bg-blue-100 text-blue-800',
      'مرافق': 'bg-green-100 text-green-800',
      'مكتبية': 'bg-purple-100 text-purple-800',
      'صيانة': 'bg-orange-100 text-orange-800',
      'رواتب': 'bg-indigo-100 text-indigo-800',
      'تسويق': 'bg-pink-100 text-pink-800',
      'سفر': 'bg-cyan-100 text-cyan-800',
      'اتصالات': 'bg-teal-100 text-teal-800',
      'تأمين': 'bg-red-100 text-red-800',
      'أخرى': 'bg-gray-100 text-gray-800'
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="bg-white rounded-2xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
        >
          {/* الرأس */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 print:hidden">
            <h2 className="text-xl font-semibold text-gray-900">تفاصيل المصروف</h2>
            <div className="flex items-center space-x-4 space-x-reverse">
              <Button
                onClick={handlePrint}
                variant="outline"
                className="text-blue-600 hover:text-blue-800"
              >
                <PrinterIcon className="w-4 h-4 ml-2" />
                طباعة
              </Button>
              <Button
                onClick={handleDownload}
                variant="outline"
                className="text-green-600 hover:text-green-800"
              >
                <DocumentArrowDownIcon className="w-4 h-4 ml-2" />
                تحميل PDF
              </Button>
              <button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg"
              >
                <XMarkIcon className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* المحتوى */}
          <div className="p-8 print:p-4">
            {/* رأس المصروف */}
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">شركة إدارة الأعمال</h1>
              <p className="text-gray-600">الرياض، المملكة العربية السعودية</p>
              <p className="text-gray-600">هاتف: 0112345678 | البريد الإلكتروني: <EMAIL></p>
              <div className="mt-4 pt-4 border-t border-gray-200">
                <h2 className="text-xl font-semibold text-gray-900">سند صرف</h2>
              </div>
            </div>

            {/* معلومات المصروف */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">معلومات المصروف</h3>
                <div className="space-y-3">
                  <div>
                    <span className="text-sm font-medium text-gray-600">كود المصروف:</span>
                    <p className="text-gray-900 font-mono">{expense.code}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-600">التاريخ:</span>
                    <p className="text-gray-900">{new Date(expense.date).toLocaleDateString('ar-SA')}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-600">الفئة:</span>
                    <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${getCategoryColor(expense.category)}`}>
                      {expense.category}
                    </span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-600">الحالة:</span>
                    <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(expense.status)}`}>
                      {expense.status}
                    </span>
                  </div>
                  {expense.isRecurring && (
                    <div>
                      <span className="text-sm font-medium text-gray-600">مصروف متكرر:</span>
                      <p className="text-gray-900">{expense.recurringPeriod}</p>
                    </div>
                  )}
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">تفاصيل الدفع</h3>
                <div className="space-y-3">
                  <div>
                    <span className="text-sm font-medium text-gray-600">طريقة الدفع:</span>
                    <p className="text-gray-900">{expense.paymentMethod}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-600">المورد/الجهة:</span>
                    <p className="text-gray-900">{expense.vendor || '-'}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-600">رقم المرجع:</span>
                    <p className="text-gray-900 font-mono">{expense.reference || '-'}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-600">رقم الإيصال:</span>
                    <p className="text-gray-900 font-mono">{expense.receiptNumber || '-'}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-600">معتمد من:</span>
                    <p className="text-gray-900">{expense.approvedBy || '-'}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* وصف المصروف */}
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">وصف المصروف</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-gray-900">{expense.description}</p>
              </div>
            </div>

            {/* تفاصيل المبلغ */}
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">تفاصيل المبلغ</h3>
              <div className="bg-gray-50 p-6 rounded-lg">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">المبلغ الأساسي:</span>
                    <span className="text-gray-900 font-medium">{formatCurrency(expense.amount)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">ضريبة القيمة المضافة (15%):</span>
                    <span className="text-gray-900 font-medium">{formatCurrency(expense.taxAmount)}</span>
                  </div>
                  <div className="border-t border-gray-300 pt-3">
                    <div className="flex justify-between items-center">
                      <span className="text-lg font-semibold text-gray-900">الإجمالي النهائي:</span>
                      <span className="text-2xl font-bold text-red-600">
                        {formatCurrency(expense.amount + expense.taxAmount)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* الملاحظات */}
            {expense.notes && (
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">ملاحظات</h3>
                <p className="text-gray-700 bg-gray-50 p-4 rounded-lg">{expense.notes}</p>
              </div>
            )}

            {/* التوقيعات */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
              <div className="text-center">
                <div className="border-t border-gray-300 pt-2 mt-16">
                  <p className="font-medium">طالب الصرف</p>
                </div>
              </div>
              <div className="text-center">
                <div className="border-t border-gray-300 pt-2 mt-16">
                  <p className="font-medium">المعتمد</p>
                  {expense.approvedBy && (
                    <p className="text-sm text-gray-600 mt-1">{expense.approvedBy}</p>
                  )}
                </div>
              </div>
              <div className="text-center">
                <div className="border-t border-gray-300 pt-2 mt-16">
                  <p className="font-medium">المحاسب</p>
                </div>
              </div>
            </div>

            {/* تذييل */}
            <div className="text-center mt-8 pt-8 border-t border-gray-200 text-sm text-gray-600">
              <p>سند صرف إلكتروني</p>
              <p>تاريخ الطباعة: {new Date().toLocaleDateString('ar-SA')}</p>
            </div>
          </div>

          {/* زر الإغلاق للشاشة فقط */}
          <div className="flex justify-end p-6 border-t border-gray-200 print:hidden">
            <Button onClick={onClose} variant="outline">
              إغلاق
            </Button>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default ExpenseDetails;
