import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  CubeIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../components/ui/table';
import inventoryAPI from '../api/inventoryAPI';
import ProductForm from '../components/Inventory/ProductForm';
import ProductDetails from '../components/Inventory/ProductDetails';
import toast from 'react-hot-toast';

const Inventory = () => {
  const [products, setProducts] = useState([]);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [showForm, setShowForm] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [stats, setStats] = useState({});
  const [lowStockProducts, setLowStockProducts] = useState([]);

  useEffect(() => {
    loadProducts();
    loadStats();
    loadLowStockProducts();
  }, []);

  useEffect(() => {
    filterProducts();
  }, [products, searchTerm]);

  const loadProducts = () => {
    const productsData = inventoryAPI.getAllProducts();
    setProducts(productsData);
  };

  const loadStats = () => {
    const inventoryStats = inventoryAPI.getInventoryStats();
    setStats(inventoryStats);
  };

  const loadLowStockProducts = () => {
    const lowStock = inventoryAPI.getLowStockProducts();
    setLowStockProducts(lowStock);
  };

  const filterProducts = () => {
    if (!searchTerm) {
      setFilteredProducts(products);
    } else {
      const filtered = inventoryAPI.searchProducts(searchTerm);
      setFilteredProducts(filtered);
    }
  };

  const handleAddProduct = () => {
    setSelectedProduct(null);
    setIsEditing(false);
    setShowForm(true);
  };

  const handleEditProduct = (product) => {
    setSelectedProduct(product);
    setIsEditing(true);
    setShowForm(true);
  };

  const handleViewDetails = (product) => {
    setSelectedProduct(product);
    setShowDetails(true);
  };

  const handleDeleteProduct = (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
      inventoryAPI.deleteProduct(id);
      loadProducts();
      loadStats();
      loadLowStockProducts();
      toast.success('تم حذف المنتج بنجاح');
    }
  };

  const handleSaveProduct = (productData) => {
    try {
      if (isEditing) {
        inventoryAPI.updateProduct(selectedProduct.id, productData);
        toast.success('تم تحديث المنتج بنجاح');
      } else {
        inventoryAPI.addProduct(productData);
        toast.success('تم إضافة المنتج بنجاح');
      }
      
      loadProducts();
      loadStats();
      loadLowStockProducts();
      setShowForm(false);
    } catch (error) {
      toast.error('حدث خطأ أثناء حفظ المنتج');
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const getStockStatus = (product) => {
    if (product.currentStock === 0) {
      return { text: 'نفد المخزون', color: 'bg-red-100 text-red-800' };
    } else if (product.currentStock <= product.minStock) {
      return { text: 'مخزون منخفض', color: 'bg-yellow-100 text-yellow-800' };
    } else {
      return { text: 'متوفر', color: 'bg-green-100 text-green-800' };
    }
  };

  return (
    <div className="space-y-6">
      {/* العنوان والإحصائيات */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة المخزون</h1>
            <p className="text-gray-600 mt-2">إدارة المنتجات والمخزون</p>
          </div>
          <Button
            onClick={handleAddProduct}
            className="bg-purple-600 hover:bg-purple-700 text-white"
          >
            <PlusIcon className="w-4 h-4 ml-2" />
            منتج جديد
          </Button>
        </div>

        {/* بطاقات الإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">إجمالي المنتجات</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalProducts || 0}</p>
                </div>
                <div className="p-3 bg-purple-100 rounded-full">
                  <CubeIcon className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">قيمة المخزون</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(stats.totalValue || 0)}
                  </p>
                </div>
                <div className="p-3 bg-green-100 rounded-full">
                  <CubeIcon className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">مخزون منخفض</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.lowStockProducts || 0}</p>
                </div>
                <div className="p-3 bg-yellow-100 rounded-full">
                  <ExclamationTriangleIcon className="w-6 h-6 text-yellow-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">نفد المخزون</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.outOfStockProducts || 0}</p>
                </div>
                <div className="p-3 bg-red-100 rounded-full">
                  <ExclamationTriangleIcon className="w-6 h-6 text-red-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* تنبيه المخزون المنخفض */}
        {lowStockProducts.length > 0 && (
          <Card className="border-yellow-200 bg-yellow-50">
            <CardHeader>
              <CardTitle className="text-yellow-800 flex items-center">
                <ExclamationTriangleIcon className="w-5 h-5 ml-2" />
                تنبيه: منتجات بمخزون منخفض
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {lowStockProducts.slice(0, 6).map((product) => (
                  <div key={product.id} className="bg-white p-3 rounded-lg border">
                    <p className="font-medium text-gray-900">{product.name}</p>
                    <p className="text-sm text-gray-600">المخزون الحالي: {product.currentStock}</p>
                    <p className="text-sm text-gray-600">الحد الأدنى: {product.minStock}</p>
                  </div>
                ))}
              </div>
              {lowStockProducts.length > 6 && (
                <p className="text-sm text-yellow-700 mt-3">
                  وهناك {lowStockProducts.length - 6} منتجات أخرى بمخزون منخفض
                </p>
              )}
            </CardContent>
          </Card>
        )}
      </motion.div>

      {/* البحث والجدول */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>قائمة المنتجات</CardTitle>
              <div className="flex items-center space-x-4 space-x-reverse">
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="البحث في المنتجات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10 w-64"
                  />
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>كود المنتج</TableHead>
                    <TableHead>اسم المنتج</TableHead>
                    <TableHead>الفئة</TableHead>
                    <TableHead>المخزون الحالي</TableHead>
                    <TableHead>سعر التكلفة</TableHead>
                    <TableHead>سعر البيع</TableHead>
                    <TableHead>الحالة</TableHead>
                    <TableHead className="text-center">الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredProducts.map((product) => {
                    const stockStatus = getStockStatus(product);
                    return (
                      <TableRow key={product.id}>
                        <TableCell className="font-medium">{product.code}</TableCell>
                        <TableCell>{product.name}</TableCell>
                        <TableCell>{product.category}</TableCell>
                        <TableCell>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${stockStatus.color}`}>
                            {product.currentStock} {product.unit}
                          </span>
                        </TableCell>
                        <TableCell>{formatCurrency(product.costPrice)}</TableCell>
                        <TableCell>{formatCurrency(product.sellingPrice)}</TableCell>
                        <TableCell>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${stockStatus.color}`}>
                            {stockStatus.text}
                          </span>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center justify-center space-x-2 space-x-reverse">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleViewDetails(product)}
                            >
                              <EyeIcon className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEditProduct(product)}
                            >
                              <PencilIcon className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDeleteProduct(product.id)}
                              className="text-red-600 hover:text-red-800"
                            >
                              <TrashIcon className="w-4 h-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* نموذج إضافة/تعديل المنتج */}
      {showForm && (
        <ProductForm
          product={selectedProduct}
          isEditing={isEditing}
          onSave={handleSaveProduct}
          onClose={() => setShowForm(false)}
        />
      )}

      {/* تفاصيل المنتج */}
      {showDetails && selectedProduct && (
        <ProductDetails
          product={selectedProduct}
          onClose={() => setShowDetails(false)}
        />
      )}
    </div>
  );
};

export default Inventory;
