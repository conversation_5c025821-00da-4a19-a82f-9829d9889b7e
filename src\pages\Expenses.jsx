import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  CurrencyDollarIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../components/ui/table';
import expensesAPI from '../api/expensesAPI';
import ExpenseForm from '../components/Expenses/ExpenseForm';
import ExpenseDetails from '../components/Expenses/ExpenseDetails';
import toast from 'react-hot-toast';

const Expenses = () => {
  const [expenses, setExpenses] = useState([]);
  const [filteredExpenses, setFilteredExpenses] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [showForm, setShowForm] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [selectedExpense, setSelectedExpense] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [stats, setStats] = useState({});

  useEffect(() => {
    loadExpenses();
    loadStats();
  }, []);

  useEffect(() => {
    filterExpenses();
  }, [expenses, searchTerm]);

  const loadExpenses = () => {
    const expensesData = expensesAPI.getAllExpenses();
    setExpenses(expensesData);
  };

  const loadStats = () => {
    const expensesStats = expensesAPI.getExpensesStats();
    setStats(expensesStats);
  };

  const filterExpenses = () => {
    if (!searchTerm) {
      setFilteredExpenses(expenses);
    } else {
      const filtered = expensesAPI.searchExpenses(searchTerm);
      setFilteredExpenses(filtered);
    }
  };

  const handleAddExpense = () => {
    setSelectedExpense(null);
    setIsEditing(false);
    setShowForm(true);
  };

  const handleEditExpense = (expense) => {
    setSelectedExpense(expense);
    setIsEditing(true);
    setShowForm(true);
  };

  const handleViewDetails = (expense) => {
    setSelectedExpense(expense);
    setShowDetails(true);
  };

  const handleDeleteExpense = (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذا المصروف؟')) {
      expensesAPI.deleteExpense(id);
      loadExpenses();
      loadStats();
      toast.success('تم حذف المصروف بنجاح');
    }
  };

  const handleSaveExpense = (expenseData) => {
    try {
      if (isEditing) {
        expensesAPI.updateExpense(selectedExpense.id, expenseData);
        toast.success('تم تحديث المصروف بنجاح');
      } else {
        expensesAPI.addExpense(expenseData);
        toast.success('تم إضافة المصروف بنجاح');
      }
      
      loadExpenses();
      loadStats();
      setShowForm(false);
    } catch (error) {
      toast.error('حدث خطأ أثناء حفظ المصروف');
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'مدفوع':
        return 'bg-green-100 text-green-800';
      case 'معلق':
        return 'bg-yellow-100 text-yellow-800';
      case 'ملغي':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryColor = (category) => {
    const colors = {
      'إيجار': 'bg-blue-100 text-blue-800',
      'مرافق': 'bg-green-100 text-green-800',
      'مكتبية': 'bg-purple-100 text-purple-800',
      'صيانة': 'bg-orange-100 text-orange-800',
      'رواتب': 'bg-indigo-100 text-indigo-800',
      'تسويق': 'bg-pink-100 text-pink-800',
      'سفر': 'bg-cyan-100 text-cyan-800',
      'اتصالات': 'bg-teal-100 text-teal-800',
      'تأمين': 'bg-red-100 text-red-800',
      'أخرى': 'bg-gray-100 text-gray-800'
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="space-y-6">
      {/* العنوان والإحصائيات */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة المصاريف</h1>
            <p className="text-gray-600 mt-2">إدارة وتتبع جميع مصاريف الشركة</p>
          </div>
          <Button
            onClick={handleAddExpense}
            className="bg-red-600 hover:bg-red-700 text-white"
          >
            <PlusIcon className="w-4 h-4 ml-2" />
            مصروف جديد
          </Button>
        </div>

        {/* بطاقات الإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">إجمالي المصاريف</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(stats.totalExpenses || 0)}
                  </p>
                </div>
                <div className="p-3 bg-red-100 rounded-full">
                  <CurrencyDollarIcon className="w-6 h-6 text-red-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">مصاريف هذا الشهر</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(stats.monthExpenses || 0)}
                  </p>
                </div>
                <div className="p-3 bg-blue-100 rounded-full">
                  <CurrencyDollarIcon className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">عدد المصاريف</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalCount || 0}</p>
                </div>
                <div className="p-3 bg-purple-100 rounded-full">
                  <CurrencyDollarIcon className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">مصاريف معلقة</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.pendingExpenses || 0}</p>
                </div>
                <div className="p-3 bg-yellow-100 rounded-full">
                  <ClockIcon className="w-6 h-6 text-yellow-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </motion.div>

      {/* البحث والجدول */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>قائمة المصاريف</CardTitle>
              <div className="flex items-center space-x-4 space-x-reverse">
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="البحث في المصاريف..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10 w-64"
                  />
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>كود المصروف</TableHead>
                    <TableHead>الوصف</TableHead>
                    <TableHead>الفئة</TableHead>
                    <TableHead>المبلغ</TableHead>
                    <TableHead>التاريخ</TableHead>
                    <TableHead>المورد</TableHead>
                    <TableHead>طريقة الدفع</TableHead>
                    <TableHead>الحالة</TableHead>
                    <TableHead className="text-center">الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredExpenses.map((expense) => (
                    <TableRow key={expense.id}>
                      <TableCell className="font-medium">{expense.code}</TableCell>
                      <TableCell className="max-w-xs truncate">{expense.description}</TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(expense.category)}`}>
                          {expense.category}
                        </span>
                      </TableCell>
                      <TableCell className="font-medium">{formatCurrency(expense.amount)}</TableCell>
                      <TableCell>{new Date(expense.date).toLocaleDateString('ar-SA')}</TableCell>
                      <TableCell className="max-w-xs truncate">{expense.vendor}</TableCell>
                      <TableCell>{expense.paymentMethod}</TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(expense.status)}`}>
                          {expense.status}
                        </span>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center justify-center space-x-2 space-x-reverse">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleViewDetails(expense)}
                          >
                            <EyeIcon className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditExpense(expense)}
                          >
                            <PencilIcon className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteExpense(expense.id)}
                            className="text-red-600 hover:text-red-800"
                          >
                            <TrashIcon className="w-4 h-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* نموذج إضافة/تعديل المصروف */}
      {showForm && (
        <ExpenseForm
          expense={selectedExpense}
          isEditing={isEditing}
          onSave={handleSaveExpense}
          onClose={() => setShowForm(false)}
        />
      )}

      {/* تفاصيل المصروف */}
      {showDetails && selectedExpense && (
        <ExpenseDetails
          expense={selectedExpense}
          onClose={() => setShowDetails(false)}
        />
      )}
    </div>
  );
};

export default Expenses;
