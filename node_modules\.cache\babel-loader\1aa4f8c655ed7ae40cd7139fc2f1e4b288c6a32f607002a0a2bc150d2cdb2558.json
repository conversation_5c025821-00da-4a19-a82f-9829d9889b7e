{"ast": null, "code": "import m, { useMemo as d, useRef as M } from \"react\";\nimport { Features as H, Hidden as T } from '../internal/hidden.js';\nimport { useEvent as E } from './use-event.js';\nimport { useOwnerDocument as b } from './use-owner.js';\nfunction N({\n  defaultContainers: o = [],\n  portals: r,\n  mainTreeNodeRef: u\n} = {}) {\n  var f;\n  let t = M((f = u == null ? void 0 : u.current) != null ? f : null),\n    l = b(t),\n    c = E(() => {\n      var i, s, a;\n      let n = [];\n      for (let e of o) e !== null && (e instanceof HTMLElement ? n.push(e) : \"current\" in e && e.current instanceof HTMLElement && n.push(e.current));\n      if (r != null && r.current) for (let e of r.current) n.push(e);\n      for (let e of (i = l == null ? void 0 : l.querySelectorAll(\"html > *, body > *\")) != null ? i : []) e !== document.body && e !== document.head && e instanceof HTMLElement && e.id !== \"headlessui-portal-root\" && (e.contains(t.current) || e.contains((a = (s = t.current) == null ? void 0 : s.getRootNode()) == null ? void 0 : a.host) || n.some(L => e.contains(L)) || n.push(e));\n      return n;\n    });\n  return {\n    resolveContainers: c,\n    contains: E(n => c().some(i => i.contains(n))),\n    mainTreeNodeRef: t,\n    MainTreeNode: d(() => function () {\n      return u != null ? null : m.createElement(T, {\n        features: H.Hidden,\n        ref: t\n      });\n    }, [t, u])\n  };\n}\nfunction y() {\n  let o = M(null);\n  return {\n    mainTreeNodeRef: o,\n    MainTreeNode: d(() => function () {\n      return m.createElement(T, {\n        features: H.Hidden,\n        ref: o\n      });\n    }, [o])\n  };\n}\nexport { y as useMainTreeNode, N as useRootContainers };", "map": {"version": 3, "names": ["m", "useMemo", "d", "useRef", "M", "Features", "H", "Hidden", "T", "useEvent", "E", "useOwnerDocument", "b", "N", "defaultContainers", "o", "portals", "r", "mainTreeNodeRef", "u", "f", "t", "current", "l", "c", "i", "s", "a", "n", "e", "HTMLElement", "push", "querySelectorAll", "document", "body", "head", "id", "contains", "getRootNode", "host", "some", "L", "resolveContainers", "MainTreeNode", "createElement", "features", "ref", "y", "useMainTreeNode", "useRootContainers"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/node_modules/@headlessui/react/dist/hooks/use-root-containers.js"], "sourcesContent": ["import m,{useMemo as d,useRef as M}from\"react\";import{Features as H,Hidden as T}from'../internal/hidden.js';import{useEvent as E}from'./use-event.js';import{useOwnerDocument as b}from'./use-owner.js';function N({defaultContainers:o=[],portals:r,mainTreeNodeRef:u}={}){var f;let t=M((f=u==null?void 0:u.current)!=null?f:null),l=b(t),c=E(()=>{var i,s,a;let n=[];for(let e of o)e!==null&&(e instanceof HTMLElement?n.push(e):\"current\"in e&&e.current instanceof HTMLElement&&n.push(e.current));if(r!=null&&r.current)for(let e of r.current)n.push(e);for(let e of(i=l==null?void 0:l.querySelectorAll(\"html > *, body > *\"))!=null?i:[])e!==document.body&&e!==document.head&&e instanceof HTMLElement&&e.id!==\"headlessui-portal-root\"&&(e.contains(t.current)||e.contains((a=(s=t.current)==null?void 0:s.getRootNode())==null?void 0:a.host)||n.some(L=>e.contains(L))||n.push(e));return n});return{resolveContainers:c,contains:E(n=>c().some(i=>i.contains(n))),mainTreeNodeRef:t,MainTreeNode:d(()=>function(){return u!=null?null:m.createElement(T,{features:H.Hidden,ref:t})},[t,u])}}function y(){let o=M(null);return{mainTreeNodeRef:o,MainTreeNode:d(()=>function(){return m.createElement(T,{features:H.Hidden,ref:o})},[o])}}export{y as useMainTreeNode,N as useRootContainers};\n"], "mappings": "AAAA,OAAOA,CAAC,IAAEC,OAAO,IAAIC,CAAC,EAACC,MAAM,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,QAAQ,IAAIC,CAAC,EAACC,MAAM,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,gBAAgB;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,gBAAgB;AAAC,SAASC,CAACA,CAAC;EAACC,iBAAiB,EAACC,CAAC,GAAC,EAAE;EAACC,OAAO,EAACC,CAAC;EAACC,eAAe,EAACC;AAAC,CAAC,GAAC,CAAC,CAAC,EAAC;EAAC,IAAIC,CAAC;EAAC,IAAIC,CAAC,GAACjB,CAAC,CAAC,CAACgB,CAAC,GAACD,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACG,OAAO,KAAG,IAAI,GAACF,CAAC,GAAC,IAAI,CAAC;IAACG,CAAC,GAACX,CAAC,CAACS,CAAC,CAAC;IAACG,CAAC,GAACd,CAAC,CAAC,MAAI;MAAC,IAAIe,CAAC,EAACC,CAAC,EAACC,CAAC;MAAC,IAAIC,CAAC,GAAC,EAAE;MAAC,KAAI,IAAIC,CAAC,IAAId,CAAC,EAACc,CAAC,KAAG,IAAI,KAAGA,CAAC,YAAYC,WAAW,GAACF,CAAC,CAACG,IAAI,CAACF,CAAC,CAAC,GAAC,SAAS,IAAGA,CAAC,IAAEA,CAAC,CAACP,OAAO,YAAYQ,WAAW,IAAEF,CAAC,CAACG,IAAI,CAACF,CAAC,CAACP,OAAO,CAAC,CAAC;MAAC,IAAGL,CAAC,IAAE,IAAI,IAAEA,CAAC,CAACK,OAAO,EAAC,KAAI,IAAIO,CAAC,IAAIZ,CAAC,CAACK,OAAO,EAACM,CAAC,CAACG,IAAI,CAACF,CAAC,CAAC;MAAC,KAAI,IAAIA,CAAC,IAAG,CAACJ,CAAC,GAACF,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACS,gBAAgB,CAAC,oBAAoB,CAAC,KAAG,IAAI,GAACP,CAAC,GAAC,EAAE,EAACI,CAAC,KAAGI,QAAQ,CAACC,IAAI,IAAEL,CAAC,KAAGI,QAAQ,CAACE,IAAI,IAAEN,CAAC,YAAYC,WAAW,IAAED,CAAC,CAACO,EAAE,KAAG,wBAAwB,KAAGP,CAAC,CAACQ,QAAQ,CAAChB,CAAC,CAACC,OAAO,CAAC,IAAEO,CAAC,CAACQ,QAAQ,CAAC,CAACV,CAAC,GAAC,CAACD,CAAC,GAACL,CAAC,CAACC,OAAO,KAAG,IAAI,GAAC,KAAK,CAAC,GAACI,CAAC,CAACY,WAAW,CAAC,CAAC,KAAG,IAAI,GAAC,KAAK,CAAC,GAACX,CAAC,CAACY,IAAI,CAAC,IAAEX,CAAC,CAACY,IAAI,CAACC,CAAC,IAAEZ,CAAC,CAACQ,QAAQ,CAACI,CAAC,CAAC,CAAC,IAAEb,CAAC,CAACG,IAAI,CAACF,CAAC,CAAC,CAAC;MAAC,OAAOD,CAAC;IAAA,CAAC,CAAC;EAAC,OAAM;IAACc,iBAAiB,EAAClB,CAAC;IAACa,QAAQ,EAAC3B,CAAC,CAACkB,CAAC,IAAEJ,CAAC,CAAC,CAAC,CAACgB,IAAI,CAACf,CAAC,IAAEA,CAAC,CAACY,QAAQ,CAACT,CAAC,CAAC,CAAC,CAAC;IAACV,eAAe,EAACG,CAAC;IAACsB,YAAY,EAACzC,CAAC,CAAC,MAAI,YAAU;MAAC,OAAOiB,CAAC,IAAE,IAAI,GAAC,IAAI,GAACnB,CAAC,CAAC4C,aAAa,CAACpC,CAAC,EAAC;QAACqC,QAAQ,EAACvC,CAAC,CAACC,MAAM;QAACuC,GAAG,EAACzB;MAAC,CAAC,CAAC;IAAA,CAAC,EAAC,CAACA,CAAC,EAACF,CAAC,CAAC;EAAC,CAAC;AAAA;AAAC,SAAS4B,CAACA,CAAA,EAAE;EAAC,IAAIhC,CAAC,GAACX,CAAC,CAAC,IAAI,CAAC;EAAC,OAAM;IAACc,eAAe,EAACH,CAAC;IAAC4B,YAAY,EAACzC,CAAC,CAAC,MAAI,YAAU;MAAC,OAAOF,CAAC,CAAC4C,aAAa,CAACpC,CAAC,EAAC;QAACqC,QAAQ,EAACvC,CAAC,CAACC,MAAM;QAACuC,GAAG,EAAC/B;MAAC,CAAC,CAAC;IAAA,CAAC,EAAC,CAACA,CAAC,CAAC;EAAC,CAAC;AAAA;AAAC,SAAOgC,CAAC,IAAIC,eAAe,EAACnC,CAAC,IAAIoC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}