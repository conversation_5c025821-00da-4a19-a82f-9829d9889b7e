{"ast": null, "code": "// API لإدارة العملاء\nclass CustomersAPI {\n  constructor() {\n    this.storageKey = 'customers_data';\n    this.initializeData();\n  }\n  initializeData() {\n    const existingData = localStorage.getItem(this.storageKey);\n    if (!existingData) {\n      const initialData = {\n        customers: [{\n          id: 1,\n          code: 'CUST-001',\n          name: 'أحمد محمد علي',\n          type: 'فرد',\n          phone: '0501234567',\n          email: '<EMAIL>',\n          address: 'الرياض، حي النخيل، شارع الملك فهد',\n          city: 'الرياض',\n          country: 'السعودية',\n          taxNumber: '*********',\n          creditLimit: 50000,\n          currentBalance: 8712.5,\n          status: 'نشط',\n          customerSince: '2023-06-15',\n          lastPurchase: '2024-01-15',\n          totalPurchases: 25000,\n          notes: 'عميل مميز - يحصل على خصم 5%',\n          contactPerson: 'أحمد محمد',\n          paymentTerms: 'نقدي',\n          discount: 5\n        }, {\n          id: 2,\n          code: 'CUST-002',\n          name: 'فاطمة أحمد',\n          type: 'فرد',\n          phone: '0507654321',\n          email: '<EMAIL>',\n          address: 'جدة، حي الصفا، شارع التحلية',\n          city: 'جدة',\n          country: 'السعودية',\n          taxNumber: '*********',\n          creditLimit: 30000,\n          currentBalance: 920,\n          status: 'نشط',\n          customerSince: '2023-08-20',\n          lastPurchase: '2024-01-16',\n          totalPurchases: 15000,\n          notes: 'عميلة منتظمة',\n          contactPerson: 'فاطمة أحمد',\n          paymentTerms: 'آجل 30 يوم',\n          discount: 0\n        }, {\n          id: 3,\n          code: 'CUST-003',\n          name: 'شركة التقنية الحديثة',\n          type: 'شركة',\n          phone: '0112345678',\n          email: '<EMAIL>',\n          address: 'الدمام، حي الشاطئ، مجمع الأعمال',\n          city: 'الدمام',\n          country: 'السعودية',\n          taxNumber: '*********',\n          creditLimit: 100000,\n          currentBalance: 0,\n          status: 'نشط',\n          customerSince: '2023-03-10',\n          lastPurchase: '2023-12-28',\n          totalPurchases: 85000,\n          notes: 'عميل مؤسسي - طلبات كبيرة',\n          contactPerson: 'خالد السعد',\n          paymentTerms: 'آجل 45 يوم',\n          discount: 10\n        }],\n        lastId: 3\n      };\n      localStorage.setItem(this.storageKey, JSON.stringify(initialData));\n    }\n  }\n  getData() {\n    return JSON.parse(localStorage.getItem(this.storageKey));\n  }\n  saveData(data) {\n    localStorage.setItem(this.storageKey, JSON.stringify(data));\n  }\n\n  // الحصول على جميع العملاء\n  getAllCustomers() {\n    return this.getData().customers;\n  }\n\n  // إضافة عميل جديد\n  addCustomer(customerData) {\n    const data = this.getData();\n    const newCustomer = {\n      id: data.lastId + 1,\n      code: `CUST-${String(data.lastId + 1).padStart(3, '0')}`,\n      status: 'نشط',\n      customerSince: new Date().toISOString().split('T')[0],\n      currentBalance: 0,\n      totalPurchases: 0,\n      ...customerData\n    };\n    data.customers.push(newCustomer);\n    data.lastId += 1;\n    this.saveData(data);\n    return newCustomer;\n  }\n\n  // تحديث عميل\n  updateCustomer(id, customerData) {\n    const data = this.getData();\n    const index = data.customers.findIndex(customer => customer.id === id);\n    if (index !== -1) {\n      data.customers[index] = {\n        ...data.customers[index],\n        ...customerData\n      };\n      this.saveData(data);\n      return data.customers[index];\n    }\n    return null;\n  }\n\n  // حذف عميل\n  deleteCustomer(id) {\n    const data = this.getData();\n    data.customers = data.customers.filter(customer => customer.id !== id);\n    this.saveData(data);\n    return true;\n  }\n\n  // الحصول على عميل بالمعرف\n  getCustomerById(id) {\n    const data = this.getData();\n    return data.customers.find(customer => customer.id === id);\n  }\n\n  // تحديث رصيد العميل\n  updateCustomerBalance(customerId, amount) {\n    const data = this.getData();\n    const customer = data.customers.find(c => c.id === customerId);\n    if (customer) {\n      customer.currentBalance += amount;\n      customer.totalPurchases += Math.max(0, amount);\n      customer.lastPurchase = new Date().toISOString().split('T')[0];\n      this.saveData(data);\n      return customer;\n    }\n    return null;\n  }\n\n  // إحصائيات العملاء\n  getCustomersStats() {\n    const customers = this.getAllCustomers();\n    return {\n      totalCustomers: customers.length,\n      activeCustomers: customers.filter(customer => customer.status === 'نشط').length,\n      individualCustomers: customers.filter(customer => customer.type === 'فرد').length,\n      corporateCustomers: customers.filter(customer => customer.type === 'شركة').length,\n      totalBalance: customers.reduce((sum, customer) => sum + customer.currentBalance, 0),\n      totalSales: customers.reduce((sum, customer) => sum + customer.totalPurchases, 0),\n      averagePurchase: customers.length > 0 ? customers.reduce((sum, customer) => sum + customer.totalPurchases, 0) / customers.length : 0\n    };\n  }\n\n  // العملاء المدينون\n  getDebtorCustomers() {\n    const customers = this.getAllCustomers();\n    return customers.filter(customer => customer.currentBalance > 0);\n  }\n\n  // أفضل العملاء\n  getTopCustomers(limit = 10) {\n    const customers = this.getAllCustomers();\n    return customers.sort((a, b) => b.totalPurchases - a.totalPurchases).slice(0, limit);\n  }\n\n  // البحث في العملاء\n  searchCustomers(query) {\n    const customers = this.getAllCustomers();\n    return customers.filter(customer => customer.name.toLowerCase().includes(query.toLowerCase()) || customer.code.toLowerCase().includes(query.toLowerCase()) || customer.phone.includes(query) || customer.email.toLowerCase().includes(query.toLowerCase()));\n  }\n}\nexport default new CustomersAPI();", "map": {"version": 3, "names": ["CustomersAPI", "constructor", "storageKey", "initializeData", "existingData", "localStorage", "getItem", "initialData", "customers", "id", "code", "name", "type", "phone", "email", "address", "city", "country", "taxNumber", "creditLimit", "currentBalance", "status", "customerSince", "lastPurchase", "totalPurchases", "notes", "<PERSON><PERSON><PERSON>", "paymentTerms", "discount", "lastId", "setItem", "JSON", "stringify", "getData", "parse", "saveData", "data", "getAllCustomers", "addCustomer", "customerData", "newCustomer", "String", "padStart", "Date", "toISOString", "split", "push", "updateCustomer", "index", "findIndex", "customer", "deleteCustomer", "filter", "getCustomerById", "find", "updateCustomerBalance", "customerId", "amount", "c", "Math", "max", "getCustomersStats", "totalCustomers", "length", "activeCustomers", "individualCustomers", "corporateCustomers", "totalBalance", "reduce", "sum", "totalSales", "averagePurchase", "getDebtorCustomers", "getTopCustomers", "limit", "sort", "a", "b", "slice", "searchCustomers", "query", "toLowerCase", "includes"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/src/api/customersAPI.js"], "sourcesContent": ["// API لإدارة العملاء\nclass CustomersAPI {\n  constructor() {\n    this.storageKey = 'customers_data';\n    this.initializeData();\n  }\n\n  initializeData() {\n    const existingData = localStorage.getItem(this.storageKey);\n    if (!existingData) {\n      const initialData = {\n        customers: [\n          {\n            id: 1,\n            code: 'CUST-001',\n            name: 'أحمد محمد علي',\n            type: 'فرد',\n            phone: '0501234567',\n            email: '<EMAIL>',\n            address: 'الرياض، حي النخيل، شارع الملك فهد',\n            city: 'الرياض',\n            country: 'السعودية',\n            taxNumber: '*********',\n            creditLimit: 50000,\n            currentBalance: 8712.5,\n            status: 'نشط',\n            customerSince: '2023-06-15',\n            lastPurchase: '2024-01-15',\n            totalPurchases: 25000,\n            notes: 'عميل مميز - يحصل على خصم 5%',\n            contactPerson: 'أحمد محمد',\n            paymentTerms: 'نقدي',\n            discount: 5\n          },\n          {\n            id: 2,\n            code: 'CUST-002',\n            name: 'فاطمة أحمد',\n            type: 'فرد',\n            phone: '0507654321',\n            email: '<EMAIL>',\n            address: 'جدة، حي الصفا، شارع التحلية',\n            city: 'جدة',\n            country: 'السعودية',\n            taxNumber: '*********',\n            creditLimit: 30000,\n            currentBalance: 920,\n            status: 'نشط',\n            customerSince: '2023-08-20',\n            lastPurchase: '2024-01-16',\n            totalPurchases: 15000,\n            notes: 'عميلة منتظمة',\n            contactPerson: 'فاطمة أحمد',\n            paymentTerms: 'آجل 30 يوم',\n            discount: 0\n          },\n          {\n            id: 3,\n            code: 'CUST-003',\n            name: 'شركة التقنية الحديثة',\n            type: 'شركة',\n            phone: '0112345678',\n            email: '<EMAIL>',\n            address: 'الدمام، حي الشاطئ، مجمع الأعمال',\n            city: 'الدمام',\n            country: 'السعودية',\n            taxNumber: '*********',\n            creditLimit: 100000,\n            currentBalance: 0,\n            status: 'نشط',\n            customerSince: '2023-03-10',\n            lastPurchase: '2023-12-28',\n            totalPurchases: 85000,\n            notes: 'عميل مؤسسي - طلبات كبيرة',\n            contactPerson: 'خالد السعد',\n            paymentTerms: 'آجل 45 يوم',\n            discount: 10\n          }\n        ],\n        lastId: 3\n      };\n      localStorage.setItem(this.storageKey, JSON.stringify(initialData));\n    }\n  }\n\n  getData() {\n    return JSON.parse(localStorage.getItem(this.storageKey));\n  }\n\n  saveData(data) {\n    localStorage.setItem(this.storageKey, JSON.stringify(data));\n  }\n\n  // الحصول على جميع العملاء\n  getAllCustomers() {\n    return this.getData().customers;\n  }\n\n  // إضافة عميل جديد\n  addCustomer(customerData) {\n    const data = this.getData();\n    const newCustomer = {\n      id: data.lastId + 1,\n      code: `CUST-${String(data.lastId + 1).padStart(3, '0')}`,\n      status: 'نشط',\n      customerSince: new Date().toISOString().split('T')[0],\n      currentBalance: 0,\n      totalPurchases: 0,\n      ...customerData\n    };\n    \n    data.customers.push(newCustomer);\n    data.lastId += 1;\n    this.saveData(data);\n    return newCustomer;\n  }\n\n  // تحديث عميل\n  updateCustomer(id, customerData) {\n    const data = this.getData();\n    const index = data.customers.findIndex(customer => customer.id === id);\n    if (index !== -1) {\n      data.customers[index] = { ...data.customers[index], ...customerData };\n      this.saveData(data);\n      return data.customers[index];\n    }\n    return null;\n  }\n\n  // حذف عميل\n  deleteCustomer(id) {\n    const data = this.getData();\n    data.customers = data.customers.filter(customer => customer.id !== id);\n    this.saveData(data);\n    return true;\n  }\n\n  // الحصول على عميل بالمعرف\n  getCustomerById(id) {\n    const data = this.getData();\n    return data.customers.find(customer => customer.id === id);\n  }\n\n  // تحديث رصيد العميل\n  updateCustomerBalance(customerId, amount) {\n    const data = this.getData();\n    const customer = data.customers.find(c => c.id === customerId);\n    if (customer) {\n      customer.currentBalance += amount;\n      customer.totalPurchases += Math.max(0, amount);\n      customer.lastPurchase = new Date().toISOString().split('T')[0];\n      this.saveData(data);\n      return customer;\n    }\n    return null;\n  }\n\n  // إحصائيات العملاء\n  getCustomersStats() {\n    const customers = this.getAllCustomers();\n    \n    return {\n      totalCustomers: customers.length,\n      activeCustomers: customers.filter(customer => customer.status === 'نشط').length,\n      individualCustomers: customers.filter(customer => customer.type === 'فرد').length,\n      corporateCustomers: customers.filter(customer => customer.type === 'شركة').length,\n      totalBalance: customers.reduce((sum, customer) => sum + customer.currentBalance, 0),\n      totalSales: customers.reduce((sum, customer) => sum + customer.totalPurchases, 0),\n      averagePurchase: customers.length > 0 ? customers.reduce((sum, customer) => sum + customer.totalPurchases, 0) / customers.length : 0\n    };\n  }\n\n  // العملاء المدينون\n  getDebtorCustomers() {\n    const customers = this.getAllCustomers();\n    return customers.filter(customer => customer.currentBalance > 0);\n  }\n\n  // أفضل العملاء\n  getTopCustomers(limit = 10) {\n    const customers = this.getAllCustomers();\n    return customers\n      .sort((a, b) => b.totalPurchases - a.totalPurchases)\n      .slice(0, limit);\n  }\n\n  // البحث في العملاء\n  searchCustomers(query) {\n    const customers = this.getAllCustomers();\n    return customers.filter(customer => \n      customer.name.toLowerCase().includes(query.toLowerCase()) ||\n      customer.code.toLowerCase().includes(query.toLowerCase()) ||\n      customer.phone.includes(query) ||\n      customer.email.toLowerCase().includes(query.toLowerCase())\n    );\n  }\n}\n\nexport default new CustomersAPI();\n"], "mappings": "AAAA;AACA,MAAMA,YAAY,CAAC;EACjBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,UAAU,GAAG,gBAAgB;IAClC,IAAI,CAACC,cAAc,CAAC,CAAC;EACvB;EAEAA,cAAcA,CAAA,EAAG;IACf,MAAMC,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,IAAI,CAACJ,UAAU,CAAC;IAC1D,IAAI,CAACE,YAAY,EAAE;MACjB,MAAMG,WAAW,GAAG;QAClBC,SAAS,EAAE,CACT;UACEC,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,UAAU;UAChBC,IAAI,EAAE,eAAe;UACrBC,IAAI,EAAE,KAAK;UACXC,KAAK,EAAE,YAAY;UACnBC,KAAK,EAAE,mBAAmB;UAC1BC,OAAO,EAAE,mCAAmC;UAC5CC,IAAI,EAAE,QAAQ;UACdC,OAAO,EAAE,UAAU;UACnBC,SAAS,EAAE,WAAW;UACtBC,WAAW,EAAE,KAAK;UAClBC,cAAc,EAAE,MAAM;UACtBC,MAAM,EAAE,KAAK;UACbC,aAAa,EAAE,YAAY;UAC3BC,YAAY,EAAE,YAAY;UAC1BC,cAAc,EAAE,KAAK;UACrBC,KAAK,EAAE,6BAA6B;UACpCC,aAAa,EAAE,WAAW;UAC1BC,YAAY,EAAE,MAAM;UACpBC,QAAQ,EAAE;QACZ,CAAC,EACD;UACEnB,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,UAAU;UAChBC,IAAI,EAAE,YAAY;UAClBC,IAAI,EAAE,KAAK;UACXC,KAAK,EAAE,YAAY;UACnBC,KAAK,EAAE,oBAAoB;UAC3BC,OAAO,EAAE,6BAA6B;UACtCC,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,UAAU;UACnBC,SAAS,EAAE,WAAW;UACtBC,WAAW,EAAE,KAAK;UAClBC,cAAc,EAAE,GAAG;UACnBC,MAAM,EAAE,KAAK;UACbC,aAAa,EAAE,YAAY;UAC3BC,YAAY,EAAE,YAAY;UAC1BC,cAAc,EAAE,KAAK;UACrBC,KAAK,EAAE,cAAc;UACrBC,aAAa,EAAE,YAAY;UAC3BC,YAAY,EAAE,YAAY;UAC1BC,QAAQ,EAAE;QACZ,CAAC,EACD;UACEnB,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,UAAU;UAChBC,IAAI,EAAE,sBAAsB;UAC5BC,IAAI,EAAE,MAAM;UACZC,KAAK,EAAE,YAAY;UACnBC,KAAK,EAAE,qBAAqB;UAC5BC,OAAO,EAAE,iCAAiC;UAC1CC,IAAI,EAAE,QAAQ;UACdC,OAAO,EAAE,UAAU;UACnBC,SAAS,EAAE,WAAW;UACtBC,WAAW,EAAE,MAAM;UACnBC,cAAc,EAAE,CAAC;UACjBC,MAAM,EAAE,KAAK;UACbC,aAAa,EAAE,YAAY;UAC3BC,YAAY,EAAE,YAAY;UAC1BC,cAAc,EAAE,KAAK;UACrBC,KAAK,EAAE,0BAA0B;UACjCC,aAAa,EAAE,YAAY;UAC3BC,YAAY,EAAE,YAAY;UAC1BC,QAAQ,EAAE;QACZ,CAAC,CACF;QACDC,MAAM,EAAE;MACV,CAAC;MACDxB,YAAY,CAACyB,OAAO,CAAC,IAAI,CAAC5B,UAAU,EAAE6B,IAAI,CAACC,SAAS,CAACzB,WAAW,CAAC,CAAC;IACpE;EACF;EAEA0B,OAAOA,CAAA,EAAG;IACR,OAAOF,IAAI,CAACG,KAAK,CAAC7B,YAAY,CAACC,OAAO,CAAC,IAAI,CAACJ,UAAU,CAAC,CAAC;EAC1D;EAEAiC,QAAQA,CAACC,IAAI,EAAE;IACb/B,YAAY,CAACyB,OAAO,CAAC,IAAI,CAAC5B,UAAU,EAAE6B,IAAI,CAACC,SAAS,CAACI,IAAI,CAAC,CAAC;EAC7D;;EAEA;EACAC,eAAeA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACJ,OAAO,CAAC,CAAC,CAACzB,SAAS;EACjC;;EAEA;EACA8B,WAAWA,CAACC,YAAY,EAAE;IACxB,MAAMH,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC,CAAC;IAC3B,MAAMO,WAAW,GAAG;MAClB/B,EAAE,EAAE2B,IAAI,CAACP,MAAM,GAAG,CAAC;MACnBnB,IAAI,EAAE,QAAQ+B,MAAM,CAACL,IAAI,CAACP,MAAM,GAAG,CAAC,CAAC,CAACa,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;MACxDrB,MAAM,EAAE,KAAK;MACbC,aAAa,EAAE,IAAIqB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACrDzB,cAAc,EAAE,CAAC;MACjBI,cAAc,EAAE,CAAC;MACjB,GAAGe;IACL,CAAC;IAEDH,IAAI,CAAC5B,SAAS,CAACsC,IAAI,CAACN,WAAW,CAAC;IAChCJ,IAAI,CAACP,MAAM,IAAI,CAAC;IAChB,IAAI,CAACM,QAAQ,CAACC,IAAI,CAAC;IACnB,OAAOI,WAAW;EACpB;;EAEA;EACAO,cAAcA,CAACtC,EAAE,EAAE8B,YAAY,EAAE;IAC/B,MAAMH,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC,CAAC;IAC3B,MAAMe,KAAK,GAAGZ,IAAI,CAAC5B,SAAS,CAACyC,SAAS,CAACC,QAAQ,IAAIA,QAAQ,CAACzC,EAAE,KAAKA,EAAE,CAAC;IACtE,IAAIuC,KAAK,KAAK,CAAC,CAAC,EAAE;MAChBZ,IAAI,CAAC5B,SAAS,CAACwC,KAAK,CAAC,GAAG;QAAE,GAAGZ,IAAI,CAAC5B,SAAS,CAACwC,KAAK,CAAC;QAAE,GAAGT;MAAa,CAAC;MACrE,IAAI,CAACJ,QAAQ,CAACC,IAAI,CAAC;MACnB,OAAOA,IAAI,CAAC5B,SAAS,CAACwC,KAAK,CAAC;IAC9B;IACA,OAAO,IAAI;EACb;;EAEA;EACAG,cAAcA,CAAC1C,EAAE,EAAE;IACjB,MAAM2B,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC,CAAC;IAC3BG,IAAI,CAAC5B,SAAS,GAAG4B,IAAI,CAAC5B,SAAS,CAAC4C,MAAM,CAACF,QAAQ,IAAIA,QAAQ,CAACzC,EAAE,KAAKA,EAAE,CAAC;IACtE,IAAI,CAAC0B,QAAQ,CAACC,IAAI,CAAC;IACnB,OAAO,IAAI;EACb;;EAEA;EACAiB,eAAeA,CAAC5C,EAAE,EAAE;IAClB,MAAM2B,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC,CAAC;IAC3B,OAAOG,IAAI,CAAC5B,SAAS,CAAC8C,IAAI,CAACJ,QAAQ,IAAIA,QAAQ,CAACzC,EAAE,KAAKA,EAAE,CAAC;EAC5D;;EAEA;EACA8C,qBAAqBA,CAACC,UAAU,EAAEC,MAAM,EAAE;IACxC,MAAMrB,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC,CAAC;IAC3B,MAAMiB,QAAQ,GAAGd,IAAI,CAAC5B,SAAS,CAAC8C,IAAI,CAACI,CAAC,IAAIA,CAAC,CAACjD,EAAE,KAAK+C,UAAU,CAAC;IAC9D,IAAIN,QAAQ,EAAE;MACZA,QAAQ,CAAC9B,cAAc,IAAIqC,MAAM;MACjCP,QAAQ,CAAC1B,cAAc,IAAImC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEH,MAAM,CAAC;MAC9CP,QAAQ,CAAC3B,YAAY,GAAG,IAAIoB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC9D,IAAI,CAACV,QAAQ,CAACC,IAAI,CAAC;MACnB,OAAOc,QAAQ;IACjB;IACA,OAAO,IAAI;EACb;;EAEA;EACAW,iBAAiBA,CAAA,EAAG;IAClB,MAAMrD,SAAS,GAAG,IAAI,CAAC6B,eAAe,CAAC,CAAC;IAExC,OAAO;MACLyB,cAAc,EAAEtD,SAAS,CAACuD,MAAM;MAChCC,eAAe,EAAExD,SAAS,CAAC4C,MAAM,CAACF,QAAQ,IAAIA,QAAQ,CAAC7B,MAAM,KAAK,KAAK,CAAC,CAAC0C,MAAM;MAC/EE,mBAAmB,EAAEzD,SAAS,CAAC4C,MAAM,CAACF,QAAQ,IAAIA,QAAQ,CAACtC,IAAI,KAAK,KAAK,CAAC,CAACmD,MAAM;MACjFG,kBAAkB,EAAE1D,SAAS,CAAC4C,MAAM,CAACF,QAAQ,IAAIA,QAAQ,CAACtC,IAAI,KAAK,MAAM,CAAC,CAACmD,MAAM;MACjFI,YAAY,EAAE3D,SAAS,CAAC4D,MAAM,CAAC,CAACC,GAAG,EAAEnB,QAAQ,KAAKmB,GAAG,GAAGnB,QAAQ,CAAC9B,cAAc,EAAE,CAAC,CAAC;MACnFkD,UAAU,EAAE9D,SAAS,CAAC4D,MAAM,CAAC,CAACC,GAAG,EAAEnB,QAAQ,KAAKmB,GAAG,GAAGnB,QAAQ,CAAC1B,cAAc,EAAE,CAAC,CAAC;MACjF+C,eAAe,EAAE/D,SAAS,CAACuD,MAAM,GAAG,CAAC,GAAGvD,SAAS,CAAC4D,MAAM,CAAC,CAACC,GAAG,EAAEnB,QAAQ,KAAKmB,GAAG,GAAGnB,QAAQ,CAAC1B,cAAc,EAAE,CAAC,CAAC,GAAGhB,SAAS,CAACuD,MAAM,GAAG;IACrI,CAAC;EACH;;EAEA;EACAS,kBAAkBA,CAAA,EAAG;IACnB,MAAMhE,SAAS,GAAG,IAAI,CAAC6B,eAAe,CAAC,CAAC;IACxC,OAAO7B,SAAS,CAAC4C,MAAM,CAACF,QAAQ,IAAIA,QAAQ,CAAC9B,cAAc,GAAG,CAAC,CAAC;EAClE;;EAEA;EACAqD,eAAeA,CAACC,KAAK,GAAG,EAAE,EAAE;IAC1B,MAAMlE,SAAS,GAAG,IAAI,CAAC6B,eAAe,CAAC,CAAC;IACxC,OAAO7B,SAAS,CACbmE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACrD,cAAc,GAAGoD,CAAC,CAACpD,cAAc,CAAC,CACnDsD,KAAK,CAAC,CAAC,EAAEJ,KAAK,CAAC;EACpB;;EAEA;EACAK,eAAeA,CAACC,KAAK,EAAE;IACrB,MAAMxE,SAAS,GAAG,IAAI,CAAC6B,eAAe,CAAC,CAAC;IACxC,OAAO7B,SAAS,CAAC4C,MAAM,CAACF,QAAQ,IAC9BA,QAAQ,CAACvC,IAAI,CAACsE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC,IACzD/B,QAAQ,CAACxC,IAAI,CAACuE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC,IACzD/B,QAAQ,CAACrC,KAAK,CAACqE,QAAQ,CAACF,KAAK,CAAC,IAC9B9B,QAAQ,CAACpC,KAAK,CAACmE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,KAAK,CAACC,WAAW,CAAC,CAAC,CAC3D,CAAC;EACH;AACF;AAEA,eAAe,IAAIjF,YAAY,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}