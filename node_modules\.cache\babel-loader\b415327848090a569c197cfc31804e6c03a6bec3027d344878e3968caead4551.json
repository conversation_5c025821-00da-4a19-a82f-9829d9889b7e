{"ast": null, "code": "import { ascendingDefined, compareDefined } from \"./sort.js\";\n\n// Based on https://github.com/mourner/quickselect\n// ISC license, Copyright 2018 Vladimir Agafonkin.\nexport default function quickselect(array, k, left = 0, right = Infinity, compare) {\n  k = Math.floor(k);\n  left = Math.floor(Math.max(0, left));\n  right = Math.floor(Math.min(array.length - 1, right));\n  if (!(left <= k && k <= right)) return array;\n  compare = compare === undefined ? ascendingDefined : compareDefined(compare);\n  while (right > left) {\n    if (right - left > 600) {\n      const n = right - left + 1;\n      const m = k - left + 1;\n      const z = Math.log(n);\n      const s = 0.5 * Math.exp(2 * z / 3);\n      const sd = 0.5 * Math.sqrt(z * s * (n - s) / n) * (m - n / 2 < 0 ? -1 : 1);\n      const newLeft = Math.max(left, Math.floor(k - m * s / n + sd));\n      const newRight = Math.min(right, Math.floor(k + (n - m) * s / n + sd));\n      quickselect(array, k, newLeft, newRight, compare);\n    }\n    const t = array[k];\n    let i = left;\n    let j = right;\n    swap(array, left, k);\n    if (compare(array[right], t) > 0) swap(array, left, right);\n    while (i < j) {\n      swap(array, i, j), ++i, --j;\n      while (compare(array[i], t) < 0) ++i;\n      while (compare(array[j], t) > 0) --j;\n    }\n    if (compare(array[left], t) === 0) swap(array, left, j);else ++j, swap(array, j, right);\n    if (j <= k) left = j + 1;\n    if (k <= j) right = j - 1;\n  }\n  return array;\n}\nfunction swap(array, i, j) {\n  const t = array[i];\n  array[i] = array[j];\n  array[j] = t;\n}", "map": {"version": 3, "names": ["ascendingDefined", "compareDefined", "quickselect", "array", "k", "left", "right", "Infinity", "compare", "Math", "floor", "max", "min", "length", "undefined", "n", "m", "z", "log", "s", "exp", "sd", "sqrt", "newLeft", "newRight", "t", "i", "j", "swap"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/node_modules/d3-array/src/quickselect.js"], "sourcesContent": ["import {ascendingDefined, compareDefined} from \"./sort.js\";\n\n// Based on https://github.com/mourner/quickselect\n// ISC license, Copyright 2018 Vladimir Agafonkin.\nexport default function quickselect(array, k, left = 0, right = Infinity, compare) {\n  k = Math.floor(k);\n  left = Math.floor(Math.max(0, left));\n  right = Math.floor(Math.min(array.length - 1, right));\n\n  if (!(left <= k && k <= right)) return array;\n\n  compare = compare === undefined ? ascendingDefined : compareDefined(compare);\n\n  while (right > left) {\n    if (right - left > 600) {\n      const n = right - left + 1;\n      const m = k - left + 1;\n      const z = Math.log(n);\n      const s = 0.5 * Math.exp(2 * z / 3);\n      const sd = 0.5 * Math.sqrt(z * s * (n - s) / n) * (m - n / 2 < 0 ? -1 : 1);\n      const newLeft = Math.max(left, Math.floor(k - m * s / n + sd));\n      const newRight = Math.min(right, Math.floor(k + (n - m) * s / n + sd));\n      quickselect(array, k, newLeft, newRight, compare);\n    }\n\n    const t = array[k];\n    let i = left;\n    let j = right;\n\n    swap(array, left, k);\n    if (compare(array[right], t) > 0) swap(array, left, right);\n\n    while (i < j) {\n      swap(array, i, j), ++i, --j;\n      while (compare(array[i], t) < 0) ++i;\n      while (compare(array[j], t) > 0) --j;\n    }\n\n    if (compare(array[left], t) === 0) swap(array, left, j);\n    else ++j, swap(array, j, right);\n\n    if (j <= k) left = j + 1;\n    if (k <= j) right = j - 1;\n  }\n\n  return array;\n}\n\nfunction swap(array, i, j) {\n  const t = array[i];\n  array[i] = array[j];\n  array[j] = t;\n}\n"], "mappings": "AAAA,SAAQA,gBAAgB,EAAEC,cAAc,QAAO,WAAW;;AAE1D;AACA;AACA,eAAe,SAASC,WAAWA,CAACC,KAAK,EAAEC,CAAC,EAAEC,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAGC,QAAQ,EAAEC,OAAO,EAAE;EACjFJ,CAAC,GAAGK,IAAI,CAACC,KAAK,CAACN,CAAC,CAAC;EACjBC,IAAI,GAAGI,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEN,IAAI,CAAC,CAAC;EACpCC,KAAK,GAAGG,IAAI,CAACC,KAAK,CAACD,IAAI,CAACG,GAAG,CAACT,KAAK,CAACU,MAAM,GAAG,CAAC,EAAEP,KAAK,CAAC,CAAC;EAErD,IAAI,EAAED,IAAI,IAAID,CAAC,IAAIA,CAAC,IAAIE,KAAK,CAAC,EAAE,OAAOH,KAAK;EAE5CK,OAAO,GAAGA,OAAO,KAAKM,SAAS,GAAGd,gBAAgB,GAAGC,cAAc,CAACO,OAAO,CAAC;EAE5E,OAAOF,KAAK,GAAGD,IAAI,EAAE;IACnB,IAAIC,KAAK,GAAGD,IAAI,GAAG,GAAG,EAAE;MACtB,MAAMU,CAAC,GAAGT,KAAK,GAAGD,IAAI,GAAG,CAAC;MAC1B,MAAMW,CAAC,GAAGZ,CAAC,GAAGC,IAAI,GAAG,CAAC;MACtB,MAAMY,CAAC,GAAGR,IAAI,CAACS,GAAG,CAACH,CAAC,CAAC;MACrB,MAAMI,CAAC,GAAG,GAAG,GAAGV,IAAI,CAACW,GAAG,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,CAAC;MACnC,MAAMI,EAAE,GAAG,GAAG,GAAGZ,IAAI,CAACa,IAAI,CAACL,CAAC,GAAGE,CAAC,IAAIJ,CAAC,GAAGI,CAAC,CAAC,GAAGJ,CAAC,CAAC,IAAIC,CAAC,GAAGD,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MAC1E,MAAMQ,OAAO,GAAGd,IAAI,CAACE,GAAG,CAACN,IAAI,EAAEI,IAAI,CAACC,KAAK,CAACN,CAAC,GAAGY,CAAC,GAAGG,CAAC,GAAGJ,CAAC,GAAGM,EAAE,CAAC,CAAC;MAC9D,MAAMG,QAAQ,GAAGf,IAAI,CAACG,GAAG,CAACN,KAAK,EAAEG,IAAI,CAACC,KAAK,CAACN,CAAC,GAAG,CAACW,CAAC,GAAGC,CAAC,IAAIG,CAAC,GAAGJ,CAAC,GAAGM,EAAE,CAAC,CAAC;MACtEnB,WAAW,CAACC,KAAK,EAAEC,CAAC,EAAEmB,OAAO,EAAEC,QAAQ,EAAEhB,OAAO,CAAC;IACnD;IAEA,MAAMiB,CAAC,GAAGtB,KAAK,CAACC,CAAC,CAAC;IAClB,IAAIsB,CAAC,GAAGrB,IAAI;IACZ,IAAIsB,CAAC,GAAGrB,KAAK;IAEbsB,IAAI,CAACzB,KAAK,EAAEE,IAAI,EAAED,CAAC,CAAC;IACpB,IAAII,OAAO,CAACL,KAAK,CAACG,KAAK,CAAC,EAAEmB,CAAC,CAAC,GAAG,CAAC,EAAEG,IAAI,CAACzB,KAAK,EAAEE,IAAI,EAAEC,KAAK,CAAC;IAE1D,OAAOoB,CAAC,GAAGC,CAAC,EAAE;MACZC,IAAI,CAACzB,KAAK,EAAEuB,CAAC,EAAEC,CAAC,CAAC,EAAE,EAAED,CAAC,EAAE,EAAEC,CAAC;MAC3B,OAAOnB,OAAO,CAACL,KAAK,CAACuB,CAAC,CAAC,EAAED,CAAC,CAAC,GAAG,CAAC,EAAE,EAAEC,CAAC;MACpC,OAAOlB,OAAO,CAACL,KAAK,CAACwB,CAAC,CAAC,EAAEF,CAAC,CAAC,GAAG,CAAC,EAAE,EAAEE,CAAC;IACtC;IAEA,IAAInB,OAAO,CAACL,KAAK,CAACE,IAAI,CAAC,EAAEoB,CAAC,CAAC,KAAK,CAAC,EAAEG,IAAI,CAACzB,KAAK,EAAEE,IAAI,EAAEsB,CAAC,CAAC,CAAC,KACnD,EAAEA,CAAC,EAAEC,IAAI,CAACzB,KAAK,EAAEwB,CAAC,EAAErB,KAAK,CAAC;IAE/B,IAAIqB,CAAC,IAAIvB,CAAC,EAAEC,IAAI,GAAGsB,CAAC,GAAG,CAAC;IACxB,IAAIvB,CAAC,IAAIuB,CAAC,EAAErB,KAAK,GAAGqB,CAAC,GAAG,CAAC;EAC3B;EAEA,OAAOxB,KAAK;AACd;AAEA,SAASyB,IAAIA,CAACzB,KAAK,EAAEuB,CAAC,EAAEC,CAAC,EAAE;EACzB,MAAMF,CAAC,GAAGtB,KAAK,CAACuB,CAAC,CAAC;EAClBvB,KAAK,CAACuB,CAAC,CAAC,GAAGvB,KAAK,CAACwB,CAAC,CAAC;EACnBxB,KAAK,CAACwB,CAAC,CAAC,GAAGF,CAAC;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}