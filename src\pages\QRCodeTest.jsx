import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { QrCodeIcon, DocumentTextIcon, CheckCircleIcon } from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import InvoiceQRCode from '../components/QRCode/InvoiceQRCode';
import { generateQRCodeData, generateSimpleQRCode, validateSaudiVATNumber } from '../utils/qrCodeGenerator';

const QRCodeTest = () => {
  const [testInvoice] = useState({
    id: 1,
    invoiceNumber: 'INV-2024-001',
    customerId: 1,
    customerName: 'أحمد محمد',
    date: '2024-01-15',
    items: [
      { productId: 1, productName: 'لابتوب Dell', quantity: 2, price: 3500, total: 7000 },
      { productId: 2, productName: 'ماوس لاسلكي', quantity: 5, price: 150, total: 750 }
    ],
    subtotal: 7750,
    tax: 1162.5,
    discount: 200,
    total: 8712.5,
    status: 'مكتملة',
    paymentMethod: 'نقدي',
    notes: 'فاتورة اختبار للتحقق من QR Code'
  });

  const [qrData, setQrData] = useState('');
  const [qrType, setQrType] = useState('phase2');

  const generateTestQR = () => {
    let data = '';
    if (qrType === 'phase2') {
      data = generateQRCodeData(testInvoice);
    } else {
      data = generateSimpleQRCode(testInvoice);
    }
    setQrData(data);
  };

  const testVATNumber = (vatNumber) => {
    return validateSaudiVATNumber(vatNumber);
  };

  const testCases = [
    { vatNumber: '300000000000003', expected: true, description: 'رقم ضريبي صحيح (15 رقم)' },
    { vatNumber: '12345678901234', expected: false, description: 'رقم ضريبي قصير (14 رقم)' },
    { vatNumber: '1234567890123456', expected: false, description: 'رقم ضريبي طويل (16 رقم)' },
    { vatNumber: '30000000000000A', expected: false, description: 'رقم ضريبي يحتوي على أحرف' },
    { vatNumber: '', expected: false, description: 'رقم ضريبي فارغ' }
  ];

  return (
    <div className="space-y-6">
      {/* العنوان */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div className="flex items-center mb-6">
          <QrCodeIcon className="w-8 h-8 text-blue-600 ml-3" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">اختبار QR Code</h1>
            <p className="text-gray-600 mt-2">اختبار وتجربة QR Code للفوترة الإلكترونية</p>
          </div>
        </div>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* QR Code للفاتورة التجريبية */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <DocumentTextIcon className="w-5 h-5 ml-2" />
                فاتورة تجريبية
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* معلومات الفاتورة */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-3">تفاصيل الفاتورة:</h4>
                  <div className="grid grid-cols-2 gap-3 text-sm">
                    <div>
                      <span className="text-gray-600">رقم الفاتورة:</span>
                      <p className="font-medium">{testInvoice.invoiceNumber}</p>
                    </div>
                    <div>
                      <span className="text-gray-600">العميل:</span>
                      <p className="font-medium">{testInvoice.customerName}</p>
                    </div>
                    <div>
                      <span className="text-gray-600">التاريخ:</span>
                      <p className="font-medium">{new Date(testInvoice.date).toLocaleDateString('ar-SA')}</p>
                    </div>
                    <div>
                      <span className="text-gray-600">الإجمالي:</span>
                      <p className="font-medium">{testInvoice.total.toFixed(2)} ر.س</p>
                    </div>
                  </div>
                </div>

                {/* QR Code */}
                <InvoiceQRCode 
                  invoiceData={testInvoice} 
                  size={200} 
                  showDetails={true}
                />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* اختبار مولد QR Code */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card>
            <CardHeader>
              <CardTitle>اختبار مولد QR Code</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* اختيار نوع QR Code */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  نوع QR Code:
                </label>
                <select
                  value={qrType}
                  onChange={(e) => setQrType(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="phase2">المرحلة الثانية (TLV + Base64)</option>
                  <option value="simple">مبسط (JSON)</option>
                </select>
              </div>

              {/* زر إنشاء QR Code */}
              <Button
                onClick={generateTestQR}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white"
              >
                إنشاء QR Code
              </Button>

              {/* عرض البيانات المُنشأة */}
              {qrData && (
                <div className="space-y-3">
                  <h4 className="font-medium text-gray-900">البيانات المُنشأة:</h4>
                  <div className="bg-gray-100 p-3 rounded text-xs font-mono break-all max-h-40 overflow-y-auto">
                    {qrData}
                  </div>
                  <div className="text-sm text-gray-600">
                    طول البيانات: {qrData.length} حرف
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* اختبار التحقق من الرقم الضريبي */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <Card>
          <CardHeader>
            <CardTitle>اختبار التحقق من الرقم الضريبي</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-gray-600 mb-4">
                اختبار دالة التحقق من صحة الرقم الضريبي السعودي (15 رقم):
              </p>
              
              <div className="space-y-3">
                {testCases.map((testCase, index) => {
                  const result = testVATNumber(testCase.vatNumber);
                  const isCorrect = result === testCase.expected;
                  
                  return (
                    <div
                      key={index}
                      className={`p-3 rounded-lg border ${
                        isCorrect ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-mono text-sm">
                            {testCase.vatNumber || '(فارغ)'}
                          </p>
                          <p className="text-xs text-gray-600">
                            {testCase.description}
                          </p>
                        </div>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <span className={`text-xs px-2 py-1 rounded ${
                            result ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                          }`}>
                            {result ? 'صحيح' : 'خطأ'}
                          </span>
                          {isCorrect && (
                            <CheckCircleIcon className="w-4 h-4 text-green-600" />
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* معلومات تقنية */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <Card>
          <CardHeader>
            <CardTitle>معلومات تقنية</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-3">متطلبات المرحلة الثانية:</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-center">
                    <CheckCircleIcon className="w-4 h-4 text-green-500 ml-2" />
                    تنسيق TLV (Tag-Length-Value)
                  </li>
                  <li className="flex items-center">
                    <CheckCircleIcon className="w-4 h-4 text-green-500 ml-2" />
                    تشفير Base64
                  </li>
                  <li className="flex items-center">
                    <CheckCircleIcon className="w-4 h-4 text-green-500 ml-2" />
                    5 حقول مطلوبة (اسم البائع، الرقم الضريبي، الطابع الزمني، الإجمالي، الضريبة)
                  </li>
                  <li className="flex items-center">
                    <CheckCircleIcon className="w-4 h-4 text-green-500 ml-2" />
                    التحقق من صحة الرقم الضريبي
                  </li>
                </ul>
              </div>

              <div>
                <h4 className="font-medium text-gray-900 mb-3">تنسيق TLV:</h4>
                <div className="space-y-2 text-sm text-gray-600">
                  <div className="bg-gray-50 p-2 rounded font-mono">
                    Tag 1: اسم البائع
                  </div>
                  <div className="bg-gray-50 p-2 rounded font-mono">
                    Tag 2: الرقم الضريبي
                  </div>
                  <div className="bg-gray-50 p-2 rounded font-mono">
                    Tag 3: الطابع الزمني
                  </div>
                  <div className="bg-gray-50 p-2 rounded font-mono">
                    Tag 4: إجمالي الفاتورة
                  </div>
                  <div className="bg-gray-50 p-2 rounded font-mono">
                    Tag 5: إجمالي الضريبة
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default QRCodeTest;
