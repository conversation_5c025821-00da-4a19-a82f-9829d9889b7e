{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst TextCursorInput = createLucideIcon(\"TextCursorInput\", [[\"path\", {\n  d: \"M5 4h1a3 3 0 0 1 3 3 3 3 0 0 1 3-3h1\",\n  key: \"18xjzo\"\n}], [\"path\", {\n  d: \"M13 20h-1a3 3 0 0 1-3-3 3 3 0 0 1-3 3H5\",\n  key: \"fj48gi\"\n}], [\"path\", {\n  d: \"M5 16H4a2 2 0 0 1-2-2v-4a2 2 0 0 1 2-2h1\",\n  key: \"1n9rhb\"\n}], [\"path\", {\n  d: \"M13 8h7a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2h-7\",\n  key: \"13ksps\"\n}], [\"path\", {\n  d: \"M9 7v10\",\n  key: \"1vc8ob\"\n}]]);\nexport { TextCursorInput as default };", "map": {"version": 3, "names": ["TextCursorInput", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\node_modules\\lucide-react\\src\\icons\\text-cursor-input.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name TextCursorInput\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSA0aDFhMyAzIDAgMCAxIDMgMyAzIDMgMCAwIDEgMy0zaDEiIC8+CiAgPHBhdGggZD0iTTEzIDIwaC0xYTMgMyAwIDAgMS0zLTMgMyAzIDAgMCAxLTMgM0g1IiAvPgogIDxwYXRoIGQ9Ik01IDE2SDRhMiAyIDAgMCAxLTItMnYtNGEyIDIgMCAwIDEgMi0yaDEiIC8+CiAgPHBhdGggZD0iTTEzIDhoN2EyIDIgMCAwIDEgMiAydjRhMiAyIDAgMCAxLTIgMmgtNyIgLz4KICA8cGF0aCBkPSJNOSA3djEwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/text-cursor-input\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TextCursorInput = createLucideIcon('TextCursorInput', [\n  ['path', { d: 'M5 4h1a3 3 0 0 1 3 3 3 3 0 0 1 3-3h1', key: '18xjzo' }],\n  ['path', { d: 'M13 20h-1a3 3 0 0 1-3-3 3 3 0 0 1-3 3H5', key: 'fj48gi' }],\n  ['path', { d: 'M5 16H4a2 2 0 0 1-2-2v-4a2 2 0 0 1 2-2h1', key: '1n9rhb' }],\n  ['path', { d: 'M13 8h7a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2h-7', key: '13ksps' }],\n  ['path', { d: 'M9 7v10', key: '1vc8ob' }],\n]);\n\nexport default TextCursorInput;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,eAAA,GAAkBC,gBAAA,CAAiB,iBAAmB,GAC1D,CAAC,MAAQ;EAAEC,CAAA,EAAG,sCAAwC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrE,CAAC,MAAQ;EAAED,CAAA,EAAG,yCAA2C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxE,CAAC,MAAQ;EAAED,CAAA,EAAG,0CAA4C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzE,CAAC,MAAQ;EAAED,CAAA,EAAG,0CAA4C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzE,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,EACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}