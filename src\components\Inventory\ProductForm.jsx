import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import inventoryAPI from '../../api/inventoryAPI';
import toast from 'react-hot-toast';

const ProductForm = ({ product, isEditing, onSave, onClose }) => {
  const [formData, setFormData] = useState({
    name: '',
    category: '',
    brand: '',
    description: '',
    costPrice: 0,
    sellingPrice: 0,
    currentStock: 0,
    minStock: 0,
    maxStock: 0,
    unit: 'قطعة',
    location: '',
    supplier: '',
    barcode: ''
  });

  const [categories, setCategories] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadCategories();
    
    if (isEditing && product) {
      setFormData({
        name: product.name || '',
        category: product.category || '',
        brand: product.brand || '',
        description: product.description || '',
        costPrice: product.costPrice || 0,
        sellingPrice: product.sellingPrice || 0,
        currentStock: product.currentStock || 0,
        minStock: product.minStock || 0,
        maxStock: product.maxStock || 0,
        unit: product.unit || 'قطعة',
        location: product.location || '',
        supplier: product.supplier || '',
        barcode: product.barcode || ''
      });
    }
  }, [isEditing, product]);

  const loadCategories = () => {
    const categoriesData = inventoryAPI.getCategories();
    setCategories(categoriesData);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast.error('يرجى إدخال اسم المنتج');
      return;
    }

    if (!formData.category.trim()) {
      toast.error('يرجى اختيار فئة المنتج');
      return;
    }

    if (formData.costPrice <= 0) {
      toast.error('يرجى إدخال سعر تكلفة صحيح');
      return;
    }

    if (formData.sellingPrice <= 0) {
      toast.error('يرجى إدخال سعر بيع صحيح');
      return;
    }

    setIsLoading(true);
    try {
      await onSave(formData);
    } catch (error) {
      toast.error('حدث خطأ أثناء حفظ المنتج');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCategoryChange = (category) => {
    setFormData(prev => ({ ...prev, category }));
    
    // إضافة فئة جديدة إذا لم تكن موجودة
    if (category && !categories.includes(category)) {
      inventoryAPI.addCategory(category);
      setCategories(prev => [...prev, category]);
    }
  };

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="bg-white rounded-2xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
        >
          {/* الرأس */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">
              {isEditing ? 'تعديل المنتج' : 'منتج جديد'}
            </h2>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>

          {/* المحتوى */}
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* المعلومات الأساسية */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">المعلومات الأساسية</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                    اسم المنتج *
                  </Label>
                  <Input
                    id="name"
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="أدخل اسم المنتج"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
                    الفئة *
                  </Label>
                  <input
                    id="category"
                    list="categories"
                    value={formData.category}
                    onChange={(e) => handleCategoryChange(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    placeholder="اختر أو أدخل فئة جديدة"
                    required
                  />
                  <datalist id="categories">
                    {categories.map((category, index) => (
                      <option key={index} value={category} />
                    ))}
                  </datalist>
                </div>

                <div>
                  <Label htmlFor="brand" className="block text-sm font-medium text-gray-700 mb-2">
                    العلامة التجارية
                  </Label>
                  <Input
                    id="brand"
                    type="text"
                    value={formData.brand}
                    onChange={(e) => setFormData(prev => ({ ...prev, brand: e.target.value }))}
                    placeholder="أدخل العلامة التجارية"
                  />
                </div>

                <div>
                  <Label htmlFor="unit" className="block text-sm font-medium text-gray-700 mb-2">
                    الوحدة
                  </Label>
                  <select
                    id="unit"
                    value={formData.unit}
                    onChange={(e) => setFormData(prev => ({ ...prev, unit: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  >
                    <option value="قطعة">قطعة</option>
                    <option value="كيلو">كيلو</option>
                    <option value="لتر">لتر</option>
                    <option value="متر">متر</option>
                    <option value="علبة">علبة</option>
                    <option value="كرتون">كرتون</option>
                  </select>
                </div>
              </div>

              <div className="mt-4">
                <Label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                  الوصف
                </Label>
                <textarea
                  id="description"
                  rows={3}
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  placeholder="أدخل وصف المنتج"
                />
              </div>
            </div>

            {/* الأسعار */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">الأسعار</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="costPrice" className="block text-sm font-medium text-gray-700 mb-2">
                    سعر التكلفة *
                  </Label>
                  <Input
                    id="costPrice"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.costPrice}
                    onChange={(e) => setFormData(prev => ({ ...prev, costPrice: parseFloat(e.target.value) || 0 }))}
                    placeholder="0.00"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="sellingPrice" className="block text-sm font-medium text-gray-700 mb-2">
                    سعر البيع *
                  </Label>
                  <Input
                    id="sellingPrice"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.sellingPrice}
                    onChange={(e) => setFormData(prev => ({ ...prev, sellingPrice: parseFloat(e.target.value) || 0 }))}
                    placeholder="0.00"
                    required
                  />
                </div>
              </div>

              {formData.costPrice > 0 && formData.sellingPrice > 0 && (
                <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                  <p className="text-sm text-blue-800">
                    هامش الربح: {((formData.sellingPrice - formData.costPrice) / formData.costPrice * 100).toFixed(2)}%
                  </p>
                </div>
              )}
            </div>

            {/* المخزون */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">إدارة المخزون</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <Label htmlFor="currentStock" className="block text-sm font-medium text-gray-700 mb-2">
                    المخزون الحالي
                  </Label>
                  <Input
                    id="currentStock"
                    type="number"
                    min="0"
                    value={formData.currentStock}
                    onChange={(e) => setFormData(prev => ({ ...prev, currentStock: parseInt(e.target.value) || 0 }))}
                    placeholder="0"
                  />
                </div>

                <div>
                  <Label htmlFor="minStock" className="block text-sm font-medium text-gray-700 mb-2">
                    الحد الأدنى للمخزون
                  </Label>
                  <Input
                    id="minStock"
                    type="number"
                    min="0"
                    value={formData.minStock}
                    onChange={(e) => setFormData(prev => ({ ...prev, minStock: parseInt(e.target.value) || 0 }))}
                    placeholder="0"
                  />
                </div>

                <div>
                  <Label htmlFor="maxStock" className="block text-sm font-medium text-gray-700 mb-2">
                    الحد الأقصى للمخزون
                  </Label>
                  <Input
                    id="maxStock"
                    type="number"
                    min="0"
                    value={formData.maxStock}
                    onChange={(e) => setFormData(prev => ({ ...prev, maxStock: parseInt(e.target.value) || 0 }))}
                    placeholder="0"
                  />
                </div>
              </div>
            </div>

            {/* معلومات إضافية */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">معلومات إضافية</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <Label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-2">
                    موقع التخزين
                  </Label>
                  <Input
                    id="location"
                    type="text"
                    value={formData.location}
                    onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                    placeholder="مثال: مخزن A - رف 1"
                  />
                </div>

                <div>
                  <Label htmlFor="supplier" className="block text-sm font-medium text-gray-700 mb-2">
                    المورد
                  </Label>
                  <Input
                    id="supplier"
                    type="text"
                    value={formData.supplier}
                    onChange={(e) => setFormData(prev => ({ ...prev, supplier: e.target.value }))}
                    placeholder="اسم المورد"
                  />
                </div>

                <div>
                  <Label htmlFor="barcode" className="block text-sm font-medium text-gray-700 mb-2">
                    الباركود
                  </Label>
                  <Input
                    id="barcode"
                    type="text"
                    value={formData.barcode}
                    onChange={(e) => setFormData(prev => ({ ...prev, barcode: e.target.value }))}
                    placeholder="رقم الباركود"
                  />
                </div>
              </div>
            </div>

            {/* الأزرار */}
            <div className="flex items-center justify-end space-x-4 space-x-reverse pt-6 border-t border-gray-200">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isLoading}
              >
                إلغاء
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
                className="bg-purple-600 hover:bg-purple-700 text-white"
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ml-2"></div>
                    جاري الحفظ...
                  </div>
                ) : (
                  isEditing ? 'حفظ التعديلات' : 'حفظ المنتج'
                )}
              </Button>
            </div>
          </form>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default ProductForm;
