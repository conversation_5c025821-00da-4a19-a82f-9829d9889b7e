/**
 * API client for companies management
 * Handles all CRUD operations for companies
 */
const API_URL = process.env.REACT_APP_API_URL || 'https://your-api-domain.com/api';

/**
 * Handles API response
 * @param {Response} response - Fetch API response object
 * @returns {Promise<object>} Parsed JSON data
 * @throws {Error} When response is not OK
 */
const handleResponse = async (response) => {
  if (!response.ok) {
    const error = await response.json().catch(() => ({
      message: `HTTP error! status: ${response.status}`
    }));
    throw new Error(error.message || 'Request failed');
  }
  return response.json();
};

const companiesAPI = {
  /**
   * Fetches all companies
   * @returns {Promise<Array>} Array of company objects
   */
  getAll: async () => {
    try {
      const response = await fetch(`${API_URL}/companies`);
      return handleResponse(response);
    } catch (error) {
      console.error('Failed to fetch companies:', error);
      throw error;
    }
  },

  /**
   * Creates a new company
   * @param {object} data - Company data to create
   * @returns {Promise<object>} Created company object
   */
  create: async (data) => {
    try {
      const response = await fetch(`${API_URL}/companies`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Failed to create company:', error);
      throw error;
    }
  },

  /**
   * Updates an existing company
   * @param {string} id - Company ID to update
   * @param {object} data - Updated company data
   * @returns {Promise<object>} Updated company object
   */
  update: async (id, data) => {
    try {
      const response = await fetch(`${API_URL}/companies/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      return handleResponse(response);
    } catch (error) {
      console.error(`Failed to update company ${id}:`, error);
      throw error;
    }
  },

  /**
   * Deletes a company
   * @param {string} id - Company ID to delete
   * @returns {Promise<object>} Delete confirmation
   */
  delete: async (id) => {
    try {
      const response = await fetch(`${API_URL}/companies/${id}`, {
        method: 'DELETE',
      });
      return handleResponse(response);
    } catch (error) {
      console.error(`Failed to delete company ${id}:`, error);
      throw error;
    }
  }
};

export default companiesAPI;