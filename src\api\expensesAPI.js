// API لإدارة المصاريف
class ExpensesAPI {
  constructor() {
    this.storageKey = 'expenses_data';
    this.initializeData();
  }

  initializeData() {
    const existingData = localStorage.getItem(this.storageKey);
    if (!existingData) {
      const initialData = {
        expenses: [
          {
            id: 1,
            code: 'EXP-2024-001',
            description: 'إيجار المكتب - يناير 2024',
            category: 'إيجار',
            amount: 15000,
            date: '2024-01-01',
            paymentMethod: 'تحويل بنكي',
            vendor: 'شركة العقارات المتميزة',
            reference: 'RENT-JAN-2024',
            status: 'مدفوع',
            approvedBy: 'أحمد المدير',
            notes: 'إيجار شهري منتظم',
            receiptNumber: 'REC-001',
            taxAmount: 2250,
            isRecurring: true,
            recurringPeriod: 'شهري'
          },
          {
            id: 2,
            code: 'EXP-2024-002',
            description: 'فاتورة كهرباء - ديسمبر 2023',
            category: 'مرافق',
            amount: 3500,
            date: '2024-01-05',
            paymentMethod: 'نقدي',
            vendor: 'شركة الكهرباء الوطنية',
            reference: 'ELEC-DEC-2023',
            status: 'مدفوع',
            approvedBy: 'فاطمة المحاسبة',
            notes: 'فاتورة شهرية',
            receiptNumber: 'REC-002',
            taxAmount: 525,
            isRecurring: true,
            recurringPeriod: 'شهري'
          },
          {
            id: 3,
            code: 'EXP-2024-003',
            description: 'شراء أدوات مكتبية',
            category: 'مكتبية',
            amount: 850,
            date: '2024-01-10',
            paymentMethod: 'بطاقة ائتمان',
            vendor: 'مكتبة الأعمال',
            reference: 'OFF-SUP-001',
            status: 'معلق',
            approvedBy: '',
            notes: 'أقلام، أوراق، مجلدات',
            receiptNumber: '',
            taxAmount: 127.5,
            isRecurring: false,
            recurringPeriod: ''
          },
          {
            id: 4,
            code: 'EXP-2024-004',
            description: 'صيانة أجهزة الكمبيوتر',
            category: 'صيانة',
            amount: 2200,
            date: '2024-01-12',
            paymentMethod: 'نقدي',
            vendor: 'مركز الصيانة التقني',
            reference: 'MAINT-001',
            status: 'مدفوع',
            approvedBy: 'خالد التقني',
            notes: 'صيانة دورية للأجهزة',
            receiptNumber: 'REC-003',
            taxAmount: 330,
            isRecurring: false,
            recurringPeriod: ''
          }
        ],
        categories: ['إيجار', 'مرافق', 'مكتبية', 'صيانة', 'رواتب', 'تسويق', 'سفر', 'اتصالات', 'تأمين', 'أخرى'],
        lastId: 4
      };
      localStorage.setItem(this.storageKey, JSON.stringify(initialData));
    }
  }

  getData() {
    return JSON.parse(localStorage.getItem(this.storageKey));
  }

  saveData(data) {
    localStorage.setItem(this.storageKey, JSON.stringify(data));
  }

  // الحصول على جميع المصاريف
  getAllExpenses() {
    return this.getData().expenses;
  }

  // إضافة مصروف جديد
  addExpense(expenseData) {
    const data = this.getData();
    const newExpense = {
      id: data.lastId + 1,
      code: `EXP-${new Date().getFullYear()}-${String(data.lastId + 1).padStart(3, '0')}`,
      date: new Date().toISOString().split('T')[0],
      status: 'معلق',
      isRecurring: false,
      ...expenseData
    };
    
    data.expenses.push(newExpense);
    data.lastId += 1;
    this.saveData(data);
    return newExpense;
  }

  // تحديث مصروف
  updateExpense(id, expenseData) {
    const data = this.getData();
    const index = data.expenses.findIndex(expense => expense.id === id);
    if (index !== -1) {
      data.expenses[index] = { ...data.expenses[index], ...expenseData };
      this.saveData(data);
      return data.expenses[index];
    }
    return null;
  }

  // حذف مصروف
  deleteExpense(id) {
    const data = this.getData();
    data.expenses = data.expenses.filter(expense => expense.id !== id);
    this.saveData(data);
    return true;
  }

  // الحصول على مصروف بالمعرف
  getExpenseById(id) {
    const data = this.getData();
    return data.expenses.find(expense => expense.id === id);
  }

  // إحصائيات المصاريف
  getExpensesStats() {
    const expenses = this.getAllExpenses();
    const today = new Date().toISOString().split('T')[0];
    const thisMonth = new Date().toISOString().slice(0, 7);
    const thisYear = new Date().getFullYear().toString();
    
    return {
      totalExpenses: expenses.reduce((sum, expense) => sum + expense.amount, 0),
      todayExpenses: expenses.filter(expense => expense.date === today).reduce((sum, expense) => sum + expense.amount, 0),
      monthExpenses: expenses.filter(expense => expense.date.startsWith(thisMonth)).reduce((sum, expense) => sum + expense.amount, 0),
      yearExpenses: expenses.filter(expense => expense.date.startsWith(thisYear)).reduce((sum, expense) => sum + expense.amount, 0),
      totalCount: expenses.length,
      paidExpenses: expenses.filter(expense => expense.status === 'مدفوع').length,
      pendingExpenses: expenses.filter(expense => expense.status === 'معلق').length,
      recurringExpenses: expenses.filter(expense => expense.isRecurring).length
    };
  }

  // المصاريف حسب الفئة
  getExpensesByCategory() {
    const expenses = this.getAllExpenses();
    const categories = {};
    
    expenses.forEach(expense => {
      if (!categories[expense.category]) {
        categories[expense.category] = {
          total: 0,
          count: 0,
          expenses: []
        };
      }
      categories[expense.category].total += expense.amount;
      categories[expense.category].count += 1;
      categories[expense.category].expenses.push(expense);
    });
    
    return categories;
  }

  // المصاريف المعلقة
  getPendingExpenses() {
    const expenses = this.getAllExpenses();
    return expenses.filter(expense => expense.status === 'معلق');
  }

  // المصاريف المتكررة
  getRecurringExpenses() {
    const expenses = this.getAllExpenses();
    return expenses.filter(expense => expense.isRecurring);
  }

  // البحث في المصاريف
  searchExpenses(query) {
    const expenses = this.getAllExpenses();
    return expenses.filter(expense => 
      expense.description.toLowerCase().includes(query.toLowerCase()) ||
      expense.code.toLowerCase().includes(query.toLowerCase()) ||
      expense.category.toLowerCase().includes(query.toLowerCase()) ||
      expense.vendor.toLowerCase().includes(query.toLowerCase())
    );
  }

  // الحصول على الفئات
  getCategories() {
    return this.getData().categories;
  }

  // إضافة فئة جديدة
  addCategory(category) {
    const data = this.getData();
    if (!data.categories.includes(category)) {
      data.categories.push(category);
      this.saveData(data);
    }
    return data.categories;
  }

  // تقرير المصاريف الشهرية
  getMonthlyExpensesReport(year) {
    const expenses = this.getAllExpenses();
    const monthlyData = {};
    
    for (let month = 1; month <= 12; month++) {
      const monthStr = `${year}-${String(month).padStart(2, '0')}`;
      monthlyData[monthStr] = {
        total: 0,
        count: 0,
        categories: {}
      };
    }
    
    expenses.forEach(expense => {
      const expenseMonth = expense.date.slice(0, 7);
      if (expenseMonth.startsWith(year)) {
        monthlyData[expenseMonth].total += expense.amount;
        monthlyData[expenseMonth].count += 1;
        
        if (!monthlyData[expenseMonth].categories[expense.category]) {
          monthlyData[expenseMonth].categories[expense.category] = 0;
        }
        monthlyData[expenseMonth].categories[expense.category] += expense.amount;
      }
    });
    
    return monthlyData;
  }
}

export default new ExpensesAPI();
