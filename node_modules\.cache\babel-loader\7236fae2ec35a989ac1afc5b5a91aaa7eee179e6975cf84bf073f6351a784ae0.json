{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst FolderCog = createLucideIcon(\"FolderCog\", [[\"circle\", {\n  cx: \"18\",\n  cy: \"18\",\n  r: \"3\",\n  key: \"1xkwt0\"\n}], [\"path\", {\n  d: \"M10.3 20H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H20a2 2 0 0 1 2 2v3.3\",\n  key: \"1k8050\"\n}], [\"path\", {\n  d: \"m21.7 19.4-.9-.3\",\n  key: \"1qgwi9\"\n}], [\"path\", {\n  d: \"m15.2 16.9-.9-.3\",\n  key: \"1t7mvx\"\n}], [\"path\", {\n  d: \"m16.6 21.7.3-.9\",\n  key: \"1j67ps\"\n}], [\"path\", {\n  d: \"m19.1 15.2.3-.9\",\n  key: \"18r7jp\"\n}], [\"path\", {\n  d: \"m19.6 21.7-.4-1\",\n  key: \"z2vh2\"\n}], [\"path\", {\n  d: \"m16.8 15.3-.4-1\",\n  key: \"1ei7r6\"\n}], [\"path\", {\n  d: \"m14.3 19.6 1-.4\",\n  key: \"11sv9r\"\n}], [\"path\", {\n  d: \"m20.7 16.8 1-.4\",\n  key: \"19m87a\"\n}]]);\nexport { FolderCog as default };", "map": {"version": 3, "names": ["FolderCog", "createLucideIcon", "cx", "cy", "r", "key", "d"], "sources": ["C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\node_modules\\lucide-react\\src\\icons\\folder-cog.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name FolderCog\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxOCIgY3k9IjE4IiByPSIzIiAvPgogIDxwYXRoIGQ9Ik0xMC4zIDIwSDRhMiAyIDAgMCAxLTItMlY1YTIgMiAwIDAgMSAyLTJoMy45YTIgMiAwIDAgMSAxLjY5LjlsLjgxIDEuMmEyIDIgMCAwIDAgMS42Ny45SDIwYTIgMiAwIDAgMSAyIDJ2My4zIiAvPgogIDxwYXRoIGQ9Im0yMS43IDE5LjQtLjktLjMiIC8+CiAgPHBhdGggZD0ibTE1LjIgMTYuOS0uOS0uMyIgLz4KICA8cGF0aCBkPSJtMTYuNiAyMS43LjMtLjkiIC8+CiAgPHBhdGggZD0ibTE5LjEgMTUuMi4zLS45IiAvPgogIDxwYXRoIGQ9Im0xOS42IDIxLjctLjQtMSIgLz4KICA8cGF0aCBkPSJtMTYuOCAxNS4zLS40LTEiIC8+CiAgPHBhdGggZD0ibTE0LjMgMTkuNiAxLS40IiAvPgogIDxwYXRoIGQ9Im0yMC43IDE2LjggMS0uNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/folder-cog\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FolderCog = createLucideIcon('FolderCog', [\n  ['circle', { cx: '18', cy: '18', r: '3', key: '1xkwt0' }],\n  [\n    'path',\n    {\n      d: 'M10.3 20H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H20a2 2 0 0 1 2 2v3.3',\n      key: '1k8050',\n    },\n  ],\n  ['path', { d: 'm21.7 19.4-.9-.3', key: '1qgwi9' }],\n  ['path', { d: 'm15.2 16.9-.9-.3', key: '1t7mvx' }],\n  ['path', { d: 'm16.6 21.7.3-.9', key: '1j67ps' }],\n  ['path', { d: 'm19.1 15.2.3-.9', key: '18r7jp' }],\n  ['path', { d: 'm19.6 21.7-.4-1', key: 'z2vh2' }],\n  ['path', { d: 'm16.8 15.3-.4-1', key: '1ei7r6' }],\n  ['path', { d: 'm14.3 19.6 1-.4', key: '11sv9r' }],\n  ['path', { d: 'm20.7 16.8 1-.4', key: '19m87a' }],\n]);\n\nexport default FolderCog;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACxD,CACE,QACA;EACEC,CAAG;EACHD,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAEC,CAAA,EAAG,kBAAoB;EAAAD,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAEC,CAAA,EAAG,kBAAoB;EAAAD,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAEC,CAAA,EAAG,iBAAmB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAEC,CAAA,EAAG,iBAAmB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAEC,CAAA,EAAG,iBAAmB;EAAAD,GAAA,EAAK;AAAA,CAAS,GAC/C,CAAC,MAAQ;EAAEC,CAAA,EAAG,iBAAmB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAEC,CAAA,EAAG,iBAAmB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAEC,CAAA,EAAG,iBAAmB;EAAAD,GAAA,EAAK;AAAA,CAAU,EACjD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}