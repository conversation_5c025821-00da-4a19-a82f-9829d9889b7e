import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  DocumentArrowDownIcon,
  ChartBarIcon,
  CalendarIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import ReportsChart from '../components/Reports/ReportsChart';
import ExportOptions from '../components/Reports/ExportOptions';
import ReportFilters from '../components/Reports/ReportFilters';

const Reports = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('month');
  const [showExportModal, setShowExportModal] = useState(false);

  const reportStats = [
    {
      title: 'إجمالي الشركات',
      value: '156',
      period: 'هذا الشهر',
      change: '+12%',
      color: 'blue'
    },
    {
      title: 'الشركات الجديدة',
      value: '24',
      period: 'هذا الشهر',
      change: '+18%',
      color: 'green'
    },
    {
      title: 'الشركات المحدثة',
      value: '45',
      period: 'هذا الشهر',
      change: '+8%',
      color: 'purple'
    },
    {
      title: 'التقارير المصدرة',
      value: '89',
      period: 'هذا الشهر',
      change: '+25%',
      color: 'orange'
    }
  ];

  return (
    <div className="space-y-6">
      {/* العنوان والفلاتر */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4"
      >
        <div>
          <h1 className="text-3xl font-bold text-gray-900">التقارير والإحصائيات</h1>
          <p className="text-gray-600 mt-2">تحليل شامل لبيانات الشركات والأنشطة</p>
        </div>
        
        <div className="flex items-center space-x-4 space-x-reverse">
          <Button
            onClick={() => setShowExportModal(true)}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            <DocumentArrowDownIcon className="w-4 h-4 ml-2" />
            تصدير التقرير
          </Button>
          
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="week">هذا الأسبوع</option>
            <option value="month">هذا الشهر</option>
            <option value="quarter">هذا الربع</option>
            <option value="year">هذا العام</option>
          </select>
        </div>
      </motion.div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {reportStats.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900 mt-1">{stat.value}</p>
                    <p className="text-xs text-gray-500 mt-1">{stat.period}</p>
                  </div>
                  <div className={`text-sm font-medium ${
                    stat.change.startsWith('+') ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {stat.change}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* الفلاتر */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <ReportFilters />
      </motion.div>

      {/* الرسوم البيانية */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.5 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <ChartBarIcon className="w-5 h-5 ml-2 text-blue-600" />
                نمو الشركات الشهري
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ReportsChart type="line" />
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.6 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <ChartBarIcon className="w-5 h-5 ml-2 text-green-600" />
                توزيع الشركات حسب النوع
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ReportsChart type="pie" />
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* جدول التقارير التفصيلية */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.7 }}
      >
        <Card>
          <CardHeader>
            <CardTitle>التقارير التفصيلية</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-right py-3 px-4 font-medium text-gray-900">اسم التقرير</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-900">التاريخ</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-900">النوع</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-900">الحالة</th>
                    <th className="text-center py-3 px-4 font-medium text-gray-900">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {[
                    { name: 'تقرير الشركات الشهري', date: '2024-01-15', type: 'شهري', status: 'مكتمل' },
                    { name: 'تقرير الأنشطة الأسبوعي', date: '2024-01-10', type: 'أسبوعي', status: 'قيد المعالجة' },
                    { name: 'تقرير الإحصائيات السنوي', date: '2024-01-01', type: 'سنوي', status: 'مكتمل' },
                  ].map((report, index) => (
                    <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-4 text-gray-900">{report.name}</td>
                      <td className="py-3 px-4 text-gray-600">{report.date}</td>
                      <td className="py-3 px-4">
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                          {report.type}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          report.status === 'مكتمل' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {report.status}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-center">
                        <Button size="sm" variant="outline">
                          تحميل
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* مودال التصدير */}
      {showExportModal && (
        <ExportOptions onClose={() => setShowExportModal(false)} />
      )}
    </div>
  );
};

export default Reports;
