{"ast": null, "code": "// API لإدارة المخزون\nclass InventoryAPI {\n  constructor() {\n    this.storageKey = 'inventory_data';\n    this.initializeData();\n  }\n  initializeData() {\n    const existingData = localStorage.getItem(this.storageKey);\n    if (!existingData) {\n      const initialData = {\n        products: [{\n          id: 1,\n          code: 'PROD-001',\n          name: 'لابتوب Dell Inspiron 15',\n          category: 'أجهزة كمبيوتر',\n          brand: 'Dell',\n          description: 'لابتوب Dell Inspiron 15 - معالج Intel Core i5',\n          costPrice: 3000,\n          sellingPrice: 3500,\n          currentStock: 25,\n          minStock: 5,\n          maxStock: 100,\n          unit: 'قطعة',\n          location: 'مخزن A - رف 1',\n          supplier: 'شركة التقنية المتقدمة',\n          barcode: '1234567890123',\n          status: 'نشط',\n          lastUpdated: '2024-01-15',\n          movements: [{\n            date: '2024-01-10',\n            type: 'شراء',\n            quantity: 10,\n            reference: 'PUR-2024-001'\n          }, {\n            date: '2024-01-15',\n            type: 'بيع',\n            quantity: -2,\n            reference: 'INV-2024-001'\n          }]\n        }, {\n          id: 2,\n          code: 'PROD-002',\n          name: 'ماوس لاسلكي Logitech',\n          category: 'ملحقات',\n          brand: 'Logitech',\n          description: 'ماوس لاسلكي بتقنية البلوتوث',\n          costPrice: 100,\n          sellingPrice: 150,\n          currentStock: 45,\n          minStock: 10,\n          maxStock: 200,\n          unit: 'قطعة',\n          location: 'مخزن B - رف 2',\n          supplier: 'شركة التقنية المتقدمة',\n          barcode: '1234567890124',\n          status: 'نشط',\n          lastUpdated: '2024-01-15',\n          movements: [{\n            date: '2024-01-10',\n            type: 'شراء',\n            quantity: 50,\n            reference: 'PUR-2024-001'\n          }, {\n            date: '2024-01-15',\n            type: 'بيع',\n            quantity: -5,\n            reference: 'INV-2024-001'\n          }]\n        }, {\n          id: 3,\n          code: 'PROD-003',\n          name: 'طابعة HP LaserJet',\n          category: 'طابعات',\n          brand: 'HP',\n          description: 'طابعة ليزر أحادية اللون',\n          costPrice: 600,\n          sellingPrice: 800,\n          currentStock: 4,\n          minStock: 2,\n          maxStock: 20,\n          unit: 'قطعة',\n          location: 'مخزن A - رف 3',\n          supplier: 'مؤسسة الإلكترونيات',\n          barcode: '1234567890125',\n          status: 'نشط',\n          lastUpdated: '2024-01-16',\n          movements: [{\n            date: '2024-01-12',\n            type: 'شراء',\n            quantity: 5,\n            reference: 'PUR-2024-002'\n          }, {\n            date: '2024-01-16',\n            type: 'بيع',\n            quantity: -1,\n            reference: 'INV-2024-002'\n          }]\n        }],\n        categories: ['أجهزة كمبيوتر', 'ملحقات', 'طابعات', 'شاشات', 'أجهزة شبكات'],\n        lastId: 3\n      };\n      localStorage.setItem(this.storageKey, JSON.stringify(initialData));\n    }\n  }\n  getData() {\n    return JSON.parse(localStorage.getItem(this.storageKey));\n  }\n  saveData(data) {\n    localStorage.setItem(this.storageKey, JSON.stringify(data));\n  }\n\n  // الحصول على جميع المنتجات\n  getAllProducts() {\n    return this.getData().products;\n  }\n\n  // إضافة منتج جديد\n  addProduct(productData) {\n    const data = this.getData();\n    const newProduct = {\n      id: data.lastId + 1,\n      code: `PROD-${String(data.lastId + 1).padStart(3, '0')}`,\n      status: 'نشط',\n      lastUpdated: new Date().toISOString().split('T')[0],\n      movements: [],\n      ...productData\n    };\n    data.products.push(newProduct);\n    data.lastId += 1;\n    this.saveData(data);\n    return newProduct;\n  }\n\n  // تحديث منتج\n  updateProduct(id, productData) {\n    const data = this.getData();\n    const index = data.products.findIndex(product => product.id === id);\n    if (index !== -1) {\n      data.products[index] = {\n        ...data.products[index],\n        ...productData,\n        lastUpdated: new Date().toISOString().split('T')[0]\n      };\n      this.saveData(data);\n      return data.products[index];\n    }\n    return null;\n  }\n\n  // حذف منتج\n  deleteProduct(id) {\n    const data = this.getData();\n    data.products = data.products.filter(product => product.id !== id);\n    this.saveData(data);\n    return true;\n  }\n\n  // الحصول على منتج بالمعرف\n  getProductById(id) {\n    const data = this.getData();\n    return data.products.find(product => product.id === id);\n  }\n\n  // تحديث المخزون\n  updateStock(productId, quantity, type, reference) {\n    const data = this.getData();\n    const product = data.products.find(p => p.id === productId);\n    if (product) {\n      product.currentStock += quantity;\n      product.movements.push({\n        date: new Date().toISOString().split('T')[0],\n        type,\n        quantity,\n        reference\n      });\n      product.lastUpdated = new Date().toISOString().split('T')[0];\n      this.saveData(data);\n      return product;\n    }\n    return null;\n  }\n\n  // إحصائيات المخزون\n  getInventoryStats() {\n    const products = this.getAllProducts();\n    return {\n      totalProducts: products.length,\n      totalValue: products.reduce((sum, product) => sum + product.currentStock * product.costPrice, 0),\n      lowStockProducts: products.filter(product => product.currentStock <= product.minStock).length,\n      outOfStockProducts: products.filter(product => product.currentStock === 0).length,\n      activeProducts: products.filter(product => product.status === 'نشط').length,\n      totalCategories: [...new Set(products.map(product => product.category))].length\n    };\n  }\n\n  // المنتجات منخفضة المخزون\n  getLowStockProducts() {\n    const products = this.getAllProducts();\n    return products.filter(product => product.currentStock <= product.minStock);\n  }\n\n  // المنتجات نفدت من المخزون\n  getOutOfStockProducts() {\n    const products = this.getAllProducts();\n    return products.filter(product => product.currentStock === 0);\n  }\n\n  // البحث في المنتجات\n  searchProducts(query) {\n    const products = this.getAllProducts();\n    return products.filter(product => product.name.toLowerCase().includes(query.toLowerCase()) || product.code.toLowerCase().includes(query.toLowerCase()) || product.category.toLowerCase().includes(query.toLowerCase()) || product.brand.toLowerCase().includes(query.toLowerCase()));\n  }\n\n  // الحصول على الفئات\n  getCategories() {\n    return this.getData().categories;\n  }\n\n  // إضافة فئة جديدة\n  addCategory(category) {\n    const data = this.getData();\n    if (!data.categories.includes(category)) {\n      data.categories.push(category);\n      this.saveData(data);\n    }\n    return data.categories;\n  }\n}\nexport default new InventoryAPI();", "map": {"version": 3, "names": ["InventoryAPI", "constructor", "storageKey", "initializeData", "existingData", "localStorage", "getItem", "initialData", "products", "id", "code", "name", "category", "brand", "description", "costPrice", "sellingPrice", "currentStock", "minStock", "maxStock", "unit", "location", "supplier", "barcode", "status", "lastUpdated", "movements", "date", "type", "quantity", "reference", "categories", "lastId", "setItem", "JSON", "stringify", "getData", "parse", "saveData", "data", "getAllProducts", "addProduct", "productData", "newProduct", "String", "padStart", "Date", "toISOString", "split", "push", "updateProduct", "index", "findIndex", "product", "deleteProduct", "filter", "getProductById", "find", "updateStock", "productId", "p", "getInventoryStats", "totalProducts", "length", "totalValue", "reduce", "sum", "lowStockProducts", "outOfStockProducts", "activeProducts", "totalCategories", "Set", "map", "getLowStockProducts", "getOutOfStockProducts", "searchProducts", "query", "toLowerCase", "includes", "getCategories", "addCategory"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/src/api/inventoryAPI.js"], "sourcesContent": ["// API لإدارة المخزون\nclass InventoryAPI {\n  constructor() {\n    this.storageKey = 'inventory_data';\n    this.initializeData();\n  }\n\n  initializeData() {\n    const existingData = localStorage.getItem(this.storageKey);\n    if (!existingData) {\n      const initialData = {\n        products: [\n          {\n            id: 1,\n            code: 'PROD-001',\n            name: 'لابتوب Dell Inspiron 15',\n            category: 'أجهزة كمبيوتر',\n            brand: 'Dell',\n            description: 'لابتوب Dell Inspiron 15 - معالج Intel Core i5',\n            costPrice: 3000,\n            sellingPrice: 3500,\n            currentStock: 25,\n            minStock: 5,\n            maxStock: 100,\n            unit: 'قطعة',\n            location: 'مخزن A - رف 1',\n            supplier: 'شركة التقنية المتقدمة',\n            barcode: '1234567890123',\n            status: 'نشط',\n            lastUpdated: '2024-01-15',\n            movements: [\n              { date: '2024-01-10', type: 'شراء', quantity: 10, reference: 'PUR-2024-001' },\n              { date: '2024-01-15', type: 'بيع', quantity: -2, reference: 'INV-2024-001' }\n            ]\n          },\n          {\n            id: 2,\n            code: 'PROD-002',\n            name: 'ماوس لاسلكي Logitech',\n            category: 'ملحقات',\n            brand: 'Logitech',\n            description: 'ماوس لاسلكي بتقنية البلوتوث',\n            costPrice: 100,\n            sellingPrice: 150,\n            currentStock: 45,\n            minStock: 10,\n            maxStock: 200,\n            unit: 'قطعة',\n            location: 'مخزن B - رف 2',\n            supplier: 'شركة التقنية المتقدمة',\n            barcode: '1234567890124',\n            status: 'نشط',\n            lastUpdated: '2024-01-15',\n            movements: [\n              { date: '2024-01-10', type: 'شراء', quantity: 50, reference: 'PUR-2024-001' },\n              { date: '2024-01-15', type: 'بيع', quantity: -5, reference: 'INV-2024-001' }\n            ]\n          },\n          {\n            id: 3,\n            code: 'PROD-003',\n            name: 'طابعة HP LaserJet',\n            category: 'طابعات',\n            brand: 'HP',\n            description: 'طابعة ليزر أحادية اللون',\n            costPrice: 600,\n            sellingPrice: 800,\n            currentStock: 4,\n            minStock: 2,\n            maxStock: 20,\n            unit: 'قطعة',\n            location: 'مخزن A - رف 3',\n            supplier: 'مؤسسة الإلكترونيات',\n            barcode: '1234567890125',\n            status: 'نشط',\n            lastUpdated: '2024-01-16',\n            movements: [\n              { date: '2024-01-12', type: 'شراء', quantity: 5, reference: 'PUR-2024-002' },\n              { date: '2024-01-16', type: 'بيع', quantity: -1, reference: 'INV-2024-002' }\n            ]\n          }\n        ],\n        categories: ['أجهزة كمبيوتر', 'ملحقات', 'طابعات', 'شاشات', 'أجهزة شبكات'],\n        lastId: 3\n      };\n      localStorage.setItem(this.storageKey, JSON.stringify(initialData));\n    }\n  }\n\n  getData() {\n    return JSON.parse(localStorage.getItem(this.storageKey));\n  }\n\n  saveData(data) {\n    localStorage.setItem(this.storageKey, JSON.stringify(data));\n  }\n\n  // الحصول على جميع المنتجات\n  getAllProducts() {\n    return this.getData().products;\n  }\n\n  // إضافة منتج جديد\n  addProduct(productData) {\n    const data = this.getData();\n    const newProduct = {\n      id: data.lastId + 1,\n      code: `PROD-${String(data.lastId + 1).padStart(3, '0')}`,\n      status: 'نشط',\n      lastUpdated: new Date().toISOString().split('T')[0],\n      movements: [],\n      ...productData\n    };\n    \n    data.products.push(newProduct);\n    data.lastId += 1;\n    this.saveData(data);\n    return newProduct;\n  }\n\n  // تحديث منتج\n  updateProduct(id, productData) {\n    const data = this.getData();\n    const index = data.products.findIndex(product => product.id === id);\n    if (index !== -1) {\n      data.products[index] = { \n        ...data.products[index], \n        ...productData,\n        lastUpdated: new Date().toISOString().split('T')[0]\n      };\n      this.saveData(data);\n      return data.products[index];\n    }\n    return null;\n  }\n\n  // حذف منتج\n  deleteProduct(id) {\n    const data = this.getData();\n    data.products = data.products.filter(product => product.id !== id);\n    this.saveData(data);\n    return true;\n  }\n\n  // الحصول على منتج بالمعرف\n  getProductById(id) {\n    const data = this.getData();\n    return data.products.find(product => product.id === id);\n  }\n\n  // تحديث المخزون\n  updateStock(productId, quantity, type, reference) {\n    const data = this.getData();\n    const product = data.products.find(p => p.id === productId);\n    if (product) {\n      product.currentStock += quantity;\n      product.movements.push({\n        date: new Date().toISOString().split('T')[0],\n        type,\n        quantity,\n        reference\n      });\n      product.lastUpdated = new Date().toISOString().split('T')[0];\n      this.saveData(data);\n      return product;\n    }\n    return null;\n  }\n\n  // إحصائيات المخزون\n  getInventoryStats() {\n    const products = this.getAllProducts();\n    \n    return {\n      totalProducts: products.length,\n      totalValue: products.reduce((sum, product) => sum + (product.currentStock * product.costPrice), 0),\n      lowStockProducts: products.filter(product => product.currentStock <= product.minStock).length,\n      outOfStockProducts: products.filter(product => product.currentStock === 0).length,\n      activeProducts: products.filter(product => product.status === 'نشط').length,\n      totalCategories: [...new Set(products.map(product => product.category))].length\n    };\n  }\n\n  // المنتجات منخفضة المخزون\n  getLowStockProducts() {\n    const products = this.getAllProducts();\n    return products.filter(product => product.currentStock <= product.minStock);\n  }\n\n  // المنتجات نفدت من المخزون\n  getOutOfStockProducts() {\n    const products = this.getAllProducts();\n    return products.filter(product => product.currentStock === 0);\n  }\n\n  // البحث في المنتجات\n  searchProducts(query) {\n    const products = this.getAllProducts();\n    return products.filter(product => \n      product.name.toLowerCase().includes(query.toLowerCase()) ||\n      product.code.toLowerCase().includes(query.toLowerCase()) ||\n      product.category.toLowerCase().includes(query.toLowerCase()) ||\n      product.brand.toLowerCase().includes(query.toLowerCase())\n    );\n  }\n\n  // الحصول على الفئات\n  getCategories() {\n    return this.getData().categories;\n  }\n\n  // إضافة فئة جديدة\n  addCategory(category) {\n    const data = this.getData();\n    if (!data.categories.includes(category)) {\n      data.categories.push(category);\n      this.saveData(data);\n    }\n    return data.categories;\n  }\n}\n\nexport default new InventoryAPI();\n"], "mappings": "AAAA;AACA,MAAMA,YAAY,CAAC;EACjBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,UAAU,GAAG,gBAAgB;IAClC,IAAI,CAACC,cAAc,CAAC,CAAC;EACvB;EAEAA,cAAcA,CAAA,EAAG;IACf,MAAMC,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,IAAI,CAACJ,UAAU,CAAC;IAC1D,IAAI,CAACE,YAAY,EAAE;MACjB,MAAMG,WAAW,GAAG;QAClBC,QAAQ,EAAE,CACR;UACEC,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,UAAU;UAChBC,IAAI,EAAE,yBAAyB;UAC/BC,QAAQ,EAAE,eAAe;UACzBC,KAAK,EAAE,MAAM;UACbC,WAAW,EAAE,+CAA+C;UAC5DC,SAAS,EAAE,IAAI;UACfC,YAAY,EAAE,IAAI;UAClBC,YAAY,EAAE,EAAE;UAChBC,QAAQ,EAAE,CAAC;UACXC,QAAQ,EAAE,GAAG;UACbC,IAAI,EAAE,MAAM;UACZC,QAAQ,EAAE,eAAe;UACzBC,QAAQ,EAAE,uBAAuB;UACjCC,OAAO,EAAE,eAAe;UACxBC,MAAM,EAAE,KAAK;UACbC,WAAW,EAAE,YAAY;UACzBC,SAAS,EAAE,CACT;YAAEC,IAAI,EAAE,YAAY;YAAEC,IAAI,EAAE,MAAM;YAAEC,QAAQ,EAAE,EAAE;YAAEC,SAAS,EAAE;UAAe,CAAC,EAC7E;YAAEH,IAAI,EAAE,YAAY;YAAEC,IAAI,EAAE,KAAK;YAAEC,QAAQ,EAAE,CAAC,CAAC;YAAEC,SAAS,EAAE;UAAe,CAAC;QAEhF,CAAC,EACD;UACErB,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,UAAU;UAChBC,IAAI,EAAE,sBAAsB;UAC5BC,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE,UAAU;UACjBC,WAAW,EAAE,6BAA6B;UAC1CC,SAAS,EAAE,GAAG;UACdC,YAAY,EAAE,GAAG;UACjBC,YAAY,EAAE,EAAE;UAChBC,QAAQ,EAAE,EAAE;UACZC,QAAQ,EAAE,GAAG;UACbC,IAAI,EAAE,MAAM;UACZC,QAAQ,EAAE,eAAe;UACzBC,QAAQ,EAAE,uBAAuB;UACjCC,OAAO,EAAE,eAAe;UACxBC,MAAM,EAAE,KAAK;UACbC,WAAW,EAAE,YAAY;UACzBC,SAAS,EAAE,CACT;YAAEC,IAAI,EAAE,YAAY;YAAEC,IAAI,EAAE,MAAM;YAAEC,QAAQ,EAAE,EAAE;YAAEC,SAAS,EAAE;UAAe,CAAC,EAC7E;YAAEH,IAAI,EAAE,YAAY;YAAEC,IAAI,EAAE,KAAK;YAAEC,QAAQ,EAAE,CAAC,CAAC;YAAEC,SAAS,EAAE;UAAe,CAAC;QAEhF,CAAC,EACD;UACErB,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,UAAU;UAChBC,IAAI,EAAE,mBAAmB;UACzBC,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE,IAAI;UACXC,WAAW,EAAE,yBAAyB;UACtCC,SAAS,EAAE,GAAG;UACdC,YAAY,EAAE,GAAG;UACjBC,YAAY,EAAE,CAAC;UACfC,QAAQ,EAAE,CAAC;UACXC,QAAQ,EAAE,EAAE;UACZC,IAAI,EAAE,MAAM;UACZC,QAAQ,EAAE,eAAe;UACzBC,QAAQ,EAAE,oBAAoB;UAC9BC,OAAO,EAAE,eAAe;UACxBC,MAAM,EAAE,KAAK;UACbC,WAAW,EAAE,YAAY;UACzBC,SAAS,EAAE,CACT;YAAEC,IAAI,EAAE,YAAY;YAAEC,IAAI,EAAE,MAAM;YAAEC,QAAQ,EAAE,CAAC;YAAEC,SAAS,EAAE;UAAe,CAAC,EAC5E;YAAEH,IAAI,EAAE,YAAY;YAAEC,IAAI,EAAE,KAAK;YAAEC,QAAQ,EAAE,CAAC,CAAC;YAAEC,SAAS,EAAE;UAAe,CAAC;QAEhF,CAAC,CACF;QACDC,UAAU,EAAE,CAAC,eAAe,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,aAAa,CAAC;QACzEC,MAAM,EAAE;MACV,CAAC;MACD3B,YAAY,CAAC4B,OAAO,CAAC,IAAI,CAAC/B,UAAU,EAAEgC,IAAI,CAACC,SAAS,CAAC5B,WAAW,CAAC,CAAC;IACpE;EACF;EAEA6B,OAAOA,CAAA,EAAG;IACR,OAAOF,IAAI,CAACG,KAAK,CAAChC,YAAY,CAACC,OAAO,CAAC,IAAI,CAACJ,UAAU,CAAC,CAAC;EAC1D;EAEAoC,QAAQA,CAACC,IAAI,EAAE;IACblC,YAAY,CAAC4B,OAAO,CAAC,IAAI,CAAC/B,UAAU,EAAEgC,IAAI,CAACC,SAAS,CAACI,IAAI,CAAC,CAAC;EAC7D;;EAEA;EACAC,cAAcA,CAAA,EAAG;IACf,OAAO,IAAI,CAACJ,OAAO,CAAC,CAAC,CAAC5B,QAAQ;EAChC;;EAEA;EACAiC,UAAUA,CAACC,WAAW,EAAE;IACtB,MAAMH,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC,CAAC;IAC3B,MAAMO,UAAU,GAAG;MACjBlC,EAAE,EAAE8B,IAAI,CAACP,MAAM,GAAG,CAAC;MACnBtB,IAAI,EAAE,QAAQkC,MAAM,CAACL,IAAI,CAACP,MAAM,GAAG,CAAC,CAAC,CAACa,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;MACxDrB,MAAM,EAAE,KAAK;MACbC,WAAW,EAAE,IAAIqB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACnDtB,SAAS,EAAE,EAAE;MACb,GAAGgB;IACL,CAAC;IAEDH,IAAI,CAAC/B,QAAQ,CAACyC,IAAI,CAACN,UAAU,CAAC;IAC9BJ,IAAI,CAACP,MAAM,IAAI,CAAC;IAChB,IAAI,CAACM,QAAQ,CAACC,IAAI,CAAC;IACnB,OAAOI,UAAU;EACnB;;EAEA;EACAO,aAAaA,CAACzC,EAAE,EAAEiC,WAAW,EAAE;IAC7B,MAAMH,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC,CAAC;IAC3B,MAAMe,KAAK,GAAGZ,IAAI,CAAC/B,QAAQ,CAAC4C,SAAS,CAACC,OAAO,IAAIA,OAAO,CAAC5C,EAAE,KAAKA,EAAE,CAAC;IACnE,IAAI0C,KAAK,KAAK,CAAC,CAAC,EAAE;MAChBZ,IAAI,CAAC/B,QAAQ,CAAC2C,KAAK,CAAC,GAAG;QACrB,GAAGZ,IAAI,CAAC/B,QAAQ,CAAC2C,KAAK,CAAC;QACvB,GAAGT,WAAW;QACdjB,WAAW,EAAE,IAAIqB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;MACpD,CAAC;MACD,IAAI,CAACV,QAAQ,CAACC,IAAI,CAAC;MACnB,OAAOA,IAAI,CAAC/B,QAAQ,CAAC2C,KAAK,CAAC;IAC7B;IACA,OAAO,IAAI;EACb;;EAEA;EACAG,aAAaA,CAAC7C,EAAE,EAAE;IAChB,MAAM8B,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC,CAAC;IAC3BG,IAAI,CAAC/B,QAAQ,GAAG+B,IAAI,CAAC/B,QAAQ,CAAC+C,MAAM,CAACF,OAAO,IAAIA,OAAO,CAAC5C,EAAE,KAAKA,EAAE,CAAC;IAClE,IAAI,CAAC6B,QAAQ,CAACC,IAAI,CAAC;IACnB,OAAO,IAAI;EACb;;EAEA;EACAiB,cAAcA,CAAC/C,EAAE,EAAE;IACjB,MAAM8B,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC,CAAC;IAC3B,OAAOG,IAAI,CAAC/B,QAAQ,CAACiD,IAAI,CAACJ,OAAO,IAAIA,OAAO,CAAC5C,EAAE,KAAKA,EAAE,CAAC;EACzD;;EAEA;EACAiD,WAAWA,CAACC,SAAS,EAAE9B,QAAQ,EAAED,IAAI,EAAEE,SAAS,EAAE;IAChD,MAAMS,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC,CAAC;IAC3B,MAAMiB,OAAO,GAAGd,IAAI,CAAC/B,QAAQ,CAACiD,IAAI,CAACG,CAAC,IAAIA,CAAC,CAACnD,EAAE,KAAKkD,SAAS,CAAC;IAC3D,IAAIN,OAAO,EAAE;MACXA,OAAO,CAACpC,YAAY,IAAIY,QAAQ;MAChCwB,OAAO,CAAC3B,SAAS,CAACuB,IAAI,CAAC;QACrBtB,IAAI,EAAE,IAAImB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC5CpB,IAAI;QACJC,QAAQ;QACRC;MACF,CAAC,CAAC;MACFuB,OAAO,CAAC5B,WAAW,GAAG,IAAIqB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC5D,IAAI,CAACV,QAAQ,CAACC,IAAI,CAAC;MACnB,OAAOc,OAAO;IAChB;IACA,OAAO,IAAI;EACb;;EAEA;EACAQ,iBAAiBA,CAAA,EAAG;IAClB,MAAMrD,QAAQ,GAAG,IAAI,CAACgC,cAAc,CAAC,CAAC;IAEtC,OAAO;MACLsB,aAAa,EAAEtD,QAAQ,CAACuD,MAAM;MAC9BC,UAAU,EAAExD,QAAQ,CAACyD,MAAM,CAAC,CAACC,GAAG,EAAEb,OAAO,KAAKa,GAAG,GAAIb,OAAO,CAACpC,YAAY,GAAGoC,OAAO,CAACtC,SAAU,EAAE,CAAC,CAAC;MAClGoD,gBAAgB,EAAE3D,QAAQ,CAAC+C,MAAM,CAACF,OAAO,IAAIA,OAAO,CAACpC,YAAY,IAAIoC,OAAO,CAACnC,QAAQ,CAAC,CAAC6C,MAAM;MAC7FK,kBAAkB,EAAE5D,QAAQ,CAAC+C,MAAM,CAACF,OAAO,IAAIA,OAAO,CAACpC,YAAY,KAAK,CAAC,CAAC,CAAC8C,MAAM;MACjFM,cAAc,EAAE7D,QAAQ,CAAC+C,MAAM,CAACF,OAAO,IAAIA,OAAO,CAAC7B,MAAM,KAAK,KAAK,CAAC,CAACuC,MAAM;MAC3EO,eAAe,EAAE,CAAC,GAAG,IAAIC,GAAG,CAAC/D,QAAQ,CAACgE,GAAG,CAACnB,OAAO,IAAIA,OAAO,CAACzC,QAAQ,CAAC,CAAC,CAAC,CAACmD;IAC3E,CAAC;EACH;;EAEA;EACAU,mBAAmBA,CAAA,EAAG;IACpB,MAAMjE,QAAQ,GAAG,IAAI,CAACgC,cAAc,CAAC,CAAC;IACtC,OAAOhC,QAAQ,CAAC+C,MAAM,CAACF,OAAO,IAAIA,OAAO,CAACpC,YAAY,IAAIoC,OAAO,CAACnC,QAAQ,CAAC;EAC7E;;EAEA;EACAwD,qBAAqBA,CAAA,EAAG;IACtB,MAAMlE,QAAQ,GAAG,IAAI,CAACgC,cAAc,CAAC,CAAC;IACtC,OAAOhC,QAAQ,CAAC+C,MAAM,CAACF,OAAO,IAAIA,OAAO,CAACpC,YAAY,KAAK,CAAC,CAAC;EAC/D;;EAEA;EACA0D,cAAcA,CAACC,KAAK,EAAE;IACpB,MAAMpE,QAAQ,GAAG,IAAI,CAACgC,cAAc,CAAC,CAAC;IACtC,OAAOhC,QAAQ,CAAC+C,MAAM,CAACF,OAAO,IAC5BA,OAAO,CAAC1C,IAAI,CAACkE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC,IACxDxB,OAAO,CAAC3C,IAAI,CAACmE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC,IACxDxB,OAAO,CAACzC,QAAQ,CAACiE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC,IAC5DxB,OAAO,CAACxC,KAAK,CAACgE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,KAAK,CAACC,WAAW,CAAC,CAAC,CAC1D,CAAC;EACH;;EAEA;EACAE,aAAaA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC3C,OAAO,CAAC,CAAC,CAACL,UAAU;EAClC;;EAEA;EACAiD,WAAWA,CAACpE,QAAQ,EAAE;IACpB,MAAM2B,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACG,IAAI,CAACR,UAAU,CAAC+C,QAAQ,CAAClE,QAAQ,CAAC,EAAE;MACvC2B,IAAI,CAACR,UAAU,CAACkB,IAAI,CAACrC,QAAQ,CAAC;MAC9B,IAAI,CAAC0B,QAAQ,CAACC,IAAI,CAAC;IACrB;IACA,OAAOA,IAAI,CAACR,UAAU;EACxB;AACF;AAEA,eAAe,IAAI/B,YAAY,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}