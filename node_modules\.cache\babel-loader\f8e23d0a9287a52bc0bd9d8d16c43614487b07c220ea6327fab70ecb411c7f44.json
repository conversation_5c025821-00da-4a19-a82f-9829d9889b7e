{"ast": null, "code": "// مولد QR Code متوافق مع المرحلة الثانية من الفوترة الإلكترونية السعودية\n// وفقاً لمعايير هيئة الزكاة والضريبة والجمارك\n\n/**\n * تشفير النص إلى Base64\n * @param {string} text - النص المراد تشفيره\n * @returns {string} النص مشفر بـ Base64\n */\nconst encodeToBase64 = text => {\n  return btoa(unescape(encodeURIComponent(text)));\n};\n\n/**\n * تحويل إلى TLV (Tag-Length-Value) format بالطريقة الصحيحة\n * @param {number} tag - رقم العلامة\n * @param {string} value - القيمة\n * @returns {Uint8Array} البيانات بصيغة TLV\n */\nconst toTLV = (tag, value) => {\n  const valueBytes = new TextEncoder().encode(value);\n  const result = new Uint8Array(2 + valueBytes.length);\n  result[0] = tag;\n  result[1] = valueBytes.length;\n  result.set(valueBytes, 2);\n  return result;\n};\n\n/**\n * تحويل Uint8Array إلى Base64\n * @param {Uint8Array} bytes - البيانات\n * @returns {string} البيانات مشفرة بـ Base64\n */\nconst arrayToBase64 = bytes => {\n  let binary = '';\n  for (let i = 0; i < bytes.length; i++) {\n    binary += String.fromCharCode(bytes[i]);\n  }\n  return btoa(binary);\n};\n\n/**\n * إنشاء QR Code متوافق مع المرحلة الثانية\n * @param {Object} invoiceData - بيانات الفاتورة\n * @returns {string} محتوى QR Code\n */\nexport const generateQRCodeData = invoiceData => {\n  try {\n    // تحميل إعدادات الشركة من التخزين المحلي\n    const companyConfig = localStorage.getItem('company_config');\n    const eInvoiceSettings = localStorage.getItem('einvoice_settings');\n    let companyName = 'شركة إدارة الأعمال';\n    let vatNumber = '300000000000003';\n\n    // استخدام بيانات الشركة الحقيقية\n    if (companyConfig) {\n      try {\n        var _config$companyInfo, _config$companyInfo2;\n        const config = JSON.parse(companyConfig);\n        companyName = ((_config$companyInfo = config.companyInfo) === null || _config$companyInfo === void 0 ? void 0 : _config$companyInfo.name) || companyName;\n        vatNumber = ((_config$companyInfo2 = config.companyInfo) === null || _config$companyInfo2 === void 0 ? void 0 : _config$companyInfo2.vatNumber) || vatNumber;\n      } catch (error) {\n        console.warn('خطأ في قراءة إعدادات الشركة:', error);\n      }\n    }\n\n    // استخدام إعدادات الفوترة الإلكترونية إذا وجدت\n    if (eInvoiceSettings) {\n      try {\n        const settings = JSON.parse(eInvoiceSettings);\n        companyName = settings.companyName || companyName;\n        vatNumber = settings.vatNumber || vatNumber;\n      } catch (error) {\n        console.warn('خطأ في قراءة إعدادات الفوترة الإلكترونية:', error);\n      }\n    }\n    const companyData = {\n      name: companyName,\n      vatNumber: vatNumber,\n      timestamp: new Date().toISOString(),\n      invoiceTotal: invoiceData.total.toFixed(2),\n      vatAmount: invoiceData.tax.toFixed(2)\n    };\n\n    // إنشاء البيانات بصيغة TLV وفقاً للمعايير السعودية\n    const tlvArrays = [];\n\n    // Tag 1: اسم البائع (Seller Name)\n    tlvArrays.push(toTLV(1, companyData.name));\n\n    // Tag 2: الرقم الضريبي للبائع (Seller VAT Number)\n    tlvArrays.push(toTLV(2, companyData.vatNumber));\n\n    // Tag 3: الطابع الزمني (Timestamp)\n    tlvArrays.push(toTLV(3, companyData.timestamp));\n\n    // Tag 4: إجمالي الفاتورة شامل الضريبة (Invoice Total with VAT)\n    tlvArrays.push(toTLV(4, companyData.invoiceTotal));\n\n    // Tag 5: إجمالي ضريبة القيمة المضافة (VAT Total)\n    tlvArrays.push(toTLV(5, companyData.vatAmount));\n\n    // دمج جميع المصفوفات\n    const totalLength = tlvArrays.reduce((sum, arr) => sum + arr.length, 0);\n    const combinedArray = new Uint8Array(totalLength);\n    let offset = 0;\n    for (const arr of tlvArrays) {\n      combinedArray.set(arr, offset);\n      offset += arr.length;\n    }\n\n    // تحويل البيانات إلى Base64\n    const base64Data = arrayToBase64(combinedArray);\n    return base64Data;\n  } catch (error) {\n    console.error('خطأ في إنشاء QR Code:', error);\n    return '';\n  }\n};\n\n/**\n * إنشاء QR Code مبسط للاختبار\n * @param {Object} invoiceData - بيانات الفاتورة\n * @returns {string} محتوى QR Code مبسط\n */\nexport const generateSimpleQRCode = invoiceData => {\n  const qrData = {\n    seller: 'شركة إدارة الأعمال',\n    vatNumber: '300000000000003',\n    invoiceNumber: invoiceData.invoiceNumber,\n    date: invoiceData.date,\n    total: invoiceData.total.toFixed(2),\n    vat: invoiceData.tax.toFixed(2),\n    customer: invoiceData.customerName\n  };\n  return JSON.stringify(qrData);\n};\n\n/**\n * التحقق من صحة الرقم الضريبي السعودي\n * @param {string} vatNumber - الرقم الضريبي\n * @returns {boolean} صحة الرقم الضريبي\n */\nexport const validateSaudiVATNumber = vatNumber => {\n  // الرقم الضريبي السعودي يجب أن يكون 15 رقم\n  const vatRegex = /^[0-9]{15}$/;\n  return vatRegex.test(vatNumber);\n};\n\n/**\n * تنسيق التاريخ للفوترة الإلكترونية\n * @param {Date} date - التاريخ\n * @returns {string} التاريخ منسق\n */\nexport const formatDateForEInvoice = date => {\n  return new Date(date).toISOString();\n};\n\n/**\n * فك تشفير QR Code للتحقق من صحة البيانات\n * @param {string} base64Data - بيانات QR Code مشفرة\n * @returns {Object} بيانات الفاتورة مفكوكة\n */\nexport const decodeQRCodeData = base64Data => {\n  try {\n    // فك تشفير Base64\n    const binaryString = atob(base64Data);\n    const bytes = new Uint8Array(binaryString.length);\n    for (let i = 0; i < binaryString.length; i++) {\n      bytes[i] = binaryString.charCodeAt(i);\n    }\n    const result = {};\n    let offset = 0;\n\n    // قراءة بيانات TLV\n    while (offset < bytes.length) {\n      const tag = bytes[offset];\n      const length = bytes[offset + 1];\n      const value = new TextDecoder().decode(bytes.slice(offset + 2, offset + 2 + length));\n      switch (tag) {\n        case 1:\n          result.sellerName = value;\n          break;\n        case 2:\n          result.vatNumber = value;\n          break;\n        case 3:\n          result.timestamp = value;\n          break;\n        case 4:\n          result.invoiceTotal = value;\n          break;\n        case 5:\n          result.vatAmount = value;\n          break;\n      }\n      offset += 2 + length;\n    }\n    return result;\n  } catch (error) {\n    console.error('خطأ في فك تشفير QR Code:', error);\n    return null;\n  }\n};\n\n/**\n * حساب hash للفاتورة (للمرحلة الثانية المتقدمة)\n * @param {Object} invoiceData - بيانات الفاتورة\n * @returns {string} hash الفاتورة\n */\nexport const generateInvoiceHash = invoiceData => {\n  const dataString = JSON.stringify({\n    invoiceNumber: invoiceData.invoiceNumber,\n    date: invoiceData.date,\n    total: invoiceData.total,\n    items: invoiceData.items\n  });\n\n  // استخدام hash بسيط (في الإنتاج يجب استخدام SHA-256)\n  let hash = 0;\n  for (let i = 0; i < dataString.length; i++) {\n    const char = dataString.charCodeAt(i);\n    hash = (hash << 5) - hash + char;\n    hash = hash & hash; // تحويل إلى 32bit integer\n  }\n  return Math.abs(hash).toString(16);\n};\n\n/**\n * اختبار قراءة QR Code\n * @param {string} qrData - بيانات QR Code\n * @returns {Object} نتيجة الاختبار\n */\nexport const testQRCodeReading = qrData => {\n  const decoded = decodeQRCodeData(qrData);\n  return {\n    isValid: decoded !== null,\n    data: decoded,\n    hasAllRequiredFields: decoded && decoded.sellerName && decoded.vatNumber && decoded.timestamp && decoded.invoiceTotal && decoded.vatAmount\n  };\n};", "map": {"version": 3, "names": ["encodeToBase64", "text", "btoa", "unescape", "encodeURIComponent", "toTLV", "tag", "value", "valueBytes", "TextEncoder", "encode", "result", "Uint8Array", "length", "set", "arrayToBase64", "bytes", "binary", "i", "String", "fromCharCode", "generateQRCodeData", "invoiceData", "companyConfig", "localStorage", "getItem", "eInvoiceSettings", "companyName", "vatNumber", "_config$companyInfo", "_config$companyInfo2", "config", "JSON", "parse", "companyInfo", "name", "error", "console", "warn", "settings", "companyData", "timestamp", "Date", "toISOString", "invoiceTotal", "total", "toFixed", "vatAmount", "tax", "tlvArrays", "push", "totalLength", "reduce", "sum", "arr", "combinedArray", "offset", "base64Data", "generateSimpleQRCode", "qrData", "seller", "invoiceNumber", "date", "vat", "customer", "customerName", "stringify", "validateSaudiVATNumber", "vatRegex", "test", "formatDateForEInvoice", "decodeQRCodeData", "binaryString", "atob", "charCodeAt", "TextDecoder", "decode", "slice", "sellerName", "generateInvoiceHash", "dataString", "items", "hash", "char", "Math", "abs", "toString", "testQRCodeReading", "decoded", "<PERSON><PERSON><PERSON><PERSON>", "data", "hasAllRequiredFields"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/src/utils/qrCodeGenerator.js"], "sourcesContent": ["// مولد QR Code متوافق مع المرحلة الثانية من الفوترة الإلكترونية السعودية\n// وفقاً لمعايير هيئة الزكاة والضريبة والجمارك\n\n/**\n * تشفير النص إلى Base64\n * @param {string} text - النص المراد تشفيره\n * @returns {string} النص مشفر بـ Base64\n */\nconst encodeToBase64 = (text) => {\n  return btoa(unescape(encodeURIComponent(text)));\n};\n\n/**\n * تحويل إلى TLV (Tag-Length-Value) format بالطريقة الصحيحة\n * @param {number} tag - رقم العلامة\n * @param {string} value - القيمة\n * @returns {Uint8Array} البيانات بصيغة TLV\n */\nconst toTLV = (tag, value) => {\n  const valueBytes = new TextEncoder().encode(value);\n  const result = new Uint8Array(2 + valueBytes.length);\n\n  result[0] = tag;\n  result[1] = valueBytes.length;\n  result.set(valueBytes, 2);\n\n  return result;\n};\n\n/**\n * تحويل Uint8Array إلى Base64\n * @param {Uint8Array} bytes - البيانات\n * @returns {string} البيانات مشفرة بـ Base64\n */\nconst arrayToBase64 = (bytes) => {\n  let binary = '';\n  for (let i = 0; i < bytes.length; i++) {\n    binary += String.fromCharCode(bytes[i]);\n  }\n  return btoa(binary);\n};\n\n/**\n * إنشاء QR Code متوافق مع المرحلة الثانية\n * @param {Object} invoiceData - بيانات الفاتورة\n * @returns {string} محتوى QR Code\n */\nexport const generateQRCodeData = (invoiceData) => {\n  try {\n    // تحميل إعدادات الشركة من التخزين المحلي\n    const companyConfig = localStorage.getItem('company_config');\n    const eInvoiceSettings = localStorage.getItem('einvoice_settings');\n\n    let companyName = 'شركة إدارة الأعمال';\n    let vatNumber = '300000000000003';\n\n    // استخدام بيانات الشركة الحقيقية\n    if (companyConfig) {\n      try {\n        const config = JSON.parse(companyConfig);\n        companyName = config.companyInfo?.name || companyName;\n        vatNumber = config.companyInfo?.vatNumber || vatNumber;\n      } catch (error) {\n        console.warn('خطأ في قراءة إعدادات الشركة:', error);\n      }\n    }\n\n    // استخدام إعدادات الفوترة الإلكترونية إذا وجدت\n    if (eInvoiceSettings) {\n      try {\n        const settings = JSON.parse(eInvoiceSettings);\n        companyName = settings.companyName || companyName;\n        vatNumber = settings.vatNumber || vatNumber;\n      } catch (error) {\n        console.warn('خطأ في قراءة إعدادات الفوترة الإلكترونية:', error);\n      }\n    }\n\n    const companyData = {\n      name: companyName,\n      vatNumber: vatNumber,\n      timestamp: new Date().toISOString(),\n      invoiceTotal: invoiceData.total.toFixed(2),\n      vatAmount: invoiceData.tax.toFixed(2)\n    };\n\n    // إنشاء البيانات بصيغة TLV وفقاً للمعايير السعودية\n    const tlvArrays = [];\n\n    // Tag 1: اسم البائع (Seller Name)\n    tlvArrays.push(toTLV(1, companyData.name));\n\n    // Tag 2: الرقم الضريبي للبائع (Seller VAT Number)\n    tlvArrays.push(toTLV(2, companyData.vatNumber));\n\n    // Tag 3: الطابع الزمني (Timestamp)\n    tlvArrays.push(toTLV(3, companyData.timestamp));\n\n    // Tag 4: إجمالي الفاتورة شامل الضريبة (Invoice Total with VAT)\n    tlvArrays.push(toTLV(4, companyData.invoiceTotal));\n\n    // Tag 5: إجمالي ضريبة القيمة المضافة (VAT Total)\n    tlvArrays.push(toTLV(5, companyData.vatAmount));\n\n    // دمج جميع المصفوفات\n    const totalLength = tlvArrays.reduce((sum, arr) => sum + arr.length, 0);\n    const combinedArray = new Uint8Array(totalLength);\n    let offset = 0;\n\n    for (const arr of tlvArrays) {\n      combinedArray.set(arr, offset);\n      offset += arr.length;\n    }\n\n    // تحويل البيانات إلى Base64\n    const base64Data = arrayToBase64(combinedArray);\n\n    return base64Data;\n\n  } catch (error) {\n    console.error('خطأ في إنشاء QR Code:', error);\n    return '';\n  }\n};\n\n/**\n * إنشاء QR Code مبسط للاختبار\n * @param {Object} invoiceData - بيانات الفاتورة\n * @returns {string} محتوى QR Code مبسط\n */\nexport const generateSimpleQRCode = (invoiceData) => {\n  const qrData = {\n    seller: 'شركة إدارة الأعمال',\n    vatNumber: '300000000000003',\n    invoiceNumber: invoiceData.invoiceNumber,\n    date: invoiceData.date,\n    total: invoiceData.total.toFixed(2),\n    vat: invoiceData.tax.toFixed(2),\n    customer: invoiceData.customerName\n  };\n\n  return JSON.stringify(qrData);\n};\n\n/**\n * التحقق من صحة الرقم الضريبي السعودي\n * @param {string} vatNumber - الرقم الضريبي\n * @returns {boolean} صحة الرقم الضريبي\n */\nexport const validateSaudiVATNumber = (vatNumber) => {\n  // الرقم الضريبي السعودي يجب أن يكون 15 رقم\n  const vatRegex = /^[0-9]{15}$/;\n  return vatRegex.test(vatNumber);\n};\n\n/**\n * تنسيق التاريخ للفوترة الإلكترونية\n * @param {Date} date - التاريخ\n * @returns {string} التاريخ منسق\n */\nexport const formatDateForEInvoice = (date) => {\n  return new Date(date).toISOString();\n};\n\n/**\n * فك تشفير QR Code للتحقق من صحة البيانات\n * @param {string} base64Data - بيانات QR Code مشفرة\n * @returns {Object} بيانات الفاتورة مفكوكة\n */\nexport const decodeQRCodeData = (base64Data) => {\n  try {\n    // فك تشفير Base64\n    const binaryString = atob(base64Data);\n    const bytes = new Uint8Array(binaryString.length);\n    for (let i = 0; i < binaryString.length; i++) {\n      bytes[i] = binaryString.charCodeAt(i);\n    }\n\n    const result = {};\n    let offset = 0;\n\n    // قراءة بيانات TLV\n    while (offset < bytes.length) {\n      const tag = bytes[offset];\n      const length = bytes[offset + 1];\n      const value = new TextDecoder().decode(bytes.slice(offset + 2, offset + 2 + length));\n\n      switch (tag) {\n        case 1:\n          result.sellerName = value;\n          break;\n        case 2:\n          result.vatNumber = value;\n          break;\n        case 3:\n          result.timestamp = value;\n          break;\n        case 4:\n          result.invoiceTotal = value;\n          break;\n        case 5:\n          result.vatAmount = value;\n          break;\n      }\n\n      offset += 2 + length;\n    }\n\n    return result;\n  } catch (error) {\n    console.error('خطأ في فك تشفير QR Code:', error);\n    return null;\n  }\n};\n\n/**\n * حساب hash للفاتورة (للمرحلة الثانية المتقدمة)\n * @param {Object} invoiceData - بيانات الفاتورة\n * @returns {string} hash الفاتورة\n */\nexport const generateInvoiceHash = (invoiceData) => {\n  const dataString = JSON.stringify({\n    invoiceNumber: invoiceData.invoiceNumber,\n    date: invoiceData.date,\n    total: invoiceData.total,\n    items: invoiceData.items\n  });\n\n  // استخدام hash بسيط (في الإنتاج يجب استخدام SHA-256)\n  let hash = 0;\n  for (let i = 0; i < dataString.length; i++) {\n    const char = dataString.charCodeAt(i);\n    hash = ((hash << 5) - hash) + char;\n    hash = hash & hash; // تحويل إلى 32bit integer\n  }\n\n  return Math.abs(hash).toString(16);\n};\n\n/**\n * اختبار قراءة QR Code\n * @param {string} qrData - بيانات QR Code\n * @returns {Object} نتيجة الاختبار\n */\nexport const testQRCodeReading = (qrData) => {\n  const decoded = decodeQRCodeData(qrData);\n\n  return {\n    isValid: decoded !== null,\n    data: decoded,\n    hasAllRequiredFields: decoded &&\n      decoded.sellerName &&\n      decoded.vatNumber &&\n      decoded.timestamp &&\n      decoded.invoiceTotal &&\n      decoded.vatAmount\n  };\n};\n"], "mappings": "AAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMA,cAAc,GAAIC,IAAI,IAAK;EAC/B,OAAOC,IAAI,CAACC,QAAQ,CAACC,kBAAkB,CAACH,IAAI,CAAC,CAAC,CAAC;AACjD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,KAAK,GAAGA,CAACC,GAAG,EAAEC,KAAK,KAAK;EAC5B,MAAMC,UAAU,GAAG,IAAIC,WAAW,CAAC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;EAClD,MAAMI,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC,GAAGJ,UAAU,CAACK,MAAM,CAAC;EAEpDF,MAAM,CAAC,CAAC,CAAC,GAAGL,GAAG;EACfK,MAAM,CAAC,CAAC,CAAC,GAAGH,UAAU,CAACK,MAAM;EAC7BF,MAAM,CAACG,GAAG,CAACN,UAAU,EAAE,CAAC,CAAC;EAEzB,OAAOG,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,MAAMI,aAAa,GAAIC,KAAK,IAAK;EAC/B,IAAIC,MAAM,GAAG,EAAE;EACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACH,MAAM,EAAEK,CAAC,EAAE,EAAE;IACrCD,MAAM,IAAIE,MAAM,CAACC,YAAY,CAACJ,KAAK,CAACE,CAAC,CAAC,CAAC;EACzC;EACA,OAAOhB,IAAI,CAACe,MAAM,CAAC;AACrB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMI,kBAAkB,GAAIC,WAAW,IAAK;EACjD,IAAI;IACF;IACA,MAAMC,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC5D,MAAMC,gBAAgB,GAAGF,YAAY,CAACC,OAAO,CAAC,mBAAmB,CAAC;IAElE,IAAIE,WAAW,GAAG,oBAAoB;IACtC,IAAIC,SAAS,GAAG,iBAAiB;;IAEjC;IACA,IAAIL,aAAa,EAAE;MACjB,IAAI;QAAA,IAAAM,mBAAA,EAAAC,oBAAA;QACF,MAAMC,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACV,aAAa,CAAC;QACxCI,WAAW,GAAG,EAAAE,mBAAA,GAAAE,MAAM,CAACG,WAAW,cAAAL,mBAAA,uBAAlBA,mBAAA,CAAoBM,IAAI,KAAIR,WAAW;QACrDC,SAAS,GAAG,EAAAE,oBAAA,GAAAC,MAAM,CAACG,WAAW,cAAAJ,oBAAA,uBAAlBA,oBAAA,CAAoBF,SAAS,KAAIA,SAAS;MACxD,CAAC,CAAC,OAAOQ,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,8BAA8B,EAAEF,KAAK,CAAC;MACrD;IACF;;IAEA;IACA,IAAIV,gBAAgB,EAAE;MACpB,IAAI;QACF,MAAMa,QAAQ,GAAGP,IAAI,CAACC,KAAK,CAACP,gBAAgB,CAAC;QAC7CC,WAAW,GAAGY,QAAQ,CAACZ,WAAW,IAAIA,WAAW;QACjDC,SAAS,GAAGW,QAAQ,CAACX,SAAS,IAAIA,SAAS;MAC7C,CAAC,CAAC,OAAOQ,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,2CAA2C,EAAEF,KAAK,CAAC;MAClE;IACF;IAEA,MAAMI,WAAW,GAAG;MAClBL,IAAI,EAAER,WAAW;MACjBC,SAAS,EAAEA,SAAS;MACpBa,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnCC,YAAY,EAAEtB,WAAW,CAACuB,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC;MAC1CC,SAAS,EAAEzB,WAAW,CAAC0B,GAAG,CAACF,OAAO,CAAC,CAAC;IACtC,CAAC;;IAED;IACA,MAAMG,SAAS,GAAG,EAAE;;IAEpB;IACAA,SAAS,CAACC,IAAI,CAAC7C,KAAK,CAAC,CAAC,EAAEmC,WAAW,CAACL,IAAI,CAAC,CAAC;;IAE1C;IACAc,SAAS,CAACC,IAAI,CAAC7C,KAAK,CAAC,CAAC,EAAEmC,WAAW,CAACZ,SAAS,CAAC,CAAC;;IAE/C;IACAqB,SAAS,CAACC,IAAI,CAAC7C,KAAK,CAAC,CAAC,EAAEmC,WAAW,CAACC,SAAS,CAAC,CAAC;;IAE/C;IACAQ,SAAS,CAACC,IAAI,CAAC7C,KAAK,CAAC,CAAC,EAAEmC,WAAW,CAACI,YAAY,CAAC,CAAC;;IAElD;IACAK,SAAS,CAACC,IAAI,CAAC7C,KAAK,CAAC,CAAC,EAAEmC,WAAW,CAACO,SAAS,CAAC,CAAC;;IAE/C;IACA,MAAMI,WAAW,GAAGF,SAAS,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAACzC,MAAM,EAAE,CAAC,CAAC;IACvE,MAAM0C,aAAa,GAAG,IAAI3C,UAAU,CAACuC,WAAW,CAAC;IACjD,IAAIK,MAAM,GAAG,CAAC;IAEd,KAAK,MAAMF,GAAG,IAAIL,SAAS,EAAE;MAC3BM,aAAa,CAACzC,GAAG,CAACwC,GAAG,EAAEE,MAAM,CAAC;MAC9BA,MAAM,IAAIF,GAAG,CAACzC,MAAM;IACtB;;IAEA;IACA,MAAM4C,UAAU,GAAG1C,aAAa,CAACwC,aAAa,CAAC;IAE/C,OAAOE,UAAU;EAEnB,CAAC,CAAC,OAAOrB,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC7C,OAAO,EAAE;EACX;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMsB,oBAAoB,GAAIpC,WAAW,IAAK;EACnD,MAAMqC,MAAM,GAAG;IACbC,MAAM,EAAE,oBAAoB;IAC5BhC,SAAS,EAAE,iBAAiB;IAC5BiC,aAAa,EAAEvC,WAAW,CAACuC,aAAa;IACxCC,IAAI,EAAExC,WAAW,CAACwC,IAAI;IACtBjB,KAAK,EAAEvB,WAAW,CAACuB,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC;IACnCiB,GAAG,EAAEzC,WAAW,CAAC0B,GAAG,CAACF,OAAO,CAAC,CAAC,CAAC;IAC/BkB,QAAQ,EAAE1C,WAAW,CAAC2C;EACxB,CAAC;EAED,OAAOjC,IAAI,CAACkC,SAAS,CAACP,MAAM,CAAC;AAC/B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMQ,sBAAsB,GAAIvC,SAAS,IAAK;EACnD;EACA,MAAMwC,QAAQ,GAAG,aAAa;EAC9B,OAAOA,QAAQ,CAACC,IAAI,CAACzC,SAAS,CAAC;AACjC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM0C,qBAAqB,GAAIR,IAAI,IAAK;EAC7C,OAAO,IAAIpB,IAAI,CAACoB,IAAI,CAAC,CAACnB,WAAW,CAAC,CAAC;AACrC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM4B,gBAAgB,GAAId,UAAU,IAAK;EAC9C,IAAI;IACF;IACA,MAAMe,YAAY,GAAGC,IAAI,CAAChB,UAAU,CAAC;IACrC,MAAMzC,KAAK,GAAG,IAAIJ,UAAU,CAAC4D,YAAY,CAAC3D,MAAM,CAAC;IACjD,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsD,YAAY,CAAC3D,MAAM,EAAEK,CAAC,EAAE,EAAE;MAC5CF,KAAK,CAACE,CAAC,CAAC,GAAGsD,YAAY,CAACE,UAAU,CAACxD,CAAC,CAAC;IACvC;IAEA,MAAMP,MAAM,GAAG,CAAC,CAAC;IACjB,IAAI6C,MAAM,GAAG,CAAC;;IAEd;IACA,OAAOA,MAAM,GAAGxC,KAAK,CAACH,MAAM,EAAE;MAC5B,MAAMP,GAAG,GAAGU,KAAK,CAACwC,MAAM,CAAC;MACzB,MAAM3C,MAAM,GAAGG,KAAK,CAACwC,MAAM,GAAG,CAAC,CAAC;MAChC,MAAMjD,KAAK,GAAG,IAAIoE,WAAW,CAAC,CAAC,CAACC,MAAM,CAAC5D,KAAK,CAAC6D,KAAK,CAACrB,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAG,CAAC,GAAG3C,MAAM,CAAC,CAAC;MAEpF,QAAQP,GAAG;QACT,KAAK,CAAC;UACJK,MAAM,CAACmE,UAAU,GAAGvE,KAAK;UACzB;QACF,KAAK,CAAC;UACJI,MAAM,CAACiB,SAAS,GAAGrB,KAAK;UACxB;QACF,KAAK,CAAC;UACJI,MAAM,CAAC8B,SAAS,GAAGlC,KAAK;UACxB;QACF,KAAK,CAAC;UACJI,MAAM,CAACiC,YAAY,GAAGrC,KAAK;UAC3B;QACF,KAAK,CAAC;UACJI,MAAM,CAACoC,SAAS,GAAGxC,KAAK;UACxB;MACJ;MAEAiD,MAAM,IAAI,CAAC,GAAG3C,MAAM;IACtB;IAEA,OAAOF,MAAM;EACf,CAAC,CAAC,OAAOyB,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAChD,OAAO,IAAI;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM2C,mBAAmB,GAAIzD,WAAW,IAAK;EAClD,MAAM0D,UAAU,GAAGhD,IAAI,CAACkC,SAAS,CAAC;IAChCL,aAAa,EAAEvC,WAAW,CAACuC,aAAa;IACxCC,IAAI,EAAExC,WAAW,CAACwC,IAAI;IACtBjB,KAAK,EAAEvB,WAAW,CAACuB,KAAK;IACxBoC,KAAK,EAAE3D,WAAW,CAAC2D;EACrB,CAAC,CAAC;;EAEF;EACA,IAAIC,IAAI,GAAG,CAAC;EACZ,KAAK,IAAIhE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8D,UAAU,CAACnE,MAAM,EAAEK,CAAC,EAAE,EAAE;IAC1C,MAAMiE,IAAI,GAAGH,UAAU,CAACN,UAAU,CAACxD,CAAC,CAAC;IACrCgE,IAAI,GAAI,CAACA,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAIC,IAAI;IAClCD,IAAI,GAAGA,IAAI,GAAGA,IAAI,CAAC,CAAC;EACtB;EAEA,OAAOE,IAAI,CAACC,GAAG,CAACH,IAAI,CAAC,CAACI,QAAQ,CAAC,EAAE,CAAC;AACpC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,iBAAiB,GAAI5B,MAAM,IAAK;EAC3C,MAAM6B,OAAO,GAAGjB,gBAAgB,CAACZ,MAAM,CAAC;EAExC,OAAO;IACL8B,OAAO,EAAED,OAAO,KAAK,IAAI;IACzBE,IAAI,EAAEF,OAAO;IACbG,oBAAoB,EAAEH,OAAO,IAC3BA,OAAO,CAACV,UAAU,IAClBU,OAAO,CAAC5D,SAAS,IACjB4D,OAAO,CAAC/C,SAAS,IACjB+C,OAAO,CAAC5C,YAAY,IACpB4C,OAAO,CAACzC;EACZ,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}