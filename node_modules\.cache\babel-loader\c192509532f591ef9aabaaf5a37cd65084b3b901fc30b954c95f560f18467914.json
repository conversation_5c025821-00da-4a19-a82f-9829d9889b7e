{"ast": null, "code": "var getNative = require('./_getNative'),\n  root = require('./_root');\n\n/* Built-in method references that are verified to be native. */\nvar Set = getNative(root, 'Set');\nmodule.exports = Set;", "map": {"version": 3, "names": ["getNative", "require", "root", "Set", "module", "exports"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/node_modules/lodash/_Set.js"], "sourcesContent": ["var getNative = require('./_getNative'),\n    root = require('./_root');\n\n/* Built-in method references that are verified to be native. */\nvar Set = getNative(root, 'Set');\n\nmodule.exports = Set;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,OAAO,CAAC,cAAc,CAAC;EACnCC,IAAI,GAAGD,OAAO,CAAC,SAAS,CAAC;;AAE7B;AACA,IAAIE,GAAG,GAAGH,SAAS,CAACE,IAAI,EAAE,KAAK,CAAC;AAEhCE,MAAM,CAACC,OAAO,GAAGF,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}