# 📱 دليل QR Code للفوترة الإلكترونية - المرحلة الثانية

## 🎯 نظرة عامة

تم تطوير نظام QR Code متوافق مع **المرحلة الثانية من الفوترة الإلكترونية** في المملكة العربية السعودية وفقاً لمتطلبات **هيئة الزكاة والضريبة والجمارك (ZATCA)**.

## 🔧 المتطلبات التقنية

### المكتبات المستخدمة:
- `qrcode` - لإنشاء QR Code
- `react-qr-code` - مكون React لعرض QR Code
- `btoa` - تشفير Base64 (مدمج في المتصفح)

### التثبيت:
```bash
npm install qrcode react-qr-code
```

## 📋 متطلبات المرحلة الثانية

### الحقول المطلوبة في QR Code:

| Tag | الحقل | الوصف | مطلوب |
|-----|-------|--------|--------|
| 1 | اسم البائع | اسم الشركة أو التاجر | ✅ |
| 2 | الرقم الضريبي | رقم ضريبي سعودي (15 رقم) | ✅ |
| 3 | الطابع الزمني | تاريخ ووقت إصدار الفاتورة (ISO 8601) | ✅ |
| 4 | إجمالي الفاتورة | المبلغ الإجمالي شامل الضريبة | ✅ |
| 5 | إجمالي الضريبة | مبلغ ضريبة القيمة المضافة | ✅ |

### تنسيق TLV (Tag-Length-Value):
```
Tag (1 byte) + Length (1 byte) + Value (variable length)
```

## 🏗️ هيكل النظام

### 1. مولد QR Code (`src/utils/qrCodeGenerator.js`)

#### الوظائف الرئيسية:

```javascript
// إنشاء QR Code متوافق مع المرحلة الثانية
generateQRCodeData(invoiceData)

// إنشاء QR Code مبسط للاختبار
generateSimpleQRCode(invoiceData)

// التحقق من صحة الرقم الضريبي السعودي
validateSaudiVATNumber(vatNumber)

// تنسيق التاريخ للفوترة الإلكترونية
formatDateForEInvoice(date)

// إنشاء hash للفاتورة
generateInvoiceHash(invoiceData)
```

#### مثال على الاستخدام:
```javascript
import { generateQRCodeData } from '../utils/qrCodeGenerator';

const invoiceData = {
  invoiceNumber: 'INV-2024-001',
  date: '2024-01-15',
  total: 8712.5,
  tax: 1162.5,
  customerName: 'أحمد محمد'
};

const qrData = generateQRCodeData(invoiceData);
```

### 2. مكون QR Code (`src/components/QRCode/InvoiceQRCode.jsx`)

#### الميزات:
- عرض QR Code مع تفاصيل الفاتورة
- تبديل بين المرحلة الثانية والنسخة المبسطة
- نسخ بيانات QR Code
- تحميل QR Code كصورة PNG
- عرض معلومات المطابقة

#### الخصائص (Props):
```javascript
<InvoiceQRCode
  invoiceData={sale}        // بيانات الفاتورة
  size={150}               // حجم QR Code
  showDetails={true}       // عرض التفاصيل
/>
```

### 3. إعدادات الفوترة الإلكترونية (`src/components/Settings/EInvoiceSettings.jsx`)

#### الإعدادات المتاحة:
- معلومات الشركة (الاسم، الرقم الضريبي، العنوان)
- إعدادات QR Code (تفعيل المرحلة الثانية، الإنشاء التلقائي)
- التحقق من صحة البيانات
- حفظ الإعدادات في التخزين المحلي

## 🔍 كيفية عمل النظام

### 1. تحميل إعدادات الشركة:
```javascript
const savedSettings = localStorage.getItem('einvoice_settings');
const companySettings = savedSettings ? JSON.parse(savedSettings) : defaultSettings;
```

### 2. إنشاء بيانات TLV:
```javascript
// Tag 1: اسم البائع
tlvData += toTLV(1, companyData.name);

// Tag 2: الرقم الضريبي
tlvData += toTLV(2, companyData.vatNumber);

// Tag 3: الطابع الزمني
tlvData += toTLV(3, companyData.timestamp);

// Tag 4: إجمالي الفاتورة
tlvData += toTLV(4, companyData.invoiceTotal);

// Tag 5: إجمالي الضريبة
tlvData += toTLV(5, companyData.vatAmount);
```

### 3. تشفير Base64:
```javascript
const base64Data = encodeToBase64(tlvData);
```

### 4. إنشاء QR Code:
```javascript
<QRCode
  value={qrData}
  size={size}
  level="M"
  includeMargin={true}
/>
```

## 🧪 اختبار النظام

### صفحة الاختبار (`/qr-test` - للمطورين فقط):

#### الميزات:
- اختبار QR Code مع فاتورة تجريبية
- مقارنة بين المرحلة الثانية والنسخة المبسطة
- اختبار التحقق من الرقم الضريبي
- عرض البيانات المُنشأة
- معلومات تقنية مفصلة

#### الوصول:
```
http://localhost:3001/qr-test
```
(متاح فقط في بيئة التطوير)

## ✅ التحقق من المطابقة

### 1. التحقق من الرقم الضريبي:
```javascript
const isValid = validateSaudiVATNumber('300000000000003');
// يجب أن يكون 15 رقم بالضبط
```

### 2. التحقق من تنسيق TLV:
- كل حقل يبدأ بـ Tag (1 byte)
- يتبعه Length (1 byte)
- ثم Value (بطول متغير)

### 3. التحقق من Base64:
- البيانات مشفرة بـ Base64
- قابلة للفك والقراءة

### 4. التحقق من الطابع الزمني:
- تنسيق ISO 8601
- يتضمن التاريخ والوقت والمنطقة الزمنية

## 🔧 الإعدادات والتخصيص

### إعدادات الشركة:
```javascript
{
  companyName: 'شركة إدارة الأعمال',
  vatNumber: '300000000000003',
  crNumber: '1010000000',
  address: 'الرياض، المملكة العربية السعودية',
  phone: '0112345678',
  email: '<EMAIL>',
  website: 'www.company.com'
}
```

### إعدادات QR Code:
```javascript
{
  phase2Enabled: true,      // تفعيل المرحلة الثانية
  qrCodeEnabled: true,      // تفعيل QR Code
  autoGenerateQR: true      // إنشاء تلقائي
}
```

## 📱 الاستخدام في الفواتير

### 1. في فاتورة المبيعات:
```javascript
// تم إضافة QR Code تلقائياً في SalesInvoice.jsx
<InvoiceQRCode
  invoiceData={sale}
  size={150}
  showDetails={false}
/>
```

### 2. في فاتورة المشتريات:
```javascript
// يمكن إضافة QR Code للمشتريات أيضاً
<InvoiceQRCode
  invoiceData={purchase}
  size={150}
  showDetails={false}
/>
```

## 🔒 الأمان والموثوقية

### 1. التحقق من البيانات:
- التحقق من صحة الرقم الضريبي
- التحقق من اكتمال البيانات المطلوبة
- التحقق من تنسيق التاريخ

### 2. تشفير البيانات:
- استخدام Base64 للتشفير
- تنسيق TLV المعياري
- hash للفاتورة للتحقق من التكامل

### 3. معالجة الأخطاء:
```javascript
try {
  const qrData = generateQRCodeData(invoiceData);
  return qrData;
} catch (error) {
  console.error('خطأ في إنشاء QR Code:', error);
  return '';
}
```

## 📊 مثال كامل

### بيانات الفاتورة:
```javascript
const invoiceData = {
  id: 1,
  invoiceNumber: 'INV-2024-001',
  customerId: 1,
  customerName: 'أحمد محمد',
  date: '2024-01-15',
  items: [
    {
      productId: 1,
      productName: 'لابتوب Dell',
      quantity: 2,
      price: 3500,
      total: 7000
    }
  ],
  subtotal: 7000,
  tax: 1050,      // 15% ضريبة القيمة المضافة
  discount: 0,
  total: 8050,
  status: 'مكتملة',
  paymentMethod: 'نقدي'
};
```

### QR Code المُنشأ:
```
Tag 1: شركة إدارة الأعمال
Tag 2: 300000000000003
Tag 3: 2024-01-15T10:30:00.000Z
Tag 4: 8050.00
Tag 5: 1050.00
```

### بعد التشفير Base64:
```
AQzYtNin2YPYp9mE2KfYr9ipINin2YTYo9i52YXYp9mEAg8zMDAwMDAwMDAwMDAwMDMDGTIwMjQtMDEtMTVUMTA6MzA6MDAuMDAwWgQHODA1MC4wMAUIMTA1MC4wMA==
```

## 🚀 التطوير المستقبلي

### المرحلة الثالثة (مستقبلاً):
- ربط مع منصة فاتورة
- إرسال الفواتير إلكترونياً
- التوقيع الرقمي
- التشفير المتقدم

### تحسينات مقترحة:
- دعم أنواع فواتير إضافية
- تخصيص تصميم QR Code
- تصدير QR Code بصيغ متعددة
- إحصائيات استخدام QR Code

## 📞 الدعم والمساعدة

### الأخطاء الشائعة:

1. **"الرقم الضريبي غير صحيح"**
   - تأكد من أن الرقم 15 رقم بالضبط
   - لا يحتوي على أحرف أو رموز

2. **"فشل في إنشاء QR Code"**
   - تحقق من اكتمال بيانات الفاتورة
   - تأكد من صحة إعدادات الشركة

3. **"QR Code لا يظهر"**
   - تحقق من تفعيل QR Code في الإعدادات
   - تأكد من تحميل المكتبات المطلوبة

### للمطورين:
- استخدم صفحة `/qr-test` للاختبار
- راجع console للأخطاء
- تحقق من localStorage للإعدادات

## 📚 المراجع

- [دليل الفوترة الإلكترونية - هيئة الزكاة والضريبة والجمارك](https://zatca.gov.sa)
- [معايير QR Code - ISO/IEC 18004](https://www.iso.org/standard/62021.html)
- [تنسيق TLV - ITU-T X.690](https://www.itu.int/rec/T-REC-X.690)
- [Base64 Encoding - RFC 4648](https://tools.ietf.org/html/rfc4648)

---

**ملاحظة**: هذا النظام متوافق مع المرحلة الثانية من الفوترة الإلكترونية ويمكن تطويره للمراحل المتقدمة حسب الحاجة.
