{"ast": null, "code": "import ascending from \"./ascending.js\";\nimport maxIndex from \"./maxIndex.js\";\nexport default function greatestIndex(values, compare = ascending) {\n  if (compare.length === 1) return maxIndex(values, compare);\n  let maxValue;\n  let max = -1;\n  let index = -1;\n  for (const value of values) {\n    ++index;\n    if (max < 0 ? compare(value, value) === 0 : compare(value, maxValue) > 0) {\n      maxValue = value;\n      max = index;\n    }\n  }\n  return max;\n}", "map": {"version": 3, "names": ["ascending", "maxIndex", "greatestIndex", "values", "compare", "length", "maxValue", "max", "index", "value"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/node_modules/d3-array/src/greatestIndex.js"], "sourcesContent": ["import ascending from \"./ascending.js\";\nimport maxIndex from \"./maxIndex.js\";\n\nexport default function greatestIndex(values, compare = ascending) {\n  if (compare.length === 1) return maxIndex(values, compare);\n  let maxValue;\n  let max = -1;\n  let index = -1;\n  for (const value of values) {\n    ++index;\n    if (max < 0\n        ? compare(value, value) === 0\n        : compare(value, maxValue) > 0) {\n      maxValue = value;\n      max = index;\n    }\n  }\n  return max;\n}\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;AACtC,OAAOC,QAAQ,MAAM,eAAe;AAEpC,eAAe,SAASC,aAAaA,CAACC,MAAM,EAAEC,OAAO,GAAGJ,SAAS,EAAE;EACjE,IAAII,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE,OAAOJ,QAAQ,CAACE,MAAM,EAAEC,OAAO,CAAC;EAC1D,IAAIE,QAAQ;EACZ,IAAIC,GAAG,GAAG,CAAC,CAAC;EACZ,IAAIC,KAAK,GAAG,CAAC,CAAC;EACd,KAAK,MAAMC,KAAK,IAAIN,MAAM,EAAE;IAC1B,EAAEK,KAAK;IACP,IAAID,GAAG,GAAG,CAAC,GACLH,OAAO,CAACK,KAAK,EAAEA,KAAK,CAAC,KAAK,CAAC,GAC3BL,OAAO,CAACK,KAAK,EAAEH,QAAQ,CAAC,GAAG,CAAC,EAAE;MAClCA,QAAQ,GAAGG,KAAK;MAChBF,GAAG,GAAGC,KAAK;IACb;EACF;EACA,OAAOD,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}