// API لإدارة المبيعات
class SalesAPI {
  constructor() {
    this.storageKey = 'sales_data';
    this.initializeData();
  }

  initializeData() {
    const existingData = localStorage.getItem(this.storageKey);
    if (!existingData) {
      const initialData = {
        sales: [
          {
            id: 1,
            invoiceNumber: 'INV-2024-001',
            customerId: 1,
            customerName: 'أحمد محمد',
            date: '2024-01-15',
            items: [
              { productId: 1, productName: 'لابتوب Dell', quantity: 2, price: 3500, total: 7000 },
              { productId: 2, productName: 'ماوس لاسلكي', quantity: 5, price: 150, total: 750 }
            ],
            subtotal: 7750,
            tax: 1162.5,
            discount: 200,
            total: 8712.5,
            status: 'مكتملة',
            paymentMethod: 'نقدي',
            notes: 'عميل مميز - خصم خاص'
          },
          {
            id: 2,
            invoiceNumber: 'INV-2024-002',
            customerId: 2,
            customerName: 'فاطمة أحمد',
            date: '2024-01-16',
            items: [
              { productId: 3, productName: 'طابعة HP', quantity: 1, price: 800, total: 800 }
            ],
            subtotal: 800,
            tax: 120,
            discount: 0,
            total: 920,
            status: 'معلقة',
            paymentMethod: 'آجل',
            notes: ''
          }
        ],
        lastId: 2
      };
      localStorage.setItem(this.storageKey, JSON.stringify(initialData));
    }
  }

  getData() {
    return JSON.parse(localStorage.getItem(this.storageKey));
  }

  saveData(data) {
    localStorage.setItem(this.storageKey, JSON.stringify(data));
  }

  // الحصول على جميع المبيعات
  getAllSales() {
    return this.getData().sales;
  }

  // إضافة فاتورة مبيعات جديدة
  addSale(saleData) {
    const data = this.getData();
    const newSale = {
      id: data.lastId + 1,
      invoiceNumber: `INV-${new Date().getFullYear()}-${String(data.lastId + 1).padStart(3, '0')}`,
      date: new Date().toISOString().split('T')[0],
      status: 'مكتملة',
      ...saleData
    };
    
    data.sales.push(newSale);
    data.lastId += 1;
    this.saveData(data);
    return newSale;
  }

  // تحديث فاتورة مبيعات
  updateSale(id, saleData) {
    const data = this.getData();
    const index = data.sales.findIndex(sale => sale.id === id);
    if (index !== -1) {
      data.sales[index] = { ...data.sales[index], ...saleData };
      this.saveData(data);
      return data.sales[index];
    }
    return null;
  }

  // حذف فاتورة مبيعات
  deleteSale(id) {
    const data = this.getData();
    data.sales = data.sales.filter(sale => sale.id !== id);
    this.saveData(data);
    return true;
  }

  // الحصول على فاتورة بالمعرف
  getSaleById(id) {
    const data = this.getData();
    return data.sales.find(sale => sale.id === id);
  }

  // إحصائيات المبيعات
  getSalesStats() {
    const sales = this.getAllSales();
    const today = new Date().toISOString().split('T')[0];
    const thisMonth = new Date().toISOString().slice(0, 7);
    
    return {
      totalSales: sales.reduce((sum, sale) => sum + sale.total, 0),
      todaySales: sales.filter(sale => sale.date === today).reduce((sum, sale) => sum + sale.total, 0),
      monthSales: sales.filter(sale => sale.date.startsWith(thisMonth)).reduce((sum, sale) => sum + sale.total, 0),
      totalInvoices: sales.length,
      completedSales: sales.filter(sale => sale.status === 'مكتملة').length,
      pendingSales: sales.filter(sale => sale.status === 'معلقة').length
    };
  }

  // البحث في المبيعات
  searchSales(query) {
    const sales = this.getAllSales();
    return sales.filter(sale => 
      sale.invoiceNumber.toLowerCase().includes(query.toLowerCase()) ||
      sale.customerName.toLowerCase().includes(query.toLowerCase()) ||
      sale.status.toLowerCase().includes(query.toLowerCase())
    );
  }
}

export default new SalesAPI();
