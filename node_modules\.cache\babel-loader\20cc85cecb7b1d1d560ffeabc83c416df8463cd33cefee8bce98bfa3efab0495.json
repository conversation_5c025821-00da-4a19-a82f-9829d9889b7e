{"ast": null, "code": "export class InternMap extends Map {\n  constructor(entries, key = keyof) {\n    super();\n    Object.defineProperties(this, {\n      _intern: {\n        value: new Map()\n      },\n      _key: {\n        value: key\n      }\n    });\n    if (entries != null) for (const [key, value] of entries) this.set(key, value);\n  }\n  get(key) {\n    return super.get(intern_get(this, key));\n  }\n  has(key) {\n    return super.has(intern_get(this, key));\n  }\n  set(key, value) {\n    return super.set(intern_set(this, key), value);\n  }\n  delete(key) {\n    return super.delete(intern_delete(this, key));\n  }\n}\nexport class InternSet extends Set {\n  constructor(values, key = keyof) {\n    super();\n    Object.defineProperties(this, {\n      _intern: {\n        value: new Map()\n      },\n      _key: {\n        value: key\n      }\n    });\n    if (values != null) for (const value of values) this.add(value);\n  }\n  has(value) {\n    return super.has(intern_get(this, value));\n  }\n  add(value) {\n    return super.add(intern_set(this, value));\n  }\n  delete(value) {\n    return super.delete(intern_delete(this, value));\n  }\n}\nfunction intern_get({\n  _intern,\n  _key\n}, value) {\n  const key = _key(value);\n  return _intern.has(key) ? _intern.get(key) : value;\n}\nfunction intern_set({\n  _intern,\n  _key\n}, value) {\n  const key = _key(value);\n  if (_intern.has(key)) return _intern.get(key);\n  _intern.set(key, value);\n  return value;\n}\nfunction intern_delete({\n  _intern,\n  _key\n}, value) {\n  const key = _key(value);\n  if (_intern.has(key)) {\n    value = _intern.get(key);\n    _intern.delete(key);\n  }\n  return value;\n}\nfunction keyof(value) {\n  return value !== null && typeof value === \"object\" ? value.valueOf() : value;\n}", "map": {"version": 3, "names": ["InternMap", "Map", "constructor", "entries", "key", "keyof", "Object", "defineProperties", "_intern", "value", "_key", "set", "get", "intern_get", "has", "intern_set", "delete", "intern_delete", "InternSet", "Set", "values", "add", "valueOf"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/node_modules/internmap/src/index.js"], "sourcesContent": ["export class InternMap extends Map {\n  constructor(entries, key = keyof) {\n    super();\n    Object.defineProperties(this, {_intern: {value: new Map()}, _key: {value: key}});\n    if (entries != null) for (const [key, value] of entries) this.set(key, value);\n  }\n  get(key) {\n    return super.get(intern_get(this, key));\n  }\n  has(key) {\n    return super.has(intern_get(this, key));\n  }\n  set(key, value) {\n    return super.set(intern_set(this, key), value);\n  }\n  delete(key) {\n    return super.delete(intern_delete(this, key));\n  }\n}\n\nexport class InternSet extends Set {\n  constructor(values, key = keyof) {\n    super();\n    Object.defineProperties(this, {_intern: {value: new Map()}, _key: {value: key}});\n    if (values != null) for (const value of values) this.add(value);\n  }\n  has(value) {\n    return super.has(intern_get(this, value));\n  }\n  add(value) {\n    return super.add(intern_set(this, value));\n  }\n  delete(value) {\n    return super.delete(intern_delete(this, value));\n  }\n}\n\nfunction intern_get({_intern, _key}, value) {\n  const key = _key(value);\n  return _intern.has(key) ? _intern.get(key) : value;\n}\n\nfunction intern_set({_intern, _key}, value) {\n  const key = _key(value);\n  if (_intern.has(key)) return _intern.get(key);\n  _intern.set(key, value);\n  return value;\n}\n\nfunction intern_delete({_intern, _key}, value) {\n  const key = _key(value);\n  if (_intern.has(key)) {\n    value = _intern.get(key);\n    _intern.delete(key);\n  }\n  return value;\n}\n\nfunction keyof(value) {\n  return value !== null && typeof value === \"object\" ? value.valueOf() : value;\n}\n"], "mappings": "AAAA,OAAO,MAAMA,SAAS,SAASC,GAAG,CAAC;EACjCC,WAAWA,CAACC,OAAO,EAAEC,GAAG,GAAGC,KAAK,EAAE;IAChC,KAAK,CAAC,CAAC;IACPC,MAAM,CAACC,gBAAgB,CAAC,IAAI,EAAE;MAACC,OAAO,EAAE;QAACC,KAAK,EAAE,IAAIR,GAAG,CAAC;MAAC,CAAC;MAAES,IAAI,EAAE;QAACD,KAAK,EAAEL;MAAG;IAAC,CAAC,CAAC;IAChF,IAAID,OAAO,IAAI,IAAI,EAAE,KAAK,MAAM,CAACC,GAAG,EAAEK,KAAK,CAAC,IAAIN,OAAO,EAAE,IAAI,CAACQ,GAAG,CAACP,GAAG,EAAEK,KAAK,CAAC;EAC/E;EACAG,GAAGA,CAACR,GAAG,EAAE;IACP,OAAO,KAAK,CAACQ,GAAG,CAACC,UAAU,CAAC,IAAI,EAAET,GAAG,CAAC,CAAC;EACzC;EACAU,GAAGA,CAACV,GAAG,EAAE;IACP,OAAO,KAAK,CAACU,GAAG,CAACD,UAAU,CAAC,IAAI,EAAET,GAAG,CAAC,CAAC;EACzC;EACAO,GAAGA,CAACP,GAAG,EAAEK,KAAK,EAAE;IACd,OAAO,KAAK,CAACE,GAAG,CAACI,UAAU,CAAC,IAAI,EAAEX,GAAG,CAAC,EAAEK,KAAK,CAAC;EAChD;EACAO,MAAMA,CAACZ,GAAG,EAAE;IACV,OAAO,KAAK,CAACY,MAAM,CAACC,aAAa,CAAC,IAAI,EAAEb,GAAG,CAAC,CAAC;EAC/C;AACF;AAEA,OAAO,MAAMc,SAAS,SAASC,GAAG,CAAC;EACjCjB,WAAWA,CAACkB,MAAM,EAAEhB,GAAG,GAAGC,KAAK,EAAE;IAC/B,KAAK,CAAC,CAAC;IACPC,MAAM,CAACC,gBAAgB,CAAC,IAAI,EAAE;MAACC,OAAO,EAAE;QAACC,KAAK,EAAE,IAAIR,GAAG,CAAC;MAAC,CAAC;MAAES,IAAI,EAAE;QAACD,KAAK,EAAEL;MAAG;IAAC,CAAC,CAAC;IAChF,IAAIgB,MAAM,IAAI,IAAI,EAAE,KAAK,MAAMX,KAAK,IAAIW,MAAM,EAAE,IAAI,CAACC,GAAG,CAACZ,KAAK,CAAC;EACjE;EACAK,GAAGA,CAACL,KAAK,EAAE;IACT,OAAO,KAAK,CAACK,GAAG,CAACD,UAAU,CAAC,IAAI,EAAEJ,KAAK,CAAC,CAAC;EAC3C;EACAY,GAAGA,CAACZ,KAAK,EAAE;IACT,OAAO,KAAK,CAACY,GAAG,CAACN,UAAU,CAAC,IAAI,EAAEN,KAAK,CAAC,CAAC;EAC3C;EACAO,MAAMA,CAACP,KAAK,EAAE;IACZ,OAAO,KAAK,CAACO,MAAM,CAACC,aAAa,CAAC,IAAI,EAAER,KAAK,CAAC,CAAC;EACjD;AACF;AAEA,SAASI,UAAUA,CAAC;EAACL,OAAO;EAAEE;AAAI,CAAC,EAAED,KAAK,EAAE;EAC1C,MAAML,GAAG,GAAGM,IAAI,CAACD,KAAK,CAAC;EACvB,OAAOD,OAAO,CAACM,GAAG,CAACV,GAAG,CAAC,GAAGI,OAAO,CAACI,GAAG,CAACR,GAAG,CAAC,GAAGK,KAAK;AACpD;AAEA,SAASM,UAAUA,CAAC;EAACP,OAAO;EAAEE;AAAI,CAAC,EAAED,KAAK,EAAE;EAC1C,MAAML,GAAG,GAAGM,IAAI,CAACD,KAAK,CAAC;EACvB,IAAID,OAAO,CAACM,GAAG,CAACV,GAAG,CAAC,EAAE,OAAOI,OAAO,CAACI,GAAG,CAACR,GAAG,CAAC;EAC7CI,OAAO,CAACG,GAAG,CAACP,GAAG,EAAEK,KAAK,CAAC;EACvB,OAAOA,KAAK;AACd;AAEA,SAASQ,aAAaA,CAAC;EAACT,OAAO;EAAEE;AAAI,CAAC,EAAED,KAAK,EAAE;EAC7C,MAAML,GAAG,GAAGM,IAAI,CAACD,KAAK,CAAC;EACvB,IAAID,OAAO,CAACM,GAAG,CAACV,GAAG,CAAC,EAAE;IACpBK,KAAK,GAAGD,OAAO,CAACI,GAAG,CAACR,GAAG,CAAC;IACxBI,OAAO,CAACQ,MAAM,CAACZ,GAAG,CAAC;EACrB;EACA,OAAOK,KAAK;AACd;AAEA,SAASJ,KAAKA,CAACI,KAAK,EAAE;EACpB,OAAOA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAACa,OAAO,CAAC,CAAC,GAAGb,KAAK;AAC9E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}