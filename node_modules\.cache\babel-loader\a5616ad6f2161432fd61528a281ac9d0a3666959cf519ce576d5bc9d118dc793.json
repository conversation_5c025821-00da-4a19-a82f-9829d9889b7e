{"ast": null, "code": "export default function mean(values, valueof) {\n  let count = 0;\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        ++count, sum += value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        ++count, sum += value;\n      }\n    }\n  }\n  if (count) return sum / count;\n}", "map": {"version": 3, "names": ["mean", "values", "valueof", "count", "sum", "undefined", "value", "index"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/node_modules/d3-array/src/mean.js"], "sourcesContent": ["export default function mean(values, valueof) {\n  let count = 0;\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        ++count, sum += value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        ++count, sum += value;\n      }\n    }\n  }\n  if (count) return sum / count;\n}\n"], "mappings": "AAAA,eAAe,SAASA,IAAIA,CAACC,MAAM,EAAEC,OAAO,EAAE;EAC5C,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,GAAG,GAAG,CAAC;EACX,IAAIF,OAAO,KAAKG,SAAS,EAAE;IACzB,KAAK,IAAIC,KAAK,IAAIL,MAAM,EAAE;MACxB,IAAIK,KAAK,IAAI,IAAI,IAAI,CAACA,KAAK,GAAG,CAACA,KAAK,KAAKA,KAAK,EAAE;QAC9C,EAAEH,KAAK,EAAEC,GAAG,IAAIE,KAAK;MACvB;IACF;EACF,CAAC,MAAM;IACL,IAAIC,KAAK,GAAG,CAAC,CAAC;IACd,KAAK,IAAID,KAAK,IAAIL,MAAM,EAAE;MACxB,IAAI,CAACK,KAAK,GAAGJ,OAAO,CAACI,KAAK,EAAE,EAAEC,KAAK,EAAEN,MAAM,CAAC,KAAK,IAAI,IAAI,CAACK,KAAK,GAAG,CAACA,KAAK,KAAKA,KAAK,EAAE;QAClF,EAAEH,KAAK,EAAEC,GAAG,IAAIE,KAAK;MACvB;IACF;EACF;EACA,IAAIH,KAAK,EAAE,OAAOC,GAAG,GAAGD,KAAK;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}