{"ast": null, "code": "import { clsx } from \"clsx\";\n\n/**\n * Utility function to merge class names\n * @param {...any} inputs - Class names to merge\n * @returns {string} Merged class names\n */\nexport function cn(...inputs) {\n  return clsx(inputs);\n}", "map": {"version": 3, "names": ["clsx", "cn", "inputs"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/src/lib/utils.js"], "sourcesContent": ["import { clsx } from \"clsx\";\n\n/**\n * Utility function to merge class names\n * @param {...any} inputs - Class names to merge\n * @returns {string} Merged class names\n */\nexport function cn(...inputs) {\n  return clsx(inputs);\n}\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,MAAM;;AAE3B;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,EAAEA,CAAC,GAAGC,MAAM,EAAE;EAC5B,OAAOF,IAAI,CAACE,MAAM,CAAC;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}