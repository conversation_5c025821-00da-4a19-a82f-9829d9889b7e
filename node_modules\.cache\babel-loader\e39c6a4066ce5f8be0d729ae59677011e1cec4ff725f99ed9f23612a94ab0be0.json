{"ast": null, "code": "import { slice } from \"./array.js\";\nimport bisect from \"./bisect.js\";\nimport constant from \"./constant.js\";\nimport extent from \"./extent.js\";\nimport identity from \"./identity.js\";\nimport nice from \"./nice.js\";\nimport ticks, { tickIncrement } from \"./ticks.js\";\nimport sturges from \"./threshold/sturges.js\";\nexport default function bin() {\n  var value = identity,\n    domain = extent,\n    threshold = sturges;\n  function histogram(data) {\n    if (!Array.isArray(data)) data = Array.from(data);\n    var i,\n      n = data.length,\n      x,\n      step,\n      values = new Array(n);\n    for (i = 0; i < n; ++i) {\n      values[i] = value(data[i], i, data);\n    }\n    var xz = domain(values),\n      x0 = xz[0],\n      x1 = xz[1],\n      tz = threshold(values, x0, x1);\n\n    // Convert number of thresholds into uniform thresholds, and nice the\n    // default domain accordingly.\n    if (!Array.isArray(tz)) {\n      const max = x1,\n        tn = +tz;\n      if (domain === extent) [x0, x1] = nice(x0, x1, tn);\n      tz = ticks(x0, x1, tn);\n\n      // If the domain is aligned with the first tick (which it will by\n      // default), then we can use quantization rather than bisection to bin\n      // values, which is substantially faster.\n      if (tz[0] <= x0) step = tickIncrement(x0, x1, tn);\n\n      // If the last threshold is coincident with the domain’s upper bound, the\n      // last bin will be zero-width. If the default domain is used, and this\n      // last threshold is coincident with the maximum input value, we can\n      // extend the niced upper bound by one tick to ensure uniform bin widths;\n      // otherwise, we simply remove the last threshold. Note that we don’t\n      // coerce values or the domain to numbers, and thus must be careful to\n      // compare order (>=) rather than strict equality (===)!\n      if (tz[tz.length - 1] >= x1) {\n        if (max >= x1 && domain === extent) {\n          const step = tickIncrement(x0, x1, tn);\n          if (isFinite(step)) {\n            if (step > 0) {\n              x1 = (Math.floor(x1 / step) + 1) * step;\n            } else if (step < 0) {\n              x1 = (Math.ceil(x1 * -step) + 1) / -step;\n            }\n          }\n        } else {\n          tz.pop();\n        }\n      }\n    }\n\n    // Remove any thresholds outside the domain.\n    // Be careful not to mutate an array owned by the user!\n    var m = tz.length,\n      a = 0,\n      b = m;\n    while (tz[a] <= x0) ++a;\n    while (tz[b - 1] > x1) --b;\n    if (a || b < m) tz = tz.slice(a, b), m = b - a;\n    var bins = new Array(m + 1),\n      bin;\n\n    // Initialize bins.\n    for (i = 0; i <= m; ++i) {\n      bin = bins[i] = [];\n      bin.x0 = i > 0 ? tz[i - 1] : x0;\n      bin.x1 = i < m ? tz[i] : x1;\n    }\n\n    // Assign data to bins by value, ignoring any outside the domain.\n    if (isFinite(step)) {\n      if (step > 0) {\n        for (i = 0; i < n; ++i) {\n          if ((x = values[i]) != null && x0 <= x && x <= x1) {\n            bins[Math.min(m, Math.floor((x - x0) / step))].push(data[i]);\n          }\n        }\n      } else if (step < 0) {\n        for (i = 0; i < n; ++i) {\n          if ((x = values[i]) != null && x0 <= x && x <= x1) {\n            const j = Math.floor((x0 - x) * step);\n            bins[Math.min(m, j + (tz[j] <= x))].push(data[i]); // handle off-by-one due to rounding\n          }\n        }\n      }\n    } else {\n      for (i = 0; i < n; ++i) {\n        if ((x = values[i]) != null && x0 <= x && x <= x1) {\n          bins[bisect(tz, x, 0, m)].push(data[i]);\n        }\n      }\n    }\n    return bins;\n  }\n  histogram.value = function (_) {\n    return arguments.length ? (value = typeof _ === \"function\" ? _ : constant(_), histogram) : value;\n  };\n  histogram.domain = function (_) {\n    return arguments.length ? (domain = typeof _ === \"function\" ? _ : constant([_[0], _[1]]), histogram) : domain;\n  };\n  histogram.thresholds = function (_) {\n    return arguments.length ? (threshold = typeof _ === \"function\" ? _ : constant(Array.isArray(_) ? slice.call(_) : _), histogram) : threshold;\n  };\n  return histogram;\n}", "map": {"version": 3, "names": ["slice", "bisect", "constant", "extent", "identity", "nice", "ticks", "tickIncrement", "sturges", "bin", "value", "domain", "threshold", "histogram", "data", "Array", "isArray", "from", "i", "n", "length", "x", "step", "values", "xz", "x0", "x1", "tz", "max", "tn", "isFinite", "Math", "floor", "ceil", "pop", "m", "a", "b", "bins", "min", "push", "j", "_", "arguments", "thresholds", "call"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/node_modules/d3-array/src/bin.js"], "sourcesContent": ["import {slice} from \"./array.js\";\nimport bisect from \"./bisect.js\";\nimport constant from \"./constant.js\";\nimport extent from \"./extent.js\";\nimport identity from \"./identity.js\";\nimport nice from \"./nice.js\";\nimport ticks, {tickIncrement} from \"./ticks.js\";\nimport sturges from \"./threshold/sturges.js\";\n\nexport default function bin() {\n  var value = identity,\n      domain = extent,\n      threshold = sturges;\n\n  function histogram(data) {\n    if (!Array.isArray(data)) data = Array.from(data);\n\n    var i,\n        n = data.length,\n        x,\n        step,\n        values = new Array(n);\n\n    for (i = 0; i < n; ++i) {\n      values[i] = value(data[i], i, data);\n    }\n\n    var xz = domain(values),\n        x0 = xz[0],\n        x1 = xz[1],\n        tz = threshold(values, x0, x1);\n\n    // Convert number of thresholds into uniform thresholds, and nice the\n    // default domain accordingly.\n    if (!Array.isArray(tz)) {\n      const max = x1, tn = +tz;\n      if (domain === extent) [x0, x1] = nice(x0, x1, tn);\n      tz = ticks(x0, x1, tn);\n\n      // If the domain is aligned with the first tick (which it will by\n      // default), then we can use quantization rather than bisection to bin\n      // values, which is substantially faster.\n      if (tz[0] <= x0) step = tickIncrement(x0, x1, tn);\n\n      // If the last threshold is coincident with the domain’s upper bound, the\n      // last bin will be zero-width. If the default domain is used, and this\n      // last threshold is coincident with the maximum input value, we can\n      // extend the niced upper bound by one tick to ensure uniform bin widths;\n      // otherwise, we simply remove the last threshold. Note that we don’t\n      // coerce values or the domain to numbers, and thus must be careful to\n      // compare order (>=) rather than strict equality (===)!\n      if (tz[tz.length - 1] >= x1) {\n        if (max >= x1 && domain === extent) {\n          const step = tickIncrement(x0, x1, tn);\n          if (isFinite(step)) {\n            if (step > 0) {\n              x1 = (Math.floor(x1 / step) + 1) * step;\n            } else if (step < 0) {\n              x1 = (Math.ceil(x1 * -step) + 1) / -step;\n            }\n          }\n        } else {\n          tz.pop();\n        }\n      }\n    }\n\n    // Remove any thresholds outside the domain.\n    // Be careful not to mutate an array owned by the user!\n    var m = tz.length, a = 0, b = m;\n    while (tz[a] <= x0) ++a;\n    while (tz[b - 1] > x1) --b;\n    if (a || b < m) tz = tz.slice(a, b), m = b - a;\n\n    var bins = new Array(m + 1),\n        bin;\n\n    // Initialize bins.\n    for (i = 0; i <= m; ++i) {\n      bin = bins[i] = [];\n      bin.x0 = i > 0 ? tz[i - 1] : x0;\n      bin.x1 = i < m ? tz[i] : x1;\n    }\n\n    // Assign data to bins by value, ignoring any outside the domain.\n    if (isFinite(step)) {\n      if (step > 0) {\n        for (i = 0; i < n; ++i) {\n          if ((x = values[i]) != null && x0 <= x && x <= x1) {\n            bins[Math.min(m, Math.floor((x - x0) / step))].push(data[i]);\n          }\n        }\n      } else if (step < 0) {\n        for (i = 0; i < n; ++i) {\n          if ((x = values[i]) != null && x0 <= x && x <= x1) {\n            const j = Math.floor((x0 - x) * step);\n            bins[Math.min(m, j + (tz[j] <= x))].push(data[i]); // handle off-by-one due to rounding\n          }\n        }\n      }\n    } else {\n      for (i = 0; i < n; ++i) {\n        if ((x = values[i]) != null && x0 <= x && x <= x1) {\n          bins[bisect(tz, x, 0, m)].push(data[i]);\n        }\n      }\n    }\n\n    return bins;\n  }\n\n  histogram.value = function(_) {\n    return arguments.length ? (value = typeof _ === \"function\" ? _ : constant(_), histogram) : value;\n  };\n\n  histogram.domain = function(_) {\n    return arguments.length ? (domain = typeof _ === \"function\" ? _ : constant([_[0], _[1]]), histogram) : domain;\n  };\n\n  histogram.thresholds = function(_) {\n    return arguments.length ? (threshold = typeof _ === \"function\" ? _ : constant(Array.isArray(_) ? slice.call(_) : _), histogram) : threshold;\n  };\n\n  return histogram;\n}\n"], "mappings": "AAAA,SAAQA,KAAK,QAAO,YAAY;AAChC,OAAOC,MAAM,MAAM,aAAa;AAChC,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,MAAM,MAAM,aAAa;AAChC,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,IAAI,MAAM,WAAW;AAC5B,OAAOC,KAAK,IAAGC,aAAa,QAAO,YAAY;AAC/C,OAAOC,OAAO,MAAM,wBAAwB;AAE5C,eAAe,SAASC,GAAGA,CAAA,EAAG;EAC5B,IAAIC,KAAK,GAAGN,QAAQ;IAChBO,MAAM,GAAGR,MAAM;IACfS,SAAS,GAAGJ,OAAO;EAEvB,SAASK,SAASA,CAACC,IAAI,EAAE;IACvB,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAEA,IAAI,GAAGC,KAAK,CAACE,IAAI,CAACH,IAAI,CAAC;IAEjD,IAAII,CAAC;MACDC,CAAC,GAAGL,IAAI,CAACM,MAAM;MACfC,CAAC;MACDC,IAAI;MACJC,MAAM,GAAG,IAAIR,KAAK,CAACI,CAAC,CAAC;IAEzB,KAAKD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;MACtBK,MAAM,CAACL,CAAC,CAAC,GAAGR,KAAK,CAACI,IAAI,CAACI,CAAC,CAAC,EAAEA,CAAC,EAAEJ,IAAI,CAAC;IACrC;IAEA,IAAIU,EAAE,GAAGb,MAAM,CAACY,MAAM,CAAC;MACnBE,EAAE,GAAGD,EAAE,CAAC,CAAC,CAAC;MACVE,EAAE,GAAGF,EAAE,CAAC,CAAC,CAAC;MACVG,EAAE,GAAGf,SAAS,CAACW,MAAM,EAAEE,EAAE,EAAEC,EAAE,CAAC;;IAElC;IACA;IACA,IAAI,CAACX,KAAK,CAACC,OAAO,CAACW,EAAE,CAAC,EAAE;MACtB,MAAMC,GAAG,GAAGF,EAAE;QAAEG,EAAE,GAAG,CAACF,EAAE;MACxB,IAAIhB,MAAM,KAAKR,MAAM,EAAE,CAACsB,EAAE,EAAEC,EAAE,CAAC,GAAGrB,IAAI,CAACoB,EAAE,EAAEC,EAAE,EAAEG,EAAE,CAAC;MAClDF,EAAE,GAAGrB,KAAK,CAACmB,EAAE,EAAEC,EAAE,EAAEG,EAAE,CAAC;;MAEtB;MACA;MACA;MACA,IAAIF,EAAE,CAAC,CAAC,CAAC,IAAIF,EAAE,EAAEH,IAAI,GAAGf,aAAa,CAACkB,EAAE,EAAEC,EAAE,EAAEG,EAAE,CAAC;;MAEjD;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAIF,EAAE,CAACA,EAAE,CAACP,MAAM,GAAG,CAAC,CAAC,IAAIM,EAAE,EAAE;QAC3B,IAAIE,GAAG,IAAIF,EAAE,IAAIf,MAAM,KAAKR,MAAM,EAAE;UAClC,MAAMmB,IAAI,GAAGf,aAAa,CAACkB,EAAE,EAAEC,EAAE,EAAEG,EAAE,CAAC;UACtC,IAAIC,QAAQ,CAACR,IAAI,CAAC,EAAE;YAClB,IAAIA,IAAI,GAAG,CAAC,EAAE;cACZI,EAAE,GAAG,CAACK,IAAI,CAACC,KAAK,CAACN,EAAE,GAAGJ,IAAI,CAAC,GAAG,CAAC,IAAIA,IAAI;YACzC,CAAC,MAAM,IAAIA,IAAI,GAAG,CAAC,EAAE;cACnBI,EAAE,GAAG,CAACK,IAAI,CAACE,IAAI,CAACP,EAAE,GAAG,CAACJ,IAAI,CAAC,GAAG,CAAC,IAAI,CAACA,IAAI;YAC1C;UACF;QACF,CAAC,MAAM;UACLK,EAAE,CAACO,GAAG,CAAC,CAAC;QACV;MACF;IACF;;IAEA;IACA;IACA,IAAIC,CAAC,GAAGR,EAAE,CAACP,MAAM;MAAEgB,CAAC,GAAG,CAAC;MAAEC,CAAC,GAAGF,CAAC;IAC/B,OAAOR,EAAE,CAACS,CAAC,CAAC,IAAIX,EAAE,EAAE,EAAEW,CAAC;IACvB,OAAOT,EAAE,CAACU,CAAC,GAAG,CAAC,CAAC,GAAGX,EAAE,EAAE,EAAEW,CAAC;IAC1B,IAAID,CAAC,IAAIC,CAAC,GAAGF,CAAC,EAAER,EAAE,GAAGA,EAAE,CAAC3B,KAAK,CAACoC,CAAC,EAAEC,CAAC,CAAC,EAAEF,CAAC,GAAGE,CAAC,GAAGD,CAAC;IAE9C,IAAIE,IAAI,GAAG,IAAIvB,KAAK,CAACoB,CAAC,GAAG,CAAC,CAAC;MACvB1B,GAAG;;IAEP;IACA,KAAKS,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIiB,CAAC,EAAE,EAAEjB,CAAC,EAAE;MACvBT,GAAG,GAAG6B,IAAI,CAACpB,CAAC,CAAC,GAAG,EAAE;MAClBT,GAAG,CAACgB,EAAE,GAAGP,CAAC,GAAG,CAAC,GAAGS,EAAE,CAACT,CAAC,GAAG,CAAC,CAAC,GAAGO,EAAE;MAC/BhB,GAAG,CAACiB,EAAE,GAAGR,CAAC,GAAGiB,CAAC,GAAGR,EAAE,CAACT,CAAC,CAAC,GAAGQ,EAAE;IAC7B;;IAEA;IACA,IAAII,QAAQ,CAACR,IAAI,CAAC,EAAE;MAClB,IAAIA,IAAI,GAAG,CAAC,EAAE;QACZ,KAAKJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;UACtB,IAAI,CAACG,CAAC,GAAGE,MAAM,CAACL,CAAC,CAAC,KAAK,IAAI,IAAIO,EAAE,IAAIJ,CAAC,IAAIA,CAAC,IAAIK,EAAE,EAAE;YACjDY,IAAI,CAACP,IAAI,CAACQ,GAAG,CAACJ,CAAC,EAAEJ,IAAI,CAACC,KAAK,CAAC,CAACX,CAAC,GAAGI,EAAE,IAAIH,IAAI,CAAC,CAAC,CAAC,CAACkB,IAAI,CAAC1B,IAAI,CAACI,CAAC,CAAC,CAAC;UAC9D;QACF;MACF,CAAC,MAAM,IAAII,IAAI,GAAG,CAAC,EAAE;QACnB,KAAKJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;UACtB,IAAI,CAACG,CAAC,GAAGE,MAAM,CAACL,CAAC,CAAC,KAAK,IAAI,IAAIO,EAAE,IAAIJ,CAAC,IAAIA,CAAC,IAAIK,EAAE,EAAE;YACjD,MAAMe,CAAC,GAAGV,IAAI,CAACC,KAAK,CAAC,CAACP,EAAE,GAAGJ,CAAC,IAAIC,IAAI,CAAC;YACrCgB,IAAI,CAACP,IAAI,CAACQ,GAAG,CAACJ,CAAC,EAAEM,CAAC,IAAId,EAAE,CAACc,CAAC,CAAC,IAAIpB,CAAC,CAAC,CAAC,CAAC,CAACmB,IAAI,CAAC1B,IAAI,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC;UACrD;QACF;MACF;IACF,CAAC,MAAM;MACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;QACtB,IAAI,CAACG,CAAC,GAAGE,MAAM,CAACL,CAAC,CAAC,KAAK,IAAI,IAAIO,EAAE,IAAIJ,CAAC,IAAIA,CAAC,IAAIK,EAAE,EAAE;UACjDY,IAAI,CAACrC,MAAM,CAAC0B,EAAE,EAAEN,CAAC,EAAE,CAAC,EAAEc,CAAC,CAAC,CAAC,CAACK,IAAI,CAAC1B,IAAI,CAACI,CAAC,CAAC,CAAC;QACzC;MACF;IACF;IAEA,OAAOoB,IAAI;EACb;EAEAzB,SAAS,CAACH,KAAK,GAAG,UAASgC,CAAC,EAAE;IAC5B,OAAOC,SAAS,CAACvB,MAAM,IAAIV,KAAK,GAAG,OAAOgC,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGxC,QAAQ,CAACwC,CAAC,CAAC,EAAE7B,SAAS,IAAIH,KAAK;EAClG,CAAC;EAEDG,SAAS,CAACF,MAAM,GAAG,UAAS+B,CAAC,EAAE;IAC7B,OAAOC,SAAS,CAACvB,MAAM,IAAIT,MAAM,GAAG,OAAO+B,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGxC,QAAQ,CAAC,CAACwC,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE7B,SAAS,IAAIF,MAAM;EAC/G,CAAC;EAEDE,SAAS,CAAC+B,UAAU,GAAG,UAASF,CAAC,EAAE;IACjC,OAAOC,SAAS,CAACvB,MAAM,IAAIR,SAAS,GAAG,OAAO8B,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGxC,QAAQ,CAACa,KAAK,CAACC,OAAO,CAAC0B,CAAC,CAAC,GAAG1C,KAAK,CAAC6C,IAAI,CAACH,CAAC,CAAC,GAAGA,CAAC,CAAC,EAAE7B,SAAS,IAAID,SAAS;EAC7I,CAAC;EAED,OAAOC,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}