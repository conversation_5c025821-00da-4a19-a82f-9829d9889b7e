{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst ThermometerSnowflake = createLucideIcon(\"ThermometerSnowflake\", [[\"path\", {\n  d: \"M2 12h10\",\n  key: \"19562f\"\n}], [\"path\", {\n  d: \"M9 4v16\",\n  key: \"81ygyz\"\n}], [\"path\", {\n  d: \"m3 9 3 3-3 3\",\n  key: \"1sas0l\"\n}], [\"path\", {\n  d: \"M12 6 9 9 6 6\",\n  key: \"pfrgxu\"\n}], [\"path\", {\n  d: \"m6 18 3-3 1.5 1.5\",\n  key: \"1e277p\"\n}], [\"path\", {\n  d: \"M20 4v10.54a4 4 0 1 1-4 0V4a2 2 0 0 1 4 0Z\",\n  key: \"iof6y5\"\n}]]);\nexport { ThermometerSnowflake as default };", "map": {"version": 3, "names": ["ThermometerSnowflake", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\333\\node_modules\\lucide-react\\src\\icons\\thermometer-snowflake.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ThermometerSnowflake\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMiAxMmgxMCIgLz4KICA8cGF0aCBkPSJNOSA0djE2IiAvPgogIDxwYXRoIGQ9Im0zIDkgMyAzLTMgMyIgLz4KICA8cGF0aCBkPSJNMTIgNiA5IDkgNiA2IiAvPgogIDxwYXRoIGQ9Im02IDE4IDMtMyAxLjUgMS41IiAvPgogIDxwYXRoIGQ9Ik0yMCA0djEwLjU0YTQgNCAwIDEgMS00IDBWNGEyIDIgMCAwIDEgNCAwWiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/thermometer-snowflake\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ThermometerSnowflake = createLucideIcon('ThermometerSnowflake', [\n  ['path', { d: 'M2 12h10', key: '19562f' }],\n  ['path', { d: 'M9 4v16', key: '81ygyz' }],\n  ['path', { d: 'm3 9 3 3-3 3', key: '1sas0l' }],\n  ['path', { d: 'M12 6 9 9 6 6', key: 'pfrgxu' }],\n  ['path', { d: 'm6 18 3-3 1.5 1.5', key: '1e277p' }],\n  ['path', { d: 'M20 4v10.54a4 4 0 1 1-4 0V4a2 2 0 0 1 4 0Z', key: 'iof6y5' }],\n]);\n\nexport default ThermometerSnowflake;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,oBAAA,GAAuBC,gBAAA,CAAiB,sBAAwB,GACpE,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,mBAAqB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClD,CAAC,MAAQ;EAAED,CAAA,EAAG,4CAA8C;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC5E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}