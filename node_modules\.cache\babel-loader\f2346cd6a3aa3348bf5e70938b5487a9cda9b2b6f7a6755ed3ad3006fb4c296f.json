{"ast": null, "code": "import * as r from \"react\";\nconst s = r.createContext(null);\nfunction a() {\n  return {\n    groups: new Map(),\n    get(n, t) {\n      var c;\n      let e = this.groups.get(n);\n      e || (e = new Map(), this.groups.set(n, e));\n      let l = (c = e.get(t)) != null ? c : 0;\n      e.set(t, l + 1);\n      let o = Array.from(e.keys()).indexOf(t);\n      function i() {\n        let u = e.get(t);\n        u > 1 ? e.set(t, u - 1) : e.delete(t);\n      }\n      return [o, i];\n    }\n  };\n}\nfunction C({\n  children: n\n}) {\n  let t = r.useRef(a());\n  return r.createElement(s.Provider, {\n    value: t\n  }, n);\n}\nfunction d(n) {\n  let t = r.useContext(s);\n  if (!t) throw new Error(\"You must wrap your component in a <StableCollection>\");\n  let e = f(),\n    [l, o] = t.current.get(n, e);\n  return r.useEffect(() => o, []), l;\n}\nfunction f() {\n  var l, o, i;\n  let n = (i = (o = (l = r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED) == null ? void 0 : l.ReactCurrentOwner) == null ? void 0 : o.current) != null ? i : null;\n  if (!n) return Symbol();\n  let t = [],\n    e = n;\n  for (; e;) t.push(e.index), e = e.return;\n  return \"$.\" + t.join(\".\");\n}\nexport { C as StableCollection, d as useStableCollectionIndex };", "map": {"version": 3, "names": ["r", "s", "createContext", "a", "groups", "Map", "get", "n", "t", "c", "e", "set", "l", "o", "Array", "from", "keys", "indexOf", "i", "u", "delete", "C", "children", "useRef", "createElement", "Provider", "value", "d", "useContext", "Error", "f", "current", "useEffect", "__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED", "ReactCurrentOwner", "Symbol", "push", "index", "return", "join", "StableCollection", "useStableCollectionIndex"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/node_modules/@headlessui/react/dist/utils/stable-collection.js"], "sourcesContent": ["import*as r from\"react\";const s=r.createContext(null);function a(){return{groups:new Map,get(n,t){var c;let e=this.groups.get(n);e||(e=new Map,this.groups.set(n,e));let l=(c=e.get(t))!=null?c:0;e.set(t,l+1);let o=Array.from(e.keys()).indexOf(t);function i(){let u=e.get(t);u>1?e.set(t,u-1):e.delete(t)}return[o,i]}}}function C({children:n}){let t=r.useRef(a());return r.createElement(s.Provider,{value:t},n)}function d(n){let t=r.useContext(s);if(!t)throw new Error(\"You must wrap your component in a <StableCollection>\");let e=f(),[l,o]=t.current.get(n,e);return r.useEffect(()=>o,[]),l}function f(){var l,o,i;let n=(i=(o=(l=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED)==null?void 0:l.ReactCurrentOwner)==null?void 0:o.current)!=null?i:null;if(!n)return Symbol();let t=[],e=n;for(;e;)t.push(e.index),e=e.return;return\"$.\"+t.join(\".\")}export{C as StableCollection,d as useStableCollectionIndex};\n"], "mappings": "AAAA,OAAM,KAAIA,CAAC,MAAK,OAAO;AAAC,MAAMC,CAAC,GAACD,CAAC,CAACE,aAAa,CAAC,IAAI,CAAC;AAAC,SAASC,CAACA,CAAA,EAAE;EAAC,OAAM;IAACC,MAAM,EAAC,IAAIC,GAAG,CAAD,CAAC;IAACC,GAAGA,CAACC,CAAC,EAACC,CAAC,EAAC;MAAC,IAAIC,CAAC;MAAC,IAAIC,CAAC,GAAC,IAAI,CAACN,MAAM,CAACE,GAAG,CAACC,CAAC,CAAC;MAACG,CAAC,KAAGA,CAAC,GAAC,IAAIL,GAAG,CAAD,CAAC,EAAC,IAAI,CAACD,MAAM,CAACO,GAAG,CAACJ,CAAC,EAACG,CAAC,CAAC,CAAC;MAAC,IAAIE,CAAC,GAAC,CAACH,CAAC,GAACC,CAAC,CAACJ,GAAG,CAACE,CAAC,CAAC,KAAG,IAAI,GAACC,CAAC,GAAC,CAAC;MAACC,CAAC,CAACC,GAAG,CAACH,CAAC,EAACI,CAAC,GAAC,CAAC,CAAC;MAAC,IAAIC,CAAC,GAACC,KAAK,CAACC,IAAI,CAACL,CAAC,CAACM,IAAI,CAAC,CAAC,CAAC,CAACC,OAAO,CAACT,CAAC,CAAC;MAAC,SAASU,CAACA,CAAA,EAAE;QAAC,IAAIC,CAAC,GAACT,CAAC,CAACJ,GAAG,CAACE,CAAC,CAAC;QAACW,CAAC,GAAC,CAAC,GAACT,CAAC,CAACC,GAAG,CAACH,CAAC,EAACW,CAAC,GAAC,CAAC,CAAC,GAACT,CAAC,CAACU,MAAM,CAACZ,CAAC,CAAC;MAAA;MAAC,OAAM,CAACK,CAAC,EAACK,CAAC,CAAC;IAAA;EAAC,CAAC;AAAA;AAAC,SAASG,CAACA,CAAC;EAACC,QAAQ,EAACf;AAAC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACR,CAAC,CAACuB,MAAM,CAACpB,CAAC,CAAC,CAAC,CAAC;EAAC,OAAOH,CAAC,CAACwB,aAAa,CAACvB,CAAC,CAACwB,QAAQ,EAAC;IAACC,KAAK,EAAClB;EAAC,CAAC,EAACD,CAAC,CAAC;AAAA;AAAC,SAASoB,CAACA,CAACpB,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACR,CAAC,CAAC4B,UAAU,CAAC3B,CAAC,CAAC;EAAC,IAAG,CAACO,CAAC,EAAC,MAAM,IAAIqB,KAAK,CAAC,sDAAsD,CAAC;EAAC,IAAInB,CAAC,GAACoB,CAAC,CAAC,CAAC;IAAC,CAAClB,CAAC,EAACC,CAAC,CAAC,GAACL,CAAC,CAACuB,OAAO,CAACzB,GAAG,CAACC,CAAC,EAACG,CAAC,CAAC;EAAC,OAAOV,CAAC,CAACgC,SAAS,CAAC,MAAInB,CAAC,EAAC,EAAE,CAAC,EAACD,CAAC;AAAA;AAAC,SAASkB,CAACA,CAAA,EAAE;EAAC,IAAIlB,CAAC,EAACC,CAAC,EAACK,CAAC;EAAC,IAAIX,CAAC,GAAC,CAACW,CAAC,GAAC,CAACL,CAAC,GAAC,CAACD,CAAC,GAACZ,CAAC,CAACiC,kDAAkD,KAAG,IAAI,GAAC,KAAK,CAAC,GAACrB,CAAC,CAACsB,iBAAiB,KAAG,IAAI,GAAC,KAAK,CAAC,GAACrB,CAAC,CAACkB,OAAO,KAAG,IAAI,GAACb,CAAC,GAAC,IAAI;EAAC,IAAG,CAACX,CAAC,EAAC,OAAO4B,MAAM,CAAC,CAAC;EAAC,IAAI3B,CAAC,GAAC,EAAE;IAACE,CAAC,GAACH,CAAC;EAAC,OAAKG,CAAC,GAAEF,CAAC,CAAC4B,IAAI,CAAC1B,CAAC,CAAC2B,KAAK,CAAC,EAAC3B,CAAC,GAACA,CAAC,CAAC4B,MAAM;EAAC,OAAM,IAAI,GAAC9B,CAAC,CAAC+B,IAAI,CAAC,GAAG,CAAC;AAAA;AAAC,SAAOlB,CAAC,IAAImB,gBAAgB,EAACb,CAAC,IAAIc,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}