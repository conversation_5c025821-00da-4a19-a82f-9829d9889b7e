{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u0633\\u0637\\u062D \\u0627\\u0644\\u0645\\u0643\\u062A\\u0628\\\\333\\\\src\\\\components\\\\Layout\\\\Sidebar.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { NavLink, useLocation } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { HomeIcon, BuildingOfficeIcon, ShoppingBagIcon, ShoppingCartIcon, CubeIcon, UsersIcon, CurrencyDollarIcon, ChartBarIcon, Cog6ToothIcon, XMarkIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst navigation = [{\n  name: 'لوحة التحكم',\n  href: '/',\n  icon: HomeIcon\n}, {\n  name: 'إدارة الشركات',\n  href: '/companies',\n  icon: BuildingOfficeIcon\n}, {\n  name: 'المبيعات',\n  href: '/sales',\n  icon: ShoppingBagIcon\n}, {\n  name: 'المشتريات',\n  href: '/purchases',\n  icon: ShoppingCartIcon\n}, {\n  name: 'المخزون',\n  href: '/inventory',\n  icon: CubeIcon\n}, {\n  name: 'العملاء',\n  href: '/customers',\n  icon: UsersIcon\n}, {\n  name: 'المصاريف',\n  href: '/expenses',\n  icon: CurrencyDollarIcon\n}, {\n  name: 'التقارير',\n  href: '/reports',\n  icon: ChartBarIcon\n}, {\n  name: 'الإعدادات',\n  href: '/settings',\n  icon: Cog6ToothIcon\n}];\nconst Sidebar = ({\n  onClose\n}) => {\n  _s();\n  const location = useLocation();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-full bg-white shadow-lg\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between h-16 px-6 border-b border-gray-200\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(BuildingOfficeIcon, {\n            className: \"w-5 h-5 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"mr-3 text-lg font-semibold text-gray-900\",\n          children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0634\\u0631\\u0643\\u0627\\u062A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), onClose && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onClose,\n        className: \"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100\",\n        children: /*#__PURE__*/_jsxDEV(XMarkIcon, {\n          className: \"w-6 h-6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"flex-1 px-4 py-6 space-y-2\",\n      children: navigation.map((item, index) => {\n        const isActive = location.pathname === item.href;\n        return /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: -20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            delay: index * 0.1\n          },\n          children: /*#__PURE__*/_jsxDEV(NavLink, {\n            to: item.href,\n            onClick: onClose,\n            className: ({\n              isActive\n            }) => `group flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 ${isActive ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'}`,\n            children: [/*#__PURE__*/_jsxDEV(item.icon, {\n              className: `ml-3 h-5 w-5 transition-colors ${isActive ? 'text-white' : 'text-gray-400 group-hover:text-gray-600'}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 17\n            }, this), item.name, isActive && /*#__PURE__*/_jsxDEV(motion.div, {\n              layoutId: \"activeTab\",\n              className: \"absolute left-0 w-1 h-8 bg-white rounded-r-full\",\n              initial: false,\n              transition: {\n                type: \"spring\",\n                stiffness: 500,\n                damping: 30\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 15\n          }, this)\n        }, item.name, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 border-t border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-medium text-gray-900 mb-1\",\n          children: \"\\u0646\\u0635\\u064A\\u062D\\u0629 \\u0627\\u0644\\u064A\\u0648\\u0645\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-600\",\n          children: \"\\u0627\\u0633\\u062A\\u062E\\u062F\\u0645 \\u0627\\u0644\\u062A\\u0642\\u0627\\u0631\\u064A\\u0631 \\u0644\\u0645\\u062A\\u0627\\u0628\\u0639\\u0629 \\u0623\\u062F\\u0627\\u0621 \\u0634\\u0631\\u0643\\u0627\\u062A\\u0643 \\u0628\\u0634\\u0643\\u0644 \\u062F\\u0648\\u0631\\u064A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n};\n_s(Sidebar, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "NavLink", "useLocation", "motion", "HomeIcon", "BuildingOfficeIcon", "ShoppingBagIcon", "ShoppingCartIcon", "CubeIcon", "UsersIcon", "CurrencyDollarIcon", "ChartBarIcon", "Cog6ToothIcon", "XMarkIcon", "jsxDEV", "_jsxDEV", "navigation", "name", "href", "icon", "Sidebar", "onClose", "_s", "location", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "map", "item", "index", "isActive", "pathname", "div", "initial", "opacity", "x", "animate", "transition", "delay", "to", "layoutId", "type", "stiffness", "damping", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/src/components/Layout/Sidebar.jsx"], "sourcesContent": ["import React from 'react';\nimport { NavLink, useLocation } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport {\n  HomeIcon,\n  BuildingOfficeIcon,\n  ShoppingBagIcon,\n  ShoppingCartIcon,\n  CubeIcon,\n  UsersIcon,\n  CurrencyDollarIcon,\n  ChartBarIcon,\n  Cog6ToothIcon,\n  XMarkIcon\n} from '@heroicons/react/24/outline';\n\nconst navigation = [\n  { name: 'لوحة التحكم', href: '/', icon: HomeIcon },\n  { name: 'إدارة الشركات', href: '/companies', icon: BuildingOfficeIcon },\n  { name: 'المبيعات', href: '/sales', icon: ShoppingBagIcon },\n  { name: 'المشتريات', href: '/purchases', icon: ShoppingCartIcon },\n  { name: 'المخزون', href: '/inventory', icon: CubeIcon },\n  { name: 'العملاء', href: '/customers', icon: UsersIcon },\n  { name: 'المصاريف', href: '/expenses', icon: CurrencyDollarIcon },\n  { name: 'التقارير', href: '/reports', icon: ChartBarIcon },\n  { name: 'الإعدادات', href: '/settings', icon: Cog6ToothIcon },\n];\n\nconst Sidebar = ({ onClose }) => {\n  const location = useLocation();\n\n  return (\n    <div className=\"flex flex-col h-full bg-white shadow-lg\">\n      {/* الشعار */}\n      <div className=\"flex items-center justify-between h-16 px-6 border-b border-gray-200\">\n        <div className=\"flex items-center\">\n          <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center\">\n            <BuildingOfficeIcon className=\"w-5 h-5 text-white\" />\n          </div>\n          <span className=\"mr-3 text-lg font-semibold text-gray-900\">إدارة الشركات</span>\n        </div>\n\n        {/* زر الإغلاق للموبايل */}\n        {onClose && (\n          <button\n            onClick={onClose}\n            className=\"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100\"\n          >\n            <XMarkIcon className=\"w-6 h-6\" />\n          </button>\n        )}\n      </div>\n\n      {/* قائمة التنقل */}\n      <nav className=\"flex-1 px-4 py-6 space-y-2\">\n        {navigation.map((item, index) => {\n          const isActive = location.pathname === item.href;\n\n          return (\n            <motion.div\n              key={item.name}\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: index * 0.1 }}\n            >\n              <NavLink\n                to={item.href}\n                onClick={onClose}\n                className={({ isActive }) =>\n                  `group flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 ${\n                    isActive\n                      ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg'\n                      : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'\n                  }`\n                }\n              >\n                <item.icon\n                  className={`ml-3 h-5 w-5 transition-colors ${\n                    isActive ? 'text-white' : 'text-gray-400 group-hover:text-gray-600'\n                  }`}\n                />\n                {item.name}\n\n                {/* مؤشر النشاط */}\n                {isActive && (\n                  <motion.div\n                    layoutId=\"activeTab\"\n                    className=\"absolute left-0 w-1 h-8 bg-white rounded-r-full\"\n                    initial={false}\n                    transition={{ type: \"spring\", stiffness: 500, damping: 30 }}\n                  />\n                )}\n              </NavLink>\n            </motion.div>\n          );\n        })}\n      </nav>\n\n      {/* معلومات إضافية */}\n      <div className=\"p-4 border-t border-gray-200\">\n        <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4\">\n          <h3 className=\"text-sm font-medium text-gray-900 mb-1\">نصيحة اليوم</h3>\n          <p className=\"text-xs text-gray-600\">\n            استخدم التقارير لمتابعة أداء شركاتك بشكل دوري\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,EAAEC,WAAW,QAAQ,kBAAkB;AACvD,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,QAAQ,EACRC,kBAAkB,EAClBC,eAAe,EACfC,gBAAgB,EAChBC,QAAQ,EACRC,SAAS,EACTC,kBAAkB,EAClBC,YAAY,EACZC,aAAa,EACbC,SAAS,QACJ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,UAAU,GAAG,CACjB;EAAEC,IAAI,EAAE,aAAa;EAAEC,IAAI,EAAE,GAAG;EAAEC,IAAI,EAAEf;AAAS,CAAC,EAClD;EAAEa,IAAI,EAAE,eAAe;EAAEC,IAAI,EAAE,YAAY;EAAEC,IAAI,EAAEd;AAAmB,CAAC,EACvE;EAAEY,IAAI,EAAE,UAAU;EAAEC,IAAI,EAAE,QAAQ;EAAEC,IAAI,EAAEb;AAAgB,CAAC,EAC3D;EAAEW,IAAI,EAAE,WAAW;EAAEC,IAAI,EAAE,YAAY;EAAEC,IAAI,EAAEZ;AAAiB,CAAC,EACjE;EAAEU,IAAI,EAAE,SAAS;EAAEC,IAAI,EAAE,YAAY;EAAEC,IAAI,EAAEX;AAAS,CAAC,EACvD;EAAES,IAAI,EAAE,SAAS;EAAEC,IAAI,EAAE,YAAY;EAAEC,IAAI,EAAEV;AAAU,CAAC,EACxD;EAAEQ,IAAI,EAAE,UAAU;EAAEC,IAAI,EAAE,WAAW;EAAEC,IAAI,EAAET;AAAmB,CAAC,EACjE;EAAEO,IAAI,EAAE,UAAU;EAAEC,IAAI,EAAE,UAAU;EAAEC,IAAI,EAAER;AAAa,CAAC,EAC1D;EAAEM,IAAI,EAAE,WAAW;EAAEC,IAAI,EAAE,WAAW;EAAEC,IAAI,EAAEP;AAAc,CAAC,CAC9D;AAED,MAAMQ,OAAO,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC/B,MAAMC,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAE9B,oBACEa,OAAA;IAAKS,SAAS,EAAC,yCAAyC;IAAAC,QAAA,gBAEtDV,OAAA;MAAKS,SAAS,EAAC,sEAAsE;MAAAC,QAAA,gBACnFV,OAAA;QAAKS,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCV,OAAA;UAAKS,SAAS,EAAC,kGAAkG;UAAAC,QAAA,eAC/GV,OAAA,CAACV,kBAAkB;YAACmB,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACNd,OAAA;UAAMS,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E,CAAC,EAGLR,OAAO,iBACNN,OAAA;QACEe,OAAO,EAAET,OAAQ;QACjBG,SAAS,EAAC,8EAA8E;QAAAC,QAAA,eAExFV,OAAA,CAACF,SAAS;UAACW,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNd,OAAA;MAAKS,SAAS,EAAC,4BAA4B;MAAAC,QAAA,EACxCT,UAAU,CAACe,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;QAC/B,MAAMC,QAAQ,GAAGX,QAAQ,CAACY,QAAQ,KAAKH,IAAI,CAACd,IAAI;QAEhD,oBACEH,OAAA,CAACZ,MAAM,CAACiC,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,KAAK,EAAET,KAAK,GAAG;UAAI,CAAE;UAAAR,QAAA,eAEnCV,OAAA,CAACd,OAAO;YACN0C,EAAE,EAAEX,IAAI,CAACd,IAAK;YACdY,OAAO,EAAET,OAAQ;YACjBG,SAAS,EAAEA,CAAC;cAAEU;YAAS,CAAC,KACtB,gGACEA,QAAQ,GACJ,mEAAmE,GACnE,qDAAqD,EAE5D;YAAAT,QAAA,gBAEDV,OAAA,CAACiB,IAAI,CAACb,IAAI;cACRK,SAAS,EAAE,kCACTU,QAAQ,GAAG,YAAY,GAAG,yCAAyC;YAClE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EACDG,IAAI,CAACf,IAAI,EAGTiB,QAAQ,iBACPnB,OAAA,CAACZ,MAAM,CAACiC,GAAG;cACTQ,QAAQ,EAAC,WAAW;cACpBpB,SAAS,EAAC,iDAAiD;cAC3Da,OAAO,EAAE,KAAM;cACfI,UAAU,EAAE;gBAAEI,IAAI,EAAE,QAAQ;gBAAEC,SAAS,EAAE,GAAG;gBAAEC,OAAO,EAAE;cAAG;YAAE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM;QAAC,GAhCLG,IAAI,CAACf,IAAI;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAiCJ,CAAC;MAEjB,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNd,OAAA;MAAKS,SAAS,EAAC,8BAA8B;MAAAC,QAAA,eAC3CV,OAAA;QAAKS,SAAS,EAAC,2DAA2D;QAAAC,QAAA,gBACxEV,OAAA;UAAIS,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvEd,OAAA;UAAGS,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAErC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACP,EAAA,CAjFIF,OAAO;EAAA,QACMlB,WAAW;AAAA;AAAA8C,EAAA,GADxB5B,OAAO;AAmFb,eAAeA,OAAO;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}