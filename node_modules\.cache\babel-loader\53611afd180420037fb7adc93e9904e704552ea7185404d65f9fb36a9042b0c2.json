{"ast": null, "code": "import a from \"react\";\nimport { useLatestValue as n } from './use-latest-value.js';\nlet o = function (t) {\n  let e = n(t);\n  return a.useCallback((...r) => e.current(...r), [e]);\n};\nexport { o as useEvent };", "map": {"version": 3, "names": ["a", "useLatestValue", "n", "o", "t", "e", "useCallback", "r", "current", "useEvent"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/node_modules/@headlessui/react/dist/hooks/use-event.js"], "sourcesContent": ["import a from\"react\";import{useLatestValue as n}from'./use-latest-value.js';let o=function(t){let e=n(t);return a.useCallback((...r)=>e.current(...r),[e])};export{o as useEvent};\n"], "mappings": "AAAA,OAAOA,CAAC,MAAK,OAAO;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,uBAAuB;AAAC,IAAIC,CAAC,GAAC,SAAAA,CAASC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACH,CAAC,CAACE,CAAC,CAAC;EAAC,OAAOJ,CAAC,CAACM,WAAW,CAAC,CAAC,GAAGC,CAAC,KAAGF,CAAC,CAACG,OAAO,CAAC,GAAGD,CAAC,CAAC,EAAC,CAACF,CAAC,CAAC,CAAC;AAAA,CAAC;AAAC,SAAOF,CAAC,IAAIM,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}