import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { XMarkIcon, PlusIcon, TrashIcon } from '@heroicons/react/24/outline';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import inventoryAPI from '../../api/inventoryAPI';
import toast from 'react-hot-toast';

const PurchaseForm = ({ purchase, isEditing, onSave, onClose }) => {
  const [formData, setFormData] = useState({
    supplierName: '',
    items: [{ productId: '', productName: '', quantity: 1, price: 0, total: 0 }],
    subtotal: 0,
    tax: 0,
    discount: 0,
    total: 0,
    paymentMethod: 'تحويل بنكي',
    dueDate: '',
    notes: ''
  });

  const [products, setProducts] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadProducts();
    
    if (isEditing && purchase) {
      setFormData({
        supplierName: purchase.supplierName,
        items: purchase.items || [{ productId: '', productName: '', quantity: 1, price: 0, total: 0 }],
        subtotal: purchase.subtotal,
        tax: purchase.tax,
        discount: purchase.discount,
        total: purchase.total,
        paymentMethod: purchase.paymentMethod || 'تحويل بنكي',
        dueDate: purchase.dueDate || '',
        notes: purchase.notes || ''
      });
    }
  }, [isEditing, purchase]);

  useEffect(() => {
    calculateTotals();
  }, [formData.items, formData.discount]);

  const loadProducts = () => {
    const productsData = inventoryAPI.getAllProducts();
    setProducts(productsData);
  };

  const calculateTotals = () => {
    const subtotal = formData.items.reduce((sum, item) => sum + item.total, 0);
    const tax = subtotal * 0.15;
    const total = subtotal + tax - formData.discount;

    setFormData(prev => ({
      ...prev,
      subtotal,
      tax,
      total: Math.max(0, total)
    }));
  };

  const handleProductChange = (index, productId) => {
    const product = products.find(p => p.id === parseInt(productId));
    if (product) {
      const newItems = [...formData.items];
      newItems[index] = {
        ...newItems[index],
        productId: parseInt(productId),
        productName: product.name,
        price: product.costPrice,
        total: newItems[index].quantity * product.costPrice
      };
      setFormData(prev => ({ ...prev, items: newItems }));
    }
  };

  const handleQuantityChange = (index, quantity) => {
    const newItems = [...formData.items];
    newItems[index] = {
      ...newItems[index],
      quantity: parseInt(quantity) || 0,
      total: (parseInt(quantity) || 0) * newItems[index].price
    };
    setFormData(prev => ({ ...prev, items: newItems }));
  };

  const handlePriceChange = (index, price) => {
    const newItems = [...formData.items];
    newItems[index] = {
      ...newItems[index],
      price: parseFloat(price) || 0,
      total: newItems[index].quantity * (parseFloat(price) || 0)
    };
    setFormData(prev => ({ ...prev, items: newItems }));
  };

  const addItem = () => {
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, { productId: '', productName: '', quantity: 1, price: 0, total: 0 }]
    }));
  };

  const removeItem = (index) => {
    if (formData.items.length > 1) {
      const newItems = formData.items.filter((_, i) => i !== index);
      setFormData(prev => ({ ...prev, items: newItems }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.supplierName.trim()) {
      toast.error('يرجى إدخال اسم المورد');
      return;
    }

    if (formData.items.length === 0 || !formData.items[0].productId) {
      toast.error('يرجى إضافة منتج واحد على الأقل');
      return;
    }

    setIsLoading(true);
    try {
      await onSave(formData);
    } catch (error) {
      toast.error('حدث خطأ أثناء حفظ الفاتورة');
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="bg-white rounded-2xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
        >
          {/* الرأس */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">
              {isEditing ? 'تعديل فاتورة المشتريات' : 'فاتورة مشتريات جديدة'}
            </h2>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>

          {/* المحتوى */}
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* معلومات المورد */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <Label htmlFor="supplier" className="block text-sm font-medium text-gray-700 mb-2">
                  اسم المورد *
                </Label>
                <Input
                  id="supplier"
                  type="text"
                  value={formData.supplierName}
                  onChange={(e) => setFormData(prev => ({ ...prev, supplierName: e.target.value }))}
                  placeholder="أدخل اسم المورد"
                  required
                />
              </div>

              <div>
                <Label htmlFor="paymentMethod" className="block text-sm font-medium text-gray-700 mb-2">
                  طريقة الدفع
                </Label>
                <select
                  id="paymentMethod"
                  value={formData.paymentMethod}
                  onChange={(e) => setFormData(prev => ({ ...prev, paymentMethod: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                >
                  <option value="تحويل بنكي">تحويل بنكي</option>
                  <option value="نقدي">نقدي</option>
                  <option value="آجل">آجل</option>
                  <option value="بطاقة ائتمان">بطاقة ائتمان</option>
                </select>
              </div>

              <div>
                <Label htmlFor="dueDate" className="block text-sm font-medium text-gray-700 mb-2">
                  تاريخ الاستحقاق
                </Label>
                <Input
                  id="dueDate"
                  type="date"
                  value={formData.dueDate}
                  onChange={(e) => setFormData(prev => ({ ...prev, dueDate: e.target.value }))}
                />
              </div>
            </div>

            {/* الأصناف */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <Label className="text-sm font-medium text-gray-700">الأصناف</Label>
                <Button
                  type="button"
                  onClick={addItem}
                  className="bg-green-600 hover:bg-green-700 text-white"
                  size="sm"
                >
                  <PlusIcon className="w-4 h-4 ml-2" />
                  إضافة صنف
                </Button>
              </div>

              <div className="space-y-4">
                {formData.items.map((item, index) => (
                  <div key={index} className="grid grid-cols-12 gap-4 items-end p-4 border border-gray-200 rounded-lg">
                    <div className="col-span-4">
                      <Label className="block text-sm font-medium text-gray-700 mb-2">
                        المنتج
                      </Label>
                      <select
                        value={item.productId}
                        onChange={(e) => handleProductChange(index, e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                        required
                      >
                        <option value="">اختر المنتج</option>
                        {products.map((product) => (
                          <option key={product.id} value={product.id}>
                            {product.name} - {formatCurrency(product.costPrice)}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="col-span-2">
                      <Label className="block text-sm font-medium text-gray-700 mb-2">
                        الكمية
                      </Label>
                      <Input
                        type="number"
                        min="1"
                        value={item.quantity}
                        onChange={(e) => handleQuantityChange(index, e.target.value)}
                        className="w-full"
                        required
                      />
                    </div>

                    <div className="col-span-2">
                      <Label className="block text-sm font-medium text-gray-700 mb-2">
                        السعر
                      </Label>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        value={item.price}
                        onChange={(e) => handlePriceChange(index, e.target.value)}
                        className="w-full"
                        required
                      />
                    </div>

                    <div className="col-span-3">
                      <Label className="block text-sm font-medium text-gray-700 mb-2">
                        الإجمالي
                      </Label>
                      <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg">
                        {formatCurrency(item.total)}
                      </div>
                    </div>

                    <div className="col-span-1">
                      {formData.items.length > 1 && (
                        <Button
                          type="button"
                          onClick={() => removeItem(index)}
                          variant="outline"
                          size="sm"
                          className="text-red-600 hover:text-red-800"
                        >
                          <TrashIcon className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* الإجماليات */}
            <div className="bg-gray-50 p-6 rounded-lg">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <Label htmlFor="discount" className="block text-sm font-medium text-gray-700 mb-2">
                    الخصم
                  </Label>
                  <Input
                    id="discount"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.discount}
                    onChange={(e) => setFormData(prev => ({ ...prev, discount: parseFloat(e.target.value) || 0 }))}
                  />
                </div>

                <div>
                  <Label className="block text-sm font-medium text-gray-700 mb-2">
                    المجموع الفرعي
                  </Label>
                  <div className="px-3 py-2 bg-white border border-gray-300 rounded-lg">
                    {formatCurrency(formData.subtotal)}
                  </div>
                </div>

                <div>
                  <Label className="block text-sm font-medium text-gray-700 mb-2">
                    ضريبة القيمة المضافة (15%)
                  </Label>
                  <div className="px-3 py-2 bg-white border border-gray-300 rounded-lg">
                    {formatCurrency(formData.tax)}
                  </div>
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-gray-200">
                <div className="flex justify-between items-center">
                  <span className="text-lg font-semibold text-gray-900">الإجمالي النهائي:</span>
                  <span className="text-2xl font-bold text-green-600">
                    {formatCurrency(formData.total)}
                  </span>
                </div>
              </div>
            </div>

            {/* الملاحظات */}
            <div>
              <Label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
                ملاحظات
              </Label>
              <textarea
                id="notes"
                rows={3}
                value={formData.notes}
                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="أدخل أي ملاحظات إضافية..."
              />
            </div>

            {/* الأزرار */}
            <div className="flex items-center justify-end space-x-4 space-x-reverse pt-6 border-t border-gray-200">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isLoading}
              >
                إلغاء
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ml-2"></div>
                    جاري الحفظ...
                  </div>
                ) : (
                  isEditing ? 'حفظ التعديلات' : 'حفظ الفاتورة'
                )}
              </Button>
            </div>
          </form>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default PurchaseForm;
