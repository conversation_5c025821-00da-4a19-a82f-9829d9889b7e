import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  HomeIcon,
  BuildingOfficeIcon,
  ShoppingBagIcon,
  ShoppingCartIcon,
  CubeIcon,
  UsersIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  Cog6ToothIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

const navigation = [
  { name: 'لوحة التحكم', href: '/', icon: HomeIcon },
  { name: 'إدارة الشركات', href: '/companies', icon: BuildingOfficeIcon },
  { name: 'المبيعات', href: '/sales', icon: ShoppingBagIcon },
  { name: 'المشتريات', href: '/purchases', icon: ShoppingCartIcon },
  { name: 'المخزون', href: '/inventory', icon: CubeIcon },
  { name: 'العملاء', href: '/customers', icon: UsersIcon },
  { name: 'المصاريف', href: '/expenses', icon: CurrencyDollarIcon },
  { name: 'التقارير', href: '/reports', icon: ChartBarIcon },
  { name: 'الإعدادات', href: '/settings', icon: Cog6ToothIcon },
];

const Sidebar = ({ onClose }) => {
  const location = useLocation();

  return (
    <div className="flex flex-col h-full bg-white shadow-lg">
      {/* الشعار */}
      <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200">
        <div className="flex items-center">
          <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
            <BuildingOfficeIcon className="w-5 h-5 text-white" />
          </div>
          <span className="mr-3 text-lg font-semibold text-gray-900">إدارة الشركات</span>
        </div>

        {/* زر الإغلاق للموبايل */}
        {onClose && (
          <button
            onClick={onClose}
            className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        )}
      </div>

      {/* قائمة التنقل */}
      <nav className="flex-1 px-4 py-6 space-y-2">
        {navigation.map((item, index) => {
          const isActive = location.pathname === item.href;

          return (
            <motion.div
              key={item.name}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <NavLink
                to={item.href}
                onClick={onClose}
                className={({ isActive }) =>
                  `group flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 ${
                    isActive
                      ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg'
                      : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                  }`
                }
              >
                <item.icon
                  className={`ml-3 h-5 w-5 transition-colors ${
                    isActive ? 'text-white' : 'text-gray-400 group-hover:text-gray-600'
                  }`}
                />
                {item.name}

                {/* مؤشر النشاط */}
                {isActive && (
                  <motion.div
                    layoutId="activeTab"
                    className="absolute left-0 w-1 h-8 bg-white rounded-r-full"
                    initial={false}
                    transition={{ type: "spring", stiffness: 500, damping: 30 }}
                  />
                )}
              </NavLink>
            </motion.div>
          );
        })}
      </nav>

      {/* معلومات إضافية */}
      <div className="p-4 border-t border-gray-200">
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4">
          <h3 className="text-sm font-medium text-gray-900 mb-1">نصيحة اليوم</h3>
          <p className="text-xs text-gray-600">
            استخدم التقارير لمتابعة أداء شركاتك بشكل دوري
          </p>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
