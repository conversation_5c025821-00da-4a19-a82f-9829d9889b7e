{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u0633\\u0637\\u062D \\u0627\\u0644\\u0645\\u0643\\u062A\\u0628\\\\333\\\\src\\\\components\\\\ui\\\\input.jsx\";\nimport React from 'react';\nimport { cn } from '../../lib/utils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Input = /*#__PURE__*/React.forwardRef(_c = ({\n  className,\n  type,\n  ...props\n}, ref) => {\n  return /*#__PURE__*/_jsxDEV(\"input\", {\n    type: type,\n    className: cn(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n    ref: ref,\n    ...props\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n});\n_c2 = Input;\nInput.displayName = \"Input\";\nexport { Input };\nvar _c, _c2;\n$RefreshReg$(_c, \"Input$React.forwardRef\");\n$RefreshReg$(_c2, \"Input\");", "map": {"version": 3, "names": ["React", "cn", "jsxDEV", "_jsxDEV", "Input", "forwardRef", "_c", "className", "type", "props", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c2", "displayName", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/src/components/ui/input.jsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '../../lib/utils';\n\nconst Input = React.forwardRef(({ className, type, ...props }, ref) => {\n  return (\n    <input\n      type={type}\n      className={cn(\n        \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      ref={ref}\n      {...props}\n    />\n  );\n});\n\nInput.displayName = \"Input\";\n\nexport { Input };\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,EAAE,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,KAAK,gBAAGJ,KAAK,CAACK,UAAU,CAAAC,EAAA,GAACA,CAAC;EAAEC,SAAS;EAAEC,IAAI;EAAE,GAAGC;AAAM,CAAC,EAAEC,GAAG,KAAK;EACrE,oBACEP,OAAA;IACEK,IAAI,EAAEA,IAAK;IACXD,SAAS,EAAEN,EAAE,CACX,8VAA8V,EAC9VM,SACF,CAAE;IACFG,GAAG,EAAEA,GAAI;IAAA,GACLD;EAAK;IAAAE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEN,CAAC,CAAC;AAACC,GAAA,GAZGX,KAAK;AAcXA,KAAK,CAACY,WAAW,GAAG,OAAO;AAE3B,SAASZ,KAAK;AAAG,IAAAE,EAAA,EAAAS,GAAA;AAAAE,YAAA,CAAAX,EAAA;AAAAW,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}