{"ast": null, "code": "import { useEffect as m, useRef as E } from \"react\";\nimport { getOwnerDocument as T } from '../utils/owner.js';\nimport { useIsoMorphicEffect as N } from './use-iso-morphic-effect.js';\nfunction F({\n  container: e,\n  accept: t,\n  walk: r,\n  enabled: c = !0\n}) {\n  let o = E(t),\n    l = E(r);\n  m(() => {\n    o.current = t, l.current = r;\n  }, [t, r]), N(() => {\n    if (!e || !c) return;\n    let n = T(e);\n    if (!n) return;\n    let f = o.current,\n      p = l.current,\n      d = Object.assign(i => f(i), {\n        acceptNode: f\n      }),\n      u = n.createTreeWalker(e, NodeFilter.SHOW_ELEMENT, d, !1);\n    for (; u.nextNode();) p(u.currentNode);\n  }, [e, c, o, l]);\n}\nexport { F as useTreeWalker };", "map": {"version": 3, "names": ["useEffect", "m", "useRef", "E", "getOwnerDocument", "T", "useIsoMorphicEffect", "N", "F", "container", "e", "accept", "t", "walk", "r", "enabled", "c", "o", "l", "current", "n", "f", "p", "d", "Object", "assign", "i", "acceptNode", "u", "createTreeWalker", "Node<PERSON><PERSON><PERSON>", "SHOW_ELEMENT", "nextNode", "currentNode", "useTreeWalker"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/node_modules/@headlessui/react/dist/hooks/use-tree-walker.js"], "sourcesContent": ["import{useEffect as m,useRef as E}from\"react\";import{getOwnerDocument as T}from'../utils/owner.js';import{useIsoMorphicEffect as N}from'./use-iso-morphic-effect.js';function F({container:e,accept:t,walk:r,enabled:c=!0}){let o=E(t),l=E(r);m(()=>{o.current=t,l.current=r},[t,r]),N(()=>{if(!e||!c)return;let n=T(e);if(!n)return;let f=o.current,p=l.current,d=Object.assign(i=>f(i),{acceptNode:f}),u=n.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,d,!1);for(;u.nextNode();)p(u.currentNode)},[e,c,o,l])}export{F as useTreeWalker};\n"], "mappings": "AAAA,SAAOA,SAAS,IAAIC,CAAC,EAACC,MAAM,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,6BAA6B;AAAC,SAASC,CAACA,CAAC;EAACC,SAAS,EAACC,CAAC;EAACC,MAAM,EAACC,CAAC;EAACC,IAAI,EAACC,CAAC;EAACC,OAAO,EAACC,CAAC,GAAC,CAAC;AAAC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACd,CAAC,CAACS,CAAC,CAAC;IAACM,CAAC,GAACf,CAAC,CAACW,CAAC,CAAC;EAACb,CAAC,CAAC,MAAI;IAACgB,CAAC,CAACE,OAAO,GAACP,CAAC,EAACM,CAAC,CAACC,OAAO,GAACL,CAAC;EAAA,CAAC,EAAC,CAACF,CAAC,EAACE,CAAC,CAAC,CAAC,EAACP,CAAC,CAAC,MAAI;IAAC,IAAG,CAACG,CAAC,IAAE,CAACM,CAAC,EAAC;IAAO,IAAII,CAAC,GAACf,CAAC,CAACK,CAAC,CAAC;IAAC,IAAG,CAACU,CAAC,EAAC;IAAO,IAAIC,CAAC,GAACJ,CAAC,CAACE,OAAO;MAACG,CAAC,GAACJ,CAAC,CAACC,OAAO;MAACI,CAAC,GAACC,MAAM,CAACC,MAAM,CAACC,CAAC,IAAEL,CAAC,CAACK,CAAC,CAAC,EAAC;QAACC,UAAU,EAACN;MAAC,CAAC,CAAC;MAACO,CAAC,GAACR,CAAC,CAACS,gBAAgB,CAACnB,CAAC,EAACoB,UAAU,CAACC,YAAY,EAACR,CAAC,EAAC,CAAC,CAAC,CAAC;IAAC,OAAKK,CAAC,CAACI,QAAQ,CAAC,CAAC,GAAEV,CAAC,CAACM,CAAC,CAACK,WAAW,CAAC;EAAA,CAAC,EAAC,CAACvB,CAAC,EAACM,CAAC,EAACC,CAAC,EAACC,CAAC,CAAC,CAAC;AAAA;AAAC,SAAOV,CAAC,IAAI0B,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}