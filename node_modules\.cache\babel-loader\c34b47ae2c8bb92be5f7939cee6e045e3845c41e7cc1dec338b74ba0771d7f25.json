{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u0633\\u0637\\u062D \\u0627\\u0644\\u0645\\u0643\\u062A\\u0628\\\\333\\\\src\\\\components\\\\ui\\\\card.jsx\";\nimport React from 'react';\nimport { cn } from '../../lib/utils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(_c = ({\n  className,\n  ...props\n}, ref) => /*#__PURE__*/_jsxDEV(\"div\", {\n  ref: ref,\n  className: cn(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n  ...props\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 5,\n  columnNumber: 3\n}, this));\n_c2 = Card;\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(_c3 = ({\n  className,\n  ...props\n}, ref) => /*#__PURE__*/_jsxDEV(\"div\", {\n  ref: ref,\n  className: cn(\"flex flex-col space-y-1.5 p-6\", className),\n  ...props\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 17,\n  columnNumber: 3\n}, this));\n_c4 = CardHeader;\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/React.forwardRef(_c5 = ({\n  className,\n  ...props\n}, ref) => /*#__PURE__*/_jsxDEV(\"h3\", {\n  ref: ref,\n  className: cn(\"text-2xl font-semibold leading-none tracking-tight\", className),\n  ...props\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 26,\n  columnNumber: 3\n}, this));\n_c6 = CardTitle;\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/React.forwardRef(_c7 = ({\n  className,\n  ...props\n}, ref) => /*#__PURE__*/_jsxDEV(\"p\", {\n  ref: ref,\n  className: cn(\"text-sm text-muted-foreground\", className),\n  ...props\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 38,\n  columnNumber: 3\n}, this));\n_c8 = CardDescription;\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/React.forwardRef(_c9 = ({\n  className,\n  ...props\n}, ref) => /*#__PURE__*/_jsxDEV(\"div\", {\n  ref: ref,\n  className: cn(\"p-6 pt-0\", className),\n  ...props\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 47,\n  columnNumber: 3\n}, this));\n_c0 = CardContent;\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/React.forwardRef(_c1 = ({\n  className,\n  ...props\n}, ref) => /*#__PURE__*/_jsxDEV(\"div\", {\n  ref: ref,\n  className: cn(\"flex items-center p-6 pt-0\", className),\n  ...props\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 52,\n  columnNumber: 3\n}, this));\n_c10 = CardFooter;\nCardFooter.displayName = \"CardFooter\";\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10;\n$RefreshReg$(_c, \"Card$React.forwardRef\");\n$RefreshReg$(_c2, \"Card\");\n$RefreshReg$(_c3, \"CardHeader$React.forwardRef\");\n$RefreshReg$(_c4, \"CardHeader\");\n$RefreshReg$(_c5, \"CardTitle$React.forwardRef\");\n$RefreshReg$(_c6, \"CardTitle\");\n$RefreshReg$(_c7, \"CardDescription$React.forwardRef\");\n$RefreshReg$(_c8, \"CardDescription\");\n$RefreshReg$(_c9, \"CardContent$React.forwardRef\");\n$RefreshReg$(_c0, \"CardContent\");\n$RefreshReg$(_c1, \"CardFooter$React.forwardRef\");\n$RefreshReg$(_c10, \"CardFooter\");", "map": {"version": 3, "names": ["React", "cn", "jsxDEV", "_jsxDEV", "Card", "forwardRef", "_c", "className", "props", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c2", "displayName", "<PERSON><PERSON><PERSON><PERSON>", "_c3", "_c4", "CardTitle", "_c5", "_c6", "CardDescription", "_c7", "_c8", "<PERSON><PERSON><PERSON><PERSON>", "_c9", "_c0", "<PERSON><PERSON><PERSON>er", "_c1", "_c10", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/src/components/ui/card.jsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '../../lib/utils';\n\nconst Card = React.forwardRef(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = \"Card\";\n\nconst CardHeader = React.forwardRef(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n));\nCardHeader.displayName = \"CardHeader\";\n\nconst CardTitle = React.forwardRef(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = \"CardTitle\";\n\nconst CardDescription = React.forwardRef(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n));\nCardDescription.displayName = \"CardDescription\";\n\nconst CardContent = React.forwardRef(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n));\nCardContent.displayName = \"CardContent\";\n\nconst CardFooter = React.forwardRef(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n));\nCardFooter.displayName = \"CardFooter\";\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,EAAE,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,IAAI,gBAAGJ,KAAK,CAACK,UAAU,CAAAC,EAAA,GAACA,CAAC;EAAEC,SAAS;EAAE,GAAGC;AAAM,CAAC,EAAEC,GAAG,kBACzDN,OAAA;EACEM,GAAG,EAAEA,GAAI;EACTF,SAAS,EAAEN,EAAE,CACX,0DAA0D,EAC1DM,SACF,CAAE;EAAA,GACEC;AAAK;EAAAE,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACV,CACF,CAAC;AAACC,GAAA,GATGV,IAAI;AAUVA,IAAI,CAACW,WAAW,GAAG,MAAM;AAEzB,MAAMC,UAAU,gBAAGhB,KAAK,CAACK,UAAU,CAAAY,GAAA,GAACA,CAAC;EAAEV,SAAS;EAAE,GAAGC;AAAM,CAAC,EAAEC,GAAG,kBAC/DN,OAAA;EACEM,GAAG,EAAEA,GAAI;EACTF,SAAS,EAAEN,EAAE,CAAC,+BAA+B,EAAEM,SAAS,CAAE;EAAA,GACtDC;AAAK;EAAAE,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACV,CACF,CAAC;AAACK,GAAA,GANGF,UAAU;AAOhBA,UAAU,CAACD,WAAW,GAAG,YAAY;AAErC,MAAMI,SAAS,gBAAGnB,KAAK,CAACK,UAAU,CAAAe,GAAA,GAACA,CAAC;EAAEb,SAAS;EAAE,GAAGC;AAAM,CAAC,EAAEC,GAAG,kBAC9DN,OAAA;EACEM,GAAG,EAAEA,GAAI;EACTF,SAAS,EAAEN,EAAE,CACX,oDAAoD,EACpDM,SACF,CAAE;EAAA,GACEC;AAAK;EAAAE,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACV,CACF,CAAC;AAACQ,GAAA,GATGF,SAAS;AAUfA,SAAS,CAACJ,WAAW,GAAG,WAAW;AAEnC,MAAMO,eAAe,gBAAGtB,KAAK,CAACK,UAAU,CAAAkB,GAAA,GAACA,CAAC;EAAEhB,SAAS;EAAE,GAAGC;AAAM,CAAC,EAAEC,GAAG,kBACpEN,OAAA;EACEM,GAAG,EAAEA,GAAI;EACTF,SAAS,EAAEN,EAAE,CAAC,+BAA+B,EAAEM,SAAS,CAAE;EAAA,GACtDC;AAAK;EAAAE,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACV,CACF,CAAC;AAACW,GAAA,GANGF,eAAe;AAOrBA,eAAe,CAACP,WAAW,GAAG,iBAAiB;AAE/C,MAAMU,WAAW,gBAAGzB,KAAK,CAACK,UAAU,CAAAqB,GAAA,GAACA,CAAC;EAAEnB,SAAS;EAAE,GAAGC;AAAM,CAAC,EAAEC,GAAG,kBAChEN,OAAA;EAAKM,GAAG,EAAEA,GAAI;EAACF,SAAS,EAAEN,EAAE,CAAC,UAAU,EAAEM,SAAS,CAAE;EAAA,GAAKC;AAAK;EAAAE,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAG,CAClE,CAAC;AAACc,GAAA,GAFGF,WAAW;AAGjBA,WAAW,CAACV,WAAW,GAAG,aAAa;AAEvC,MAAMa,UAAU,gBAAG5B,KAAK,CAACK,UAAU,CAAAwB,GAAA,GAACA,CAAC;EAAEtB,SAAS;EAAE,GAAGC;AAAM,CAAC,EAAEC,GAAG,kBAC/DN,OAAA;EACEM,GAAG,EAAEA,GAAI;EACTF,SAAS,EAAEN,EAAE,CAAC,4BAA4B,EAAEM,SAAS,CAAE;EAAA,GACnDC;AAAK;EAAAE,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACV,CACF,CAAC;AAACiB,IAAA,GANGF,UAAU;AAOhBA,UAAU,CAACb,WAAW,GAAG,YAAY;AAErC,SAASX,IAAI,EAAEY,UAAU,EAAEY,UAAU,EAAET,SAAS,EAAEG,eAAe,EAAEG,WAAW;AAAG,IAAAnB,EAAA,EAAAQ,GAAA,EAAAG,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,IAAA;AAAAC,YAAA,CAAAzB,EAAA;AAAAyB,YAAA,CAAAjB,GAAA;AAAAiB,YAAA,CAAAd,GAAA;AAAAc,YAAA,CAAAb,GAAA;AAAAa,YAAA,CAAAX,GAAA;AAAAW,YAAA,CAAAV,GAAA;AAAAU,YAAA,CAAAR,GAAA;AAAAQ,YAAA,CAAAP,GAAA;AAAAO,YAAA,CAAAL,GAAA;AAAAK,YAAA,CAAAJ,GAAA;AAAAI,YAAA,CAAAF,GAAA;AAAAE,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}