import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { XMarkIcon, PrinterIcon, DocumentArrowDownIcon } from '@heroicons/react/24/outline';
import { Button } from '../ui/button';
import toast from 'react-hot-toast';

const PurchaseInvoice = ({ purchase, onClose }) => {
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const handlePrint = () => {
    window.print();
    toast.success('تم إرسال الفاتورة للطباعة');
  };

  const handleDownload = () => {
    toast.success('تم تحميل الفاتورة');
  };

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="bg-white rounded-2xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
        >
          {/* الرأس */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 print:hidden">
            <h2 className="text-xl font-semibold text-gray-900">فاتورة مشتريات</h2>
            <div className="flex items-center space-x-4 space-x-reverse">
              <Button
                onClick={handlePrint}
                variant="outline"
                className="text-blue-600 hover:text-blue-800"
              >
                <PrinterIcon className="w-4 h-4 ml-2" />
                طباعة
              </Button>
              <Button
                onClick={handleDownload}
                variant="outline"
                className="text-green-600 hover:text-green-800"
              >
                <DocumentArrowDownIcon className="w-4 h-4 ml-2" />
                تحميل PDF
              </Button>
              <button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg"
              >
                <XMarkIcon className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* محتوى الفاتورة */}
          <div className="p-8 print:p-4">
            {/* رأس الفاتورة */}
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">شركة إدارة الأعمال</h1>
              <p className="text-gray-600">الرياض، المملكة العربية السعودية</p>
              <p className="text-gray-600">هاتف: 0112345678 | البريد الإلكتروني: <EMAIL></p>
              <div className="mt-4 pt-4 border-t border-gray-200">
                <h2 className="text-xl font-semibold text-gray-900">فاتورة مشتريات</h2>
              </div>
            </div>

            {/* معلومات الفاتورة والمورد */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">معلومات الفاتورة</h3>
                <div className="space-y-2">
                  <p><span className="font-medium">رقم الفاتورة:</span> {purchase.invoiceNumber}</p>
                  <p><span className="font-medium">التاريخ:</span> {new Date(purchase.date).toLocaleDateString('ar-SA')}</p>
                  <p><span className="font-medium">تاريخ الاستحقاق:</span> {purchase.dueDate ? new Date(purchase.dueDate).toLocaleDateString('ar-SA') : '-'}</p>
                  <p><span className="font-medium">طريقة الدفع:</span> {purchase.paymentMethod}</p>
                  <p><span className="font-medium">الحالة:</span> 
                    <span className={`mr-2 px-2 py-1 rounded-full text-xs font-medium ${
                      purchase.status === 'مكتملة' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {purchase.status}
                    </span>
                  </p>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">بيانات المورد</h3>
                <div className="space-y-2">
                  <p><span className="font-medium">اسم المورد:</span> {purchase.supplierName}</p>
                  <p><span className="font-medium">رقم المورد:</span> {purchase.supplierId}</p>
                </div>
              </div>
            </div>

            {/* جدول الأصناف */}
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">تفاصيل الأصناف</h3>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="border border-gray-300 px-4 py-2 text-right">الصنف</th>
                      <th className="border border-gray-300 px-4 py-2 text-center">الكمية</th>
                      <th className="border border-gray-300 px-4 py-2 text-center">السعر</th>
                      <th className="border border-gray-300 px-4 py-2 text-center">الإجمالي</th>
                    </tr>
                  </thead>
                  <tbody>
                    {purchase.items.map((item, index) => (
                      <tr key={index}>
                        <td className="border border-gray-300 px-4 py-2">{item.productName}</td>
                        <td className="border border-gray-300 px-4 py-2 text-center">{item.quantity}</td>
                        <td className="border border-gray-300 px-4 py-2 text-center">{formatCurrency(item.price)}</td>
                        <td className="border border-gray-300 px-4 py-2 text-center">{formatCurrency(item.total)}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* الإجماليات */}
            <div className="flex justify-end mb-8">
              <div className="w-full md:w-1/2">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>المجموع الفرعي:</span>
                    <span>{formatCurrency(purchase.subtotal)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>الخصم:</span>
                    <span>-{formatCurrency(purchase.discount)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>ضريبة القيمة المضافة (15%):</span>
                    <span>{formatCurrency(purchase.tax)}</span>
                  </div>
                  <div className="flex justify-between text-lg font-bold border-t border-gray-300 pt-2">
                    <span>الإجمالي النهائي:</span>
                    <span>{formatCurrency(purchase.total)}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* الملاحظات */}
            {purchase.notes && (
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">ملاحظات</h3>
                <p className="text-gray-700 bg-gray-50 p-4 rounded-lg">{purchase.notes}</p>
              </div>
            )}

            {/* التوقيع والختم */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-12">
              <div className="text-center">
                <div className="border-t border-gray-300 pt-2 mt-16">
                  <p className="font-medium">توقيع المورد</p>
                </div>
              </div>
              <div className="text-center">
                <div className="border-t border-gray-300 pt-2 mt-16">
                  <p className="font-medium">توقيع وختم الشركة</p>
                </div>
              </div>
            </div>

            {/* تذييل الفاتورة */}
            <div className="text-center mt-8 pt-8 border-t border-gray-200 text-sm text-gray-600">
              <p>شكراً لتعاونكم معنا</p>
              <p>هذه فاتورة إلكترونية ولا تحتاج إلى توقيع</p>
            </div>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default PurchaseInvoice;
