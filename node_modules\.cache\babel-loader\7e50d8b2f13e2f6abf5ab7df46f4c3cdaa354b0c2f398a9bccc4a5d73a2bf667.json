{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u0633\\u0637\\u062D \\u0627\\u0644\\u0645\\u0643\\u062A\\u0628\\\\333\\\\src\\\\pages\\\\QRCodeTest.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { QrCodeIcon, DocumentTextIcon, CheckCircleIcon } from '@heroicons/react/24/outline';\nimport { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';\nimport { Button } from '../components/ui/button';\nimport InvoiceQRCode from '../components/QRCode/InvoiceQRCode';\nimport { generateQRCodeData, generateSimpleQRCode, validateSaudiVATNumber, testQRCodeReading, decodeQRCodeData } from '../utils/qrCodeGenerator';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QRCodeTest = () => {\n  _s();\n  const [testInvoice] = useState({\n    id: 1,\n    invoiceNumber: 'INV-2024-001',\n    customerId: 1,\n    customerName: 'أحمد محمد',\n    date: '2024-01-15',\n    items: [{\n      productId: 1,\n      productName: 'لابتوب Dell',\n      quantity: 2,\n      price: 3500,\n      total: 7000\n    }, {\n      productId: 2,\n      productName: 'ماوس لاسلكي',\n      quantity: 5,\n      price: 150,\n      total: 750\n    }],\n    subtotal: 7750,\n    tax: 1162.5,\n    discount: 200,\n    total: 8712.5,\n    status: 'مكتملة',\n    paymentMethod: 'نقدي',\n    notes: 'فاتورة اختبار للتحقق من QR Code'\n  });\n  const [qrData, setQrData] = useState('');\n  const [qrType, setQrType] = useState('phase2');\n  const [readingTest, setReadingTest] = useState(null);\n  const generateTestQR = () => {\n    let data = '';\n    if (qrType === 'phase2') {\n      data = generateQRCodeData(testInvoice);\n    } else {\n      data = generateSimpleQRCode(testInvoice);\n    }\n    setQrData(data);\n\n    // اختبار قراءة QR Code\n    if (qrType === 'phase2' && data) {\n      const testResult = testQRCodeReading(data);\n      setReadingTest(testResult);\n    } else {\n      setReadingTest(null);\n    }\n  };\n  const testVATNumber = vatNumber => {\n    return validateSaudiVATNumber(vatNumber);\n  };\n  const testCases = [{\n    vatNumber: '300000000000003',\n    expected: true,\n    description: 'رقم ضريبي صحيح (15 رقم)'\n  }, {\n    vatNumber: '12345678901234',\n    expected: false,\n    description: 'رقم ضريبي قصير (14 رقم)'\n  }, {\n    vatNumber: '1234567890123456',\n    expected: false,\n    description: 'رقم ضريبي طويل (16 رقم)'\n  }, {\n    vatNumber: '30000000000000A',\n    expected: false,\n    description: 'رقم ضريبي يحتوي على أحرف'\n  }, {\n    vatNumber: '',\n    expected: false,\n    description: 'رقم ضريبي فارغ'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(QrCodeIcon, {\n          className: \"w-8 h-8 text-blue-600 ml-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold text-gray-900\",\n            children: \"\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631 QR Code\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mt-2\",\n            children: \"\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631 \\u0648\\u062A\\u062C\\u0631\\u0628\\u0629 QR Code \\u0644\\u0644\\u0641\\u0648\\u062A\\u0631\\u0629 \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.1\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n            children: /*#__PURE__*/_jsxDEV(CardTitle, {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(DocumentTextIcon, {\n                className: \"w-5 h-5 ml-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 17\n              }, this), \"\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629 \\u062A\\u062C\\u0631\\u064A\\u0628\\u064A\\u0629\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gray-50 p-4 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-gray-900 mb-3\",\n                  children: \"\\u062A\\u0641\\u0627\\u0635\\u064A\\u0644 \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 103,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-2 gap-3 text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-600\",\n                      children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 106,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-medium\",\n                      children: testInvoice.invoiceNumber\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 107,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 105,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-600\",\n                      children: \"\\u0627\\u0644\\u0639\\u0645\\u064A\\u0644:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 110,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-medium\",\n                      children: testInvoice.customerName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 111,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 109,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-600\",\n                      children: \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 114,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-medium\",\n                      children: new Date(testInvoice.date).toLocaleDateString('ar-SA')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 115,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 113,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-600\",\n                      children: \"\\u0627\\u0644\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 118,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-medium\",\n                      children: [testInvoice.total.toFixed(2), \" \\u0631.\\u0633\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 119,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 117,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 104,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(InvoiceQRCode, {\n                invoiceData: testInvoice,\n                size: 200,\n                showDetails: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.2\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n            children: /*#__PURE__*/_jsxDEV(CardTitle, {\n              children: \"\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631 \\u0645\\u0648\\u0644\\u062F QR Code\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"\\u0646\\u0648\\u0639 QR Code:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: qrType,\n                onChange: e => setQrType(e.target.value),\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"phase2\",\n                  children: \"\\u0627\\u0644\\u0645\\u0631\\u062D\\u0644\\u0629 \\u0627\\u0644\\u062B\\u0627\\u0646\\u064A\\u0629 (TLV + Base64)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"simple\",\n                  children: \"\\u0645\\u0628\\u0633\\u0637 (JSON)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: generateTestQR,\n              className: \"w-full bg-blue-600 hover:bg-blue-700 text-white\",\n              children: \"\\u0625\\u0646\\u0634\\u0627\\u0621 QR Code\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this), qrData && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-gray-900\",\n                children: \"\\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0645\\u064F\\u0646\\u0634\\u0623\\u0629:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gray-100 p-3 rounded text-xs font-mono break-all max-h-40 overflow-y-auto\",\n                children: qrData\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: [\"\\u0637\\u0648\\u0644 \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A: \", qrData.length, \" \\u062D\\u0631\\u0641\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 17\n            }, this), readingTest && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `p-4 rounded-lg border ${readingTest.isValid && readingTest.hasAllRequiredFields ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: `font-medium mb-3 ${readingTest.isValid && readingTest.hasAllRequiredFields ? 'text-green-800' : 'text-red-800'}`,\n                children: \"\\u0646\\u062A\\u064A\\u062C\\u0629 \\u0627\\u062E\\u062A\\u0628\\u0627\\u0631 \\u0627\\u0644\\u0642\\u0631\\u0627\\u0621\\u0629:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 19\n              }, this), readingTest.isValid && readingTest.hasAllRequiredFields ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center text-green-700\",\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                    className: \"w-5 h-5 ml-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 200,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium\",\n                    children: \"QR Code \\u0642\\u0627\\u0628\\u0644 \\u0644\\u0644\\u0642\\u0631\\u0627\\u0621\\u0629 \\u0628\\u0646\\u062C\\u0627\\u062D!\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 201,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 23\n                }, this), readingTest.data && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white p-3 rounded border border-green-200\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"font-medium text-green-800 mb-2\",\n                    children: \"\\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0645\\u0642\\u0631\\u0648\\u0621\\u0629:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 206,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 gap-2 text-sm\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-medium text-gray-600\",\n                        children: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0628\\u0627\\u0626\\u0639:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 209,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"mr-2\",\n                        children: readingTest.data.sellerName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 210,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 208,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-medium text-gray-600\",\n                        children: \"\\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u064A:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 213,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"mr-2\",\n                        children: readingTest.data.vatNumber\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 214,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 212,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-medium text-gray-600\",\n                        children: \"\\u0627\\u0644\\u0637\\u0627\\u0628\\u0639 \\u0627\\u0644\\u0632\\u0645\\u0646\\u064A:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 217,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"mr-2\",\n                        children: readingTest.data.timestamp\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 218,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 216,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-medium text-gray-600\",\n                        children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 221,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"mr-2\",\n                        children: [readingTest.data.invoiceTotal, \" \\u0631.\\u0633\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 222,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 220,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-medium text-gray-600\",\n                        children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u0629:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 225,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"mr-2\",\n                        children: [readingTest.data.vatAmount, \" \\u0631.\\u0633\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 226,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 224,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-red-700\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-red-500 text-xl ml-2\",\n                    children: \"\\u2717\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium\",\n                    children: \"\\u0641\\u0634\\u0644 \\u0641\\u064A \\u0642\\u0631\\u0627\\u0621\\u0629 QR Code\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 236,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm\",\n                  children: \"\\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u063A\\u064A\\u0631 \\u0645\\u0643\\u062A\\u0645\\u0644\\u0629 \\u0623\\u0648 \\u063A\\u064A\\u0631 \\u0635\\u062D\\u064A\\u062D\\u0629. \\u064A\\u0631\\u062C\\u0649 \\u0627\\u0644\\u062A\\u062D\\u0642\\u0642 \\u0645\\u0646 \\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0634\\u0631\\u0643\\u0629.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.3\n      },\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n          children: /*#__PURE__*/_jsxDEV(CardTitle, {\n            children: \"\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631 \\u0627\\u0644\\u062A\\u062D\\u0642\\u0642 \\u0645\\u0646 \\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u064A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600 mb-4\",\n              children: \"\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631 \\u062F\\u0627\\u0644\\u0629 \\u0627\\u0644\\u062A\\u062D\\u0642\\u0642 \\u0645\\u0646 \\u0635\\u062D\\u0629 \\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u064A \\u0627\\u0644\\u0633\\u0639\\u0648\\u062F\\u064A (15 \\u0631\\u0642\\u0645):\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: testCases.map((testCase, index) => {\n                const result = testVATNumber(testCase.vatNumber);\n                const isCorrect = result === testCase.expected;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `p-3 rounded-lg border ${isCorrect ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"font-mono text-sm\",\n                        children: testCase.vatNumber || '(فارغ)'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 278,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-gray-600\",\n                        children: testCase.description\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 281,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 277,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-2 space-x-reverse\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `text-xs px-2 py-1 rounded ${result ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                        children: result ? 'صحيح' : 'خطأ'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 286,\n                        columnNumber: 27\n                      }, this), isCorrect && /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                        className: \"w-4 h-4 text-green-600\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 292,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 285,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 276,\n                    columnNumber: 23\n                  }, this)\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.4\n      },\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n          children: /*#__PURE__*/_jsxDEV(CardTitle, {\n            children: \"\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u062A\\u0642\\u0646\\u064A\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-gray-900 mb-3\",\n                children: \"\\u0645\\u062A\\u0637\\u0644\\u0628\\u0627\\u062A \\u0627\\u0644\\u0645\\u0631\\u062D\\u0644\\u0629 \\u0627\\u0644\\u062B\\u0627\\u0646\\u064A\\u0629:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"space-y-2 text-sm text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                    className: \"w-4 h-4 text-green-500 ml-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 321,\n                    columnNumber: 21\n                  }, this), \"\\u062A\\u0646\\u0633\\u064A\\u0642 TLV (Tag-Length-Value)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                    className: \"w-4 h-4 text-green-500 ml-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 325,\n                    columnNumber: 21\n                  }, this), \"\\u062A\\u0634\\u0641\\u064A\\u0631 Base64\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                    className: \"w-4 h-4 text-green-500 ml-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 329,\n                    columnNumber: 21\n                  }, this), \"5 \\u062D\\u0642\\u0648\\u0644 \\u0645\\u0637\\u0644\\u0648\\u0628\\u0629 (\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0628\\u0627\\u0626\\u0639\\u060C \\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u064A\\u060C \\u0627\\u0644\\u0637\\u0627\\u0628\\u0639 \\u0627\\u0644\\u0632\\u0645\\u0646\\u064A\\u060C \\u0627\\u0644\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A\\u060C \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u0629)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                    className: \"w-4 h-4 text-green-500 ml-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 333,\n                    columnNumber: 21\n                  }, this), \"\\u0627\\u0644\\u062A\\u062D\\u0642\\u0642 \\u0645\\u0646 \\u0635\\u062D\\u0629 \\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u064A\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-gray-900 mb-3\",\n                children: \"\\u062A\\u0646\\u0633\\u064A\\u0642 TLV:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2 text-sm text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gray-50 p-2 rounded font-mono\",\n                  children: \"Tag 1: \\u0627\\u0633\\u0645 \\u0627\\u0644\\u0628\\u0627\\u0626\\u0639\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gray-50 p-2 rounded font-mono\",\n                  children: \"Tag 2: \\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u064A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gray-50 p-2 rounded font-mono\",\n                  children: \"Tag 3: \\u0627\\u0644\\u0637\\u0627\\u0628\\u0639 \\u0627\\u0644\\u0632\\u0645\\u0646\\u064A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gray-50 p-2 rounded font-mono\",\n                  children: \"Tag 4: \\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gray-50 p-2 rounded font-mono\",\n                  children: \"Tag 5: \\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 306,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 5\n  }, this);\n};\n_s(QRCodeTest, \"3qXd4PEgMbOV9JXiamJOgkKQ54s=\");\n_c = QRCodeTest;\nexport default QRCodeTest;\nvar _c;\n$RefreshReg$(_c, \"QRCodeTest\");", "map": {"version": 3, "names": ["React", "useState", "motion", "QrCodeIcon", "DocumentTextIcon", "CheckCircleIcon", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "<PERSON><PERSON>", "InvoiceQRCode", "generateQRCodeData", "generateSimpleQRCode", "validateSaudiVATNumber", "testQRCodeReading", "decodeQRCodeData", "jsxDEV", "_jsxDEV", "QRCodeTest", "_s", "testInvoice", "id", "invoiceNumber", "customerId", "customerName", "date", "items", "productId", "productName", "quantity", "price", "total", "subtotal", "tax", "discount", "status", "paymentMethod", "notes", "qrData", "setQrData", "qrType", "setQrType", "readingTest", "setReadingTest", "generateTestQR", "data", "testResult", "testVATNumber", "vatNumber", "testCases", "expected", "description", "className", "children", "div", "initial", "opacity", "y", "animate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "transition", "delay", "Date", "toLocaleDateString", "toFixed", "invoiceData", "size", "showDetails", "value", "onChange", "e", "target", "onClick", "length", "<PERSON><PERSON><PERSON><PERSON>", "hasAllRequiredFields", "sellerName", "timestamp", "invoiceTotal", "vatAmount", "map", "testCase", "index", "result", "isCorrect", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/src/pages/QRCodeTest.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { QrCodeIcon, DocumentTextIcon, CheckCircleIcon } from '@heroicons/react/24/outline';\nimport { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';\nimport { Button } from '../components/ui/button';\nimport InvoiceQRCode from '../components/QRCode/InvoiceQRCode';\nimport {\n  generateQRCodeData,\n  generateSimpleQRCode,\n  validateSaudiVATNumber,\n  testQRCodeReading,\n  decodeQRCodeData\n} from '../utils/qrCodeGenerator';\n\nconst QRCodeTest = () => {\n  const [testInvoice] = useState({\n    id: 1,\n    invoiceNumber: 'INV-2024-001',\n    customerId: 1,\n    customerName: 'أحمد محمد',\n    date: '2024-01-15',\n    items: [\n      { productId: 1, productName: 'لابتوب Dell', quantity: 2, price: 3500, total: 7000 },\n      { productId: 2, productName: 'ماوس لاسلكي', quantity: 5, price: 150, total: 750 }\n    ],\n    subtotal: 7750,\n    tax: 1162.5,\n    discount: 200,\n    total: 8712.5,\n    status: 'مكتملة',\n    paymentMethod: 'نقدي',\n    notes: 'فاتورة اختبار للتحقق من QR Code'\n  });\n\n  const [qrData, setQrData] = useState('');\n  const [qrType, setQrType] = useState('phase2');\n  const [readingTest, setReadingTest] = useState(null);\n\n  const generateTestQR = () => {\n    let data = '';\n    if (qrType === 'phase2') {\n      data = generateQRCodeData(testInvoice);\n    } else {\n      data = generateSimpleQRCode(testInvoice);\n    }\n    setQrData(data);\n\n    // اختبار قراءة QR Code\n    if (qrType === 'phase2' && data) {\n      const testResult = testQRCodeReading(data);\n      setReadingTest(testResult);\n    } else {\n      setReadingTest(null);\n    }\n  };\n\n  const testVATNumber = (vatNumber) => {\n    return validateSaudiVATNumber(vatNumber);\n  };\n\n  const testCases = [\n    { vatNumber: '300000000000003', expected: true, description: 'رقم ضريبي صحيح (15 رقم)' },\n    { vatNumber: '12345678901234', expected: false, description: 'رقم ضريبي قصير (14 رقم)' },\n    { vatNumber: '1234567890123456', expected: false, description: 'رقم ضريبي طويل (16 رقم)' },\n    { vatNumber: '30000000000000A', expected: false, description: 'رقم ضريبي يحتوي على أحرف' },\n    { vatNumber: '', expected: false, description: 'رقم ضريبي فارغ' }\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* العنوان */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n      >\n        <div className=\"flex items-center mb-6\">\n          <QrCodeIcon className=\"w-8 h-8 text-blue-600 ml-3\" />\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900\">اختبار QR Code</h1>\n            <p className=\"text-gray-600 mt-2\">اختبار وتجربة QR Code للفوترة الإلكترونية</p>\n          </div>\n        </div>\n      </motion.div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* QR Code للفاتورة التجريبية */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.1 }}\n        >\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <DocumentTextIcon className=\"w-5 h-5 ml-2\" />\n                فاتورة تجريبية\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {/* معلومات الفاتورة */}\n                <div className=\"bg-gray-50 p-4 rounded-lg\">\n                  <h4 className=\"font-medium text-gray-900 mb-3\">تفاصيل الفاتورة:</h4>\n                  <div className=\"grid grid-cols-2 gap-3 text-sm\">\n                    <div>\n                      <span className=\"text-gray-600\">رقم الفاتورة:</span>\n                      <p className=\"font-medium\">{testInvoice.invoiceNumber}</p>\n                    </div>\n                    <div>\n                      <span className=\"text-gray-600\">العميل:</span>\n                      <p className=\"font-medium\">{testInvoice.customerName}</p>\n                    </div>\n                    <div>\n                      <span className=\"text-gray-600\">التاريخ:</span>\n                      <p className=\"font-medium\">{new Date(testInvoice.date).toLocaleDateString('ar-SA')}</p>\n                    </div>\n                    <div>\n                      <span className=\"text-gray-600\">الإجمالي:</span>\n                      <p className=\"font-medium\">{testInvoice.total.toFixed(2)} ر.س</p>\n                    </div>\n                  </div>\n                </div>\n\n                {/* QR Code */}\n                <InvoiceQRCode\n                  invoiceData={testInvoice}\n                  size={200}\n                  showDetails={true}\n                />\n              </div>\n            </CardContent>\n          </Card>\n        </motion.div>\n\n        {/* اختبار مولد QR Code */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.2 }}\n        >\n          <Card>\n            <CardHeader>\n              <CardTitle>اختبار مولد QR Code</CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              {/* اختيار نوع QR Code */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  نوع QR Code:\n                </label>\n                <select\n                  value={qrType}\n                  onChange={(e) => setQrType(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"phase2\">المرحلة الثانية (TLV + Base64)</option>\n                  <option value=\"simple\">مبسط (JSON)</option>\n                </select>\n              </div>\n\n              {/* زر إنشاء QR Code */}\n              <Button\n                onClick={generateTestQR}\n                className=\"w-full bg-blue-600 hover:bg-blue-700 text-white\"\n              >\n                إنشاء QR Code\n              </Button>\n\n              {/* عرض البيانات المُنشأة */}\n              {qrData && (\n                <div className=\"space-y-3\">\n                  <h4 className=\"font-medium text-gray-900\">البيانات المُنشأة:</h4>\n                  <div className=\"bg-gray-100 p-3 rounded text-xs font-mono break-all max-h-40 overflow-y-auto\">\n                    {qrData}\n                  </div>\n                  <div className=\"text-sm text-gray-600\">\n                    طول البيانات: {qrData.length} حرف\n                  </div>\n                </div>\n              )}\n\n              {/* نتيجة اختبار القراءة */}\n              {readingTest && (\n                <div className={`p-4 rounded-lg border ${\n                  readingTest.isValid && readingTest.hasAllRequiredFields\n                    ? 'bg-green-50 border-green-200'\n                    : 'bg-red-50 border-red-200'\n                }`}>\n                  <h4 className={`font-medium mb-3 ${\n                    readingTest.isValid && readingTest.hasAllRequiredFields\n                      ? 'text-green-800'\n                      : 'text-red-800'\n                  }`}>\n                    نتيجة اختبار القراءة:\n                  </h4>\n\n                  {readingTest.isValid && readingTest.hasAllRequiredFields ? (\n                    <div className=\"space-y-2\">\n                      <div className=\"flex items-center text-green-700\">\n                        <CheckCircleIcon className=\"w-5 h-5 ml-2\" />\n                        <span className=\"font-medium\">QR Code قابل للقراءة بنجاح!</span>\n                      </div>\n\n                      {readingTest.data && (\n                        <div className=\"bg-white p-3 rounded border border-green-200\">\n                          <h5 className=\"font-medium text-green-800 mb-2\">البيانات المقروءة:</h5>\n                          <div className=\"grid grid-cols-1 gap-2 text-sm\">\n                            <div>\n                              <span className=\"font-medium text-gray-600\">اسم البائع:</span>\n                              <span className=\"mr-2\">{readingTest.data.sellerName}</span>\n                            </div>\n                            <div>\n                              <span className=\"font-medium text-gray-600\">الرقم الضريبي:</span>\n                              <span className=\"mr-2\">{readingTest.data.vatNumber}</span>\n                            </div>\n                            <div>\n                              <span className=\"font-medium text-gray-600\">الطابع الزمني:</span>\n                              <span className=\"mr-2\">{readingTest.data.timestamp}</span>\n                            </div>\n                            <div>\n                              <span className=\"font-medium text-gray-600\">إجمالي الفاتورة:</span>\n                              <span className=\"mr-2\">{readingTest.data.invoiceTotal} ر.س</span>\n                            </div>\n                            <div>\n                              <span className=\"font-medium text-gray-600\">إجمالي الضريبة:</span>\n                              <span className=\"mr-2\">{readingTest.data.vatAmount} ر.س</span>\n                            </div>\n                          </div>\n                        </div>\n                      )}\n                    </div>\n                  ) : (\n                    <div className=\"text-red-700\">\n                      <div className=\"flex items-center mb-2\">\n                        <span className=\"text-red-500 text-xl ml-2\">✗</span>\n                        <span className=\"font-medium\">فشل في قراءة QR Code</span>\n                      </div>\n                      <p className=\"text-sm\">البيانات غير مكتملة أو غير صحيحة. يرجى التحقق من إعدادات الشركة.</p>\n                    </div>\n                  )}\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        </motion.div>\n      </div>\n\n      {/* اختبار التحقق من الرقم الضريبي */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.3 }}\n      >\n        <Card>\n          <CardHeader>\n            <CardTitle>اختبار التحقق من الرقم الضريبي</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              <p className=\"text-sm text-gray-600 mb-4\">\n                اختبار دالة التحقق من صحة الرقم الضريبي السعودي (15 رقم):\n              </p>\n\n              <div className=\"space-y-3\">\n                {testCases.map((testCase, index) => {\n                  const result = testVATNumber(testCase.vatNumber);\n                  const isCorrect = result === testCase.expected;\n\n                  return (\n                    <div\n                      key={index}\n                      className={`p-3 rounded-lg border ${\n                        isCorrect ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'\n                      }`}\n                    >\n                      <div className=\"flex items-center justify-between\">\n                        <div>\n                          <p className=\"font-mono text-sm\">\n                            {testCase.vatNumber || '(فارغ)'}\n                          </p>\n                          <p className=\"text-xs text-gray-600\">\n                            {testCase.description}\n                          </p>\n                        </div>\n                        <div className=\"flex items-center space-x-2 space-x-reverse\">\n                          <span className={`text-xs px-2 py-1 rounded ${\n                            result ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'\n                          }`}>\n                            {result ? 'صحيح' : 'خطأ'}\n                          </span>\n                          {isCorrect && (\n                            <CheckCircleIcon className=\"w-4 h-4 text-green-600\" />\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  );\n                })}\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </motion.div>\n\n      {/* معلومات تقنية */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.4 }}\n      >\n        <Card>\n          <CardHeader>\n            <CardTitle>معلومات تقنية</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <h4 className=\"font-medium text-gray-900 mb-3\">متطلبات المرحلة الثانية:</h4>\n                <ul className=\"space-y-2 text-sm text-gray-600\">\n                  <li className=\"flex items-center\">\n                    <CheckCircleIcon className=\"w-4 h-4 text-green-500 ml-2\" />\n                    تنسيق TLV (Tag-Length-Value)\n                  </li>\n                  <li className=\"flex items-center\">\n                    <CheckCircleIcon className=\"w-4 h-4 text-green-500 ml-2\" />\n                    تشفير Base64\n                  </li>\n                  <li className=\"flex items-center\">\n                    <CheckCircleIcon className=\"w-4 h-4 text-green-500 ml-2\" />\n                    5 حقول مطلوبة (اسم البائع، الرقم الضريبي، الطابع الزمني، الإجمالي، الضريبة)\n                  </li>\n                  <li className=\"flex items-center\">\n                    <CheckCircleIcon className=\"w-4 h-4 text-green-500 ml-2\" />\n                    التحقق من صحة الرقم الضريبي\n                  </li>\n                </ul>\n              </div>\n\n              <div>\n                <h4 className=\"font-medium text-gray-900 mb-3\">تنسيق TLV:</h4>\n                <div className=\"space-y-2 text-sm text-gray-600\">\n                  <div className=\"bg-gray-50 p-2 rounded font-mono\">\n                    Tag 1: اسم البائع\n                  </div>\n                  <div className=\"bg-gray-50 p-2 rounded font-mono\">\n                    Tag 2: الرقم الضريبي\n                  </div>\n                  <div className=\"bg-gray-50 p-2 rounded font-mono\">\n                    Tag 3: الطابع الزمني\n                  </div>\n                  <div className=\"bg-gray-50 p-2 rounded font-mono\">\n                    Tag 4: إجمالي الفاتورة\n                  </div>\n                  <div className=\"bg-gray-50 p-2 rounded font-mono\">\n                    Tag 5: إجمالي الضريبة\n                  </div>\n                </div>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default QRCodeTest;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,UAAU,EAAEC,gBAAgB,EAAEC,eAAe,QAAQ,6BAA6B;AAC3F,SAASC,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,SAAS,QAAQ,uBAAuB;AAChF,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,aAAa,MAAM,oCAAoC;AAC9D,SACEC,kBAAkB,EAClBC,oBAAoB,EACpBC,sBAAsB,EACtBC,iBAAiB,EACjBC,gBAAgB,QACX,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM,CAACC,WAAW,CAAC,GAAGpB,QAAQ,CAAC;IAC7BqB,EAAE,EAAE,CAAC;IACLC,aAAa,EAAE,cAAc;IAC7BC,UAAU,EAAE,CAAC;IACbC,YAAY,EAAE,WAAW;IACzBC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,CACL;MAAEC,SAAS,EAAE,CAAC;MAAEC,WAAW,EAAE,aAAa;MAAEC,QAAQ,EAAE,CAAC;MAAEC,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,EACnF;MAAEJ,SAAS,EAAE,CAAC;MAAEC,WAAW,EAAE,aAAa;MAAEC,QAAQ,EAAE,CAAC;MAAEC,KAAK,EAAE,GAAG;MAAEC,KAAK,EAAE;IAAI,CAAC,CAClF;IACDC,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAE,MAAM;IACXC,QAAQ,EAAE,GAAG;IACbH,KAAK,EAAE,MAAM;IACbI,MAAM,EAAE,QAAQ;IAChBC,aAAa,EAAE,MAAM;IACrBC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACwC,MAAM,EAAEC,SAAS,CAAC,GAAGzC,QAAQ,CAAC,QAAQ,CAAC;EAC9C,MAAM,CAAC0C,WAAW,EAAEC,cAAc,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EAEpD,MAAM4C,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIC,IAAI,GAAG,EAAE;IACb,IAAIL,MAAM,KAAK,QAAQ,EAAE;MACvBK,IAAI,GAAGlC,kBAAkB,CAACS,WAAW,CAAC;IACxC,CAAC,MAAM;MACLyB,IAAI,GAAGjC,oBAAoB,CAACQ,WAAW,CAAC;IAC1C;IACAmB,SAAS,CAACM,IAAI,CAAC;;IAEf;IACA,IAAIL,MAAM,KAAK,QAAQ,IAAIK,IAAI,EAAE;MAC/B,MAAMC,UAAU,GAAGhC,iBAAiB,CAAC+B,IAAI,CAAC;MAC1CF,cAAc,CAACG,UAAU,CAAC;IAC5B,CAAC,MAAM;MACLH,cAAc,CAAC,IAAI,CAAC;IACtB;EACF,CAAC;EAED,MAAMI,aAAa,GAAIC,SAAS,IAAK;IACnC,OAAOnC,sBAAsB,CAACmC,SAAS,CAAC;EAC1C,CAAC;EAED,MAAMC,SAAS,GAAG,CAChB;IAAED,SAAS,EAAE,iBAAiB;IAAEE,QAAQ,EAAE,IAAI;IAAEC,WAAW,EAAE;EAA0B,CAAC,EACxF;IAAEH,SAAS,EAAE,gBAAgB;IAAEE,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAA0B,CAAC,EACxF;IAAEH,SAAS,EAAE,kBAAkB;IAAEE,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAA0B,CAAC,EAC1F;IAAEH,SAAS,EAAE,iBAAiB;IAAEE,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAA2B,CAAC,EAC1F;IAAEH,SAAS,EAAE,EAAE;IAAEE,QAAQ,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAiB,CAAC,CAClE;EAED,oBACElC,OAAA;IAAKmC,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBpC,OAAA,CAAChB,MAAM,CAACqD,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAJ,QAAA,eAE9BpC,OAAA;QAAKmC,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrCpC,OAAA,CAACf,UAAU;UAACkD,SAAS,EAAC;QAA4B;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrD7C,OAAA;UAAAoC,QAAA,gBACEpC,OAAA;YAAImC,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAc;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpE7C,OAAA;YAAGmC,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAyC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAEb7C,OAAA;MAAKmC,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDpC,OAAA,CAAChB,MAAM,CAACqD,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BM,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAX,QAAA,eAE3BpC,OAAA,CAACZ,IAAI;UAAAgD,QAAA,gBACHpC,OAAA,CAACV,UAAU;YAAA8C,QAAA,eACTpC,OAAA,CAACT,SAAS;cAAC4C,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBACtCpC,OAAA,CAACd,gBAAgB;gBAACiD,SAAS,EAAC;cAAc;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mFAE/C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACb7C,OAAA,CAACX,WAAW;YAAA+C,QAAA,eACVpC,OAAA;cAAKmC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBAExBpC,OAAA;gBAAKmC,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCpC,OAAA;kBAAImC,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpE7C,OAAA;kBAAKmC,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7CpC,OAAA;oBAAAoC,QAAA,gBACEpC,OAAA;sBAAMmC,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAa;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACpD7C,OAAA;sBAAGmC,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAEjC,WAAW,CAACE;oBAAa;sBAAAqC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD,CAAC,eACN7C,OAAA;oBAAAoC,QAAA,gBACEpC,OAAA;sBAAMmC,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAO;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC9C7C,OAAA;sBAAGmC,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAEjC,WAAW,CAACI;oBAAY;sBAAAmC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC,eACN7C,OAAA;oBAAAoC,QAAA,gBACEpC,OAAA;sBAAMmC,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC/C7C,OAAA;sBAAGmC,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAE,IAAIY,IAAI,CAAC7C,WAAW,CAACK,IAAI,CAAC,CAACyC,kBAAkB,CAAC,OAAO;oBAAC;sBAAAP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpF,CAAC,eACN7C,OAAA;oBAAAoC,QAAA,gBACEpC,OAAA;sBAAMmC,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAS;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAChD7C,OAAA;sBAAGmC,SAAS,EAAC,aAAa;sBAAAC,QAAA,GAAEjC,WAAW,CAACW,KAAK,CAACoC,OAAO,CAAC,CAAC,CAAC,EAAC,gBAAI;oBAAA;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN7C,OAAA,CAACP,aAAa;gBACZ0D,WAAW,EAAEhD,WAAY;gBACzBiD,IAAI,EAAE,GAAI;gBACVC,WAAW,EAAE;cAAK;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAGb7C,OAAA,CAAChB,MAAM,CAACqD,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BM,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAX,QAAA,eAE3BpC,OAAA,CAACZ,IAAI;UAAAgD,QAAA,gBACHpC,OAAA,CAACV,UAAU;YAAA8C,QAAA,eACTpC,OAAA,CAACT,SAAS;cAAA6C,QAAA,EAAC;YAAmB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACb7C,OAAA,CAACX,WAAW;YAAC8C,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAEhCpC,OAAA;cAAAoC,QAAA,gBACEpC,OAAA;gBAAOmC,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7C,OAAA;gBACEsD,KAAK,EAAE/B,MAAO;gBACdgC,QAAQ,EAAGC,CAAC,IAAKhC,SAAS,CAACgC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAC3CnB,SAAS,EAAC,2GAA2G;gBAAAC,QAAA,gBAErHpC,OAAA;kBAAQsD,KAAK,EAAC,QAAQ;kBAAAlB,QAAA,EAAC;gBAA8B;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9D7C,OAAA;kBAAQsD,KAAK,EAAC,QAAQ;kBAAAlB,QAAA,EAAC;gBAAW;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGN7C,OAAA,CAACR,MAAM;cACLkE,OAAO,EAAE/B,cAAe;cACxBQ,SAAS,EAAC,iDAAiD;cAAAC,QAAA,EAC5D;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAGRxB,MAAM,iBACLrB,OAAA;cAAKmC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBpC,OAAA;gBAAImC,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAkB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjE7C,OAAA;gBAAKmC,SAAS,EAAC,8EAA8E;gBAAAC,QAAA,EAC1Ff;cAAM;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN7C,OAAA;gBAAKmC,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAC,uEACvB,EAACf,MAAM,CAACsC,MAAM,EAAC,qBAC/B;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,EAGApB,WAAW,iBACVzB,OAAA;cAAKmC,SAAS,EAAE,yBACdV,WAAW,CAACmC,OAAO,IAAInC,WAAW,CAACoC,oBAAoB,GACnD,8BAA8B,GAC9B,0BAA0B,EAC7B;cAAAzB,QAAA,gBACDpC,OAAA;gBAAImC,SAAS,EAAE,oBACbV,WAAW,CAACmC,OAAO,IAAInC,WAAW,CAACoC,oBAAoB,GACnD,gBAAgB,GAChB,cAAc,EACjB;gBAAAzB,QAAA,EAAC;cAEJ;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EAEJpB,WAAW,CAACmC,OAAO,IAAInC,WAAW,CAACoC,oBAAoB,gBACtD7D,OAAA;gBAAKmC,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBpC,OAAA;kBAAKmC,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAC/CpC,OAAA,CAACb,eAAe;oBAACgD,SAAS,EAAC;kBAAc;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5C7C,OAAA;oBAAMmC,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAA2B;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC,EAELpB,WAAW,CAACG,IAAI,iBACf5B,OAAA;kBAAKmC,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,gBAC3DpC,OAAA;oBAAImC,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAC;kBAAkB;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvE7C,OAAA;oBAAKmC,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,gBAC7CpC,OAAA;sBAAAoC,QAAA,gBACEpC,OAAA;wBAAMmC,SAAS,EAAC,2BAA2B;wBAAAC,QAAA,EAAC;sBAAW;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC9D7C,OAAA;wBAAMmC,SAAS,EAAC,MAAM;wBAAAC,QAAA,EAAEX,WAAW,CAACG,IAAI,CAACkC;sBAAU;wBAAApB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxD,CAAC,eACN7C,OAAA;sBAAAoC,QAAA,gBACEpC,OAAA;wBAAMmC,SAAS,EAAC,2BAA2B;wBAAAC,QAAA,EAAC;sBAAc;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACjE7C,OAAA;wBAAMmC,SAAS,EAAC,MAAM;wBAAAC,QAAA,EAAEX,WAAW,CAACG,IAAI,CAACG;sBAAS;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvD,CAAC,eACN7C,OAAA;sBAAAoC,QAAA,gBACEpC,OAAA;wBAAMmC,SAAS,EAAC,2BAA2B;wBAAAC,QAAA,EAAC;sBAAc;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACjE7C,OAAA;wBAAMmC,SAAS,EAAC,MAAM;wBAAAC,QAAA,EAAEX,WAAW,CAACG,IAAI,CAACmC;sBAAS;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvD,CAAC,eACN7C,OAAA;sBAAAoC,QAAA,gBACEpC,OAAA;wBAAMmC,SAAS,EAAC,2BAA2B;wBAAAC,QAAA,EAAC;sBAAgB;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACnE7C,OAAA;wBAAMmC,SAAS,EAAC,MAAM;wBAAAC,QAAA,GAAEX,WAAW,CAACG,IAAI,CAACoC,YAAY,EAAC,gBAAI;sBAAA;wBAAAtB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9D,CAAC,eACN7C,OAAA;sBAAAoC,QAAA,gBACEpC,OAAA;wBAAMmC,SAAS,EAAC,2BAA2B;wBAAAC,QAAA,EAAC;sBAAe;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAClE7C,OAAA;wBAAMmC,SAAS,EAAC,MAAM;wBAAAC,QAAA,GAAEX,WAAW,CAACG,IAAI,CAACqC,SAAS,EAAC,gBAAI;sBAAA;wBAAAvB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,gBAEN7C,OAAA;gBAAKmC,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BpC,OAAA;kBAAKmC,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCpC,OAAA;oBAAMmC,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAC;kBAAC;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpD7C,OAAA;oBAAMmC,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACN7C,OAAA;kBAAGmC,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAgE;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxF,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGN7C,OAAA,CAAChB,MAAM,CAACqD,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BM,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAAAX,QAAA,eAE3BpC,OAAA,CAACZ,IAAI;QAAAgD,QAAA,gBACHpC,OAAA,CAACV,UAAU;UAAA8C,QAAA,eACTpC,OAAA,CAACT,SAAS;YAAA6C,QAAA,EAAC;UAA8B;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eACb7C,OAAA,CAACX,WAAW;UAAA+C,QAAA,eACVpC,OAAA;YAAKmC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBpC,OAAA;cAAGmC,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE1C;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJ7C,OAAA;cAAKmC,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBJ,SAAS,CAACkC,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;gBAClC,MAAMC,MAAM,GAAGvC,aAAa,CAACqC,QAAQ,CAACpC,SAAS,CAAC;gBAChD,MAAMuC,SAAS,GAAGD,MAAM,KAAKF,QAAQ,CAAClC,QAAQ;gBAE9C,oBACEjC,OAAA;kBAEEmC,SAAS,EAAE,yBACTmC,SAAS,GAAG,8BAA8B,GAAG,0BAA0B,EACtE;kBAAAlC,QAAA,eAEHpC,OAAA;oBAAKmC,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,gBAChDpC,OAAA;sBAAAoC,QAAA,gBACEpC,OAAA;wBAAGmC,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,EAC7B+B,QAAQ,CAACpC,SAAS,IAAI;sBAAQ;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9B,CAAC,eACJ7C,OAAA;wBAAGmC,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EACjC+B,QAAQ,CAACjC;sBAAW;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC,eACN7C,OAAA;sBAAKmC,SAAS,EAAC,6CAA6C;sBAAAC,QAAA,gBAC1DpC,OAAA;wBAAMmC,SAAS,EAAE,6BACfkC,MAAM,GAAG,6BAA6B,GAAG,yBAAyB,EACjE;wBAAAjC,QAAA,EACAiC,MAAM,GAAG,MAAM,GAAG;sBAAK;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC,EACNyB,SAAS,iBACRtE,OAAA,CAACb,eAAe;wBAACgD,SAAS,EAAC;sBAAwB;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CACtD;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC,GAxBDuB,KAAK;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAyBP,CAAC;cAEV,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGb7C,OAAA,CAAChB,MAAM,CAACqD,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BM,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAAAX,QAAA,eAE3BpC,OAAA,CAACZ,IAAI;QAAAgD,QAAA,gBACHpC,OAAA,CAACV,UAAU;UAAA8C,QAAA,eACTpC,OAAA,CAACT,SAAS;YAAA6C,QAAA,EAAC;UAAa;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACb7C,OAAA,CAACX,WAAW;UAAA+C,QAAA,eACVpC,OAAA;YAAKmC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDpC,OAAA;cAAAoC,QAAA,gBACEpC,OAAA;gBAAImC,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAC;cAAwB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5E7C,OAAA;gBAAImC,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAC7CpC,OAAA;kBAAImC,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC/BpC,OAAA,CAACb,eAAe;oBAACgD,SAAS,EAAC;kBAA6B;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,yDAE7D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL7C,OAAA;kBAAImC,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC/BpC,OAAA,CAACb,eAAe;oBAACgD,SAAS,EAAC;kBAA6B;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,yCAE7D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL7C,OAAA;kBAAImC,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC/BpC,OAAA,CAACb,eAAe;oBAACgD,SAAS,EAAC;kBAA6B;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,qYAE7D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL7C,OAAA;kBAAImC,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC/BpC,OAAA,CAACb,eAAe;oBAACgD,SAAS,EAAC;kBAA6B;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,kJAE7D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEN7C,OAAA;cAAAoC,QAAA,gBACEpC,OAAA;gBAAImC,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAC;cAAU;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9D7C,OAAA;gBAAKmC,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAC9CpC,OAAA;kBAAKmC,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAElD;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN7C,OAAA;kBAAKmC,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAElD;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN7C,OAAA;kBAAKmC,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAElD;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN7C,OAAA;kBAAKmC,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAElD;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN7C,OAAA;kBAAKmC,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAElD;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAAC3C,EAAA,CA9VID,UAAU;AAAAsE,EAAA,GAAVtE,UAAU;AAgWhB,eAAeA,UAAU;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}