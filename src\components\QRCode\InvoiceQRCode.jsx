import React, { useState, useEffect } from 'react';
import QRCode from 'react-qr-code';
import {
  generateQRCodeData,
  generateSimpleQRCode,
  generateInvoiceHash,
  testQRCodeReading,
  decodeQRCodeData
} from '../../utils/qrCodeGenerator';
import { Button } from '../ui/button';
import { QrCodeIcon, DocumentDuplicateIcon, CheckIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';

const InvoiceQRCode = ({ invoiceData, size = 128, showDetails = true }) => {
  const [qrData, setQrData] = useState('');
  const [qrType, setQrType] = useState('phase2'); // phase2 أو simple
  const [copied, setCopied] = useState(false);
  const [invoiceHash, setInvoiceHash] = useState('');
  const [readingTest, setReadingTest] = useState(null);

  useEffect(() => {
    if (invoiceData) {
      // إنشاء QR Code حسب النوع المحدد
      let data = '';
      if (qrType === 'phase2') {
        data = generateQRCodeData(invoiceData);
      } else {
        data = generateSimpleQRCode(invoiceData);
      }

      setQrData(data);

      // إنشاء hash للفاتورة
      const hash = generateInvoiceHash(invoiceData);
      setInvoiceHash(hash);

      // اختبار قراءة QR Code
      if (qrType === 'phase2' && data) {
        const testResult = testQRCodeReading(data);
        setReadingTest(testResult);
      } else {
        setReadingTest(null);
      }
    }
  }, [invoiceData, qrType]);

  const handleCopyQRData = async () => {
    try {
      await navigator.clipboard.writeText(qrData);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('فشل في نسخ البيانات:', error);
    }
  };

  const downloadQRCode = () => {
    const svg = document.getElementById(`qr-code-${invoiceData.id}`);
    const svgData = new XMLSerializer().serializeToString(svg);
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      canvas.width = img.width;
      canvas.height = img.height;
      ctx.drawImage(img, 0, 0);

      const pngFile = canvas.toDataURL('image/png');
      const downloadLink = document.createElement('a');
      downloadLink.download = `qr-code-${invoiceData.invoiceNumber}.png`;
      downloadLink.href = pngFile;
      downloadLink.click();
    };

    img.src = 'data:image/svg+xml;base64,' + btoa(svgData);
  };

  if (!invoiceData || !qrData) {
    return (
      <div className="flex items-center justify-center p-4 bg-gray-100 rounded-lg">
        <p className="text-gray-500">جاري تحميل QR Code...</p>
      </div>
    );
  }

  return (
    <div className="bg-white p-4 rounded-lg border border-gray-200">
      {/* عنوان QR Code */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <QrCodeIcon className="w-5 h-5 text-blue-600 ml-2" />
          <h3 className="text-lg font-semibold text-gray-900">
            رمز الاستجابة السريعة
          </h3>
        </div>

        {/* تبديل نوع QR Code */}
        <div className="flex items-center space-x-2 space-x-reverse">
          <select
            value={qrType}
            onChange={(e) => setQrType(e.target.value)}
            className="text-sm border border-gray-300 rounded px-2 py-1"
          >
            <option value="phase2">المرحلة الثانية</option>
            <option value="simple">مبسط</option>
          </select>
        </div>
      </div>

      {/* QR Code */}
      <div className="flex flex-col items-center space-y-4">
        <div className="p-4 bg-white border-2 border-gray-300 rounded-lg">
          <QRCode
            id={`qr-code-${invoiceData.id}`}
            value={qrData}
            size={size}
            level="M"
            includeMargin={true}
          />
        </div>

        {/* معلومات QR Code */}
        {showDetails && (
          <div className="w-full space-y-3">
            {/* نوع QR Code */}
            <div className="bg-blue-50 p-3 rounded-lg">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-blue-800">
                  {qrType === 'phase2' ? 'متوافق مع المرحلة الثانية' : 'QR Code مبسط'}
                </span>
                <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">
                  {qrType === 'phase2' ? 'ZATCA Phase 2' : 'Simple'}
                </span>
              </div>
            </div>

            {/* معلومات الفاتورة */}
            <div className="grid grid-cols-2 gap-3 text-sm">
              <div>
                <span className="text-gray-600">رقم الفاتورة:</span>
                <p className="font-medium">{invoiceData.invoiceNumber}</p>
              </div>
              <div>
                <span className="text-gray-600">التاريخ:</span>
                <p className="font-medium">{new Date(invoiceData.date).toLocaleDateString('ar-SA')}</p>
              </div>
              <div>
                <span className="text-gray-600">الإجمالي:</span>
                <p className="font-medium">{invoiceData.total.toFixed(2)} ر.س</p>
              </div>
              <div>
                <span className="text-gray-600">الضريبة:</span>
                <p className="font-medium">{invoiceData.tax.toFixed(2)} ر.س</p>
              </div>
            </div>

            {/* hash الفاتورة */}
            <div className="bg-gray-50 p-3 rounded-lg">
              <span className="text-sm text-gray-600">Hash الفاتورة:</span>
              <p className="font-mono text-xs text-gray-800 break-all">{invoiceHash}</p>
            </div>

            {/* معلومات المرحلة الثانية */}
            {qrType === 'phase2' && (
              <div className="bg-green-50 p-3 rounded-lg">
                <h4 className="text-sm font-medium text-green-800 mb-2">
                  متطلبات المرحلة الثانية:
                </h4>
                <ul className="text-xs text-green-700 space-y-1">
                  <li>✓ اسم البائع</li>
                  <li>✓ الرقم الضريبي</li>
                  <li>✓ الطابع الزمني</li>
                  <li>✓ إجمالي الفاتورة</li>
                  <li>✓ إجمالي الضريبة</li>
                  <li>✓ تشفير Base64</li>
                </ul>
              </div>
            )}

            {/* نتيجة اختبار القراءة */}
            {readingTest && (
              <div className={`p-3 rounded-lg ${
                readingTest.isValid && readingTest.hasAllRequiredFields
                  ? 'bg-green-50 border border-green-200'
                  : 'bg-red-50 border border-red-200'
              }`}>
                <div className="flex items-center mb-2">
                  {readingTest.isValid && readingTest.hasAllRequiredFields ? (
                    <CheckIcon className="w-4 h-4 text-green-600 ml-2" />
                  ) : (
                    <ExclamationTriangleIcon className="w-4 h-4 text-red-600 ml-2" />
                  )}
                  <h4 className={`text-sm font-medium ${
                    readingTest.isValid && readingTest.hasAllRequiredFields
                      ? 'text-green-800'
                      : 'text-red-800'
                  }`}>
                    نتيجة اختبار القراءة
                  </h4>
                </div>

                {readingTest.isValid && readingTest.hasAllRequiredFields ? (
                  <div className="text-xs text-green-700">
                    <p>✓ QR Code قابل للقراءة بنجاح</p>
                    <p>✓ جميع الحقول المطلوبة موجودة</p>
                    {readingTest.data && (
                      <div className="mt-2 space-y-1">
                        <p>• اسم البائع: {readingTest.data.sellerName}</p>
                        <p>• الرقم الضريبي: {readingTest.data.vatNumber}</p>
                        <p>• إجمالي الفاتورة: {readingTest.data.invoiceTotal}</p>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-xs text-red-700">
                    <p>✗ خطأ في قراءة QR Code</p>
                    <p>✗ البيانات غير مكتملة أو غير صحيحة</p>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* أزرار العمليات */}
        <div className="flex items-center space-x-3 space-x-reverse">
          <Button
            onClick={handleCopyQRData}
            variant="outline"
            size="sm"
            className="flex items-center"
          >
            {copied ? (
              <CheckIcon className="w-4 h-4 ml-2 text-green-600" />
            ) : (
              <DocumentDuplicateIcon className="w-4 h-4 ml-2" />
            )}
            {copied ? 'تم النسخ' : 'نسخ البيانات'}
          </Button>

          <Button
            onClick={downloadQRCode}
            variant="outline"
            size="sm"
            className="flex items-center"
          >
            <QrCodeIcon className="w-4 h-4 ml-2" />
            تحميل QR
          </Button>
        </div>

        {/* معلومات إضافية للمطورين */}
        {process.env.NODE_ENV === 'development' && (
          <details className="w-full">
            <summary className="text-sm text-gray-600 cursor-pointer hover:text-gray-800">
              عرض بيانات QR Code (للمطورين)
            </summary>
            <div className="mt-2 p-3 bg-gray-100 rounded text-xs font-mono break-all">
              {qrData}
            </div>
          </details>
        )}
      </div>
    </div>
  );
};

export default InvoiceQRCode;
