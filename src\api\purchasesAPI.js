// API لإدارة المشتريات
class PurchasesAPI {
  constructor() {
    this.storageKey = 'purchases_data';
    this.initializeData();
  }

  initializeData() {
    const existingData = localStorage.getItem(this.storageKey);
    if (!existingData) {
      const initialData = {
        purchases: [
          {
            id: 1,
            invoiceNumber: 'PUR-2024-001',
            supplierId: 1,
            supplierName: 'شركة التقنية المتقدمة',
            date: '2024-01-10',
            items: [
              { productId: 1, productName: 'لابتوب Dell', quantity: 10, price: 3000, total: 30000 },
              { productId: 2, productName: 'ماوس لاسلكي', quantity: 50, price: 100, total: 5000 }
            ],
            subtotal: 35000,
            tax: 5250,
            discount: 500,
            total: 39750,
            status: 'مكتملة',
            paymentMethod: 'تحويل بنكي',
            dueDate: '2024-02-10',
            notes: 'شحنة شهرية منتظمة'
          },
          {
            id: 2,
            invoiceNumber: 'PUR-2024-002',
            supplierId: 2,
            supplierName: 'مؤسسة الإلكترونيات',
            date: '2024-01-12',
            items: [
              { productId: 3, productName: 'طابعة HP', quantity: 5, price: 600, total: 3000 }
            ],
            subtotal: 3000,
            tax: 450,
            discount: 0,
            total: 3450,
            status: 'معلقة',
            paymentMethod: 'آجل',
            dueDate: '2024-02-12',
            notes: 'في انتظار التسليم'
          }
        ],
        lastId: 2
      };
      localStorage.setItem(this.storageKey, JSON.stringify(initialData));
    }
  }

  getData() {
    return JSON.parse(localStorage.getItem(this.storageKey));
  }

  saveData(data) {
    localStorage.setItem(this.storageKey, JSON.stringify(data));
  }

  // الحصول على جميع المشتريات
  getAllPurchases() {
    return this.getData().purchases;
  }

  // إضافة فاتورة مشتريات جديدة
  addPurchase(purchaseData) {
    const data = this.getData();
    const newPurchase = {
      id: data.lastId + 1,
      invoiceNumber: `PUR-${new Date().getFullYear()}-${String(data.lastId + 1).padStart(3, '0')}`,
      date: new Date().toISOString().split('T')[0],
      status: 'معلقة',
      ...purchaseData
    };
    
    data.purchases.push(newPurchase);
    data.lastId += 1;
    this.saveData(data);
    return newPurchase;
  }

  // تحديث فاتورة مشتريات
  updatePurchase(id, purchaseData) {
    const data = this.getData();
    const index = data.purchases.findIndex(purchase => purchase.id === id);
    if (index !== -1) {
      data.purchases[index] = { ...data.purchases[index], ...purchaseData };
      this.saveData(data);
      return data.purchases[index];
    }
    return null;
  }

  // حذف فاتورة مشتريات
  deletePurchase(id) {
    const data = this.getData();
    data.purchases = data.purchases.filter(purchase => purchase.id !== id);
    this.saveData(data);
    return true;
  }

  // الحصول على فاتورة بالمعرف
  getPurchaseById(id) {
    const data = this.getData();
    return data.purchases.find(purchase => purchase.id === id);
  }

  // إحصائيات المشتريات
  getPurchasesStats() {
    const purchases = this.getAllPurchases();
    const today = new Date().toISOString().split('T')[0];
    const thisMonth = new Date().toISOString().slice(0, 7);
    
    return {
      totalPurchases: purchases.reduce((sum, purchase) => sum + purchase.total, 0),
      todayPurchases: purchases.filter(purchase => purchase.date === today).reduce((sum, purchase) => sum + purchase.total, 0),
      monthPurchases: purchases.filter(purchase => purchase.date.startsWith(thisMonth)).reduce((sum, purchase) => sum + purchase.total, 0),
      totalInvoices: purchases.length,
      completedPurchases: purchases.filter(purchase => purchase.status === 'مكتملة').length,
      pendingPurchases: purchases.filter(purchase => purchase.status === 'معلقة').length
    };
  }

  // البحث في المشتريات
  searchPurchases(query) {
    const purchases = this.getAllPurchases();
    return purchases.filter(purchase => 
      purchase.invoiceNumber.toLowerCase().includes(query.toLowerCase()) ||
      purchase.supplierName.toLowerCase().includes(query.toLowerCase()) ||
      purchase.status.toLowerCase().includes(query.toLowerCase())
    );
  }
}

export default new PurchasesAPI();
