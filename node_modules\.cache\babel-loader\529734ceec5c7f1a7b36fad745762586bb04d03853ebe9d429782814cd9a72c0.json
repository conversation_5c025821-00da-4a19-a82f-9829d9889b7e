{"ast": null, "code": "import { transition as f } from '../components/transitions/utils/transition.js';\nimport { disposables as m } from '../utils/disposables.js';\nimport { useDisposables as p } from './use-disposables.js';\nimport { useIsMounted as b } from './use-is-mounted.js';\nimport { useIsoMorphicEffect as o } from './use-iso-morphic-effect.js';\nimport { useLatestValue as g } from './use-latest-value.js';\nfunction D({\n  immediate: t,\n  container: s,\n  direction: n,\n  classes: u,\n  onStart: a,\n  onStop: c\n}) {\n  let l = b(),\n    d = p(),\n    e = g(n);\n  o(() => {\n    t && (e.current = \"enter\");\n  }, [t]), o(() => {\n    let r = m();\n    d.add(r.dispose);\n    let i = s.current;\n    if (i && e.current !== \"idle\" && l.current) return r.dispose(), a.current(e.current), r.add(f(i, u.current, e.current === \"enter\", () => {\n      r.dispose(), c.current(e.current);\n    })), r.dispose;\n  }, [n]);\n}\nexport { D as useTransition };", "map": {"version": 3, "names": ["transition", "f", "disposables", "m", "useDisposables", "p", "useIsMounted", "b", "useIsoMorphicEffect", "o", "useLatestValue", "g", "D", "immediate", "t", "container", "s", "direction", "n", "classes", "u", "onStart", "a", "onStop", "c", "l", "d", "e", "current", "r", "add", "dispose", "i", "useTransition"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/node_modules/@headlessui/react/dist/hooks/use-transition.js"], "sourcesContent": ["import{transition as f}from'../components/transitions/utils/transition.js';import{disposables as m}from'../utils/disposables.js';import{useDisposables as p}from'./use-disposables.js';import{useIsMounted as b}from'./use-is-mounted.js';import{useIsoMorphicEffect as o}from'./use-iso-morphic-effect.js';import{useLatestValue as g}from'./use-latest-value.js';function D({immediate:t,container:s,direction:n,classes:u,onStart:a,onStop:c}){let l=b(),d=p(),e=g(n);o(()=>{t&&(e.current=\"enter\")},[t]),o(()=>{let r=m();d.add(r.dispose);let i=s.current;if(i&&e.current!==\"idle\"&&l.current)return r.dispose(),a.current(e.current),r.add(f(i,u.current,e.current===\"enter\",()=>{r.dispose(),c.current(e.current)})),r.dispose},[n])}export{D as useTransition};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,+CAA+C;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,qBAAqB;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,6BAA6B;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAASC,CAACA,CAAC;EAACC,SAAS,EAACC,CAAC;EAACC,SAAS,EAACC,CAAC;EAACC,SAAS,EAACC,CAAC;EAACC,OAAO,EAACC,CAAC;EAACC,OAAO,EAACC,CAAC;EAACC,MAAM,EAACC;AAAC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAAClB,CAAC,CAAC,CAAC;IAACmB,CAAC,GAACrB,CAAC,CAAC,CAAC;IAACsB,CAAC,GAAChB,CAAC,CAACO,CAAC,CAAC;EAACT,CAAC,CAAC,MAAI;IAACK,CAAC,KAAGa,CAAC,CAACC,OAAO,GAAC,OAAO,CAAC;EAAA,CAAC,EAAC,CAACd,CAAC,CAAC,CAAC,EAACL,CAAC,CAAC,MAAI;IAAC,IAAIoB,CAAC,GAAC1B,CAAC,CAAC,CAAC;IAACuB,CAAC,CAACI,GAAG,CAACD,CAAC,CAACE,OAAO,CAAC;IAAC,IAAIC,CAAC,GAAChB,CAAC,CAACY,OAAO;IAAC,IAAGI,CAAC,IAAEL,CAAC,CAACC,OAAO,KAAG,MAAM,IAAEH,CAAC,CAACG,OAAO,EAAC,OAAOC,CAAC,CAACE,OAAO,CAAC,CAAC,EAACT,CAAC,CAACM,OAAO,CAACD,CAAC,CAACC,OAAO,CAAC,EAACC,CAAC,CAACC,GAAG,CAAC7B,CAAC,CAAC+B,CAAC,EAACZ,CAAC,CAACQ,OAAO,EAACD,CAAC,CAACC,OAAO,KAAG,OAAO,EAAC,MAAI;MAACC,CAAC,CAACE,OAAO,CAAC,CAAC,EAACP,CAAC,CAACI,OAAO,CAACD,CAAC,CAACC,OAAO,CAAC;IAAA,CAAC,CAAC,CAAC,EAACC,CAAC,CAACE,OAAO;EAAA,CAAC,EAAC,CAACb,CAAC,CAAC,CAAC;AAAA;AAAC,SAAON,CAAC,IAAIqB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}