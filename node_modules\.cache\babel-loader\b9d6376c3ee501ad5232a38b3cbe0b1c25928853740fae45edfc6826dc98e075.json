{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u0633\\u0637\\u062D \\u0627\\u0644\\u0645\\u0643\\u062A\\u0628\\\\333\\\\src\\\\components\\\\Expenses\\\\ExpenseDetails.jsx\";\nimport React from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { XMarkIcon, PrinterIcon, DocumentArrowDownIcon } from '@heroicons/react/24/outline';\nimport { Button } from '../ui/button';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ExpenseDetails = ({\n  expense,\n  onClose\n}) => {\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('ar-SA', {\n      style: 'currency',\n      currency: 'SAR'\n    }).format(amount);\n  };\n  const handlePrint = () => {\n    window.print();\n    toast.success('تم إرسال المصروف للطباعة');\n  };\n  const handleDownload = () => {\n    toast.success('تم تحميل تفاصيل المصروف');\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'مدفوع':\n        return 'bg-green-100 text-green-800';\n      case 'معلق':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'ملغي':\n        return 'bg-red-100 text-red-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  const getCategoryColor = category => {\n    const colors = {\n      'إيجار': 'bg-blue-100 text-blue-800',\n      'مرافق': 'bg-green-100 text-green-800',\n      'مكتبية': 'bg-purple-100 text-purple-800',\n      'صيانة': 'bg-orange-100 text-orange-800',\n      'رواتب': 'bg-indigo-100 text-indigo-800',\n      'تسويق': 'bg-pink-100 text-pink-800',\n      'سفر': 'bg-cyan-100 text-cyan-800',\n      'اتصالات': 'bg-teal-100 text-teal-800',\n      'تأمين': 'bg-red-100 text-red-800',\n      'أخرى': 'bg-gray-100 text-gray-800'\n    };\n    return colors[category] || 'bg-gray-100 text-gray-800';\n  };\n  return /*#__PURE__*/_jsxDEV(AnimatePresence, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          scale: 0.95\n        },\n        animate: {\n          opacity: 1,\n          scale: 1\n        },\n        exit: {\n          opacity: 0,\n          scale: 0.95\n        },\n        className: \"bg-white rounded-2xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between p-6 border-b border-gray-200 print:hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-gray-900\",\n            children: \"\\u062A\\u0641\\u0627\\u0635\\u064A\\u0644 \\u0627\\u0644\\u0645\\u0635\\u0631\\u0648\\u0641\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4 space-x-reverse\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              onClick: handlePrint,\n              variant: \"outline\",\n              className: \"text-blue-600 hover:text-blue-800\",\n              children: [/*#__PURE__*/_jsxDEV(PrinterIcon, {\n                className: \"w-4 h-4 ml-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 17\n              }, this), \"\\u0637\\u0628\\u0627\\u0639\\u0629\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: handleDownload,\n              variant: \"outline\",\n              className: \"text-green-600 hover:text-green-800\",\n              children: [/*#__PURE__*/_jsxDEV(DocumentArrowDownIcon, {\n                className: \"w-4 h-4 ml-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 17\n              }, this), \"\\u062A\\u062D\\u0645\\u064A\\u0644 PDF\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: onClose,\n              className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(XMarkIcon, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-8 print:p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-3xl font-bold text-gray-900 mb-2\",\n              children: \"\\u0634\\u0631\\u0643\\u0629 \\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0623\\u0639\\u0645\\u0627\\u0644\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"\\u0627\\u0644\\u0631\\u064A\\u0627\\u0636\\u060C \\u0627\\u0644\\u0645\\u0645\\u0644\\u0643\\u0629 \\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629 \\u0627\\u0644\\u0633\\u0639\\u0648\\u062F\\u064A\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"\\u0647\\u0627\\u062A\\u0641: 0112345678 | \\u0627\\u0644\\u0628\\u0631\\u064A\\u062F \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A: <EMAIL>\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4 pt-4 border-t border-gray-200\",\n              children: /*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-semibold text-gray-900\",\n                children: \"\\u0633\\u0646\\u062F \\u0635\\u0631\\u0641\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-8 mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                children: \"\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0645\\u0635\\u0631\\u0648\\u0641\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-600\",\n                    children: \"\\u0643\\u0648\\u062F \\u0627\\u0644\\u0645\\u0635\\u0631\\u0648\\u0641:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 109,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-900 font-mono\",\n                    children: expense.code\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 110,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 108,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-600\",\n                    children: \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 113,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-900\",\n                    children: new Date(expense.date).toLocaleDateString('ar-SA')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 114,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 112,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-600\",\n                    children: \"\\u0627\\u0644\\u0641\\u0626\\u0629:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 117,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-block px-3 py-1 rounded-full text-sm font-medium ${getCategoryColor(expense.category)}`,\n                    children: expense.category\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 118,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-600\",\n                    children: \"\\u0627\\u0644\\u062D\\u0627\\u0644\\u0629:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 123,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-block px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(expense.status)}`,\n                    children: expense.status\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 124,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 19\n                }, this), expense.isRecurring && /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-600\",\n                    children: \"\\u0645\\u0635\\u0631\\u0648\\u0641 \\u0645\\u062A\\u0643\\u0631\\u0631:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 130,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-900\",\n                    children: expense.recurringPeriod\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 131,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 129,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                children: \"\\u062A\\u0641\\u0627\\u0635\\u064A\\u0644 \\u0627\\u0644\\u062F\\u0641\\u0639\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-600\",\n                    children: \"\\u0637\\u0631\\u064A\\u0642\\u0629 \\u0627\\u0644\\u062F\\u0641\\u0639:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 141,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-900\",\n                    children: expense.paymentMethod\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 142,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 140,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-600\",\n                    children: \"\\u0627\\u0644\\u0645\\u0648\\u0631\\u062F/\\u0627\\u0644\\u062C\\u0647\\u0629:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 145,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-900\",\n                    children: expense.vendor || '-'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 146,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-600\",\n                    children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0645\\u0631\\u062C\\u0639:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 149,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-900 font-mono\",\n                    children: expense.reference || '-'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 150,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-600\",\n                    children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0625\\u064A\\u0635\\u0627\\u0644:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 153,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-900 font-mono\",\n                    children: expense.receiptNumber || '-'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 154,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-600\",\n                    children: \"\\u0645\\u0639\\u062A\\u0645\\u062F \\u0645\\u0646:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 157,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-900\",\n                    children: expense.approvedBy || '-'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 158,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"\\u0648\\u0635\\u0641 \\u0627\\u0644\\u0645\\u0635\\u0631\\u0648\\u0641\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 p-4 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-900\",\n                children: expense.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"\\u062A\\u0641\\u0627\\u0635\\u064A\\u0644 \\u0627\\u0644\\u0645\\u0628\\u0644\\u063A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 p-6 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-600\",\n                    children: \"\\u0627\\u0644\\u0645\\u0628\\u0644\\u063A \\u0627\\u0644\\u0623\\u0633\\u0627\\u0633\\u064A:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 178,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-900 font-medium\",\n                    children: formatCurrency(expense.amount)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 179,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-600\",\n                    children: \"\\u0636\\u0631\\u064A\\u0628\\u0629 \\u0627\\u0644\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0636\\u0627\\u0641\\u0629 (15%):\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 182,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-900 font-medium\",\n                    children: formatCurrency(expense.taxAmount)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 183,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"border-t border-gray-300 pt-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-lg font-semibold text-gray-900\",\n                      children: \"\\u0627\\u0644\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0646\\u0647\\u0627\\u0626\\u064A:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 187,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-2xl font-bold text-red-600\",\n                      children: formatCurrency(expense.amount + expense.taxAmount)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 188,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 186,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this), expense.notes && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"\\u0645\\u0644\\u0627\\u062D\\u0638\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 bg-gray-50 p-4 rounded-lg\",\n              children: expense.notes\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-3 gap-8 mt-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border-t border-gray-300 pt-2 mt-16\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-medium\",\n                  children: \"\\u0637\\u0627\\u0644\\u0628 \\u0627\\u0644\\u0635\\u0631\\u0641\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border-t border-gray-300 pt-2 mt-16\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-medium\",\n                  children: \"\\u0627\\u0644\\u0645\\u0639\\u062A\\u0645\\u062F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 19\n                }, this), expense.approvedBy && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600 mt-1\",\n                  children: expense.approvedBy\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border-t border-gray-300 pt-2 mt-16\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-medium\",\n                  children: \"\\u0627\\u0644\\u0645\\u062D\\u0627\\u0633\\u0628\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mt-8 pt-8 border-t border-gray-200 text-sm text-gray-600\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0633\\u0646\\u062F \\u0635\\u0631\\u0641 \\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"\\u062A\\u0627\\u0631\\u064A\\u062E \\u0627\\u0644\\u0637\\u0628\\u0627\\u0639\\u0629: \", new Date().toLocaleDateString('ar-SA')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end p-6 border-t border-gray-200 print:hidden\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: onClose,\n            variant: \"outline\",\n            children: \"\\u0625\\u063A\\u0644\\u0627\\u0642\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 5\n  }, this);\n};\n_c = ExpenseDetails;\nexport default ExpenseDetails;\nvar _c;\n$RefreshReg$(_c, \"ExpenseDetails\");", "map": {"version": 3, "names": ["React", "motion", "AnimatePresence", "XMarkIcon", "PrinterIcon", "DocumentArrowDownIcon", "<PERSON><PERSON>", "toast", "jsxDEV", "_jsxDEV", "ExpenseDetails", "expense", "onClose", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "handlePrint", "window", "print", "success", "handleDownload", "getStatusColor", "status", "getCategoryColor", "category", "colors", "children", "className", "div", "initial", "opacity", "scale", "animate", "exit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "variant", "code", "Date", "date", "toLocaleDateString", "isRecurring", "<PERSON><PERSON><PERSON><PERSON>", "paymentMethod", "vendor", "reference", "receiptNumber", "approvedBy", "description", "taxAmount", "notes", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/src/components/Expenses/ExpenseDetails.jsx"], "sourcesContent": ["import React from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { XMarkIcon, PrinterIcon, DocumentArrowDownIcon } from '@heroicons/react/24/outline';\nimport { Button } from '../ui/button';\nimport toast from 'react-hot-toast';\n\nconst ExpenseDetails = ({ expense, onClose }) => {\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('ar-SA', {\n      style: 'currency',\n      currency: 'SAR'\n    }).format(amount);\n  };\n\n  const handlePrint = () => {\n    window.print();\n    toast.success('تم إرسال المصروف للطباعة');\n  };\n\n  const handleDownload = () => {\n    toast.success('تم تحميل تفاصيل المصروف');\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'مدفوع':\n        return 'bg-green-100 text-green-800';\n      case 'معلق':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'ملغي':\n        return 'bg-red-100 text-red-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getCategoryColor = (category) => {\n    const colors = {\n      'إيجار': 'bg-blue-100 text-blue-800',\n      'مرافق': 'bg-green-100 text-green-800',\n      'مكتبية': 'bg-purple-100 text-purple-800',\n      'صيانة': 'bg-orange-100 text-orange-800',\n      'رواتب': 'bg-indigo-100 text-indigo-800',\n      'تسويق': 'bg-pink-100 text-pink-800',\n      'سفر': 'bg-cyan-100 text-cyan-800',\n      'اتصالات': 'bg-teal-100 text-teal-800',\n      'تأمين': 'bg-red-100 text-red-800',\n      'أخرى': 'bg-gray-100 text-gray-800'\n    };\n    return colors[category] || 'bg-gray-100 text-gray-800';\n  };\n\n  return (\n    <AnimatePresence>\n      <div className=\"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50\">\n        <motion.div\n          initial={{ opacity: 0, scale: 0.95 }}\n          animate={{ opacity: 1, scale: 1 }}\n          exit={{ opacity: 0, scale: 0.95 }}\n          className=\"bg-white rounded-2xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\"\n        >\n          {/* الرأس */}\n          <div className=\"flex items-center justify-between p-6 border-b border-gray-200 print:hidden\">\n            <h2 className=\"text-xl font-semibold text-gray-900\">تفاصيل المصروف</h2>\n            <div className=\"flex items-center space-x-4 space-x-reverse\">\n              <Button\n                onClick={handlePrint}\n                variant=\"outline\"\n                className=\"text-blue-600 hover:text-blue-800\"\n              >\n                <PrinterIcon className=\"w-4 h-4 ml-2\" />\n                طباعة\n              </Button>\n              <Button\n                onClick={handleDownload}\n                variant=\"outline\"\n                className=\"text-green-600 hover:text-green-800\"\n              >\n                <DocumentArrowDownIcon className=\"w-4 h-4 ml-2\" />\n                تحميل PDF\n              </Button>\n              <button\n                onClick={onClose}\n                className=\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg\"\n              >\n                <XMarkIcon className=\"w-5 h-5\" />\n              </button>\n            </div>\n          </div>\n\n          {/* المحتوى */}\n          <div className=\"p-8 print:p-4\">\n            {/* رأس المصروف */}\n            <div className=\"text-center mb-8\">\n              <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">شركة إدارة الأعمال</h1>\n              <p className=\"text-gray-600\">الرياض، المملكة العربية السعودية</p>\n              <p className=\"text-gray-600\">هاتف: 0112345678 | البريد الإلكتروني: <EMAIL></p>\n              <div className=\"mt-4 pt-4 border-t border-gray-200\">\n                <h2 className=\"text-xl font-semibold text-gray-900\">سند صرف</h2>\n              </div>\n            </div>\n\n            {/* معلومات المصروف */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 mb-8\">\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">معلومات المصروف</h3>\n                <div className=\"space-y-3\">\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-600\">كود المصروف:</span>\n                    <p className=\"text-gray-900 font-mono\">{expense.code}</p>\n                  </div>\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-600\">التاريخ:</span>\n                    <p className=\"text-gray-900\">{new Date(expense.date).toLocaleDateString('ar-SA')}</p>\n                  </div>\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-600\">الفئة:</span>\n                    <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${getCategoryColor(expense.category)}`}>\n                      {expense.category}\n                    </span>\n                  </div>\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-600\">الحالة:</span>\n                    <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(expense.status)}`}>\n                      {expense.status}\n                    </span>\n                  </div>\n                  {expense.isRecurring && (\n                    <div>\n                      <span className=\"text-sm font-medium text-gray-600\">مصروف متكرر:</span>\n                      <p className=\"text-gray-900\">{expense.recurringPeriod}</p>\n                    </div>\n                  )}\n                </div>\n              </div>\n\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">تفاصيل الدفع</h3>\n                <div className=\"space-y-3\">\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-600\">طريقة الدفع:</span>\n                    <p className=\"text-gray-900\">{expense.paymentMethod}</p>\n                  </div>\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-600\">المورد/الجهة:</span>\n                    <p className=\"text-gray-900\">{expense.vendor || '-'}</p>\n                  </div>\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-600\">رقم المرجع:</span>\n                    <p className=\"text-gray-900 font-mono\">{expense.reference || '-'}</p>\n                  </div>\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-600\">رقم الإيصال:</span>\n                    <p className=\"text-gray-900 font-mono\">{expense.receiptNumber || '-'}</p>\n                  </div>\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-600\">معتمد من:</span>\n                    <p className=\"text-gray-900\">{expense.approvedBy || '-'}</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* وصف المصروف */}\n            <div className=\"mb-8\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">وصف المصروف</h3>\n              <div className=\"bg-gray-50 p-4 rounded-lg\">\n                <p className=\"text-gray-900\">{expense.description}</p>\n              </div>\n            </div>\n\n            {/* تفاصيل المبلغ */}\n            <div className=\"mb-8\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">تفاصيل المبلغ</h3>\n              <div className=\"bg-gray-50 p-6 rounded-lg\">\n                <div className=\"space-y-3\">\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-gray-600\">المبلغ الأساسي:</span>\n                    <span className=\"text-gray-900 font-medium\">{formatCurrency(expense.amount)}</span>\n                  </div>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-gray-600\">ضريبة القيمة المضافة (15%):</span>\n                    <span className=\"text-gray-900 font-medium\">{formatCurrency(expense.taxAmount)}</span>\n                  </div>\n                  <div className=\"border-t border-gray-300 pt-3\">\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-lg font-semibold text-gray-900\">الإجمالي النهائي:</span>\n                      <span className=\"text-2xl font-bold text-red-600\">\n                        {formatCurrency(expense.amount + expense.taxAmount)}\n                      </span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* الملاحظات */}\n            {expense.notes && (\n              <div className=\"mb-8\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">ملاحظات</h3>\n                <p className=\"text-gray-700 bg-gray-50 p-4 rounded-lg\">{expense.notes}</p>\n              </div>\n            )}\n\n            {/* التوقيعات */}\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mt-12\">\n              <div className=\"text-center\">\n                <div className=\"border-t border-gray-300 pt-2 mt-16\">\n                  <p className=\"font-medium\">طالب الصرف</p>\n                </div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"border-t border-gray-300 pt-2 mt-16\">\n                  <p className=\"font-medium\">المعتمد</p>\n                  {expense.approvedBy && (\n                    <p className=\"text-sm text-gray-600 mt-1\">{expense.approvedBy}</p>\n                  )}\n                </div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"border-t border-gray-300 pt-2 mt-16\">\n                  <p className=\"font-medium\">المحاسب</p>\n                </div>\n              </div>\n            </div>\n\n            {/* تذييل */}\n            <div className=\"text-center mt-8 pt-8 border-t border-gray-200 text-sm text-gray-600\">\n              <p>سند صرف إلكتروني</p>\n              <p>تاريخ الطباعة: {new Date().toLocaleDateString('ar-SA')}</p>\n            </div>\n          </div>\n\n          {/* زر الإغلاق للشاشة فقط */}\n          <div className=\"flex justify-end p-6 border-t border-gray-200 print:hidden\">\n            <Button onClick={onClose} variant=\"outline\">\n              إغلاق\n            </Button>\n          </div>\n        </motion.div>\n      </div>\n    </AnimatePresence>\n  );\n};\n\nexport default ExpenseDetails;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,SAAS,EAAEC,WAAW,EAAEC,qBAAqB,QAAQ,6BAA6B;AAC3F,SAASC,MAAM,QAAQ,cAAc;AACrC,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,cAAc,GAAGA,CAAC;EAAEC,OAAO;EAAEC;AAAQ,CAAC,KAAK;EAC/C,MAAMC,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,MAAMM,WAAW,GAAGA,CAAA,KAAM;IACxBC,MAAM,CAACC,KAAK,CAAC,CAAC;IACdf,KAAK,CAACgB,OAAO,CAAC,0BAA0B,CAAC;EAC3C,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3BjB,KAAK,CAACgB,OAAO,CAAC,yBAAyB,CAAC;EAC1C,CAAC;EAED,MAAME,cAAc,GAAIC,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,OAAO;QACV,OAAO,6BAA6B;MACtC,KAAK,MAAM;QACT,OAAO,+BAA+B;MACxC,KAAK,MAAM;QACT,OAAO,yBAAyB;MAClC;QACE,OAAO,2BAA2B;IACtC;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAIC,QAAQ,IAAK;IACrC,MAAMC,MAAM,GAAG;MACb,OAAO,EAAE,2BAA2B;MACpC,OAAO,EAAE,6BAA6B;MACtC,QAAQ,EAAE,+BAA+B;MACzC,OAAO,EAAE,+BAA+B;MACxC,OAAO,EAAE,+BAA+B;MACxC,OAAO,EAAE,2BAA2B;MACpC,KAAK,EAAE,2BAA2B;MAClC,SAAS,EAAE,2BAA2B;MACtC,OAAO,EAAE,yBAAyB;MAClC,MAAM,EAAE;IACV,CAAC;IACD,OAAOA,MAAM,CAACD,QAAQ,CAAC,IAAI,2BAA2B;EACxD,CAAC;EAED,oBACEnB,OAAA,CAACP,eAAe;IAAA4B,QAAA,eACdrB,OAAA;MAAKsB,SAAS,EAAC,gFAAgF;MAAAD,QAAA,eAC7FrB,OAAA,CAACR,MAAM,CAAC+B,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAK,CAAE;QACrCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAE,CAAE;QAClCE,IAAI,EAAE;UAAEH,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAK,CAAE;QAClCJ,SAAS,EAAC,8EAA8E;QAAAD,QAAA,gBAGxFrB,OAAA;UAAKsB,SAAS,EAAC,6EAA6E;UAAAD,QAAA,gBAC1FrB,OAAA;YAAIsB,SAAS,EAAC,qCAAqC;YAAAD,QAAA,EAAC;UAAc;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvEhC,OAAA;YAAKsB,SAAS,EAAC,6CAA6C;YAAAD,QAAA,gBAC1DrB,OAAA,CAACH,MAAM;cACLoC,OAAO,EAAEtB,WAAY;cACrBuB,OAAO,EAAC,SAAS;cACjBZ,SAAS,EAAC,mCAAmC;cAAAD,QAAA,gBAE7CrB,OAAA,CAACL,WAAW;gBAAC2B,SAAS,EAAC;cAAc;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,kCAE1C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACThC,OAAA,CAACH,MAAM;cACLoC,OAAO,EAAElB,cAAe;cACxBmB,OAAO,EAAC,SAAS;cACjBZ,SAAS,EAAC,qCAAqC;cAAAD,QAAA,gBAE/CrB,OAAA,CAACJ,qBAAqB;gBAAC0B,SAAS,EAAC;cAAc;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,sCAEpD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACThC,OAAA;cACEiC,OAAO,EAAE9B,OAAQ;cACjBmB,SAAS,EAAC,oEAAoE;cAAAD,QAAA,eAE9ErB,OAAA,CAACN,SAAS;gBAAC4B,SAAS,EAAC;cAAS;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNhC,OAAA;UAAKsB,SAAS,EAAC,eAAe;UAAAD,QAAA,gBAE5BrB,OAAA;YAAKsB,SAAS,EAAC,kBAAkB;YAAAD,QAAA,gBAC/BrB,OAAA;cAAIsB,SAAS,EAAC,uCAAuC;cAAAD,QAAA,EAAC;YAAkB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7EhC,OAAA;cAAGsB,SAAS,EAAC,eAAe;cAAAD,QAAA,EAAC;YAAgC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACjEhC,OAAA;cAAGsB,SAAS,EAAC,eAAe;cAAAD,QAAA,EAAC;YAAsD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACvFhC,OAAA;cAAKsB,SAAS,EAAC,oCAAoC;cAAAD,QAAA,eACjDrB,OAAA;gBAAIsB,SAAS,EAAC,qCAAqC;gBAAAD,QAAA,EAAC;cAAO;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNhC,OAAA;YAAKsB,SAAS,EAAC,4CAA4C;YAAAD,QAAA,gBACzDrB,OAAA;cAAAqB,QAAA,gBACErB,OAAA;gBAAIsB,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAAe;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7EhC,OAAA;gBAAKsB,SAAS,EAAC,WAAW;gBAAAD,QAAA,gBACxBrB,OAAA;kBAAAqB,QAAA,gBACErB,OAAA;oBAAMsB,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,EAAC;kBAAY;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvEhC,OAAA;oBAAGsB,SAAS,EAAC,yBAAyB;oBAAAD,QAAA,EAAEnB,OAAO,CAACiC;kBAAI;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACNhC,OAAA;kBAAAqB,QAAA,gBACErB,OAAA;oBAAMsB,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,EAAC;kBAAQ;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnEhC,OAAA;oBAAGsB,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAE,IAAIe,IAAI,CAAClC,OAAO,CAACmC,IAAI,CAAC,CAACC,kBAAkB,CAAC,OAAO;kBAAC;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClF,CAAC,eACNhC,OAAA;kBAAAqB,QAAA,gBACErB,OAAA;oBAAMsB,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,EAAC;kBAAM;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjEhC,OAAA;oBAAMsB,SAAS,EAAE,2DAA2DJ,gBAAgB,CAAChB,OAAO,CAACiB,QAAQ,CAAC,EAAG;oBAAAE,QAAA,EAC9GnB,OAAO,CAACiB;kBAAQ;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNhC,OAAA;kBAAAqB,QAAA,gBACErB,OAAA;oBAAMsB,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,EAAC;kBAAO;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClEhC,OAAA;oBAAMsB,SAAS,EAAE,2DAA2DN,cAAc,CAACd,OAAO,CAACe,MAAM,CAAC,EAAG;oBAAAI,QAAA,EAC1GnB,OAAO,CAACe;kBAAM;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,EACL9B,OAAO,CAACqC,WAAW,iBAClBvC,OAAA;kBAAAqB,QAAA,gBACErB,OAAA;oBAAMsB,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,EAAC;kBAAY;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvEhC,OAAA;oBAAGsB,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAEnB,OAAO,CAACsC;kBAAe;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENhC,OAAA;cAAAqB,QAAA,gBACErB,OAAA;gBAAIsB,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAAY;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1EhC,OAAA;gBAAKsB,SAAS,EAAC,WAAW;gBAAAD,QAAA,gBACxBrB,OAAA;kBAAAqB,QAAA,gBACErB,OAAA;oBAAMsB,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,EAAC;kBAAY;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvEhC,OAAA;oBAAGsB,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAEnB,OAAO,CAACuC;kBAAa;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACNhC,OAAA;kBAAAqB,QAAA,gBACErB,OAAA;oBAAMsB,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,EAAC;kBAAa;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACxEhC,OAAA;oBAAGsB,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAEnB,OAAO,CAACwC,MAAM,IAAI;kBAAG;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACNhC,OAAA;kBAAAqB,QAAA,gBACErB,OAAA;oBAAMsB,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,EAAC;kBAAW;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtEhC,OAAA;oBAAGsB,SAAS,EAAC,yBAAyB;oBAAAD,QAAA,EAAEnB,OAAO,CAACyC,SAAS,IAAI;kBAAG;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE,CAAC,eACNhC,OAAA;kBAAAqB,QAAA,gBACErB,OAAA;oBAAMsB,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,EAAC;kBAAY;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvEhC,OAAA;oBAAGsB,SAAS,EAAC,yBAAyB;oBAAAD,QAAA,EAAEnB,OAAO,CAAC0C,aAAa,IAAI;kBAAG;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC,eACNhC,OAAA;kBAAAqB,QAAA,gBACErB,OAAA;oBAAMsB,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,EAAC;kBAAS;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpEhC,OAAA;oBAAGsB,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAEnB,OAAO,CAAC2C,UAAU,IAAI;kBAAG;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNhC,OAAA;YAAKsB,SAAS,EAAC,MAAM;YAAAD,QAAA,gBACnBrB,OAAA;cAAIsB,SAAS,EAAC,0CAA0C;cAAAD,QAAA,EAAC;YAAW;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzEhC,OAAA;cAAKsB,SAAS,EAAC,2BAA2B;cAAAD,QAAA,eACxCrB,OAAA;gBAAGsB,SAAS,EAAC,eAAe;gBAAAD,QAAA,EAAEnB,OAAO,CAAC4C;cAAW;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNhC,OAAA;YAAKsB,SAAS,EAAC,MAAM;YAAAD,QAAA,gBACnBrB,OAAA;cAAIsB,SAAS,EAAC,0CAA0C;cAAAD,QAAA,EAAC;YAAa;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3EhC,OAAA;cAAKsB,SAAS,EAAC,2BAA2B;cAAAD,QAAA,eACxCrB,OAAA;gBAAKsB,SAAS,EAAC,WAAW;gBAAAD,QAAA,gBACxBrB,OAAA;kBAAKsB,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,gBAChDrB,OAAA;oBAAMsB,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAe;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtDhC,OAAA;oBAAMsB,SAAS,EAAC,2BAA2B;oBAAAD,QAAA,EAAEjB,cAAc,CAACF,OAAO,CAACG,MAAM;kBAAC;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChF,CAAC,eACNhC,OAAA;kBAAKsB,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,gBAChDrB,OAAA;oBAAMsB,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAA2B;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClEhC,OAAA;oBAAMsB,SAAS,EAAC,2BAA2B;oBAAAD,QAAA,EAAEjB,cAAc,CAACF,OAAO,CAAC6C,SAAS;kBAAC;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC,eACNhC,OAAA;kBAAKsB,SAAS,EAAC,+BAA+B;kBAAAD,QAAA,eAC5CrB,OAAA;oBAAKsB,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,gBAChDrB,OAAA;sBAAMsB,SAAS,EAAC,qCAAqC;sBAAAD,QAAA,EAAC;oBAAiB;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC9EhC,OAAA;sBAAMsB,SAAS,EAAC,iCAAiC;sBAAAD,QAAA,EAC9CjB,cAAc,CAACF,OAAO,CAACG,MAAM,GAAGH,OAAO,CAAC6C,SAAS;oBAAC;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGL9B,OAAO,CAAC8C,KAAK,iBACZhD,OAAA;YAAKsB,SAAS,EAAC,MAAM;YAAAD,QAAA,gBACnBrB,OAAA;cAAIsB,SAAS,EAAC,0CAA0C;cAAAD,QAAA,EAAC;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrEhC,OAAA;cAAGsB,SAAS,EAAC,yCAAyC;cAAAD,QAAA,EAAEnB,OAAO,CAAC8C;YAAK;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CACN,eAGDhC,OAAA;YAAKsB,SAAS,EAAC,6CAA6C;YAAAD,QAAA,gBAC1DrB,OAAA;cAAKsB,SAAS,EAAC,aAAa;cAAAD,QAAA,eAC1BrB,OAAA;gBAAKsB,SAAS,EAAC,qCAAqC;gBAAAD,QAAA,eAClDrB,OAAA;kBAAGsB,SAAS,EAAC,aAAa;kBAAAD,QAAA,EAAC;gBAAU;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNhC,OAAA;cAAKsB,SAAS,EAAC,aAAa;cAAAD,QAAA,eAC1BrB,OAAA;gBAAKsB,SAAS,EAAC,qCAAqC;gBAAAD,QAAA,gBAClDrB,OAAA;kBAAGsB,SAAS,EAAC,aAAa;kBAAAD,QAAA,EAAC;gBAAO;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,EACrC9B,OAAO,CAAC2C,UAAU,iBACjB7C,OAAA;kBAAGsB,SAAS,EAAC,4BAA4B;kBAAAD,QAAA,EAAEnB,OAAO,CAAC2C;gBAAU;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAClE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNhC,OAAA;cAAKsB,SAAS,EAAC,aAAa;cAAAD,QAAA,eAC1BrB,OAAA;gBAAKsB,SAAS,EAAC,qCAAqC;gBAAAD,QAAA,eAClDrB,OAAA;kBAAGsB,SAAS,EAAC,aAAa;kBAAAD,QAAA,EAAC;gBAAO;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNhC,OAAA;YAAKsB,SAAS,EAAC,sEAAsE;YAAAD,QAAA,gBACnFrB,OAAA;cAAAqB,QAAA,EAAG;YAAgB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACvBhC,OAAA;cAAAqB,QAAA,GAAG,6EAAe,EAAC,IAAIe,IAAI,CAAC,CAAC,CAACE,kBAAkB,CAAC,OAAO,CAAC;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNhC,OAAA;UAAKsB,SAAS,EAAC,4DAA4D;UAAAD,QAAA,eACzErB,OAAA,CAACH,MAAM;YAACoC,OAAO,EAAE9B,OAAQ;YAAC+B,OAAO,EAAC,SAAS;YAAAb,QAAA,EAAC;UAE5C;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACS,CAAC;AAEtB,CAAC;AAACiB,EAAA,GA7OIhD,cAAc;AA+OpB,eAAeA,cAAc;AAAC,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}