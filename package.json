{"name": "company-management-app", "version": "1.0.0", "private": true, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@tanstack/react-table": "^8.10.3", "autoprefixer": "^10.4.15", "clsx": "^2.1.1", "lucide-react": "^0.294.0", "postcss": "^8.4.27", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-scripts": "^5.0.1", "tailwindcss": "^3.3.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}