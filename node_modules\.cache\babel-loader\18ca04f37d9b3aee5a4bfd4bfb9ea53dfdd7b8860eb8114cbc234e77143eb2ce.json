{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u0633\\u0637\\u062D \\u0627\\u0644\\u0645\\u0643\\u062A\\u0628\\\\333\\\\src\\\\components\\\\Settings\\\\EInvoiceSettings.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { QrCodeIcon, BuildingOfficeIcon, IdentificationIcon, CheckCircleIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';\nimport { Card, CardContent, CardHeader, CardTitle } from '../ui/card';\nimport { Button } from '../ui/button';\nimport { Input } from '../ui/input';\nimport { Label } from '../ui/label';\nimport { validateSaudiVATNumber } from '../../utils/qrCodeGenerator';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EInvoiceSettings = () => {\n  _s();\n  const [settings, setSettings] = useState({\n    companyName: 'شركة إدارة الأعمال',\n    vatNumber: '300000000000003',\n    crNumber: '1010000000',\n    address: 'الرياض، المملكة العربية السعودية',\n    phone: '0112345678',\n    email: '<EMAIL>',\n    website: 'www.company.com',\n    phase2Enabled: true,\n    qrCodeEnabled: true,\n    autoGenerateQR: true\n  });\n  const [isValid, setIsValid] = useState({\n    vatNumber: true,\n    crNumber: true\n  });\n  const [isSaving, setIsSaving] = useState(false);\n  useEffect(() => {\n    // تحميل الإعدادات من التخزين المحلي\n    const savedSettings = localStorage.getItem('einvoice_settings');\n    if (savedSettings) {\n      setSettings(JSON.parse(savedSettings));\n    }\n  }, []);\n  useEffect(() => {\n    // التحقق من صحة الرقم الضريبي\n    setIsValid(prev => ({\n      ...prev,\n      vatNumber: validateSaudiVATNumber(settings.vatNumber)\n    }));\n  }, [settings.vatNumber]);\n  const handleInputChange = (field, value) => {\n    setSettings(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handleSave = async () => {\n    setIsSaving(true);\n    try {\n      // التحقق من صحة البيانات\n      if (!isValid.vatNumber) {\n        toast.error('الرقم الضريبي غير صحيح');\n        return;\n      }\n      if (!settings.companyName.trim()) {\n        toast.error('اسم الشركة مطلوب');\n        return;\n      }\n\n      // حفظ الإعدادات\n      localStorage.setItem('einvoice_settings', JSON.stringify(settings));\n      toast.success('تم حفظ إعدادات الفوترة الإلكترونية بنجاح');\n    } catch (error) {\n      toast.error('حدث خطأ أثناء حفظ الإعدادات');\n    } finally {\n      setIsSaving(false);\n    }\n  };\n  const resetToDefaults = () => {\n    setSettings({\n      companyName: 'شركة إدارة الأعمال',\n      vatNumber: '300000000000003',\n      crNumber: '1010000000',\n      address: 'الرياض، المملكة العربية السعودية',\n      phone: '0112345678',\n      email: '<EMAIL>',\n      website: 'www.company.com',\n      phase2Enabled: true,\n      qrCodeEnabled: true,\n      autoGenerateQR: true\n    });\n    toast.success('تم إعادة تعيين الإعدادات للقيم الافتراضية');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(QrCodeIcon, {\n          className: \"w-8 h-8 text-blue-600 ml-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: \"\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0641\\u0648\\u062A\\u0631\\u0629 \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0645\\u062A\\u0648\\u0627\\u0641\\u0642\\u0629 \\u0645\\u0639 \\u0627\\u0644\\u0645\\u0631\\u062D\\u0644\\u0629 \\u0627\\u0644\\u062B\\u0627\\u0646\\u064A\\u0629 \\u0645\\u0646 \\u0627\\u0644\\u0641\\u0648\\u062A\\u0631\\u0629 \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.1\n      },\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        className: \"border-green-200 bg-green-50\",\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          className: \"p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n              className: \"w-6 h-6 text-green-600 ml-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-green-800\",\n                children: \"\\u0645\\u062A\\u0648\\u0627\\u0641\\u0642 \\u0645\\u0639 \\u0627\\u0644\\u0645\\u0631\\u062D\\u0644\\u0629 \\u0627\\u0644\\u062B\\u0627\\u0646\\u064A\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-green-700\",\n                children: \"\\u0627\\u0644\\u0646\\u0638\\u0627\\u0645 \\u0645\\u062A\\u0648\\u0627\\u0641\\u0642 \\u0645\\u0639 \\u0645\\u062A\\u0637\\u0644\\u0628\\u0627\\u062A \\u0647\\u064A\\u0626\\u0629 \\u0627\\u0644\\u0632\\u0643\\u0627\\u0629 \\u0648\\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u0629 \\u0648\\u0627\\u0644\\u062C\\u0645\\u0627\\u0631\\u0643\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.2\n      },\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n          children: /*#__PURE__*/_jsxDEV(CardTitle, {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(BuildingOfficeIcon, {\n              className: \"w-5 h-5 ml-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this), \"\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0634\\u0631\\u0643\\u0629\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                htmlFor: \"companyName\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0634\\u0631\\u0643\\u0629 *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                id: \"companyName\",\n                type: \"text\",\n                value: settings.companyName,\n                onChange: e => handleInputChange('companyName', e.target.value),\n                placeholder: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0634\\u0631\\u0643\\u0629\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                htmlFor: \"vatNumber\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"\\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u064A *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(Input, {\n                  id: \"vatNumber\",\n                  type: \"text\",\n                  value: settings.vatNumber,\n                  onChange: e => handleInputChange('vatNumber', e.target.value),\n                  placeholder: \"300000000000003\",\n                  className: `${!isValid.vatNumber ? 'border-red-500' : 'border-gray-300'}`,\n                  maxLength: 15,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute left-3 top-1/2 transform -translate-y-1/2\",\n                  children: isValid.vatNumber ? /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                    className: \"w-5 h-5 text-green-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 184,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(ExclamationTriangleIcon, {\n                    className: \"w-5 h-5 text-red-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 186,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this), !isValid.vatNumber && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-red-500 text-sm mt-1\",\n                children: \"\\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u064A \\u064A\\u062C\\u0628 \\u0623\\u0646 \\u064A\\u0643\\u0648\\u0646 15 \\u0631\\u0642\\u0645\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                htmlFor: \"crNumber\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0633\\u062C\\u0644 \\u0627\\u0644\\u062A\\u062C\\u0627\\u0631\\u064A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                id: \"crNumber\",\n                type: \"text\",\n                value: settings.crNumber,\n                onChange: e => handleInputChange('crNumber', e.target.value),\n                placeholder: \"1010000000\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                htmlFor: \"phone\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0647\\u0627\\u062A\\u0641\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                id: \"phone\",\n                type: \"tel\",\n                value: settings.phone,\n                onChange: e => handleInputChange('phone', e.target.value),\n                placeholder: \"0112345678\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                htmlFor: \"email\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"\\u0627\\u0644\\u0628\\u0631\\u064A\\u062F \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                id: \"email\",\n                type: \"email\",\n                value: settings.email,\n                onChange: e => handleInputChange('email', e.target.value),\n                placeholder: \"<EMAIL>\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                htmlFor: \"website\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"\\u0627\\u0644\\u0645\\u0648\\u0642\\u0639 \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                id: \"website\",\n                type: \"url\",\n                value: settings.website,\n                onChange: e => handleInputChange('website', e.target.value),\n                placeholder: \"www.company.com\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              htmlFor: \"address\",\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"\\u0627\\u0644\\u0639\\u0646\\u0648\\u0627\\u0646\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              id: \"address\",\n              rows: 3,\n              value: settings.address,\n              onChange: e => handleInputChange('address', e.target.value),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n              placeholder: \"\\u0627\\u0644\\u0639\\u0646\\u0648\\u0627\\u0646 \\u0627\\u0644\\u062A\\u0641\\u0635\\u064A\\u0644\\u064A \\u0644\\u0644\\u0634\\u0631\\u0643\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.3\n      },\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n          children: /*#__PURE__*/_jsxDEV(CardTitle, {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(QrCodeIcon, {\n              className: \"w-5 h-5 ml-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 15\n            }, this), \"\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A QR Code\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n          className: \"space-y-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: \"\\u062A\\u0641\\u0639\\u064A\\u0644 \\u0627\\u0644\\u0645\\u0631\\u062D\\u0644\\u0629 \\u0627\\u0644\\u062B\\u0627\\u0646\\u064A\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"QR Code \\u0645\\u062A\\u0648\\u0627\\u0641\\u0642 \\u0645\\u0639 \\u0645\\u062A\\u0637\\u0644\\u0628\\u0627\\u062A \\u0647\\u064A\\u0626\\u0629 \\u0627\\u0644\\u0632\\u0643\\u0627\\u0629 \\u0648\\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"relative inline-flex items-center cursor-pointer\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: settings.phase2Enabled,\n                  onChange: e => handleInputChange('phase2Enabled', e.target.checked),\n                  className: \"sr-only peer\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: \"\\u062A\\u0641\\u0639\\u064A\\u0644 QR Code\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"\\u0625\\u0636\\u0627\\u0641\\u0629 QR Code \\u062A\\u0644\\u0642\\u0627\\u0626\\u064A\\u0627\\u064B \\u0644\\u0644\\u0641\\u0648\\u0627\\u062A\\u064A\\u0631\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"relative inline-flex items-center cursor-pointer\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: settings.qrCodeEnabled,\n                  onChange: e => handleInputChange('qrCodeEnabled', e.target.checked),\n                  className: \"sr-only peer\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: \"\\u0625\\u0646\\u0634\\u0627\\u0621 \\u062A\\u0644\\u0642\\u0627\\u0626\\u064A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"\\u0625\\u0646\\u0634\\u0627\\u0621 QR Code \\u062A\\u0644\\u0642\\u0627\\u0626\\u064A\\u0627\\u064B \\u0639\\u0646\\u062F \\u0625\\u0646\\u0634\\u0627\\u0621 \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"relative inline-flex items-center cursor-pointer\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: settings.autoGenerateQR,\n                  onChange: e => handleInputChange('autoGenerateQR', e.target.checked),\n                  className: \"sr-only peer\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.4\n      },\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n          children: /*#__PURE__*/_jsxDEV(CardTitle, {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(IdentificationIcon, {\n              className: \"w-5 h-5 ml-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 15\n            }, this), \"\\u0645\\u062A\\u0637\\u0644\\u0628\\u0627\\u062A \\u0627\\u0644\\u0645\\u0637\\u0627\\u0628\\u0642\\u0629\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-gray-900\",\n                children: \"\\u0627\\u0644\\u0645\\u0631\\u062D\\u0644\\u0629 \\u0627\\u0644\\u062B\\u0627\\u0646\\u064A\\u0629 - \\u0645\\u062A\\u0637\\u0644\\u0628\\u0627\\u062A QR Code:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"space-y-2 text-sm text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                    className: \"w-4 h-4 text-green-500 ml-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 351,\n                    columnNumber: 21\n                  }, this), \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0628\\u0627\\u0626\\u0639\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                    className: \"w-4 h-4 text-green-500 ml-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 355,\n                    columnNumber: 21\n                  }, this), \"\\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u064A \\u0644\\u0644\\u0628\\u0627\\u0626\\u0639\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                    className: \"w-4 h-4 text-green-500 ml-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 359,\n                    columnNumber: 21\n                  }, this), \"\\u0627\\u0644\\u0637\\u0627\\u0628\\u0639 \\u0627\\u0644\\u0632\\u0645\\u0646\\u064A\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                    className: \"w-4 h-4 text-green-500 ml-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 363,\n                    columnNumber: 21\n                  }, this), \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629 \\u0634\\u0627\\u0645\\u0644 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u0629\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                    className: \"w-4 h-4 text-green-500 ml-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 367,\n                    columnNumber: 21\n                  }, this), \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0636\\u0631\\u064A\\u0628\\u0629 \\u0627\\u0644\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0636\\u0627\\u0641\\u0629\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-gray-900\",\n                children: \"\\u0627\\u0644\\u062A\\u0634\\u0641\\u064A\\u0631 \\u0648\\u0627\\u0644\\u062A\\u0646\\u0633\\u064A\\u0642:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"space-y-2 text-sm text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                    className: \"w-4 h-4 text-green-500 ml-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 377,\n                    columnNumber: 21\n                  }, this), \"\\u062A\\u0646\\u0633\\u064A\\u0642 TLV (Tag-Length-Value)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                    className: \"w-4 h-4 text-green-500 ml-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 381,\n                    columnNumber: 21\n                  }, this), \"\\u062A\\u0634\\u0641\\u064A\\u0631 Base64\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                    className: \"w-4 h-4 text-green-500 ml-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 385,\n                    columnNumber: 21\n                  }, this), \"\\u0645\\u0639\\u0627\\u064A\\u064A\\u0631 ISO 8601 \\u0644\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                    className: \"w-4 h-4 text-green-500 ml-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 389,\n                    columnNumber: 21\n                  }, this), \"\\u0627\\u0644\\u062A\\u062D\\u0642\\u0642 \\u0645\\u0646 \\u0635\\u062D\\u0629 \\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u064A\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 388,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 333,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.5\n      },\n      className: \"flex items-center justify-end space-x-4 space-x-reverse\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: resetToDefaults,\n        variant: \"outline\",\n        disabled: isSaving,\n        children: \"\\u0625\\u0639\\u0627\\u062F\\u0629 \\u062A\\u0639\\u064A\\u064A\\u0646\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleSave,\n        disabled: isSaving || !isValid.vatNumber,\n        className: \"bg-blue-600 hover:bg-blue-700 text-white\",\n        children: isSaving ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ml-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 15\n          }, this), \"\\u062C\\u0627\\u0631\\u064A \\u0627\\u0644\\u062D\\u0641\\u0638...\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 13\n        }, this) : 'حفظ الإعدادات'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 400,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 104,\n    columnNumber: 5\n  }, this);\n};\n_s(EInvoiceSettings, \"Y3SkYJOm0h+zkrfIU8gcaH+0gz8=\");\n_c = EInvoiceSettings;\nexport default EInvoiceSettings;\nvar _c;\n$RefreshReg$(_c, \"EInvoiceSettings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "QrCodeIcon", "BuildingOfficeIcon", "IdentificationIcon", "CheckCircleIcon", "ExclamationTriangleIcon", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "<PERSON><PERSON>", "Input", "Label", "validateSaudiVATNumber", "toast", "jsxDEV", "_jsxDEV", "EInvoiceSettings", "_s", "settings", "setSettings", "companyName", "vatNumber", "crNumber", "address", "phone", "email", "website", "phase2Enabled", "qrCodeEnabled", "autoGenerateQR", "<PERSON><PERSON><PERSON><PERSON>", "setIsValid", "isSaving", "setIsSaving", "savedSettings", "localStorage", "getItem", "JSON", "parse", "prev", "handleInputChange", "field", "value", "handleSave", "error", "trim", "setItem", "stringify", "success", "resetToDefaults", "className", "children", "div", "initial", "opacity", "y", "animate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "transition", "delay", "htmlFor", "id", "type", "onChange", "e", "target", "placeholder", "required", "max<PERSON><PERSON><PERSON>", "rows", "checked", "onClick", "variant", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/src/components/Settings/EInvoiceSettings.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { \n  QrCodeIcon, \n  BuildingOfficeIcon, \n  IdentificationIcon,\n  CheckCircleIcon,\n  ExclamationTriangleIcon\n} from '@heroicons/react/24/outline';\nimport { Card, CardContent, CardHeader, CardTitle } from '../ui/card';\nimport { Button } from '../ui/button';\nimport { Input } from '../ui/input';\nimport { Label } from '../ui/label';\nimport { validateSaudiVATNumber } from '../../utils/qrCodeGenerator';\nimport toast from 'react-hot-toast';\n\nconst EInvoiceSettings = () => {\n  const [settings, setSettings] = useState({\n    companyName: 'شركة إدارة الأعمال',\n    vatNumber: '300000000000003',\n    crNumber: '1010000000',\n    address: 'الرياض، المملكة العربية السعودية',\n    phone: '0112345678',\n    email: '<EMAIL>',\n    website: 'www.company.com',\n    phase2Enabled: true,\n    qrCodeEnabled: true,\n    autoGenerateQR: true\n  });\n\n  const [isValid, setIsValid] = useState({\n    vatNumber: true,\n    crNumber: true\n  });\n\n  const [isSaving, setIsSaving] = useState(false);\n\n  useEffect(() => {\n    // تحميل الإعدادات من التخزين المحلي\n    const savedSettings = localStorage.getItem('einvoice_settings');\n    if (savedSettings) {\n      setSettings(JSON.parse(savedSettings));\n    }\n  }, []);\n\n  useEffect(() => {\n    // التحقق من صحة الرقم الضريبي\n    setIsValid(prev => ({\n      ...prev,\n      vatNumber: validateSaudiVATNumber(settings.vatNumber)\n    }));\n  }, [settings.vatNumber]);\n\n  const handleInputChange = (field, value) => {\n    setSettings(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleSave = async () => {\n    setIsSaving(true);\n    \n    try {\n      // التحقق من صحة البيانات\n      if (!isValid.vatNumber) {\n        toast.error('الرقم الضريبي غير صحيح');\n        return;\n      }\n\n      if (!settings.companyName.trim()) {\n        toast.error('اسم الشركة مطلوب');\n        return;\n      }\n\n      // حفظ الإعدادات\n      localStorage.setItem('einvoice_settings', JSON.stringify(settings));\n      toast.success('تم حفظ إعدادات الفوترة الإلكترونية بنجاح');\n      \n    } catch (error) {\n      toast.error('حدث خطأ أثناء حفظ الإعدادات');\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  const resetToDefaults = () => {\n    setSettings({\n      companyName: 'شركة إدارة الأعمال',\n      vatNumber: '300000000000003',\n      crNumber: '1010000000',\n      address: 'الرياض، المملكة العربية السعودية',\n      phone: '0112345678',\n      email: '<EMAIL>',\n      website: 'www.company.com',\n      phase2Enabled: true,\n      qrCodeEnabled: true,\n      autoGenerateQR: true\n    });\n    toast.success('تم إعادة تعيين الإعدادات للقيم الافتراضية');\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* العنوان */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n      >\n        <div className=\"flex items-center mb-6\">\n          <QrCodeIcon className=\"w-8 h-8 text-blue-600 ml-3\" />\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-900\">إعدادات الفوترة الإلكترونية</h2>\n            <p className=\"text-gray-600\">إعدادات متوافقة مع المرحلة الثانية من الفوترة الإلكترونية</p>\n          </div>\n        </div>\n      </motion.div>\n\n      {/* حالة التوافق */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.1 }}\n      >\n        <Card className=\"border-green-200 bg-green-50\">\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center\">\n              <CheckCircleIcon className=\"w-6 h-6 text-green-600 ml-3\" />\n              <div>\n                <h3 className=\"text-lg font-semibold text-green-800\">متوافق مع المرحلة الثانية</h3>\n                <p className=\"text-green-700\">النظام متوافق مع متطلبات هيئة الزكاة والضريبة والجمارك</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </motion.div>\n\n      {/* معلومات الشركة */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.2 }}\n      >\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center\">\n              <BuildingOfficeIcon className=\"w-5 h-5 ml-2\" />\n              معلومات الشركة\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <Label htmlFor=\"companyName\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  اسم الشركة *\n                </Label>\n                <Input\n                  id=\"companyName\"\n                  type=\"text\"\n                  value={settings.companyName}\n                  onChange={(e) => handleInputChange('companyName', e.target.value)}\n                  placeholder=\"اسم الشركة\"\n                  required\n                />\n              </div>\n\n              <div>\n                <Label htmlFor=\"vatNumber\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  الرقم الضريبي *\n                </Label>\n                <div className=\"relative\">\n                  <Input\n                    id=\"vatNumber\"\n                    type=\"text\"\n                    value={settings.vatNumber}\n                    onChange={(e) => handleInputChange('vatNumber', e.target.value)}\n                    placeholder=\"300000000000003\"\n                    className={`${!isValid.vatNumber ? 'border-red-500' : 'border-gray-300'}`}\n                    maxLength={15}\n                    required\n                  />\n                  <div className=\"absolute left-3 top-1/2 transform -translate-y-1/2\">\n                    {isValid.vatNumber ? (\n                      <CheckCircleIcon className=\"w-5 h-5 text-green-500\" />\n                    ) : (\n                      <ExclamationTriangleIcon className=\"w-5 h-5 text-red-500\" />\n                    )}\n                  </div>\n                </div>\n                {!isValid.vatNumber && (\n                  <p className=\"text-red-500 text-sm mt-1\">الرقم الضريبي يجب أن يكون 15 رقم</p>\n                )}\n              </div>\n\n              <div>\n                <Label htmlFor=\"crNumber\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  رقم السجل التجاري\n                </Label>\n                <Input\n                  id=\"crNumber\"\n                  type=\"text\"\n                  value={settings.crNumber}\n                  onChange={(e) => handleInputChange('crNumber', e.target.value)}\n                  placeholder=\"1010000000\"\n                />\n              </div>\n\n              <div>\n                <Label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  رقم الهاتف\n                </Label>\n                <Input\n                  id=\"phone\"\n                  type=\"tel\"\n                  value={settings.phone}\n                  onChange={(e) => handleInputChange('phone', e.target.value)}\n                  placeholder=\"0112345678\"\n                />\n              </div>\n\n              <div>\n                <Label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  البريد الإلكتروني\n                </Label>\n                <Input\n                  id=\"email\"\n                  type=\"email\"\n                  value={settings.email}\n                  onChange={(e) => handleInputChange('email', e.target.value)}\n                  placeholder=\"<EMAIL>\"\n                />\n              </div>\n\n              <div>\n                <Label htmlFor=\"website\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  الموقع الإلكتروني\n                </Label>\n                <Input\n                  id=\"website\"\n                  type=\"url\"\n                  value={settings.website}\n                  onChange={(e) => handleInputChange('website', e.target.value)}\n                  placeholder=\"www.company.com\"\n                />\n              </div>\n            </div>\n\n            <div>\n              <Label htmlFor=\"address\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                العنوان\n              </Label>\n              <textarea\n                id=\"address\"\n                rows={3}\n                value={settings.address}\n                onChange={(e) => handleInputChange('address', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"العنوان التفصيلي للشركة\"\n              />\n            </div>\n          </CardContent>\n        </Card>\n      </motion.div>\n\n      {/* إعدادات QR Code */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.3 }}\n      >\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center\">\n              <QrCodeIcon className=\"w-5 h-5 ml-2\" />\n              إعدادات QR Code\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-6\">\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <h4 className=\"text-sm font-medium text-gray-900\">تفعيل المرحلة الثانية</h4>\n                  <p className=\"text-sm text-gray-600\">QR Code متوافق مع متطلبات هيئة الزكاة والضريبة</p>\n                </div>\n                <label className=\"relative inline-flex items-center cursor-pointer\">\n                  <input\n                    type=\"checkbox\"\n                    checked={settings.phase2Enabled}\n                    onChange={(e) => handleInputChange('phase2Enabled', e.target.checked)}\n                    className=\"sr-only peer\"\n                  />\n                  <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                </label>\n              </div>\n\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <h4 className=\"text-sm font-medium text-gray-900\">تفعيل QR Code</h4>\n                  <p className=\"text-sm text-gray-600\">إضافة QR Code تلقائياً للفواتير</p>\n                </div>\n                <label className=\"relative inline-flex items-center cursor-pointer\">\n                  <input\n                    type=\"checkbox\"\n                    checked={settings.qrCodeEnabled}\n                    onChange={(e) => handleInputChange('qrCodeEnabled', e.target.checked)}\n                    className=\"sr-only peer\"\n                  />\n                  <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                </label>\n              </div>\n\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <h4 className=\"text-sm font-medium text-gray-900\">إنشاء تلقائي</h4>\n                  <p className=\"text-sm text-gray-600\">إنشاء QR Code تلقائياً عند إنشاء الفاتورة</p>\n                </div>\n                <label className=\"relative inline-flex items-center cursor-pointer\">\n                  <input\n                    type=\"checkbox\"\n                    checked={settings.autoGenerateQR}\n                    onChange={(e) => handleInputChange('autoGenerateQR', e.target.checked)}\n                    className=\"sr-only peer\"\n                  />\n                  <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                </label>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </motion.div>\n\n      {/* معلومات المطابقة */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.4 }}\n      >\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center\">\n              <IdentificationIcon className=\"w-5 h-5 ml-2\" />\n              متطلبات المطابقة\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div className=\"space-y-3\">\n                <h4 className=\"font-medium text-gray-900\">المرحلة الثانية - متطلبات QR Code:</h4>\n                <ul className=\"space-y-2 text-sm text-gray-600\">\n                  <li className=\"flex items-center\">\n                    <CheckCircleIcon className=\"w-4 h-4 text-green-500 ml-2\" />\n                    اسم البائع\n                  </li>\n                  <li className=\"flex items-center\">\n                    <CheckCircleIcon className=\"w-4 h-4 text-green-500 ml-2\" />\n                    الرقم الضريبي للبائع\n                  </li>\n                  <li className=\"flex items-center\">\n                    <CheckCircleIcon className=\"w-4 h-4 text-green-500 ml-2\" />\n                    الطابع الزمني\n                  </li>\n                  <li className=\"flex items-center\">\n                    <CheckCircleIcon className=\"w-4 h-4 text-green-500 ml-2\" />\n                    إجمالي الفاتورة شامل الضريبة\n                  </li>\n                  <li className=\"flex items-center\">\n                    <CheckCircleIcon className=\"w-4 h-4 text-green-500 ml-2\" />\n                    إجمالي ضريبة القيمة المضافة\n                  </li>\n                </ul>\n              </div>\n\n              <div className=\"space-y-3\">\n                <h4 className=\"font-medium text-gray-900\">التشفير والتنسيق:</h4>\n                <ul className=\"space-y-2 text-sm text-gray-600\">\n                  <li className=\"flex items-center\">\n                    <CheckCircleIcon className=\"w-4 h-4 text-green-500 ml-2\" />\n                    تنسيق TLV (Tag-Length-Value)\n                  </li>\n                  <li className=\"flex items-center\">\n                    <CheckCircleIcon className=\"w-4 h-4 text-green-500 ml-2\" />\n                    تشفير Base64\n                  </li>\n                  <li className=\"flex items-center\">\n                    <CheckCircleIcon className=\"w-4 h-4 text-green-500 ml-2\" />\n                    معايير ISO 8601 للتاريخ\n                  </li>\n                  <li className=\"flex items-center\">\n                    <CheckCircleIcon className=\"w-4 h-4 text-green-500 ml-2\" />\n                    التحقق من صحة الرقم الضريبي\n                  </li>\n                </ul>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </motion.div>\n\n      {/* أزرار الحفظ */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.5 }}\n        className=\"flex items-center justify-end space-x-4 space-x-reverse\"\n      >\n        <Button\n          onClick={resetToDefaults}\n          variant=\"outline\"\n          disabled={isSaving}\n        >\n          إعادة تعيين\n        </Button>\n        <Button\n          onClick={handleSave}\n          disabled={isSaving || !isValid.vatNumber}\n          className=\"bg-blue-600 hover:bg-blue-700 text-white\"\n        >\n          {isSaving ? (\n            <div className=\"flex items-center\">\n              <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ml-2\"></div>\n              جاري الحفظ...\n            </div>\n          ) : (\n            'حفظ الإعدادات'\n          )}\n        </Button>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default EInvoiceSettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,UAAU,EACVC,kBAAkB,EAClBC,kBAAkB,EAClBC,eAAe,EACfC,uBAAuB,QAClB,6BAA6B;AACpC,SAASC,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,SAAS,QAAQ,YAAY;AACrE,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC;IACvCuB,WAAW,EAAE,oBAAoB;IACjCC,SAAS,EAAE,iBAAiB;IAC5BC,QAAQ,EAAE,YAAY;IACtBC,OAAO,EAAE,kCAAkC;IAC3CC,KAAK,EAAE,YAAY;IACnBC,KAAK,EAAE,kBAAkB;IACzBC,OAAO,EAAE,iBAAiB;IAC1BC,aAAa,EAAE,IAAI;IACnBC,aAAa,EAAE,IAAI;IACnBC,cAAc,EAAE;EAClB,CAAC,CAAC;EAEF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC;IACrCwB,SAAS,EAAE,IAAI;IACfC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAE/CC,SAAS,CAAC,MAAM;IACd;IACA,MAAMoC,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,mBAAmB,CAAC;IAC/D,IAAIF,aAAa,EAAE;MACjBf,WAAW,CAACkB,IAAI,CAACC,KAAK,CAACJ,aAAa,CAAC,CAAC;IACxC;EACF,CAAC,EAAE,EAAE,CAAC;EAENpC,SAAS,CAAC,MAAM;IACd;IACAiC,UAAU,CAACQ,IAAI,KAAK;MAClB,GAAGA,IAAI;MACPlB,SAAS,EAAET,sBAAsB,CAACM,QAAQ,CAACG,SAAS;IACtD,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAACH,QAAQ,CAACG,SAAS,CAAC,CAAC;EAExB,MAAMmB,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IAC1CvB,WAAW,CAACoB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACE,KAAK,GAAGC;IACX,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7BV,WAAW,CAAC,IAAI,CAAC;IAEjB,IAAI;MACF;MACA,IAAI,CAACH,OAAO,CAACT,SAAS,EAAE;QACtBR,KAAK,CAAC+B,KAAK,CAAC,wBAAwB,CAAC;QACrC;MACF;MAEA,IAAI,CAAC1B,QAAQ,CAACE,WAAW,CAACyB,IAAI,CAAC,CAAC,EAAE;QAChChC,KAAK,CAAC+B,KAAK,CAAC,kBAAkB,CAAC;QAC/B;MACF;;MAEA;MACAT,YAAY,CAACW,OAAO,CAAC,mBAAmB,EAAET,IAAI,CAACU,SAAS,CAAC7B,QAAQ,CAAC,CAAC;MACnEL,KAAK,CAACmC,OAAO,CAAC,0CAA0C,CAAC;IAE3D,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACd/B,KAAK,CAAC+B,KAAK,CAAC,6BAA6B,CAAC;IAC5C,CAAC,SAAS;MACRX,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAMgB,eAAe,GAAGA,CAAA,KAAM;IAC5B9B,WAAW,CAAC;MACVC,WAAW,EAAE,oBAAoB;MACjCC,SAAS,EAAE,iBAAiB;MAC5BC,QAAQ,EAAE,YAAY;MACtBC,OAAO,EAAE,kCAAkC;MAC3CC,KAAK,EAAE,YAAY;MACnBC,KAAK,EAAE,kBAAkB;MACzBC,OAAO,EAAE,iBAAiB;MAC1BC,aAAa,EAAE,IAAI;MACnBC,aAAa,EAAE,IAAI;MACnBC,cAAc,EAAE;IAClB,CAAC,CAAC;IACFhB,KAAK,CAACmC,OAAO,CAAC,2CAA2C,CAAC;EAC5D,CAAC;EAED,oBACEjC,OAAA;IAAKmC,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBpC,OAAA,CAAChB,MAAM,CAACqD,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAJ,QAAA,eAE9BpC,OAAA;QAAKmC,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrCpC,OAAA,CAACf,UAAU;UAACkD,SAAS,EAAC;QAA4B;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrD7C,OAAA;UAAAoC,QAAA,gBACEpC,OAAA;YAAImC,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAA2B;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjF7C,OAAA;YAAGmC,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAyD;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGb7C,OAAA,CAAChB,MAAM,CAACqD,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BM,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAAAX,QAAA,eAE3BpC,OAAA,CAACV,IAAI;QAAC6C,SAAS,EAAC,8BAA8B;QAAAC,QAAA,eAC5CpC,OAAA,CAACT,WAAW;UAAC4C,SAAS,EAAC,KAAK;UAAAC,QAAA,eAC1BpC,OAAA;YAAKmC,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCpC,OAAA,CAACZ,eAAe;cAAC+C,SAAS,EAAC;YAA6B;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3D7C,OAAA;cAAAoC,QAAA,gBACEpC,OAAA;gBAAImC,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAAyB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnF7C,OAAA;gBAAGmC,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAC;cAAsD;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGb7C,OAAA,CAAChB,MAAM,CAACqD,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BM,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAAAX,QAAA,eAE3BpC,OAAA,CAACV,IAAI;QAAA8C,QAAA,gBACHpC,OAAA,CAACR,UAAU;UAAA4C,QAAA,eACTpC,OAAA,CAACP,SAAS;YAAC0C,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACtCpC,OAAA,CAACd,kBAAkB;cAACiD,SAAS,EAAC;YAAc;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,mFAEjD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACb7C,OAAA,CAACT,WAAW;UAAC4C,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAChCpC,OAAA;YAAKmC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDpC,OAAA;cAAAoC,QAAA,gBACEpC,OAAA,CAACJ,KAAK;gBAACoD,OAAO,EAAC,aAAa;gBAACb,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEtF;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7C,OAAA,CAACL,KAAK;gBACJsD,EAAE,EAAC,aAAa;gBAChBC,IAAI,EAAC,MAAM;gBACXvB,KAAK,EAAExB,QAAQ,CAACE,WAAY;gBAC5B8C,QAAQ,EAAGC,CAAC,IAAK3B,iBAAiB,CAAC,aAAa,EAAE2B,CAAC,CAACC,MAAM,CAAC1B,KAAK,CAAE;gBAClE2B,WAAW,EAAC,yDAAY;gBACxBC,QAAQ;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN7C,OAAA;cAAAoC,QAAA,gBACEpC,OAAA,CAACJ,KAAK;gBAACoD,OAAO,EAAC,WAAW;gBAACb,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEpF;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7C,OAAA;gBAAKmC,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBpC,OAAA,CAACL,KAAK;kBACJsD,EAAE,EAAC,WAAW;kBACdC,IAAI,EAAC,MAAM;kBACXvB,KAAK,EAAExB,QAAQ,CAACG,SAAU;kBAC1B6C,QAAQ,EAAGC,CAAC,IAAK3B,iBAAiB,CAAC,WAAW,EAAE2B,CAAC,CAACC,MAAM,CAAC1B,KAAK,CAAE;kBAChE2B,WAAW,EAAC,iBAAiB;kBAC7BnB,SAAS,EAAE,GAAG,CAACpB,OAAO,CAACT,SAAS,GAAG,gBAAgB,GAAG,iBAAiB,EAAG;kBAC1EkD,SAAS,EAAE,EAAG;kBACdD,QAAQ;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACF7C,OAAA;kBAAKmC,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,EAChErB,OAAO,CAACT,SAAS,gBAChBN,OAAA,CAACZ,eAAe;oBAAC+C,SAAS,EAAC;kBAAwB;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAEtD7C,OAAA,CAACX,uBAAuB;oBAAC8C,SAAS,EAAC;kBAAsB;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAC5D;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACL,CAAC9B,OAAO,CAACT,SAAS,iBACjBN,OAAA;gBAAGmC,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAgC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAC7E;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEN7C,OAAA;cAAAoC,QAAA,gBACEpC,OAAA,CAACJ,KAAK;gBAACoD,OAAO,EAAC,UAAU;gBAACb,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEnF;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7C,OAAA,CAACL,KAAK;gBACJsD,EAAE,EAAC,UAAU;gBACbC,IAAI,EAAC,MAAM;gBACXvB,KAAK,EAAExB,QAAQ,CAACI,QAAS;gBACzB4C,QAAQ,EAAGC,CAAC,IAAK3B,iBAAiB,CAAC,UAAU,EAAE2B,CAAC,CAACC,MAAM,CAAC1B,KAAK,CAAE;gBAC/D2B,WAAW,EAAC;cAAY;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN7C,OAAA;cAAAoC,QAAA,gBACEpC,OAAA,CAACJ,KAAK;gBAACoD,OAAO,EAAC,OAAO;gBAACb,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhF;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7C,OAAA,CAACL,KAAK;gBACJsD,EAAE,EAAC,OAAO;gBACVC,IAAI,EAAC,KAAK;gBACVvB,KAAK,EAAExB,QAAQ,CAACM,KAAM;gBACtB0C,QAAQ,EAAGC,CAAC,IAAK3B,iBAAiB,CAAC,OAAO,EAAE2B,CAAC,CAACC,MAAM,CAAC1B,KAAK,CAAE;gBAC5D2B,WAAW,EAAC;cAAY;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN7C,OAAA;cAAAoC,QAAA,gBACEpC,OAAA,CAACJ,KAAK;gBAACoD,OAAO,EAAC,OAAO;gBAACb,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhF;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7C,OAAA,CAACL,KAAK;gBACJsD,EAAE,EAAC,OAAO;gBACVC,IAAI,EAAC,OAAO;gBACZvB,KAAK,EAAExB,QAAQ,CAACO,KAAM;gBACtByC,QAAQ,EAAGC,CAAC,IAAK3B,iBAAiB,CAAC,OAAO,EAAE2B,CAAC,CAACC,MAAM,CAAC1B,KAAK,CAAE;gBAC5D2B,WAAW,EAAC;cAAkB;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN7C,OAAA;cAAAoC,QAAA,gBACEpC,OAAA,CAACJ,KAAK;gBAACoD,OAAO,EAAC,SAAS;gBAACb,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAElF;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7C,OAAA,CAACL,KAAK;gBACJsD,EAAE,EAAC,SAAS;gBACZC,IAAI,EAAC,KAAK;gBACVvB,KAAK,EAAExB,QAAQ,CAACQ,OAAQ;gBACxBwC,QAAQ,EAAGC,CAAC,IAAK3B,iBAAiB,CAAC,SAAS,EAAE2B,CAAC,CAACC,MAAM,CAAC1B,KAAK,CAAE;gBAC9D2B,WAAW,EAAC;cAAiB;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN7C,OAAA;YAAAoC,QAAA,gBACEpC,OAAA,CAACJ,KAAK;cAACoD,OAAO,EAAC,SAAS;cAACb,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAElF;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR7C,OAAA;cACEiD,EAAE,EAAC,SAAS;cACZQ,IAAI,EAAE,CAAE;cACR9B,KAAK,EAAExB,QAAQ,CAACK,OAAQ;cACxB2C,QAAQ,EAAGC,CAAC,IAAK3B,iBAAiB,CAAC,SAAS,EAAE2B,CAAC,CAACC,MAAM,CAAC1B,KAAK,CAAE;cAC9DQ,SAAS,EAAC,2GAA2G;cACrHmB,WAAW,EAAC;YAAyB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGb7C,OAAA,CAAChB,MAAM,CAACqD,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BM,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAAAX,QAAA,eAE3BpC,OAAA,CAACV,IAAI;QAAA8C,QAAA,gBACHpC,OAAA,CAACR,UAAU;UAAA4C,QAAA,eACTpC,OAAA,CAACP,SAAS;YAAC0C,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACtCpC,OAAA,CAACf,UAAU;cAACkD,SAAS,EAAC;YAAc;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,sDAEzC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACb7C,OAAA,CAACT,WAAW;UAAC4C,SAAS,EAAC,WAAW;UAAAC,QAAA,eAChCpC,OAAA;YAAKmC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBpC,OAAA;cAAKmC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDpC,OAAA;gBAAAoC,QAAA,gBACEpC,OAAA;kBAAImC,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAqB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC5E7C,OAAA;kBAAGmC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAA8C;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpF,CAAC,eACN7C,OAAA;gBAAOmC,SAAS,EAAC,kDAAkD;gBAAAC,QAAA,gBACjEpC,OAAA;kBACEkD,IAAI,EAAC,UAAU;kBACfQ,OAAO,EAAEvD,QAAQ,CAACS,aAAc;kBAChCuC,QAAQ,EAAGC,CAAC,IAAK3B,iBAAiB,CAAC,eAAe,EAAE2B,CAAC,CAACC,MAAM,CAACK,OAAO,CAAE;kBACtEvB,SAAS,EAAC;gBAAc;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACF7C,OAAA;kBAAKmC,SAAS,EAAC;gBAAyX;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1Y,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAEN7C,OAAA;cAAKmC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDpC,OAAA;gBAAAoC,QAAA,gBACEpC,OAAA;kBAAImC,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAa;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpE7C,OAAA;kBAAGmC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAA+B;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC,eACN7C,OAAA;gBAAOmC,SAAS,EAAC,kDAAkD;gBAAAC,QAAA,gBACjEpC,OAAA;kBACEkD,IAAI,EAAC,UAAU;kBACfQ,OAAO,EAAEvD,QAAQ,CAACU,aAAc;kBAChCsC,QAAQ,EAAGC,CAAC,IAAK3B,iBAAiB,CAAC,eAAe,EAAE2B,CAAC,CAACC,MAAM,CAACK,OAAO,CAAE;kBACtEvB,SAAS,EAAC;gBAAc;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACF7C,OAAA;kBAAKmC,SAAS,EAAC;gBAAyX;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1Y,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAEN7C,OAAA;cAAKmC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDpC,OAAA;gBAAAoC,QAAA,gBACEpC,OAAA;kBAAImC,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAY;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnE7C,OAAA;kBAAGmC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAyC;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E,CAAC,eACN7C,OAAA;gBAAOmC,SAAS,EAAC,kDAAkD;gBAAAC,QAAA,gBACjEpC,OAAA;kBACEkD,IAAI,EAAC,UAAU;kBACfQ,OAAO,EAAEvD,QAAQ,CAACW,cAAe;kBACjCqC,QAAQ,EAAGC,CAAC,IAAK3B,iBAAiB,CAAC,gBAAgB,EAAE2B,CAAC,CAACC,MAAM,CAACK,OAAO,CAAE;kBACvEvB,SAAS,EAAC;gBAAc;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACF7C,OAAA;kBAAKmC,SAAS,EAAC;gBAAyX;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1Y,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGb7C,OAAA,CAAChB,MAAM,CAACqD,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BM,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAAAX,QAAA,eAE3BpC,OAAA,CAACV,IAAI;QAAA8C,QAAA,gBACHpC,OAAA,CAACR,UAAU;UAAA4C,QAAA,eACTpC,OAAA,CAACP,SAAS;YAAC0C,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACtCpC,OAAA,CAACb,kBAAkB;cAACgD,SAAS,EAAC;YAAc;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,+FAEjD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACb7C,OAAA,CAACT,WAAW;UAAA6C,QAAA,eACVpC,OAAA;YAAKmC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDpC,OAAA;cAAKmC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBpC,OAAA;gBAAImC,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAkC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjF7C,OAAA;gBAAImC,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAC7CpC,OAAA;kBAAImC,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC/BpC,OAAA,CAACZ,eAAe;oBAAC+C,SAAS,EAAC;kBAA6B;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,2DAE7D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL7C,OAAA;kBAAImC,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC/BpC,OAAA,CAACZ,eAAe;oBAAC+C,SAAS,EAAC;kBAA6B;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,kHAE7D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL7C,OAAA;kBAAImC,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC/BpC,OAAA,CAACZ,eAAe;oBAAC+C,SAAS,EAAC;kBAA6B;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,6EAE7D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL7C,OAAA;kBAAImC,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC/BpC,OAAA,CAACZ,eAAe;oBAAC+C,SAAS,EAAC;kBAA6B;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,6JAE7D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL7C,OAAA;kBAAImC,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC/BpC,OAAA,CAACZ,eAAe;oBAAC+C,SAAS,EAAC;kBAA6B;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,uJAE7D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEN7C,OAAA;cAAKmC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBpC,OAAA;gBAAImC,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAiB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChE7C,OAAA;gBAAImC,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAC7CpC,OAAA;kBAAImC,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC/BpC,OAAA,CAACZ,eAAe;oBAAC+C,SAAS,EAAC;kBAA6B;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,yDAE7D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL7C,OAAA;kBAAImC,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC/BpC,OAAA,CAACZ,eAAe;oBAAC+C,SAAS,EAAC;kBAA6B;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,yCAE7D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL7C,OAAA;kBAAImC,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC/BpC,OAAA,CAACZ,eAAe;oBAAC+C,SAAS,EAAC;kBAA6B;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,4FAE7D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL7C,OAAA;kBAAImC,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC/BpC,OAAA,CAACZ,eAAe;oBAAC+C,SAAS,EAAC;kBAA6B;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,kJAE7D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGb7C,OAAA,CAAChB,MAAM,CAACqD,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BM,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAC3BZ,SAAS,EAAC,yDAAyD;MAAAC,QAAA,gBAEnEpC,OAAA,CAACN,MAAM;QACLiE,OAAO,EAAEzB,eAAgB;QACzB0B,OAAO,EAAC,SAAS;QACjBC,QAAQ,EAAE5C,QAAS;QAAAmB,QAAA,EACpB;MAED;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT7C,OAAA,CAACN,MAAM;QACLiE,OAAO,EAAE/B,UAAW;QACpBiC,QAAQ,EAAE5C,QAAQ,IAAI,CAACF,OAAO,CAACT,SAAU;QACzC6B,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAEnDnB,QAAQ,gBACPjB,OAAA;UAAKmC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCpC,OAAA;YAAKmC,SAAS,EAAC;UAAmF;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,8DAE3G;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,GAEN;MACD;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAAC3C,EAAA,CA7ZID,gBAAgB;AAAA6D,EAAA,GAAhB7D,gBAAgB;AA+ZtB,eAAeA,gBAAgB;AAAC,IAAA6D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}