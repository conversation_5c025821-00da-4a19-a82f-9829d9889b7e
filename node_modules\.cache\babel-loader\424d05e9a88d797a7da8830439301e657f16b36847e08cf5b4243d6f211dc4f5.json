{"ast": null, "code": "// أداة اختبار QR Code للفوترة الإلكترونية\n\nimport { generateQRCodeData, decodeQRCodeData, testQRCodeReading, validateSaudiVATNumber } from './qrCodeGenerator';\n\n/**\n * اختبار شامل لـ QR Code\n * @param {Object} invoiceData - بيانات الفاتورة\n * @returns {Object} نتائج الاختبار\n */\nexport const comprehensiveQRTest = invoiceData => {\n  const results = {\n    generation: {\n      success: false,\n      data: null,\n      error: null\n    },\n    reading: {\n      success: false,\n      data: null,\n      error: null\n    },\n    validation: {\n      success: false,\n      issues: []\n    },\n    performance: {\n      generationTime: 0,\n      readingTime: 0\n    }\n  };\n  try {\n    // اختبار إنشاء QR Code\n    const startGeneration = performance.now();\n    const qrData = generateQRCodeData(invoiceData);\n    const endGeneration = performance.now();\n    results.generation.success = !!qrData;\n    results.generation.data = qrData;\n    results.performance.generationTime = endGeneration - startGeneration;\n    if (qrData) {\n      // اختبار قراءة QR Code\n      const startReading = performance.now();\n      const readingResult = testQRCodeReading(qrData);\n      const endReading = performance.now();\n      results.reading.success = readingResult.isValid && readingResult.hasAllRequiredFields;\n      results.reading.data = readingResult.data;\n      results.performance.readingTime = endReading - startReading;\n\n      // التحقق من صحة البيانات\n      if (readingResult.data) {\n        const validationIssues = [];\n\n        // التحقق من الرقم الضريبي\n        if (!validateSaudiVATNumber(readingResult.data.vatNumber)) {\n          validationIssues.push('الرقم الضريبي غير صحيح');\n        }\n\n        // التحقق من اسم البائع\n        if (!readingResult.data.sellerName || readingResult.data.sellerName.trim().length === 0) {\n          validationIssues.push('اسم البائع مفقود');\n        }\n\n        // التحقق من الطابع الزمني\n        try {\n          new Date(readingResult.data.timestamp);\n        } catch (e) {\n          validationIssues.push('تنسيق الطابع الزمني غير صحيح');\n        }\n\n        // التحقق من المبالغ\n        const invoiceTotal = parseFloat(readingResult.data.invoiceTotal);\n        const vatAmount = parseFloat(readingResult.data.vatAmount);\n        if (isNaN(invoiceTotal) || invoiceTotal <= 0) {\n          validationIssues.push('إجمالي الفاتورة غير صحيح');\n        }\n        if (isNaN(vatAmount) || vatAmount < 0) {\n          validationIssues.push('مبلغ الضريبة غير صحيح');\n        }\n\n        // التحقق من نسبة الضريبة (15%)\n        if (!isNaN(invoiceTotal) && !isNaN(vatAmount)) {\n          const expectedVat = (invoiceTotal - vatAmount) * 0.15;\n          const vatDifference = Math.abs(vatAmount - expectedVat);\n          if (vatDifference > 0.01) {\n            // هامش خطأ صغير\n            validationIssues.push('نسبة الضريبة لا تتطابق مع 15%');\n          }\n        }\n        results.validation.success = validationIssues.length === 0;\n        results.validation.issues = validationIssues;\n      }\n    }\n  } catch (error) {\n    results.generation.error = error.message;\n    results.reading.error = error.message;\n  }\n  return results;\n};\n\n/**\n * اختبار QR Code مع بيانات متنوعة\n * @returns {Array} نتائج الاختبارات\n */\nexport const runMultipleQRTests = () => {\n  const testCases = [{\n    name: 'فاتورة عادية',\n    invoice: {\n      id: 1,\n      invoiceNumber: 'INV-2024-001',\n      customerName: 'أحمد محمد',\n      date: '2024-01-15',\n      total: 1150,\n      tax: 150,\n      items: [{\n        productName: 'منتج تجريبي',\n        quantity: 1,\n        price: 1000,\n        total: 1000\n      }]\n    }\n  }, {\n    name: 'فاتورة بمبلغ كبير',\n    invoice: {\n      id: 2,\n      invoiceNumber: 'INV-2024-002',\n      customerName: 'شركة التجارة المحدودة',\n      date: '2024-01-16',\n      total: 115000,\n      tax: 15000,\n      items: [{\n        productName: 'معدات مكتبية',\n        quantity: 10,\n        price: 10000,\n        total: 100000\n      }]\n    }\n  }, {\n    name: 'فاتورة بمبلغ صغير',\n    invoice: {\n      id: 3,\n      invoiceNumber: 'INV-2024-003',\n      customerName: 'عميل فردي',\n      date: '2024-01-17',\n      total: 23,\n      tax: 3,\n      items: [{\n        productName: 'قلم',\n        quantity: 1,\n        price: 20,\n        total: 20\n      }]\n    }\n  }, {\n    name: 'فاتورة بأحرف خاصة',\n    invoice: {\n      id: 4,\n      invoiceNumber: 'INV-2024-004',\n      customerName: 'شركة الأعمال & التجارة (المحدودة)',\n      date: '2024-01-18',\n      total: 575,\n      tax: 75,\n      items: [{\n        productName: 'منتج بأحرف خاصة @#$%',\n        quantity: 2,\n        price: 250,\n        total: 500\n      }]\n    }\n  }];\n  return testCases.map(testCase => ({\n    ...testCase,\n    result: comprehensiveQRTest(testCase.invoice)\n  }));\n};\n\n/**\n * تقرير شامل عن حالة QR Code\n * @param {Array} testResults - نتائج الاختبارات\n * @returns {Object} تقرير شامل\n */\nexport const generateQRReport = testResults => {\n  const report = {\n    summary: {\n      totalTests: testResults.length,\n      passedTests: 0,\n      failedTests: 0,\n      averageGenerationTime: 0,\n      averageReadingTime: 0\n    },\n    details: [],\n    recommendations: []\n  };\n  let totalGenerationTime = 0;\n  let totalReadingTime = 0;\n  testResults.forEach(test => {\n    const passed = test.result.generation.success && test.result.reading.success && test.result.validation.success;\n    if (passed) {\n      report.summary.passedTests++;\n    } else {\n      report.summary.failedTests++;\n    }\n    totalGenerationTime += test.result.performance.generationTime;\n    totalReadingTime += test.result.performance.readingTime;\n    report.details.push({\n      testName: test.name,\n      passed: passed,\n      issues: test.result.validation.issues,\n      generationTime: test.result.performance.generationTime.toFixed(2),\n      readingTime: test.result.performance.readingTime.toFixed(2)\n    });\n  });\n  report.summary.averageGenerationTime = (totalGenerationTime / testResults.length).toFixed(2);\n  report.summary.averageReadingTime = (totalReadingTime / testResults.length).toFixed(2);\n\n  // توصيات\n  if (report.summary.failedTests > 0) {\n    report.recommendations.push('يوجد اختبارات فاشلة - يرجى مراجعة إعدادات الشركة');\n  }\n  if (parseFloat(report.summary.averageGenerationTime) > 10) {\n    report.recommendations.push('وقت إنشاء QR Code مرتفع - قد تحتاج لتحسين الأداء');\n  }\n  if (parseFloat(report.summary.averageReadingTime) > 5) {\n    report.recommendations.push('وقت قراءة QR Code مرتفع - قد تحتاج لتحسين خوارزمية القراءة');\n  }\n  if (report.summary.passedTests === report.summary.totalTests) {\n    report.recommendations.push('جميع الاختبارات نجحت - النظام جاهز للإنتاج');\n  }\n  return report;\n};\n\n/**\n * اختبار سريع لـ QR Code\n * @param {Object} invoiceData - بيانات الفاتورة\n * @returns {boolean} نجح الاختبار أم لا\n */\nexport const quickQRTest = invoiceData => {\n  try {\n    const qrData = generateQRCodeData(invoiceData);\n    if (!qrData) return false;\n    const readingResult = testQRCodeReading(qrData);\n    return readingResult.isValid && readingResult.hasAllRequiredFields;\n  } catch (error) {\n    console.error('خطأ في الاختبار السريع:', error);\n    return false;\n  }\n};\n\n/**\n * مقارنة QR Code مع البيانات الأصلية\n * @param {Object} originalData - البيانات الأصلية\n * @param {string} qrData - بيانات QR Code\n * @returns {Object} نتيجة المقارنة\n */\nexport const compareQRWithOriginal = (originalData, qrData) => {\n  const decodedData = decodeQRCodeData(qrData);\n  if (!decodedData) {\n    return {\n      match: false,\n      error: 'فشل في فك تشفير QR Code'\n    };\n  }\n  const comparison = {\n    match: true,\n    differences: []\n  };\n\n  // مقارنة المبالغ\n  const originalTotal = originalData.total.toFixed(2);\n  const decodedTotal = decodedData.invoiceTotal;\n  if (originalTotal !== decodedTotal) {\n    comparison.match = false;\n    comparison.differences.push({\n      field: 'إجمالي الفاتورة',\n      original: originalTotal,\n      decoded: decodedTotal\n    });\n  }\n  const originalTax = originalData.tax.toFixed(2);\n  const decodedTax = decodedData.vatAmount;\n  if (originalTax !== decodedTax) {\n    comparison.match = false;\n    comparison.differences.push({\n      field: 'مبلغ الضريبة',\n      original: originalTax,\n      decoded: decodedTax\n    });\n  }\n  return comparison;\n};", "map": {"version": 3, "names": ["generateQRCodeData", "decodeQRCodeData", "testQRCodeReading", "validateSaudiVATNumber", "comprehensiveQRTest", "invoiceData", "results", "generation", "success", "data", "error", "reading", "validation", "issues", "performance", "generationTime", "readingTime", "startGeneration", "now", "qrData", "endGeneration", "startReading", "readingResult", "endReading", "<PERSON><PERSON><PERSON><PERSON>", "hasAllRequiredFields", "validationIssues", "vatNumber", "push", "sellerName", "trim", "length", "Date", "timestamp", "e", "invoiceTotal", "parseFloat", "vatAmount", "isNaN", "expectedVat", "vatDifference", "Math", "abs", "message", "runMultipleQRTests", "testCases", "name", "invoice", "id", "invoiceNumber", "customerName", "date", "total", "tax", "items", "productName", "quantity", "price", "map", "testCase", "result", "generateQRReport", "testResults", "report", "summary", "totalTests", "passedTests", "failedTests", "averageGenerationTime", "averageReadingTime", "details", "recommendations", "totalGenerationTime", "totalReadingTime", "for<PERSON>ach", "test", "passed", "testName", "toFixed", "quickQRTest", "console", "compareQRWithOriginal", "originalData", "decodedData", "match", "comparison", "differences", "originalTotal", "decodedTotal", "field", "original", "decoded", "originalTax", "decodedTax"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/333/src/utils/qrCodeTester.js"], "sourcesContent": ["// أداة اختبار QR Code للفوترة الإلكترونية\n\nimport { \n  generateQRCodeData, \n  decodeQRCodeData, \n  testQRCodeReading,\n  validateSaudiVATNumber \n} from './qrCodeGenerator';\n\n/**\n * اختبار شامل لـ QR Code\n * @param {Object} invoiceData - بيانات الفاتورة\n * @returns {Object} نتائج الاختبار\n */\nexport const comprehensiveQRTest = (invoiceData) => {\n  const results = {\n    generation: { success: false, data: null, error: null },\n    reading: { success: false, data: null, error: null },\n    validation: { success: false, issues: [] },\n    performance: { generationTime: 0, readingTime: 0 }\n  };\n\n  try {\n    // اختبار إنشاء QR Code\n    const startGeneration = performance.now();\n    const qrData = generateQRCodeData(invoiceData);\n    const endGeneration = performance.now();\n    \n    results.generation.success = !!qrData;\n    results.generation.data = qrData;\n    results.performance.generationTime = endGeneration - startGeneration;\n\n    if (qrData) {\n      // اختبار قراءة QR Code\n      const startReading = performance.now();\n      const readingResult = testQRCodeReading(qrData);\n      const endReading = performance.now();\n      \n      results.reading.success = readingResult.isValid && readingResult.hasAllRequiredFields;\n      results.reading.data = readingResult.data;\n      results.performance.readingTime = endReading - startReading;\n\n      // التحقق من صحة البيانات\n      if (readingResult.data) {\n        const validationIssues = [];\n        \n        // التحقق من الرقم الضريبي\n        if (!validateSaudiVATNumber(readingResult.data.vatNumber)) {\n          validationIssues.push('الرقم الضريبي غير صحيح');\n        }\n        \n        // التحقق من اسم البائع\n        if (!readingResult.data.sellerName || readingResult.data.sellerName.trim().length === 0) {\n          validationIssues.push('اسم البائع مفقود');\n        }\n        \n        // التحقق من الطابع الزمني\n        try {\n          new Date(readingResult.data.timestamp);\n        } catch (e) {\n          validationIssues.push('تنسيق الطابع الزمني غير صحيح');\n        }\n        \n        // التحقق من المبالغ\n        const invoiceTotal = parseFloat(readingResult.data.invoiceTotal);\n        const vatAmount = parseFloat(readingResult.data.vatAmount);\n        \n        if (isNaN(invoiceTotal) || invoiceTotal <= 0) {\n          validationIssues.push('إجمالي الفاتورة غير صحيح');\n        }\n        \n        if (isNaN(vatAmount) || vatAmount < 0) {\n          validationIssues.push('مبلغ الضريبة غير صحيح');\n        }\n        \n        // التحقق من نسبة الضريبة (15%)\n        if (!isNaN(invoiceTotal) && !isNaN(vatAmount)) {\n          const expectedVat = (invoiceTotal - vatAmount) * 0.15;\n          const vatDifference = Math.abs(vatAmount - expectedVat);\n          \n          if (vatDifference > 0.01) { // هامش خطأ صغير\n            validationIssues.push('نسبة الضريبة لا تتطابق مع 15%');\n          }\n        }\n        \n        results.validation.success = validationIssues.length === 0;\n        results.validation.issues = validationIssues;\n      }\n    }\n    \n  } catch (error) {\n    results.generation.error = error.message;\n    results.reading.error = error.message;\n  }\n\n  return results;\n};\n\n/**\n * اختبار QR Code مع بيانات متنوعة\n * @returns {Array} نتائج الاختبارات\n */\nexport const runMultipleQRTests = () => {\n  const testCases = [\n    {\n      name: 'فاتورة عادية',\n      invoice: {\n        id: 1,\n        invoiceNumber: 'INV-2024-001',\n        customerName: 'أحمد محمد',\n        date: '2024-01-15',\n        total: 1150,\n        tax: 150,\n        items: [{ productName: 'منتج تجريبي', quantity: 1, price: 1000, total: 1000 }]\n      }\n    },\n    {\n      name: 'فاتورة بمبلغ كبير',\n      invoice: {\n        id: 2,\n        invoiceNumber: 'INV-2024-002',\n        customerName: 'شركة التجارة المحدودة',\n        date: '2024-01-16',\n        total: 115000,\n        tax: 15000,\n        items: [{ productName: 'معدات مكتبية', quantity: 10, price: 10000, total: 100000 }]\n      }\n    },\n    {\n      name: 'فاتورة بمبلغ صغير',\n      invoice: {\n        id: 3,\n        invoiceNumber: 'INV-2024-003',\n        customerName: 'عميل فردي',\n        date: '2024-01-17',\n        total: 23,\n        tax: 3,\n        items: [{ productName: 'قلم', quantity: 1, price: 20, total: 20 }]\n      }\n    },\n    {\n      name: 'فاتورة بأحرف خاصة',\n      invoice: {\n        id: 4,\n        invoiceNumber: 'INV-2024-004',\n        customerName: 'شركة الأعمال & التجارة (المحدودة)',\n        date: '2024-01-18',\n        total: 575,\n        tax: 75,\n        items: [{ productName: 'منتج بأحرف خاصة @#$%', quantity: 2, price: 250, total: 500 }]\n      }\n    }\n  ];\n\n  return testCases.map(testCase => ({\n    ...testCase,\n    result: comprehensiveQRTest(testCase.invoice)\n  }));\n};\n\n/**\n * تقرير شامل عن حالة QR Code\n * @param {Array} testResults - نتائج الاختبارات\n * @returns {Object} تقرير شامل\n */\nexport const generateQRReport = (testResults) => {\n  const report = {\n    summary: {\n      totalTests: testResults.length,\n      passedTests: 0,\n      failedTests: 0,\n      averageGenerationTime: 0,\n      averageReadingTime: 0\n    },\n    details: [],\n    recommendations: []\n  };\n\n  let totalGenerationTime = 0;\n  let totalReadingTime = 0;\n\n  testResults.forEach(test => {\n    const passed = test.result.generation.success && \n                   test.result.reading.success && \n                   test.result.validation.success;\n    \n    if (passed) {\n      report.summary.passedTests++;\n    } else {\n      report.summary.failedTests++;\n    }\n\n    totalGenerationTime += test.result.performance.generationTime;\n    totalReadingTime += test.result.performance.readingTime;\n\n    report.details.push({\n      testName: test.name,\n      passed: passed,\n      issues: test.result.validation.issues,\n      generationTime: test.result.performance.generationTime.toFixed(2),\n      readingTime: test.result.performance.readingTime.toFixed(2)\n    });\n  });\n\n  report.summary.averageGenerationTime = (totalGenerationTime / testResults.length).toFixed(2);\n  report.summary.averageReadingTime = (totalReadingTime / testResults.length).toFixed(2);\n\n  // توصيات\n  if (report.summary.failedTests > 0) {\n    report.recommendations.push('يوجد اختبارات فاشلة - يرجى مراجعة إعدادات الشركة');\n  }\n\n  if (parseFloat(report.summary.averageGenerationTime) > 10) {\n    report.recommendations.push('وقت إنشاء QR Code مرتفع - قد تحتاج لتحسين الأداء');\n  }\n\n  if (parseFloat(report.summary.averageReadingTime) > 5) {\n    report.recommendations.push('وقت قراءة QR Code مرتفع - قد تحتاج لتحسين خوارزمية القراءة');\n  }\n\n  if (report.summary.passedTests === report.summary.totalTests) {\n    report.recommendations.push('جميع الاختبارات نجحت - النظام جاهز للإنتاج');\n  }\n\n  return report;\n};\n\n/**\n * اختبار سريع لـ QR Code\n * @param {Object} invoiceData - بيانات الفاتورة\n * @returns {boolean} نجح الاختبار أم لا\n */\nexport const quickQRTest = (invoiceData) => {\n  try {\n    const qrData = generateQRCodeData(invoiceData);\n    if (!qrData) return false;\n    \n    const readingResult = testQRCodeReading(qrData);\n    return readingResult.isValid && readingResult.hasAllRequiredFields;\n  } catch (error) {\n    console.error('خطأ في الاختبار السريع:', error);\n    return false;\n  }\n};\n\n/**\n * مقارنة QR Code مع البيانات الأصلية\n * @param {Object} originalData - البيانات الأصلية\n * @param {string} qrData - بيانات QR Code\n * @returns {Object} نتيجة المقارنة\n */\nexport const compareQRWithOriginal = (originalData, qrData) => {\n  const decodedData = decodeQRCodeData(qrData);\n  \n  if (!decodedData) {\n    return { match: false, error: 'فشل في فك تشفير QR Code' };\n  }\n\n  const comparison = {\n    match: true,\n    differences: []\n  };\n\n  // مقارنة المبالغ\n  const originalTotal = originalData.total.toFixed(2);\n  const decodedTotal = decodedData.invoiceTotal;\n  \n  if (originalTotal !== decodedTotal) {\n    comparison.match = false;\n    comparison.differences.push({\n      field: 'إجمالي الفاتورة',\n      original: originalTotal,\n      decoded: decodedTotal\n    });\n  }\n\n  const originalTax = originalData.tax.toFixed(2);\n  const decodedTax = decodedData.vatAmount;\n  \n  if (originalTax !== decodedTax) {\n    comparison.match = false;\n    comparison.differences.push({\n      field: 'مبلغ الضريبة',\n      original: originalTax,\n      decoded: decodedTax\n    });\n  }\n\n  return comparison;\n};\n"], "mappings": "AAAA;;AAEA,SACEA,kBAAkB,EAClBC,gBAAgB,EAChBC,iBAAiB,EACjBC,sBAAsB,QACjB,mBAAmB;;AAE1B;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,mBAAmB,GAAIC,WAAW,IAAK;EAClD,MAAMC,OAAO,GAAG;IACdC,UAAU,EAAE;MAAEC,OAAO,EAAE,KAAK;MAAEC,IAAI,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC;IACvDC,OAAO,EAAE;MAAEH,OAAO,EAAE,KAAK;MAAEC,IAAI,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC;IACpDE,UAAU,EAAE;MAAEJ,OAAO,EAAE,KAAK;MAAEK,MAAM,EAAE;IAAG,CAAC;IAC1CC,WAAW,EAAE;MAAEC,cAAc,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAE;EACnD,CAAC;EAED,IAAI;IACF;IACA,MAAMC,eAAe,GAAGH,WAAW,CAACI,GAAG,CAAC,CAAC;IACzC,MAAMC,MAAM,GAAGnB,kBAAkB,CAACK,WAAW,CAAC;IAC9C,MAAMe,aAAa,GAAGN,WAAW,CAACI,GAAG,CAAC,CAAC;IAEvCZ,OAAO,CAACC,UAAU,CAACC,OAAO,GAAG,CAAC,CAACW,MAAM;IACrCb,OAAO,CAACC,UAAU,CAACE,IAAI,GAAGU,MAAM;IAChCb,OAAO,CAACQ,WAAW,CAACC,cAAc,GAAGK,aAAa,GAAGH,eAAe;IAEpE,IAAIE,MAAM,EAAE;MACV;MACA,MAAME,YAAY,GAAGP,WAAW,CAACI,GAAG,CAAC,CAAC;MACtC,MAAMI,aAAa,GAAGpB,iBAAiB,CAACiB,MAAM,CAAC;MAC/C,MAAMI,UAAU,GAAGT,WAAW,CAACI,GAAG,CAAC,CAAC;MAEpCZ,OAAO,CAACK,OAAO,CAACH,OAAO,GAAGc,aAAa,CAACE,OAAO,IAAIF,aAAa,CAACG,oBAAoB;MACrFnB,OAAO,CAACK,OAAO,CAACF,IAAI,GAAGa,aAAa,CAACb,IAAI;MACzCH,OAAO,CAACQ,WAAW,CAACE,WAAW,GAAGO,UAAU,GAAGF,YAAY;;MAE3D;MACA,IAAIC,aAAa,CAACb,IAAI,EAAE;QACtB,MAAMiB,gBAAgB,GAAG,EAAE;;QAE3B;QACA,IAAI,CAACvB,sBAAsB,CAACmB,aAAa,CAACb,IAAI,CAACkB,SAAS,CAAC,EAAE;UACzDD,gBAAgB,CAACE,IAAI,CAAC,wBAAwB,CAAC;QACjD;;QAEA;QACA,IAAI,CAACN,aAAa,CAACb,IAAI,CAACoB,UAAU,IAAIP,aAAa,CAACb,IAAI,CAACoB,UAAU,CAACC,IAAI,CAAC,CAAC,CAACC,MAAM,KAAK,CAAC,EAAE;UACvFL,gBAAgB,CAACE,IAAI,CAAC,kBAAkB,CAAC;QAC3C;;QAEA;QACA,IAAI;UACF,IAAII,IAAI,CAACV,aAAa,CAACb,IAAI,CAACwB,SAAS,CAAC;QACxC,CAAC,CAAC,OAAOC,CAAC,EAAE;UACVR,gBAAgB,CAACE,IAAI,CAAC,8BAA8B,CAAC;QACvD;;QAEA;QACA,MAAMO,YAAY,GAAGC,UAAU,CAACd,aAAa,CAACb,IAAI,CAAC0B,YAAY,CAAC;QAChE,MAAME,SAAS,GAAGD,UAAU,CAACd,aAAa,CAACb,IAAI,CAAC4B,SAAS,CAAC;QAE1D,IAAIC,KAAK,CAACH,YAAY,CAAC,IAAIA,YAAY,IAAI,CAAC,EAAE;UAC5CT,gBAAgB,CAACE,IAAI,CAAC,0BAA0B,CAAC;QACnD;QAEA,IAAIU,KAAK,CAACD,SAAS,CAAC,IAAIA,SAAS,GAAG,CAAC,EAAE;UACrCX,gBAAgB,CAACE,IAAI,CAAC,uBAAuB,CAAC;QAChD;;QAEA;QACA,IAAI,CAACU,KAAK,CAACH,YAAY,CAAC,IAAI,CAACG,KAAK,CAACD,SAAS,CAAC,EAAE;UAC7C,MAAME,WAAW,GAAG,CAACJ,YAAY,GAAGE,SAAS,IAAI,IAAI;UACrD,MAAMG,aAAa,GAAGC,IAAI,CAACC,GAAG,CAACL,SAAS,GAAGE,WAAW,CAAC;UAEvD,IAAIC,aAAa,GAAG,IAAI,EAAE;YAAE;YAC1Bd,gBAAgB,CAACE,IAAI,CAAC,+BAA+B,CAAC;UACxD;QACF;QAEAtB,OAAO,CAACM,UAAU,CAACJ,OAAO,GAAGkB,gBAAgB,CAACK,MAAM,KAAK,CAAC;QAC1DzB,OAAO,CAACM,UAAU,CAACC,MAAM,GAAGa,gBAAgB;MAC9C;IACF;EAEF,CAAC,CAAC,OAAOhB,KAAK,EAAE;IACdJ,OAAO,CAACC,UAAU,CAACG,KAAK,GAAGA,KAAK,CAACiC,OAAO;IACxCrC,OAAO,CAACK,OAAO,CAACD,KAAK,GAAGA,KAAK,CAACiC,OAAO;EACvC;EAEA,OAAOrC,OAAO;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMsC,kBAAkB,GAAGA,CAAA,KAAM;EACtC,MAAMC,SAAS,GAAG,CAChB;IACEC,IAAI,EAAE,cAAc;IACpBC,OAAO,EAAE;MACPC,EAAE,EAAE,CAAC;MACLC,aAAa,EAAE,cAAc;MAC7BC,YAAY,EAAE,WAAW;MACzBC,IAAI,EAAE,YAAY;MAClBC,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,GAAG;MACRC,KAAK,EAAE,CAAC;QAAEC,WAAW,EAAE,aAAa;QAAEC,QAAQ,EAAE,CAAC;QAAEC,KAAK,EAAE,IAAI;QAAEL,KAAK,EAAE;MAAK,CAAC;IAC/E;EACF,CAAC,EACD;IACEN,IAAI,EAAE,mBAAmB;IACzBC,OAAO,EAAE;MACPC,EAAE,EAAE,CAAC;MACLC,aAAa,EAAE,cAAc;MAC7BC,YAAY,EAAE,uBAAuB;MACrCC,IAAI,EAAE,YAAY;MAClBC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,CAAC;QAAEC,WAAW,EAAE,cAAc;QAAEC,QAAQ,EAAE,EAAE;QAAEC,KAAK,EAAE,KAAK;QAAEL,KAAK,EAAE;MAAO,CAAC;IACpF;EACF,CAAC,EACD;IACEN,IAAI,EAAE,mBAAmB;IACzBC,OAAO,EAAE;MACPC,EAAE,EAAE,CAAC;MACLC,aAAa,EAAE,cAAc;MAC7BC,YAAY,EAAE,WAAW;MACzBC,IAAI,EAAE,YAAY;MAClBC,KAAK,EAAE,EAAE;MACTC,GAAG,EAAE,CAAC;MACNC,KAAK,EAAE,CAAC;QAAEC,WAAW,EAAE,KAAK;QAAEC,QAAQ,EAAE,CAAC;QAAEC,KAAK,EAAE,EAAE;QAAEL,KAAK,EAAE;MAAG,CAAC;IACnE;EACF,CAAC,EACD;IACEN,IAAI,EAAE,mBAAmB;IACzBC,OAAO,EAAE;MACPC,EAAE,EAAE,CAAC;MACLC,aAAa,EAAE,cAAc;MAC7BC,YAAY,EAAE,mCAAmC;MACjDC,IAAI,EAAE,YAAY;MAClBC,KAAK,EAAE,GAAG;MACVC,GAAG,EAAE,EAAE;MACPC,KAAK,EAAE,CAAC;QAAEC,WAAW,EAAE,sBAAsB;QAAEC,QAAQ,EAAE,CAAC;QAAEC,KAAK,EAAE,GAAG;QAAEL,KAAK,EAAE;MAAI,CAAC;IACtF;EACF,CAAC,CACF;EAED,OAAOP,SAAS,CAACa,GAAG,CAACC,QAAQ,KAAK;IAChC,GAAGA,QAAQ;IACXC,MAAM,EAAExD,mBAAmB,CAACuD,QAAQ,CAACZ,OAAO;EAC9C,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMc,gBAAgB,GAAIC,WAAW,IAAK;EAC/C,MAAMC,MAAM,GAAG;IACbC,OAAO,EAAE;MACPC,UAAU,EAAEH,WAAW,CAAC/B,MAAM;MAC9BmC,WAAW,EAAE,CAAC;MACdC,WAAW,EAAE,CAAC;MACdC,qBAAqB,EAAE,CAAC;MACxBC,kBAAkB,EAAE;IACtB,CAAC;IACDC,OAAO,EAAE,EAAE;IACXC,eAAe,EAAE;EACnB,CAAC;EAED,IAAIC,mBAAmB,GAAG,CAAC;EAC3B,IAAIC,gBAAgB,GAAG,CAAC;EAExBX,WAAW,CAACY,OAAO,CAACC,IAAI,IAAI;IAC1B,MAAMC,MAAM,GAAGD,IAAI,CAACf,MAAM,CAACrD,UAAU,CAACC,OAAO,IAC9BmE,IAAI,CAACf,MAAM,CAACjD,OAAO,CAACH,OAAO,IAC3BmE,IAAI,CAACf,MAAM,CAAChD,UAAU,CAACJ,OAAO;IAE7C,IAAIoE,MAAM,EAAE;MACVb,MAAM,CAACC,OAAO,CAACE,WAAW,EAAE;IAC9B,CAAC,MAAM;MACLH,MAAM,CAACC,OAAO,CAACG,WAAW,EAAE;IAC9B;IAEAK,mBAAmB,IAAIG,IAAI,CAACf,MAAM,CAAC9C,WAAW,CAACC,cAAc;IAC7D0D,gBAAgB,IAAIE,IAAI,CAACf,MAAM,CAAC9C,WAAW,CAACE,WAAW;IAEvD+C,MAAM,CAACO,OAAO,CAAC1C,IAAI,CAAC;MAClBiD,QAAQ,EAAEF,IAAI,CAAC7B,IAAI;MACnB8B,MAAM,EAAEA,MAAM;MACd/D,MAAM,EAAE8D,IAAI,CAACf,MAAM,CAAChD,UAAU,CAACC,MAAM;MACrCE,cAAc,EAAE4D,IAAI,CAACf,MAAM,CAAC9C,WAAW,CAACC,cAAc,CAAC+D,OAAO,CAAC,CAAC,CAAC;MACjE9D,WAAW,EAAE2D,IAAI,CAACf,MAAM,CAAC9C,WAAW,CAACE,WAAW,CAAC8D,OAAO,CAAC,CAAC;IAC5D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFf,MAAM,CAACC,OAAO,CAACI,qBAAqB,GAAG,CAACI,mBAAmB,GAAGV,WAAW,CAAC/B,MAAM,EAAE+C,OAAO,CAAC,CAAC,CAAC;EAC5Ff,MAAM,CAACC,OAAO,CAACK,kBAAkB,GAAG,CAACI,gBAAgB,GAAGX,WAAW,CAAC/B,MAAM,EAAE+C,OAAO,CAAC,CAAC,CAAC;;EAEtF;EACA,IAAIf,MAAM,CAACC,OAAO,CAACG,WAAW,GAAG,CAAC,EAAE;IAClCJ,MAAM,CAACQ,eAAe,CAAC3C,IAAI,CAAC,kDAAkD,CAAC;EACjF;EAEA,IAAIQ,UAAU,CAAC2B,MAAM,CAACC,OAAO,CAACI,qBAAqB,CAAC,GAAG,EAAE,EAAE;IACzDL,MAAM,CAACQ,eAAe,CAAC3C,IAAI,CAAC,kDAAkD,CAAC;EACjF;EAEA,IAAIQ,UAAU,CAAC2B,MAAM,CAACC,OAAO,CAACK,kBAAkB,CAAC,GAAG,CAAC,EAAE;IACrDN,MAAM,CAACQ,eAAe,CAAC3C,IAAI,CAAC,4DAA4D,CAAC;EAC3F;EAEA,IAAImC,MAAM,CAACC,OAAO,CAACE,WAAW,KAAKH,MAAM,CAACC,OAAO,CAACC,UAAU,EAAE;IAC5DF,MAAM,CAACQ,eAAe,CAAC3C,IAAI,CAAC,4CAA4C,CAAC;EAC3E;EAEA,OAAOmC,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMgB,WAAW,GAAI1E,WAAW,IAAK;EAC1C,IAAI;IACF,MAAMc,MAAM,GAAGnB,kBAAkB,CAACK,WAAW,CAAC;IAC9C,IAAI,CAACc,MAAM,EAAE,OAAO,KAAK;IAEzB,MAAMG,aAAa,GAAGpB,iBAAiB,CAACiB,MAAM,CAAC;IAC/C,OAAOG,aAAa,CAACE,OAAO,IAAIF,aAAa,CAACG,oBAAoB;EACpE,CAAC,CAAC,OAAOf,KAAK,EAAE;IACdsE,OAAO,CAACtE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/C,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMuE,qBAAqB,GAAGA,CAACC,YAAY,EAAE/D,MAAM,KAAK;EAC7D,MAAMgE,WAAW,GAAGlF,gBAAgB,CAACkB,MAAM,CAAC;EAE5C,IAAI,CAACgE,WAAW,EAAE;IAChB,OAAO;MAAEC,KAAK,EAAE,KAAK;MAAE1E,KAAK,EAAE;IAA0B,CAAC;EAC3D;EAEA,MAAM2E,UAAU,GAAG;IACjBD,KAAK,EAAE,IAAI;IACXE,WAAW,EAAE;EACf,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGL,YAAY,CAAC9B,KAAK,CAAC0B,OAAO,CAAC,CAAC,CAAC;EACnD,MAAMU,YAAY,GAAGL,WAAW,CAAChD,YAAY;EAE7C,IAAIoD,aAAa,KAAKC,YAAY,EAAE;IAClCH,UAAU,CAACD,KAAK,GAAG,KAAK;IACxBC,UAAU,CAACC,WAAW,CAAC1D,IAAI,CAAC;MAC1B6D,KAAK,EAAE,iBAAiB;MACxBC,QAAQ,EAAEH,aAAa;MACvBI,OAAO,EAAEH;IACX,CAAC,CAAC;EACJ;EAEA,MAAMI,WAAW,GAAGV,YAAY,CAAC7B,GAAG,CAACyB,OAAO,CAAC,CAAC,CAAC;EAC/C,MAAMe,UAAU,GAAGV,WAAW,CAAC9C,SAAS;EAExC,IAAIuD,WAAW,KAAKC,UAAU,EAAE;IAC9BR,UAAU,CAACD,KAAK,GAAG,KAAK;IACxBC,UAAU,CAACC,WAAW,CAAC1D,IAAI,CAAC;MAC1B6D,KAAK,EAAE,cAAc;MACrBC,QAAQ,EAAEE,WAAW;MACrBD,OAAO,EAAEE;IACX,CAAC,CAAC;EACJ;EAEA,OAAOR,UAAU;AACnB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}